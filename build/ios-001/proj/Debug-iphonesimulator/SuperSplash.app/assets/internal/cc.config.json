{"importBase": "import", "nativeBase": "native", "name": "internal", "deps": [], "uuids": ["06585a170", "1cAq5vRJJJFbj4dJKjseTN", "509DSLyINOL48RziM7hZ+h", "609xlc7CpF67qUiVX2DoHQ", "6aLQc0vZ5N35RuyqUkmMt1", "81Dpbk5FZEaJtZ9OjzlzLA", "87HDtsc3lBnb2jeUsjmrkN", "8bvbzdXNRBALbVt8liW2EH", "97CwWYvLBHFJH7LoFEDczY", "a3zQCfCrBCDZJ4uf2rk5u8", "b51hFfA3BNfKrTwZTMcc+Y", "bc1kzGLdlD9qu+ZjGNMyAy", "c2chXYaDVLaL+7verGEAwE", "d9MFkNu5JMyIvRI80Cf57f", "ddOhRNq39B8IK4LkOgkNSW", "e9qpo+WytKx6LHBz3isrJP", "ef6OKj6s5Ce7Txy4qTfsd9", "f0QW5oAgBLd6kmT50W5JTa", "f9KAbXF2hEP6/oErzehNDw", "fdoJXLgx1GAa2UhGATlj3o"], "paths": {"1": ["db:/internal/effects/internal/builtin-graphics", 0, 1], "2": ["db:/internal/default_materials/ui-alpha-test-material", 1, 1], "3": ["db:/internal/effects/for2d/builtin-sprite", 0, 1], "4": ["db:/internal/effects/pipeline/post-process/tone-mapping", 0, 1], "5": ["db:/internal/effects/internal/builtin-clear-stencil", 0, 1], "6": ["db:/internal/effects/util/profiler", 0, 1], "7": ["db:/internal/default_materials/default-clear-stencil", 1, 1], "8": ["db:/internal/effects/util/splash-screen", 0, 1], "9": ["db:/internal/effects/builtin-unlit", 0, 1], "10": ["db:/internal/default_materials/default-spine-material", 1, 1], "11": ["db:/internal/default_materials/missing-effect-material", 1, 1], "12": ["db:/internal/effects/for2d/builtin-spine", 0, 1], "13": ["db:/internal/default_materials/missing-material", 1, 1], "14": ["db:/internal/default_materials/ui-sprite-gray-alpha-sep-material", 1, 1], "15": ["db:/internal/default_materials/ui-base-material", 1, 1], "16": ["db:/internal/default_materials/ui-sprite-gray-material", 1, 1], "17": ["db:/internal/default_materials/ui-graphics-material", 1, 1], "18": ["db:/internal/default_materials/ui-sprite-alpha-sep-material", 1, 1], "19": ["db:/internal/default_materials/ui-sprite-material", 1, 1]}, "scenes": {}, "packs": {"06585a170": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, "versions": {"import": [], "native": []}, "redirect": [], "debug": false, "extensionMap": {}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.EffectAsset", "cc.Material"]}