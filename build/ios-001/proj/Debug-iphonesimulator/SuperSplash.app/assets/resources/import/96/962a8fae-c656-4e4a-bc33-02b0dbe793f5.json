[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "README", "# 颜料预制体目录\n\n这个目录用于存放游戏中车辆喷洒的颜料预制体。\n\n## 创建颜料预制体的步骤\n\n### 1. 基础设置\n1. 在场景中创建空节点，命名如 \"PlayerPaint\" 或 \"AIPaint_Red\"\n2. 设置节点大小约为 (40, 40) 像素，与车辆大小相当\n\n### 2. 添加视觉组件\n1. 添加 Sprite 组件\n2. 设置颜料的图片资源（可以是圆形、方形等形状）\n3. 调整颜色以区分不同车辆\n\n### 3. 设置节点层级\n**重要**: 颜料预制体的节点层级会被PaintManager自动设置为 `UI_2D`，确保颜料固定在游戏世界坐标中，而不是跟随摄像头移动。\n\n### 4. 添加脚本组件\n1. 添加 PaintSpot 脚本组件\n2. 配置属性：\n   - `fadeTime`: 颜料淡化时间（如果启用淡化）\n   - `enableFade`: 是否启用自动淡化\n\n### 5. 保存为预制体\n1. 将配置好的节点拖拽到此目录\n2. 命名规范：\n   - `player-paint.prefab` - 玩家颜料\n   - `ai-paint-red.prefab` - 红色AI颜料\n   - `ai-paint-blue.prefab` - 蓝色AI颜料\n   - 等等...\n\n## 建议的颜料配色方案\n\n- **玩家**: 绿色 (#00FF00)\n- **AI车辆1**: 红色 (#FF0000)\n- **AI车辆2**: 蓝色 (#0000FF)\n- **AI车辆3**: 黄色 (#FFFF00)\n- **AI车辆4**: 紫色 (#FF00FF)\n\n## 注意事项\n\n1. 颜料预制体必须包含 PaintSpot 组件\n2. 建议使用半透明效果以增强视觉层次\n3. 颜料大小应与车辆大小匹配\n4. 不同车辆应使用明显区分的颜色\n"]], 0, 0, [], [], []]