dependencies: \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLDescriptorSet.mm \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLBuffer.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXBuffer.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXObject.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXDef.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXDef-common.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/TypeDef.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Macros.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Assertf.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/unordered_map.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/hash_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/user.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/select_compiler_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/compiler/clang.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/compiler/clang_version.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/select_stdlib_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/stdlib/libcpp.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/select_platform_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/platform/macos.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/posix_features.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/suffix.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/pmr/polymorphic_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/type_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/config_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/meta_utils.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/meta_utils_core.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/config_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/assert.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/static_assert.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/utility_core.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/core.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/dispatch_uses_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/config_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/allocator_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/container_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/std_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/std_ns_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/std_ns_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/mpl.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/mpl.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/config_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/config_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/config_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/type_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/placement_new.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/pointer_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/pointer_rebind.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/pointer_element.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/has_member_function_callable_with.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/fwd_macros.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/uses_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/uses_allocator_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/addressof.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/pair.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/variadic_templates_tools.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/adl_move_swap.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/minimal_pair_header.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/no_exceptions_support.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/new_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/throw_exception.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/ignore_unused.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/pmr/memory_resource.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/pmr/global_resource.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/auto_link.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/memory/Memory.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/string.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/vector.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/math/Math.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/hash.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/hash.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/hash_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/detail/hash_float.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/detail/float_functions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/no_tr1/cmath.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/detail/limits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/limits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/enable_if.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/integer/static_log2.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/integer_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/cstdint.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_enum.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/intrinsics.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/detail/config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/version.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/integral_constant.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_integral.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/extensions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/detail/container_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/detail/hash_float.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/detail/float_functions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/detail/limits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/extensions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/RefCounted.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLDescriptorSet.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXDescriptorSet.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLDescriptorSetLayout.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXDescriptorSetLayout.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLGPUObjects.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/../../base/Utils.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLConfig.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLDevice.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXDevice.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXCommandBuffer.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXInputAssembler.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Utils.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXFramebuffer.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Ptr.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/RefVector.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Log.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Random.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXPipelineLayout.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXPipelineState.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXQueryPool.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXQueue.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXRenderPass.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXShader.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXSwapchain.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/GFXTexture.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/array.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/states/GFXBufferBarrier.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/states/../GFXObject.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/states/GFXGeneralBarrier.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/states/GFXSampler.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-base/states/GFXTextureBarrier.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLUtils.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/queue.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLSampler.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/gfx-metal/MTLTexture.h
