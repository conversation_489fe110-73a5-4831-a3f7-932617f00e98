dependencies: \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/interfaces/modules/canvas/CanvasRenderingContext2D.cpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/interfaces/modules/canvas/CanvasRenderingContext2D.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/interfaces/modules/canvas/ICanvasRenderingContext2D.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Data.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Macros.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Assertf.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/array.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/string.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/pmr/polymorphic_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/user.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/select_compiler_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/compiler/clang.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/compiler/clang_version.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/select_stdlib_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/stdlib/libcpp.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/select_platform_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/platform/macos.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/posix_features.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/detail/suffix.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/type_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/config_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/meta_utils.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/meta_utils_core.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/config_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/assert.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/static_assert.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/utility_core.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/core.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/dispatch_uses_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/config_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/allocator_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/container_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/std_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/std_ns_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/std_ns_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/mpl.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/mpl.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/config_begin.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/config_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/config_end.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/type_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/placement_new.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/pointer_traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/workaround.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/pointer_rebind.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/pointer_element.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/has_member_function_callable_with.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/detail/fwd_macros.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/uses_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/uses_allocator_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/addressof.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/pair.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/variadic_templates_tools.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/adl_move_swap.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/intrusive/detail/minimal_pair_header.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/no_exceptions_support.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/new_allocator.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/throw_exception.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/ignore_unused.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/pmr/memory_resource.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/pmr/global_resource.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container/detail/auto_link.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/math/Geometry.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/math/Vec2.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/math/Math.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/math/MathBase.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/hash.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/hash.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/hash_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/detail/hash_float.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/detail/float_functions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/config/no_tr1/cmath.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/detail/limits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/limits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/enable_if.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/integer/static_log2.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/integer_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/cstdint.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_enum.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/intrinsics.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/detail/config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/version.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/integral_constant.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_integral.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/container_hash/extensions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/detail/container_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/hash_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/detail/hash_float.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/detail/float_functions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/detail/limits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/hash/extensions.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/math/Vec2.inl \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/interfaces/OSInterface.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/engine/EngineEvents.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/unordered_map.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/vector.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/event/EventBus.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Log.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/memop/Pool.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/TypeDef.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/event/intl/EventIntl.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/event/intl/List.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/event/intl/EventBusMacros.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/csscolorparser.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/SeApi.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/config.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/SeApi.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/Class.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/../config.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/../Define.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/../Value.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/../HandleObject.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/Base.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/../PrivateObject.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Ptr.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/RefCounted.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/memory/Memory.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/HasMemberFunction.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/container/unordered_set.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/HelperMacros.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/std/optional.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/none.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/none_t.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/optional.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/addressof.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/explicit_operator_bool.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/core/swap.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/bad_optional_access.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/throw_exception.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/exception/exception.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/assert/source_location.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/current_function.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/alignment_of.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/conditional.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/has_nothrow_constructor.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_default_constructible.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_complete.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/declval.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/add_rvalue_reference.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_void.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_reference.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_lvalue_reference.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_rvalue_reference.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/remove_reference.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_function.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/detail/is_function_cxx_11.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/detail/yes_no_type.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/type_with_alignment.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_pod.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_scalar.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_arithmetic.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_floating_point.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_pointer.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_member_pointer.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_member_function_pointer.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/detail/is_member_function_pointer_cxx_11.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/remove_const.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/decay.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_array.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/remove_bounds.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/remove_extent.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/add_pointer.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/remove_cv.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_base_of.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_base_and_derived.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_same.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_class.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_const.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_constructible.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_destructible.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_nothrow_move_assignable.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/has_trivial_move_assign.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_assignable.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_volatile.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/has_nothrow_assign.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/enable_if.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/is_nothrow_move_constructible.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/utility.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/move/traits.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/utility/compare_pointees.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/utility/result_of.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/type_traits/type_identity.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/utility/detail/result_of_variadic.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/optional_fwd.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_config.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_factory_support.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_aligned_storage.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_trivially_copyable_base.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_reference_spec.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_relops.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost/optional/detail/optional_swap.hpp \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/Object.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/PrivateObject.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/../RefCounter.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/ObjectWrap.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/ScriptEngine.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/v8/Utils.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/HandleObject.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/Object.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/State.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper/Value.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/manual/jsb_platform.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/FileUtils.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/base/Value.h \
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/apple/modules/CanvasRenderingContext2DDelegate.h
