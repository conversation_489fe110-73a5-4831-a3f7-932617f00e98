{"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"targets": "chrome 80"}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/effect.bin"], "includes": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/application.js"], "replaceOnly": ["/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/main.js"], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "db://assets/Scenes/LevelSelect.scene", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {"ios": {"executableName": "SuperSplash", "packageName": "com.rio.supersplsh", "renderBackEnd": {"gles2": false, "gles3": false, "metal": true}, "skipUpdateXcodeProject": false, "orientation": {"portrait": false, "upsideDown": false, "landscapeRight": true, "landscapeLeft": false}, "osTarget": {"iphoneos": false, "simulator": true}, "targetVersion": "15.0", "__version__": "1.0.1", "developerTeam": "UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"}, "cocos-service": {"configID": "e495ea", "services": [], "__version__": "3.0.9"}, "native": {"encrypted": false, "xxteaKey": "d6bYHoT1JMOL/tRP", "compressZip": false, "JobSystem": "none", "__version__": "1.0.2"}}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-21-2025 00-28.log", "useCache": true, "includeModules": ["2d", "affine-transform", "animation", "audio", "base", "custom-pipeline", "dragon-bones", "graphics", "intersection-2d", "mask", "particle-2d", "physics-2d-box2d", "profiler", "rich-text", "spine-3.8", "tiled-map", "tween", "ui", "video", "webview", "custom-pipeline-builtin-scripts"], "flags": {"LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false}, "designResolution": {"width": 1280, "height": 720, "fitWidth": true, "fitHeight": false}, "renderPipeline": "fd8ec536-a354-4a17-9c74-4f3883c378c8", "physicsConfig": {"gravity": {"x": 0, "y": -10, "z": 0}, "allowSleep": true, "sleepThreshold": 0.1, "autoSimulation": true, "fixedTimeStep": 0.0166667, "maxSubSteps": 1, "defaultMaterial": "ba21476f-2866-4f81-9c4d-6e359316e448"}, "customLayers": [{"name": "UI", "value": 524288}, {"name": "Block", "value": 2}, {"name": "Bullet", "value": 4}], "sortingLayers": [], "macroConfig": {"BATCHER2D_MEM_INCREMENT": 288}, "customPipeline": true, "useBuildAssetCache": true, "useBuildEngineCache": true, "useBuildTextureCompressCache": true, "useBuildAutoAtlasCache": true, "resolution": {"width": 1280, "height": 720, "policy": 4}, "engineInfo": {"typescript": {"type": "builtin", "custom": "", "builtin": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine", "path": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"}, "native": {"type": "builtin", "custom": "", "builtin": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native", "path": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native"}}, "appTemplateData": {"debugMode": false, "renderMode": false, "showFPS": false, "resolution": {"width": 1280, "height": 720, "policy": 4}, "md5Cache": false, "cocosTemplate": "", "settingsJsonPath": "src/settings.json", "hasPhysicsAmmo": false, "versionTips": "使用的 application.ejs 版本低于当前编辑器使用的版本，请检查并升级", "customVersion": "1.0.0", "versionCheckTemplate": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/templates/launcher/version-check.ejs"}, "buildEngineParam": {"debug": false, "mangleProperties": false, "inlineEnum": true, "sourceMaps": false, "includeModules": ["2d", "affine-transform", "animation", "audio", "base", "custom-pipeline", "dragon-bones", "graphics", "intersection-2d", "mask", "particle-2d", "physics-2d-box2d", "profiler", "rich-text", "spine-3.8", "tiled-map", "tween", "ui", "video", "webview", "custom-pipeline-builtin-scripts"], "engineVersion": "3.8.6", "md5Map": [], "engineName": "src/cocos-js", "platform": "IOS", "useCache": true, "nativeCodeBundleMode": "asmjs", "wasmCompressionMode": false, "output": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/cocos-js", "targets": "chrome 80", "flags": {"DEBUG": false, "LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false}, "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"}, "buildScriptParam": {"experimentalEraseModules": false, "outputName": "project", "flags": {"DEBUG": false, "LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false}, "polyfills": {"targets": "chrome 80"}, "platform": "IOS", "commonDir": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/chunks", "bundleCommonChunk": false, "targets": "chrome 80", "system": {"preset": "commonjs-like"}}, "assetSerializeOptions": {"cc.EffectAsset": {"glsl1": false, "glsl3": false, "glsl4": true}, "exportCCON": true}, "cocosParams": {"buildDir": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios", "buildAssetsDir": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data", "projDir": "/Users/<USER>/projects/cocos_project/SuperSplash", "cmakePath": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake", "nativeEnginePath": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native", "enginePath": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine", "projectName": "SuperSplash", "debug": false, "encrypted": false, "xxteaKey": "d6bYHoT1JMOL/tRP", "compressZip": false, "cMakeConfig": {"APP_NAME": "set(APP_NAME \"SuperSplash\")", "COCOS_X_PATH": "set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")", "USE_JOB_SYSTEM_TASKFLOW": false, "USE_JOB_SYSTEM_TBB": false, "ENABLE_FLOAT_OUTPUT": false, "USE_PHYSICS_PHYSX": "set(USE_PHYSICS_PHYSX OFF)", "USE_BOX2D_JSB": "set(USE_BOX2D_JSB OFF)", "USE_OCCLUSION_QUERY": "set(USE_OCCLUSION_QUERY OFF)", "USE_GEOMETRY_RENDERER": "set(USE_GEOMETRY_RENDERER OFF)", "USE_DEBUG_RENDERER": "set(USE_DEBUG_RENDERER OFF)", "USE_AUDIO": "set(USE_AUDIO ON)", "USE_VIDEO": "set(USE_VIDEO ON)", "USE_WEBVIEW": "set(USE_WEBVIEW ON)", "USE_SOCKET": "set(USE_SOCKET OFF)", "USE_WEBSOCKET_SERVER": "set(USE_WEBSOCKET_SERVER OFF)", "USE_VENDOR": "set(USE_VENDOR OFF)", "USE_SPINE_3_8": "set(USE_SPINE_3_8 ON)", "USE_SPINE_4_2": "set(USE_SPINE_4_2 OFF)", "USE_DRAGONBONES": "set(USE_DRAGONBONES ON)", "CC_USE_GLES2": false, "CC_USE_GLES3": false, "CC_USE_METAL": true, "MACOSX_BUNDLE_GUI_IDENTIFIER": "set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)", "DEVELOPMENT_TEAM": "set(DEVELOPMENT_TEAM UWR5Y8Y7U8)", "TARGET_IOS_VERSION": "set(TARGET_IOS_VERSION 15.0)", "USE_PORTRAIT": false, "CUSTOM_COPY_RESOURCE_HOOK": false, "CC_EXECUTABLE_NAME": "set(CC_EXECUTABLE_NAME \"SuperSplash\")"}, "platformParams": {"orientation": {"portrait": false, "upsideDown": false, "landscapeRight": true, "landscapeLeft": false}, "bundleId": "com.rio.supersplsh", "teamid": "UWR5Y8Y7U8", "skipUpdateXcodeProject": false, "simulator": true, "iphoneos": false}, "platform": "ios", "packageName": "com.rio.supersplsh", "executableName": "SuperSplash"}, "generateCompileConfig": true, "dest": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/assets"}