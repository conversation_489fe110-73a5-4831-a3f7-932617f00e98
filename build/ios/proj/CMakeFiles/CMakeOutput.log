The target system is: iOS -  - 
The host system is: Darwin - 24.6.0 - arm64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/clang++ 
Build flags: 
Id flags:  

The output was:
0
Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild

User defaults from command line:
    IDEPackageSupportUseBuiltinSCM = YES

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'CompilerIdCXX' in project 'CompilerIdCXX' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

Build description signature: 2f76b242bd6342cb4006025b28941b27
Build description path: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/./XCBuildData/2f76b242bd6342cb4006025b28941b27.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -o /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/EagerLinkingTBDs/Debug-iphoneos
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/EagerLinkingTBDs/Debug-iphoneos

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX-cab83bf49ed2df1b1ab81a78cb182d1a-VFS-iphoneos/all-product-headers.yaml
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xcodeproj
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX-cab83bf49ed2df1b1ab81a78cb182d1a-VFS-iphoneos/all-product-headers.yaml

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/empty-CompilerIdCXX.plist (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/empty-CompilerIdCXX.plist

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX.hmap (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-project-headers.hmap (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-project-headers.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX.DependencyMetadataFileList (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX.DependencyMetadataFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-own-target-headers.hmap (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-own-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-generated-files.hmap (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-generated-files.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CompilerIdCXX.LinkFileList (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CompilerIdCXX.LinkFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp
-target arm64-apple-ios18.2 -fpascal-strings -Os -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -fvisibility-inlines-hidden -iquote /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-generated-files.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-own-target-headers.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-all-target-headers.hmap -iquote /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-project-headers.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-all-target-headers.hmap (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-all-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-all-non-framework-target-headers.hmap (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-all-non-framework-target-headers.hmap

MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    /bin/mkdir -p /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest

ProcessInfoPlistFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/Info.plist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/empty-CompilerIdCXX.plist (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    builtin-infoPlistUtility /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/empty-CompilerIdCXX.plist -producttype com.apple.product-type.bundle.unit-test -expandbuildsettings -format binary -platform iphoneos -requiredArchitecture arm64 -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/Info.plist

CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CMakeCXXCompilerId.o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CMakeCXXCompilerId.cpp normal arm64 c++ com.apple.compilers.llvm.clang.1_0.compiler (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    
    Using response file: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c++ -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-non-virtual-dtor -Wno-overloaded-virtual -Wno-exit-time-destructors -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wno-newline-eof -Wno-c++11-extensions -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Winvalid-offsetof -Wno-sign-conversion -Wno-infinite-recursion -Wno-move -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-range-loop-analysis -Wno-semicolon-before-method-body @/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CMakeCXXCompilerId.d --serialize-diagnostics /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CMakeCXXCompilerId.dia -c /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CMakeCXXCompilerId.cpp -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CMakeCXXCompilerId.o

Ld /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/CompilerIdCXX normal (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ -Xlinker -reproducible -target arm64-apple-ios18.2 -bundle -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -Os -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/EagerLinkingTBDs/Debug-iphoneos -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX -L/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/EagerLinkingTBDs/Debug-iphoneos -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks -filelist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CompilerIdCXX.LinkFileList -Xlinker -rpath -Xlinker @loader_path/Frameworks -dead_strip -Xlinker -object_path_lto -Xlinker /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CompilerIdCXX_lto.o -framework XCTest -lXCTestSwiftSupport -Xlinker -dependency_info -Xlinker /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Objects-normal/arm64/CompilerIdCXX_dependency_info.dat -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/CompilerIdCXX

PhaseScriptExecution Run\ Script /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    /bin/sh -c /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh
GCC_VERSION=com.apple.compilers.llvm.clang.1_0
ARCHS=arm64

GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest.dSYM /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/CompilerIdCXX (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/CompilerIdCXX -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest.dSYM

RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    builtin-RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest

Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX
    /usr/bin/touch -c /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest

warning: Traditional headermap style is no longer supported; please migrate to using separate headermaps and set 'ALWAYS_SEARCH_USER_PATHS' to NO. (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
note: Run script build phase 'Run Script' will be run during every build because the option to run the script phase "Based on dependency analysis" is unchecked. (in target 'CompilerIdCXX' from project 'CompilerIdCXX')
** BUILD SUCCEEDED **



Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.xctest/CompilerIdCXX"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.xctest/Info.plist"

The CXX compiler identification is AppleClang, found in "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xctest/CompilerIdCXX"

Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_4cfca -parallelizeTargets -configuration Debug -hideShellScriptEnvironment && Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_4cfca -parallelizeTargets -configuration Debug -hideShellScriptEnvironment

User defaults from command line:
    HideShellScriptEnvironment = YES
    IDEPackageSupportUseBuiltinSCM = YES

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'cmTC_4cfca' in project 'CMAKE_TRY_COMPILE' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -V

Build description signature: e7eda42a069268832f86216aa3978f1f
Build description path: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/build/XCBuildData/e7eda42a069268832f86216aa3978f1f.xcbuilddata
CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp

ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -o /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/cmTC_4cfca.DependencyMetadataFileList (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/cmTC_4cfca.DependencyMetadataFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca.LinkFileList (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca.LinkFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp
-target arm64-apple-ios18.2 '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 '-DCMAKE_INTDIR="Debug-iphoneos"' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug

CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.o /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp normal arm64 c++ com.apple.compilers.llvm.clang.1_0.compiler (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    
    Using response file: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c++ -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-non-virtual-dtor -Wno-overloaded-virtual -Wno-exit-time-destructors -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wno-newline-eof -Wno-c++11-extensions -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Winvalid-offsetof -Wno-sign-conversion -Wno-infinite-recursion -Wno-move -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-range-loop-analysis -Wno-semicolon-before-method-body -v -Wl,-v @/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.d --serialize-diagnostics /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.dia -c /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.o
Apple clang version 16.0.0 (clang-1600.0.26.6)
Target: arm64-apple-ios18.2
Thread model: posix
InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"
 "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-ios18.2.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=18.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-a7 -target-feature +v8a -target-feature +aes -target-feature +fp-armv8 -target-feature +sha2 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-abi darwinpcs -debug-info-kind=standalone -dwarf-version=5 -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.d -skip-unused-modulemap-deps -MT dependencies -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -D _LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG -D "CMAKE_INTDIR=\"Debug-iphoneos\"" -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources-normal/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -O0 -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-non-virtual-dtor -Wno-overloaded-virtual -Wno-exit-time-destructors -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wno-newline-eof -Wno-c++11-extensions -Wno-implicit-fallthrough -Wdeprecated-declarations -Winvalid-offsetof -Wno-sign-conversion -Wno-infinite-recursion -Wno-move -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-range-loop-analysis -Wno-semicolon-before-method-body -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -ferror-limit 19 -fmacro-backtrace-limit=0 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fpascal-strings -fmax-type-align=16 -fcommon -fdiagnostics-show-note-include-stack -serialize-diagnostic-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.dia -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.o -x c++ /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp
clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.6.0
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include"
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources-normal/arm64"
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources/arm64"
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources"
ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include"
ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Library/Frameworks"
#include "..." search starts here:
#include <...> search starts here:
 /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug (framework directory)
 /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1
 /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include
 /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include
 /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
 /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/System/Library/Frameworks (framework directory)
End of search list.

Libtool /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_4cfca.a normal (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -static -arch_only arm64 -D -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -filelist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca.LinkFileList -dependency_info /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca_libtool_dependency_info.dat -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_4cfca.a

** BUILD SUCCEEDED **




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1]
    add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
    add: [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include]
    add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
  end of search list found
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1]
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include]
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
  implicit include dirs: [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include;/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_4cfca -parallelizeTargets -configuration Debug -hideShellScriptEnvironment && Command line invocation:]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_4cfca -parallelizeTargets -configuration Debug -hideShellScriptEnvironment]
  ignore line: []
  ignore line: [User defaults from command line:]
  ignore line: [    HideShellScriptEnvironment = YES]
  ignore line: [    IDEPackageSupportUseBuiltinSCM = YES]
  ignore line: []
  ignore line: [ComputeTargetDependencyGraph]
  ignore line: [note: Building targets in dependency order]
  ignore line: [note: Target dependency graph (1 target)]
  ignore line: [    Target 'cmTC_4cfca' in project 'CMAKE_TRY_COMPILE' (no dependencies)]
  ignore line: []
  ignore line: [GatherProvisioningInputs]
  ignore line: []
  ignore line: [CreateBuildDescription]
  ignore line: []
  ignore line: [ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null]
  ignore line: []
  ignore line: [ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c++ -c /dev/null]
  ignore line: []
  ignore line: [ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -V]
  ignore line: []
  ignore line: [Build description signature: e7eda42a069268832f86216aa3978f1f]
  ignore line: [Build description path: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/build/XCBuildData/e7eda42a069268832f86216aa3978f1f.xcbuilddata]
  ignore line: [CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -o /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache]
  ignore line: []
  ignore line: [CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug]
  ignore line: []
  ignore line: [CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos]
  ignore line: []
  ignore line: [WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/cmTC_4cfca.DependencyMetadataFileList (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/cmTC_4cfca.DependencyMetadataFileList]
  ignore line: []
  ignore line: [WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca.LinkFileList (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca.LinkFileList]
  ignore line: []
  ignore line: [WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp]
  ignore line: [-target arm64-apple-ios18.2 '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 '-DCMAKE_INTDIR="Debug-iphoneos"' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug]
  ignore line: []
  ignore line: [CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.o /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp normal arm64 c++ com.apple.compilers.llvm.clang.1_0.compiler (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    ]
  ignore line: [    Using response file: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp]
  ignore line: [    ]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c++ -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-non-virtual-dtor -Wno-overloaded-virtual -Wno-exit-time-destructors -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wno-newline-eof -Wno-c++11-extensions -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Winvalid-offsetof -Wno-sign-conversion -Wno-infinite-recursion -Wno-move -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-range-loop-analysis -Wno-semicolon-before-method-body -v -Wl -v @/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.d --serialize-diagnostics /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.dia -c /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.o]
  ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
  ignore line: [Target: arm64-apple-ios18.2]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
  ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
  ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"]
  ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-ios18.2.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=18.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-a7 -target-feature +v8a -target-feature +aes -target-feature +fp-armv8 -target-feature +sha2 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-abi darwinpcs -debug-info-kind=standalone -dwarf-version=5 -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.d -skip-unused-modulemap-deps -MT dependencies -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -D _LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG -D "CMAKE_INTDIR=\"Debug-iphoneos\"" -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources-normal/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -O0 -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-non-virtual-dtor -Wno-overloaded-virtual -Wno-exit-time-destructors -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wno-newline-eof -Wno-c++11-extensions -Wno-implicit-fallthrough -Wdeprecated-declarations -Winvalid-offsetof -Wno-sign-conversion -Wno-infinite-recursion -Wno-move -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-range-loop-analysis -Wno-semicolon-before-method-body -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -ferror-limit 19 -fmacro-backtrace-limit=0 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fpascal-strings -fmax-type-align=16 -fcommon -fdiagnostics-show-note-include-stack -serialize-diagnostic-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.dia -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/CMakeCXXCompilerABI.o -x c++ /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.6.0]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include"]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources-normal/arm64"]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources/arm64"]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/DerivedSources"]
  ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include"]
  ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Library/Frameworks"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug (framework directory)]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include/c++/v1]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/System/Library/Frameworks (framework directory)]
  ignore line: [End of search list.]
  ignore line: []
  ignore line: [Libtool /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_4cfca.a normal (in target 'cmTC_4cfca' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -static -arch_only arm64 -D -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -filelist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca.LinkFileList -dependency_info /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_4cfca.build/Objects-normal/arm64/cmTC_4cfca_libtool_dependency_info.dat -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_4cfca.a]
  ignore line: []
  ignore line: [** BUILD SUCCEEDED **]
  ignore line: []
  ignore line: []
  ignore line: []
  implicit libs: []
  implicit objs: []
  implicit dirs: []
  implicit fwks: []


Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /usr/bin/clang 
Build flags: 
Id flags:  

The output was:
0
Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild

User defaults from command line:
    IDEPackageSupportUseBuiltinSCM = YES

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'CompilerIdC' in project 'CompilerIdC' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

Build description signature: 7da3f659cbfdd95d507190826cc516e5
Build description path: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/./XCBuildData/7da3f659cbfdd95d507190826cc516e5.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -o /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xcodeproj
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC-d9d24303ac0ea36fa2bc7f98aeea5fe3-VFS-iphoneos/all-product-headers.yaml

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.DependencyMetadataFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
-target arm64-apple-ios18.2 -fpascal-strings -Os -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -iquote /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-generated-files.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-own-target-headers.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap -iquote /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-project-headers.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-target-headers.hmap

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/CompilerIdC-all-non-framework-target-headers.hmap

MkDir /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    /bin/mkdir -p /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest

ProcessInfoPlistFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    builtin-infoPlistUtility /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/empty-CompilerIdC.plist -producttype com.apple.product-type.bundle.unit-test -expandbuildsettings -format binary -platform iphoneos -requiredArchitecture arm64 -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/Info.plist

CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CMakeCCompilerId.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    
    Using response file: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body @/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.d --serialize-diagnostics /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.dia -c /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CMakeCCompilerId.c -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CMakeCCompilerId.o

Ld /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC normal (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target arm64-apple-ios18.2 -bundle -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -Os -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC -L/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/EagerLinkingTBDs/Debug-iphoneos -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks -filelist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC.LinkFileList -Xlinker -rpath -Xlinker @loader_path/Frameworks -dead_strip -Xlinker -object_path_lto -Xlinker /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_lto.o -framework XCTest -lXCTestSwiftSupport -Xlinker -dependency_info -Xlinker /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Objects-normal/arm64/CompilerIdC_dependency_info.dat -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC

PhaseScriptExecution Run\ Script /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    /bin/sh -c /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.build/Debug-iphoneos/CompilerIdC.build/Script-2C8FEB8E15DC1A1A00E56A5D.sh
GCC_VERSION=com.apple.compilers.llvm.clang.1_0
ARCHS=arm64

GenerateDSYMFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest.dSYM

RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    builtin-RegisterExecutionPolicyException /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest

Touch /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest (in target 'CompilerIdC' from project 'CompilerIdC')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC
    /usr/bin/touch -c /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest

warning: Traditional headermap style is no longer supported; please migrate to using separate headermaps and set 'ALWAYS_SEARCH_USER_PATHS' to NO. (in target 'CompilerIdC' from project 'CompilerIdC')
note: Run script build phase 'Run Script' will be run during every build because the option to run the script phase "Based on dependency analysis" is unchecked. (in target 'CompilerIdC' from project 'CompilerIdC')
** BUILD SUCCEEDED **



Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.xctest/CompilerIdC"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.xctest/Info.plist"

The C compiler identification is AppleClang, found in "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdC/CompilerIdC.xctest/CompilerIdC"

Checking whether the ASM compiler is Clang using "--version" matched "(clang version)":
Apple clang version 16.0.0 (clang-1600.0.26.6)
Target: arm64-apple-darwin24.6.0
Thread model: posix
InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
Detecting C compiler ABI info compiled with the following output:
Change Dir: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_9a853 -parallelizeTargets -configuration Debug -hideShellScriptEnvironment && Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_9a853 -parallelizeTargets -configuration Debug -hideShellScriptEnvironment

User defaults from command line:
    HideShellScriptEnvironment = YES
    IDEPackageSupportUseBuiltinSCM = YES

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'cmTC_9a853' in project 'CMAKE_TRY_COMPILE' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -V

Build description signature: 75344695cb17b13ed0dd304404bddd56
Build description path: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/build/XCBuildData/75344695cb17b13ed0dd304404bddd56.xcbuilddata
CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp

ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -o /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos

CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj
    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/cmTC_9a853.DependencyMetadataFileList (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/cmTC_9a853.DependencyMetadataFileList

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
-target arm64-apple-ios18.2 -fpascal-strings -O0 '-DCMAKE_INTDIR="Debug-iphoneos"' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug

WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853.LinkFileList (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853.LinkFileList

CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.o /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCCompilerABI.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    
    Using response file: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -v -Wl,-v @/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.d --serialize-diagnostics /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.dia -c /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCCompilerABI.c -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.o
Apple clang version 16.0.0 (clang-1600.0.26.6)
Target: arm64-apple-ios18.2
Thread model: posix
InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
 "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-ios18.2.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=18.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-a7 -target-feature +v8a -target-feature +aes -target-feature +fp-armv8 -target-feature +sha2 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-abi darwinpcs -debug-info-kind=standalone -dwarf-version=5 -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.d -skip-unused-modulemap-deps -MT dependencies -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -D "CMAKE_INTDIR=\"Debug-iphoneos\"" -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources-normal/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -O0 -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -ferror-limit 19 -fmacro-backtrace-limit=0 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fpascal-strings -fmax-type-align=16 -fcommon -fdiagnostics-show-note-include-stack -serialize-diagnostic-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.dia -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.o -x c /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCCompilerABI.c
clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.6.0
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include"
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources-normal/arm64"
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources/arm64"
ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources"
ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include"
ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Library/Frameworks"
#include "..." search starts here:
#include <...> search starts here:
 /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug (framework directory)
 /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include
 /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include
 /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
 /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/System/Library/Frameworks (framework directory)
End of search list.

Libtool /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_9a853.a normal (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')
    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -static -arch_only arm64 -D -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -filelist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853.LinkFileList -dependency_info /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853_libtool_dependency_info.dat -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_9a853.a

** BUILD SUCCEEDED **




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
    add: [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include]
    add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
  end of search list found
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include]
  collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
  implicit include dirs: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include;/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_9a853 -parallelizeTargets -configuration Debug -hideShellScriptEnvironment && Command line invocation:]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project CMAKE_TRY_COMPILE.xcodeproj build -target cmTC_9a853 -parallelizeTargets -configuration Debug -hideShellScriptEnvironment]
  ignore line: []
  ignore line: [User defaults from command line:]
  ignore line: [    HideShellScriptEnvironment = YES]
  ignore line: [    IDEPackageSupportUseBuiltinSCM = YES]
  ignore line: []
  ignore line: [ComputeTargetDependencyGraph]
  ignore line: [note: Building targets in dependency order]
  ignore line: [note: Target dependency graph (1 target)]
  ignore line: [    Target 'cmTC_9a853' in project 'CMAKE_TRY_COMPILE' (no dependencies)]
  ignore line: []
  ignore line: [GatherProvisioningInputs]
  ignore line: []
  ignore line: [CreateBuildDescription]
  ignore line: []
  ignore line: [ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null]
  ignore line: []
  ignore line: [ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -x c -c /dev/null]
  ignore line: []
  ignore line: [ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -V]
  ignore line: []
  ignore line: [Build description signature: 75344695cb17b13ed0dd304404bddd56]
  ignore line: [Build description path: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/build/XCBuildData/75344695cb17b13ed0dd304404bddd56.xcbuilddata]
  ignore line: [CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -o /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache]
  ignore line: []
  ignore line: [CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/EagerLinkingTBDs/Debug-iphoneos]
  ignore line: []
  ignore line: [CreateBuildDirectory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.xcodeproj]
  ignore line: [    builtin-create-build-directory /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug]
  ignore line: []
  ignore line: [WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/cmTC_9a853.DependencyMetadataFileList (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/cmTC_9a853.DependencyMetadataFileList]
  ignore line: []
  ignore line: [WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp]
  ignore line: [-target arm64-apple-ios18.2 -fpascal-strings -O0 '-DCMAKE_INTDIR="Debug-iphoneos"' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug]
  ignore line: []
  ignore line: [WriteAuxiliaryFile /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853.LinkFileList (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    write-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853.LinkFileList]
  ignore line: []
  ignore line: [CompileC /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.o /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCCompilerABI.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    ]
  ignore line: [    Using response file: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp]
  ignore line: [    ]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -v -Wl -v @/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.d --serialize-diagnostics /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.dia -c /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCCompilerABI.c -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.o]
  ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
  ignore line: [Target: arm64-apple-ios18.2]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
  ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
  ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-ios18.2.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=18.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-a7 -target-feature +v8a -target-feature +aes -target-feature +fp-armv8 -target-feature +sha2 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-abi darwinpcs -debug-info-kind=standalone -dwarf-version=5 -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.d -skip-unused-modulemap-deps -MT dependencies -ivfsstatcache /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -D "CMAKE_INTDIR=\"Debug-iphoneos\"" -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources-normal/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources/arm64 -I /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -O0 -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp -ferror-limit 19 -fmacro-backtrace-limit=0 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fpascal-strings -fmax-type-align=16 -fcommon -fdiagnostics-show-note-include-stack -serialize-diagnostic-file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.dia -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/CMakeCCompilerABI.o -x c /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.6.0]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/include"]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources-normal/arm64"]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources/arm64"]
  ignore line: [ignoring nonexistent directory "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/DerivedSources"]
  ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/local/include"]
  ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Library/Frameworks"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug (framework directory)]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/usr/include]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
  ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/System/Library/Frameworks (framework directory)]
  ignore line: [End of search list.]
  ignore line: []
  ignore line: [Libtool /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_9a853.a normal (in target 'cmTC_9a853' from project 'CMAKE_TRY_COMPILE')]
  ignore line: [    cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp]
  ignore line: [    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool -static -arch_only arm64 -D -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -L/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug -filelist /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853.LinkFileList -dependency_info /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/CMAKE_TRY_COMPILE.build/Debug-iphoneos/cmTC_9a853.build/Objects-normal/arm64/cmTC_9a853_libtool_dependency_info.dat -o /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeTmp/Debug/libcmTC_9a853.a]
  ignore line: []
  ignore line: [** BUILD SUCCEEDED **]
  ignore line: []
  ignore line: []
  ignore line: []
  implicit libs: []
  implicit objs: []
  implicit dirs: []
  implicit fwks: []


