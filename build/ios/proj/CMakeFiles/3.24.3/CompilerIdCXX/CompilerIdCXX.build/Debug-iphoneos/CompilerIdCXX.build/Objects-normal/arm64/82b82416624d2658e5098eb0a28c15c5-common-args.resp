-target arm64-apple-ios18.2 -fpascal-strings -Os -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk -g -fvisibility-inlines-hidden -iquote /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-generated-files.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-own-target-headers.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-all-target-headers.hmap -iquote /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/CompilerIdCXX-project-headers.hmap -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/include -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/DerivedSources-normal/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/DerivedSources/arm64 -I/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.build/Debug-iphoneos/CompilerIdCXX.build/DerivedSources -F/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/Developer/Library/Frameworks