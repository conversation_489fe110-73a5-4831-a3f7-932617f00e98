{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "cab83bf49ed2df1b1ab81a78cb182d1a3f938c03aee3f24af46a5caa592e362d"}], "containerPath": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/3.24.3/CompilerIdCXX/CompilerIdCXX.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": false}