// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		0FA125A7BE284A51B79F0373 /* ALL_BUILD */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 18A05171147343E5B2DD2A0E /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */;
			buildPhases = (
				493EB4488F4657C298458AE4 /* Generate CMakeFiles/ALL_BUILD */,
			);
			dependencies = (
				CA7D3537C16D479AA502145B /* PBXTargetDependency */,
				9949E11BFEE140DDA091B046 /* PBXTargetDependency */,
			);
			name = ALL_BUILD;
			productName = ALL_BUILD;
		};
		24216AAFF6ED452FB51A2CC4 /* ZERO_CHECK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 3EBA4D9FC72E4F52A54A6856 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */;
			buildPhases = (
				2238203899818DD0AC2FD552 /* Generate CMakeFiles/ZERO_CHECK */,
			);
			dependencies = (
			);
			name = ZERO_CHECK;
			productName = ZERO_CHECK;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		00E7E8A2D6FB45208BFD1F1E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 19B7C9CFE866470F864675BF /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */; };
		29B5A2EF84124A968E9283B0 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXBuildFile; fileRef = A180AA767372475FA15A2BFA /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */; };
		312650F7CE9D4660982EF53C /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = F677039458B3411E97C3728A /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */; };
		63624024881A4B34A6B74905 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXBuildFile; fileRef = 6B5431BA0D9D4419A9759769 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */; };
		70925E07C3914637978AEC99 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXBuildFile; fileRef = 01BA958257364C7B8BA65DF2 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */; };
		795633B296884713AF6F53A8 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXBuildFile; fileRef = B3A001D81DBF4D6A9EA7DA1E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */; };
		7E69E7BAEFF443A7B56E5D36 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = F7CE0F5423D64AE3A9A08B66 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */; };
		C1A70C9A802942839FA972E6 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXBuildFile; fileRef = 3922D8CA7F45444D9C2ED57E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */; };
		C40BA0405ED540E9A77DBE58 /* /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXBuildFile; fileRef = 0E8AEF35E8B2443FA6CBF3CF /* /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */; };
/* End PBXBuildFile section */

/* Begin PBXBuildStyle section */
		47A68E1B98394C4FA2047FB9 /* Debug */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Debug;
		};
		9AA07D9FEF014E44957592BB /* RelWithDebInfo */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = RelWithDebInfo;
		};
		E7A345DB503E4523AE29F83A /* MinSizeRel */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = MinSizeRel;
		};
		E9A746AB8C974473B9F25ECF /* Release */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Release;
		};
/* End PBXBuildStyle section */

/* Begin PBXContainerItemProxy section */
		45429D018EE14C91A1DDF50E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9DF4F6B1AC184A358D312268 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 24216AAFF6ED452FB51A2CC4;
			remoteInfo = ZERO_CHECK;
		};
		5A6BEE4A44AC4E80AFEAD61C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9DF4F6B1AC184A358D312268 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 24216AAFF6ED452FB51A2CC4;
			remoteInfo = ZERO_CHECK;
		};
		C31B7323734F483CA380DFF0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9DF4F6B1AC184A358D312268 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 5EA0ECBA7BAA4B7B927F05C1;
			remoteInfo = boost_container;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		01BA958257364C7B8BA65DF2 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		0E8AEF35E8B2443FA6CBF3CF /* /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXFileReference; explicitFileType = sourcecode.text.plist; fileEncoding = 4; name = Info.plist; path = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist; sourceTree = "<absolute>"; };
		19B7C9CFE866470F864675BF /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = pool_resource.cpp; path = pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		3922D8CA7F45444D9C2ED57E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = alloc_lib.c; path = alloc_lib.c; sourceTree = SOURCE_ROOT; };
		49BA6F8F7EBC4560BED69D24 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		6B5431BA0D9D4419A9759769 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = monotonic_buffer_resource.cpp; path = monotonic_buffer_resource.cpp; sourceTree = SOURCE_ROOT; };
		A180AA767372475FA15A2BFA /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = dlmalloc.cpp; path = dlmalloc.cpp; sourceTree = SOURCE_ROOT; };
		B3A001D81DBF4D6A9EA7DA1E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = global_resource.cpp; path = global_resource.cpp; sourceTree = SOURCE_ROOT; };
		C3A955F90015401EB2D434CF /* boost_container */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libboost_container.a; sourceTree = BUILT_PRODUCTS_DIR; };
		F677039458B3411E97C3728A /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = synchronized_pool_resource.cpp; path = synchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		F7CE0F5423D64AE3A9A08B66 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = unsynchronized_pool_resource.cpp; path = unsynchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8D529F56F0444C55A19A9A68 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08D31A2F4209479291FB57B7 /* boost_container */ = {
			isa = PBXGroup;
			children = (
				D18D7DE4A9E5460DA93333F1 /* Source Files */,
				A5E37C0FA3504983A6FEA805 /* Resources */,
				01BA958257364C7B8BA65DF2 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = boost_container;
			sourceTree = "<group>";
		};
		27A22FBA2A8946BBA1B90C65 = {
			isa = PBXGroup;
			children = (
				626A391CAB9745E598115920 /* Utils */,
				A80E38D4E7A84818A5BDF0F2 /* ALL_BUILD */,
				D52177B048E14C5BBE202ECC /* Products */,
				CA46E58DF23A428591A848DC /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		626A391CAB9745E598115920 /* Utils */ = {
			isa = PBXGroup;
			children = (
				08D31A2F4209479291FB57B7 /* boost_container */,
			);
			name = Utils;
			sourceTree = "<group>";
		};
		69CC0DC5AACF4355A06C44FF /* CMake Rules */ = {
			isa = PBXGroup;
			children = (
			);
			name = "CMake Rules";
			sourceTree = "<group>";
		};
		A5E37C0FA3504983A6FEA805 /* Resources */ = {
			isa = PBXGroup;
			children = (
				0E8AEF35E8B2443FA6CBF3CF /* /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		A80E38D4E7A84818A5BDF0F2 /* ALL_BUILD */ = {
			isa = PBXGroup;
			children = (
				69CC0DC5AACF4355A06C44FF /* CMake Rules */,
				49BA6F8F7EBC4560BED69D24 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = ALL_BUILD;
			sourceTree = "<group>";
		};
		CA46E58DF23A428591A848DC /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D18D7DE4A9E5460DA93333F1 /* Source Files */ = {
			isa = PBXGroup;
			children = (
				3922D8CA7F45444D9C2ED57E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				A180AA767372475FA15A2BFA /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				B3A001D81DBF4D6A9EA7DA1E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				6B5431BA0D9D4419A9759769 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				19B7C9CFE866470F864675BF /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				F677039458B3411E97C3728A /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				F7CE0F5423D64AE3A9A08B66 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			name = "Source Files";
			sourceTree = "<group>";
		};
		D52177B048E14C5BBE202ECC /* Products */ = {
			isa = PBXGroup;
			children = (
				C3A955F90015401EB2D434CF /* boost_container */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		5EA0ECBA7BAA4B7B927F05C1 /* boost_container */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 48B8F78C853843AD8EC46B53 /* Build configuration list for PBXNativeTarget "boost_container" */;
			buildPhases = (
				320C02C90047447B8FA5B7FC /* Sources */,
				8D529F56F0444C55A19A9A68 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				6CFE94B583174C37A798D0A1 /* PBXTargetDependency */,
			);
			name = boost_container;
			productName = boost_container;
			productReference = C3A955F90015401EB2D434CF /* boost_container */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9DF4F6B1AC184A358D312268 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1620;
			};
			buildConfigurationList = DC48A64DA27D429BA4C5A7FF /* Build configuration list for PBXProject "boost_container" */;
			buildSettings = {
			};
			buildStyles = (
				47A68E1B98394C4FA2047FB9 /* Debug */,
				E9A746AB8C974473B9F25ECF /* Release */,
				E7A345DB503E4523AE29F83A /* MinSizeRel */,
				9AA07D9FEF014E44957592BB /* RelWithDebInfo */,
			);
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			mainGroup = 27A22FBA2A8946BBA1B90C65;
			projectDirPath = "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container";
			projectRoot = "";
			targets = (
				0FA125A7BE284A51B79F0373 /* ALL_BUILD */,
				24216AAFF6ED452FB51A2CC4 /* ZERO_CHECK */,
				5EA0ECBA7BAA4B7B927F05C1 /* boost_container */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		2238203899818DD0AC2FD552 /* Generate CMakeFiles/ZERO_CHECK */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ZERO_CHECK";
			outputPaths = (
/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/ZERO_CHECK			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
";
			showEnvVarsInLog = 0;
		};
		3C0110FC8322DE7E50886E21 = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		493EB4488F4657C298458AE4 /* Generate CMakeFiles/ALL_BUILD */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ALL_BUILD";
			outputPaths = (
/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/CMakeFiles/ALL_BUILD			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
";
			showEnvVarsInLog = 0;
		};
		9B50530E35CC49C82C2FACCF = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		320C02C90047447B8FA5B7FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C1A70C9A802942839FA972E6 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				29B5A2EF84124A968E9283B0 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				795633B296884713AF6F53A8 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				63624024881A4B34A6B74905 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				00E7E8A2D6FB45208BFD1F1E /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				312650F7CE9D4660982EF53C /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				7E69E7BAEFF443A7B56E5D36 /* /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6CFE94B583174C37A798D0A1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 24216AAFF6ED452FB51A2CC4 /* ZERO_CHECK */;
			targetProxy = 45429D018EE14C91A1DDF50E /* PBXContainerItemProxy */;
		};
		9949E11BFEE140DDA091B046 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 5EA0ECBA7BAA4B7B927F05C1 /* boost_container */;
			targetProxy = C31B7323734F483CA380DFF0 /* PBXContainerItemProxy */;
		};
		CA7D3537C16D479AA502145B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 24216AAFF6ED452FB51A2CC4 /* ZERO_CHECK */;
			targetProxy = 5A6BEE4A44AC4E80AFEAD61C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1D54AC5FA8A045EBB7C4BEA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/archives/Debug;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("   '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "   '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		1F923BDDB23D4C3E96DEAE49 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		26FAAAB2969643E1A55D6FC6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/build;
			};
			name = Debug;
		};
		3016150F05A54F08A9742DCD /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		443482E935BA41638448A332 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/build;
			};
			name = Release;
		};
		5145F28C82F24E79B7F394E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		51AAAA6C7E67415BBFC4A902 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		58869D4568694BFF8389A701 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		5B084D10AA5E43C199227517 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/build;
			};
			name = RelWithDebInfo;
		};
		7BC7888F5013439C82E64FAF /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		7F58E8A637CF456B865CFEB3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		841C670E46CF4364B2789E08 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/archives/RelWithDebInfo;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 2;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("       -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "       -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		A3EBAFA33C9942C09C884902 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/archives/MinSizeRel;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		A87E39E6F3A0403FA2675D3D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/archives/Release;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		B6F363FC629C4B90ABC38BCB /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		C0ECDE4CB32245D1A26C35CB /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/boost/container/build;
			};
			name = MinSizeRel;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18A05171147343E5B2DD2A0E /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58869D4568694BFF8389A701 /* Debug */,
				5145F28C82F24E79B7F394E9 /* Release */,
				7BC7888F5013439C82E64FAF /* MinSizeRel */,
				B6F363FC629C4B90ABC38BCB /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		3EBA4D9FC72E4F52A54A6856 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1F923BDDB23D4C3E96DEAE49 /* Debug */,
				7F58E8A637CF456B865CFEB3 /* Release */,
				3016150F05A54F08A9742DCD /* MinSizeRel */,
				51AAAA6C7E67415BBFC4A902 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		48B8F78C853843AD8EC46B53 /* Build configuration list for PBXNativeTarget "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D54AC5FA8A045EBB7C4BEA3 /* Debug */,
				A87E39E6F3A0403FA2675D3D /* Release */,
				A3EBAFA33C9942C09C884902 /* MinSizeRel */,
				841C670E46CF4364B2789E08 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DC48A64DA27D429BA4C5A7FF /* Build configuration list for PBXProject "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				26FAAAB2969643E1A55D6FC6 /* Debug */,
				443482E935BA41638448A332 /* Release */,
				C0ECDE4CB32245D1A26C35CB /* MinSizeRel */,
				5B084D10AA5E43C199227517 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9DF4F6B1AC184A358D312268 /* Project object */;
}
