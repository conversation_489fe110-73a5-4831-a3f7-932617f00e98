#!/bin/sh
set -e
if test "$CONFIGURATION" = "Debug"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeScripts/ReRunCMake.make
fi
if test "$CONFIGURATION" = "Release"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeScripts/ReRunCMake.make
fi
if test "$CONFIGURATION" = "MinSizeRel"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeScripts/ReRunCMake.make
fi
if test "$CONFIGURATION" = "RelWithDebInfo"; then :
  cd /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj
  make -f /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeScripts/ReRunCMake.make
fi

