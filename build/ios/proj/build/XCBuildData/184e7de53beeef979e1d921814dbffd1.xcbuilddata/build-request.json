{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "64b7c3d511bbd68e11926d0fd48d8d0094dfac153820f73c5499793f7cd7efb2"}], "containerPath": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/SuperSplash.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.2", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/SuperSplash-dpszhktfeseeuacbskbzkmgrzxxc/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/SuperSplash-dpszhktfeseeuacbskbzkmgrzxxc/Index.noindex/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Debug", "overrides": {"synthesized": {"table": {"ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.3.1", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "TARGET_DEVICE_IDENTIFIER": "F4B7F487-76E2-4D07-8D32-C08B0C84A0B2", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.3.1", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}