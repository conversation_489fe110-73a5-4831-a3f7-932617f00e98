{"version": 1, "document": [{"__type__": "cc.AnimationClip", "_name": "explosion", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 0.85, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 6}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 16}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channel": {"__id__": 4}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "spriteFrame"]}, {"__type__": "cc.animation.ComponentPath", "component": "cc.Sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.ObjectCurve", "_times": [0, 0.08333333333333333, 0.16666666666666666, 0.26666666666666666, 0.38333333333333336, 0.4666666666666667, 0.5666666666666667, 0.6833333333333333, 0.8166666666666667], "_values": [{"__uuid__": "7bOlT0MSVEG6duaggQ3bCY@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "01YiY+IbtIxbbAbZevIyjb@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "fckx7P6YFIzJmfEy7cpy+m@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "7cdSnkVxBOlK10Rczz+Eu8@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "08B4lpzuxG1rZggt15ITjW@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "91zBdqf65O+ql7U8yxjWP5@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "19NQYs6hxDo54hyWTAcezs@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "0awjJEm0lOZrgmq6QRyaFo@f9941", "__expectedType__": "cc.Asset"}, {"__uuid__": "801lteK0lAEJnaaSVvh2rV@f9941", "__expectedType__": "cc.Asset"}]}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 7}, "proxy": null}, "_channels": [{"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["scale"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "bytes": {"__type__": "TypedArrayRef", "ctor": "Uint8Array", "offset": 0, "length": 86}, "keyframeValueEditorExtras": [{"broken": null}, {"broken": null}, {"broken": null}, {"broken": null}]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "bytes": {"__type__": "TypedArrayRef", "ctor": "Uint8Array", "offset": 86, "length": 86}, "keyframeValueEditorExtras": [{"broken": null}, {"broken": null}, {"broken": null}, {"broken": null}]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "bytes": {"__type__": "TypedArrayRef", "ctor": "Uint8Array", "offset": 172, "length": 86}, "keyframeValueEditorExtras": [{"broken": null}, {"broken": null}, {"broken": null}, {"broken": null}]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "bytes": {"__type__": "TypedArrayRef", "ctor": "Uint8Array", "offset": 258, "length": 6}}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}], "chunks": [".bin"]}