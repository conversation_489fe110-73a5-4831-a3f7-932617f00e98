[1, ["5fpKAgrRJNUZ59V87NwFbK", "86CHtLQoRLkYcH+h9xQG6h@f9941", "ff3ACRArVH6Zy6dSrQ9/Jg", "525Wn55e9KfJgHyymrwV2G", "7f46LBRONF7aOkgekKaZTH", "e8+xUTHKVPGYsep4lB9GUs@f9941", "4dUynpj2dD46OZ4PqaWtX0", "4942ZUssNH2a/rXbKChqGw@f9941", "38QUxvzghAw7CgTyTOEDHe@f9941", "d6xdsFp7VMObKfichsMV3P@f9941", "b96V8CSuxMAYNDHGzgNrDG@f9941", "b7HBnQSslNy6oytl/eLEBq", "a4Yof/90tAdaSDCLECf+ct", "35QYurHgpBCKJnzoyMJCLD", "46ZtbII3ZN9L4o1m44O6rO", "e8+xUTHKVPGYsep4lB9GUs@6c48a"], ["node", "targetInfo", "target", "source", "root", "_spriteFrame", "asset", "_parent", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "dartPrefab", "rocketPrefab", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos"], -1, 4, 9, 1, 2, 5], ["cc.Node", ["_name", "_mobility", "_layer", "_children", "_components", "_prefab", "_lpos", "_lrot", "_euler", "_lscale"], 0, 2, 12, 4, 5, 5, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.BoxCollider2D", ["_friction", "node", "__prefab", "_size", "_offset"], 2, 1, 4, 5, 5], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo", "sourceInfo"], 2, 1, 1, 4, 4], ["91c082Rgh1HBbV2vKQl2J1W", ["maxSpeed", "acceleration", "maxHealth", "paintSprayInterval", "fireRate", "turnSpeed", "weaponType", "node", "__prefab"], -4, 1, 4], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.RigidBody2D", ["enabledContactListener", "_type", "_gravityScale", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.PolygonCollider2D", ["node", "__prefab", "_offset", "_points"], 3, 1, 4, 5, 12]], [[9, 0, 2], [13, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [17, 0, 1, 2, 2], [4, 1, 2, 4, 3, 1], [0, 0, 1, 6, 5, 4, 8, 3], [19, 0, 1, 2, 2], [18, 0, 1, 2, 3], [0, 2, 3, 6, 4, 3], [3, 0, 1, 2, 3, 2], [12, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 2], [4, 0, 1, 2, 3, 2], [5, 0, 1, 4, 2, 3, 2], [5, 0, 1, 2, 3, 2], [14, 0, 1, 2, 3, 4, 4], [16, 0, 1, 2, 3], [1, 0, 1, 2, 3, 4, 5, 6, 7, 9, 8, 4], [2, 0, 1, 1], [6, 0, 1, 5, 2, 3, 4, 6, 7, 8, 8], [20, 0, 1, 2, 3, 1], [8, 0, 2], [0, 0, 1, 7, 5, 4, 3], [0, 0, 1, 6, 7, 5, 4, 3], [0, 0, 1, 6, 7, 5, 4, 8, 3], [0, 0, 1, 6, 5, 4, 3], [1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4], [3, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 2, 3, 4, 7, 8, 6]], [[[[22, "level-3"], [23, "level-3", 33554432, [-29, -30, -31, -32, -33], [[3, -23, [0, "2b0ALzXYtJPo0X8C3Xa/1a"], [5, 3377, 2105]], [28, 0, -24, [0, "79uc5hPb9BjJZW5PetmcIM"]], [5, -25, [0, "7cyAmiMBRE+6g7LbrLDhAi"], [0, -61.3, 1220.1], [5, 3500.1, 333.3]], [5, -26, [0, "90i9i1N2xGr7Y0i102K+Kd"], [0, -81.7, -1297.3], [5, 3537.5, 483.6]], [5, -27, [0, "38Ke9wT4JLy6aX4lrvtwau"], [0, -2025.1, -43.8], [5, 302.9, 2181.3]], [5, -28, [0, "dccW0EEldDp6vuUIynuW0Y"], [0, 1893.3, 0.3], [5, 411.7, 2105.4]]], [29, "48dRzRqnRN2bkSv9uyUwDd", null, -22, 0, [[14, ["_barSprite"], -7, [1, ["e9cQsECkREsqrYHJQVVWOE"]], -6, [1, ["e4T3FcLEZKR5ceOsPb+eF1"]]], [14, ["_barSprite"], -9, [1, ["e9cQsECkREsqrYHJQVVWOE"]], -8, [1, ["e4T3FcLEZKR5ceOsPb+eF1"]]], [14, ["_barSprite"], -11, [1, ["e9cQsECkREsqrYHJQVVWOE"]], -10, [1, ["e4T3FcLEZKR5ceOsPb+eF1"]]], [14, ["_barSprite"], -13, [1, ["e9cQsECkREsqrYHJQVVWOE"]], -12, [1, ["e4T3FcLEZKR5ceOsPb+eF1"]]], [15, ["healthBar"], -15, -14, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [15, ["healthBar"], -17, -16, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [15, ["healthBar"], -19, -18, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [15, ["healthBar"], -21, -20, [1, ["e9cQsECkREsqrYHJQVVWOE"]]]], [-1, -2, -3, -4, -5]]], [24, "walls", 0, 1, [-35, -36, -37, -38, -39, -40, -41], [[19, -34, [0, "34hM4zoHdCUqX6x/HyY/ew"]]], [2, "7euHjDGM1FV7shYpfzGOci", null, null, null, 1, 0]], [27, "car-1", 2, 33554432, [-47], [[[3, -42, [0, "15W75ZvFVLX4abw/6l2/OG"], [5, 27.8, 60]], [16, true, 1, 0, -43, [0, "93j7SzUitAWKz60YV2JexH"]], [13, 1, -44, [0, "7aH+r+uQZEA4UF98ujueQt"], [5, 27.8, 60.1]], [10, 0, -45, [0, "ad89w3uVNKW5W61hU9PuBC"], 3], -46], 4, 4, 4, 4, 1], [2, "b4Ce+Pis1BOL5qu2B4jBnq", null, null, null, 1, 0], [1, -610.581, 344.475, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [18, "car-2", 2, 33554432, [-53], [[[3, -48, [0, "0dR5BcAr5PvZKcEL4KFpN2"], [5, 27.8, 60]], [16, true, 1, 0, -49, [0, "cftW7C8TRBLIaAIx5A6Zg3"]], [13, 1, -50, [0, "45Iup+wz9N566a6jKIcsRg"], [5, 27.8, 60.1]], [10, 0, -51, [0, "b1Wlhu7LRIp7TbKgaKFQ6Q"], 5], -52], 4, 4, 4, 4, 1], [2, "50sOnQ8Y1KcKSuVZQeN0qR", null, null, null, 1, 0], [1, 609.901, 338.207, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, 90]], [18, "car-3", 2, 33554432, [-59], [[[3, -54, [0, "77v2PHeqpISLHCDO/8C5P3"], [5, 27.8, 60]], [16, true, 1, 0, -55, [0, "d5N+gccBdF1ItHQOpZ8uXt"]], [13, 1, -56, [0, "279Oh2IrxGZ43nUuERd9jS"], [5, 27.8, 60.1]], [10, 0, -57, [0, "aeXo01Po9HQKvz3eQLwO4+"], 7], -58], 4, 4, 4, 4, 1], [2, "afi6VUYD9DcZcYwA/ar5mp", null, null, null, 1, 0], [1, 610.325, -338.674, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.3, 1.3, 1], [1, 0, 0, 90]], [18, "car-4", 2, 33554432, [-65], [[[3, -60, [0, "4fZ46VYFJEy7Y/2MFK5FDd"], [5, 27.8, 60]], [16, true, 1, 0, -61, [0, "7eRuSxKudDIpl/xUdy+cwp"]], [13, 1, -62, [0, "82C4SiiX9FmZS/Pxd3gdJt"], [5, 27.8, 60.1]], [10, 0, -63, [0, "6eQI1JAkdCjKO5sGANMO+o"], 9], -64], 4, 4, 4, 4, 1], [2, "f2It3rwSVAYonZr0sxJiGz", null, null, null, 1, 0], [1, -610.157, -338.674, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [25, "cars", 33554432, 1, [3, 4, 5, 6], [[19, -66, [0, "55QaEEFuxE8qPGg1d77psJ"]]], [2, "54YmnXlspLyaBggn+ZgMJZ", null, null, null, 1, 0], [1, 0, -276.598, 0]], [9, 0, {}, 3, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -71, [12, "ecF+pvTFxDA4n5kL51Gtxr", 1, [[17, "healthBar", ["_name"], -67], [4, ["_lpos"], -68, [1, -35, 0, 0]], [4, ["_lrot"], -69, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -70, [1, 0, 0, 90]], [8, 50, ["offsetY"], [1, ["88S4hp27BPFpL6l7nafPlR"]]], [7, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [8, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 2]], [9, 0, {}, 4, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -76, [12, "bfdc8M3EpEkpLqR3RzQ9vT", 1, [[17, "healthBar", ["_name"], -72], [4, ["_lpos"], -73, [1, 35, 0, 0]], [4, ["_lrot"], -74, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -75, [1, 0, 0, 90]], [7, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [8, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 4]], [9, 0, {}, 5, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -81, [12, "00zwVdEFlGoLnPSiR6hmTZ", 1, [[17, "healthBar", ["_name"], -77], [4, ["_lpos"], -78, [1, 35, 0, 0]], [4, ["_lrot"], -79, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -80, [1, 0, 0, 90]], [7, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [8, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 6]], [9, 0, {}, 6, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -86, [12, "398EY9QyxNvoEw6E0YVYth", 1, [[17, "healthBar", ["_name"], -82], [4, ["_lpos"], -83, [1, -35, 0, 0]], [4, ["_lrot"], -84, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -85, [1, 0, 0, 90]], [7, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [8, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 8]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [6, "scene", 33554432, 1, [[3, -87, [0, "5eAsKuBINNqqi5Cv82y2hG"], [5, 4306.8949999999995, 3083.908]], [10, 0, -88, [0, "51ADw3K6JD2LIX1892TUi0"], 0]], [2, "486zniwItIy4jrXU+pB0Rc", null, null, null, 1, 0], [1, -30.479, -94.457, 0]], [9, 0, {}, 1, [11, "793wXpE0dMZZF5oHFdhgzy", null, null, -89, [12, "c7ptlcvJ1B8oUI4ZP7h749", 1, [[8, "PaintRoot", ["_name"], [1, ["793wXpE0dMZZF5oHFdhgzy"]]], [7, ["_lpos"], [1, ["793wXpE0dMZZF5oHFdhgzy"]], [1, -639.97, -359.883, 0]], [7, ["_lrot"], [1, ["793wXpE0dMZZF5oHFdhgzy"]], [3, 0, 0, 0, 1]], [7, ["_euler"], [1, ["793wXpE0dMZZF5oHFdhgzy"]], [1, 0, 0, 0]]]], 1]], [6, "tree", 1, 2, [[3, -90, [0, "05lIk6XpVAt4bPc7KUBpCY"], [5, 1716.892, 974.032]], [21, -91, [0, "b2zl2QmWVHvqs5fgUXm/RO"], [0, -2436.2, 405.90000000000003], [[[0, 1908.6, -423.5], [0, 1754.7, 80.2], [0, 3308.4, 99.3], [0, 3321.7, -676.3], [0, 2808, -654.9], [0, 2758.5, -586.5], [0, 2894.4, -396.3], [0, 3045.7, -285.4], [0, 3203.7, -383.9], [0, 3230.2, -122.7], [0, 2634, -303.8], [0, 2444.1, -271.4], [0, 2358.1, -571.9], [0, 2093.5, -502], [0, 2005.8, -352.4]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [2, "64Ukp8RBFJkIURGWUcvwar", null, null, null, 1, 0], [1, 808.9665, 555.761, 0]], [6, "tree-001", 1, 2, [[3, -92, [0, "8fkMQVQt1M+KinCxOdbNEl"], [5, 1716.892, 974.032]], [21, -93, [0, "c61lcpMwdLLLpcWWFbVgpR"], [0, -2487.2, 425.70000000000005], [[[0, 1577.5, -507.2], [0, 1581.4, 82.7], [0, 3437.4, 68.6], [0, 3413.6, -167.9], [0, 3237.6, -253.8], [0, 3089.8, -115.8], [0, 2801.4, -394.2], [0, 2688, -363.6], [0, 2625.1, -219], [0, 2424, -263.1], [0, 2362.9, -449.7], [0, 2032.1, -711.5]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [2, "55nbG1CfdH+6p5O0fEE0Pd", null, null, null, 1, 0], [1, -825.478, 561.887, 0]], [6, "tree-002", 1, 2, [[3, -94, [0, "15hyUsDoVL4Kb3QTZn/IZE"], [5, 1716.892, 974.032]], [5, -95, [0, "2dX1JiuENProwkk+yVG9zK"], [0, 631.6, 298.7], [5, 450.7, 188.6]]], [2, "c8sWI/M5dJTL+W5b0uxnt+", null, null, null, 1, 0], [1, 531.319, -518.028, 0]], [6, "tree-003", 1, 2, [[3, -96, [0, "9dhD4ztkVGR5KCLO1dZGjY"], [5, 163.723, 138.28800000000015]], [5, -97, [0, "06E0JJWBpI240fNyzVHKlN"], [0, -34, 1.4], [5, 167.3, 138.9]]], [2, "94ljXWkItMWICRVxMagIld", null, null, null, 1, 0], [1, -699.4635000000001, 451.829, 0]], [6, "tree-004", 1, 2, [[3, -98, [0, "6bS9/lOb9M0K+/q6KW1wLU"], [5, 163.723, 138.28800000000015]], [5, -99, [0, "2erEYKHpBIeb2nu07xzUQy"], [0, -33, -4.8], [5, 159.5, 115.6]]], [2, "a3TGk51klLl4cgAWI+ttil", null, null, null, 1, 0], [1, -357.728, 430.396, 0]], [6, "tree-005", 1, 2, [[3, -100, [0, "a7YrIyqfhC/6Czjr0OPa+2"], [5, 163.723, 138.28800000000015]], [5, -101, [0, "13TS0Xay1HO6WBq+7ip3Ys"], [0, -34.7, -3], [5, 121.4, 137.3]]], [2, "e6khndTCxLKp99Z2F/cmUL", null, null, null, 1, 0], [1, -189.833, 489.338, 0]], [6, "tree-006", 1, 2, [[3, -102, [0, "3dAJgJ4GpDULPQyenr26OQ"], [5, 163.723, 138.28800000000015]], [5, -103, [0, "f5fmSUjWtEeZXDT+RU0UsX"], [0, -36.5, -2.5], [5, 137.2, 151.3]]], [2, "06TosbnJ5CrI+BlpXqCrRl", null, null, null, 1, 0], [1, 1.282, 433.968, 0]], [26, "BulletRoot", 4, 1, [[19, -104, [0, "fb0w17IjtKzIPsuAwhzNPj"]]], [2, "66KS9FnnFAzp7L5GiMkxzK", null, null, null, 1, 0]], [30, 10, 10, 50, 0.1, 2, 3, [0, "c4clY0r5VDU72U+gRnPaqq"]], [20, 25, 25, 80, 100, 0.1, 0.5, 2, 4, [0, "64VaHtchFMh4Ez7APHyGJG"]], [20, 20, 20, 100, 80, 0.1, 1, 1, 5, [0, "1b379tXNNLIqa2czBfe4fo"]], [20, 20, 20, 80, 80, 0.1, 0.5, 2, 6, [0, "d9tgjNQWBLa6HJdzgYPHBS"]]], 0, [0, -1, 11, 0, -2, 10, 0, -3, 9, 0, -4, 8, 0, -5, 17, 0, 2, 8, 0, 3, 8, 0, 2, 9, 0, 3, 9, 0, 2, 10, 0, 3, 10, 0, 2, 11, 0, 3, 11, 0, 2, 9, 0, 3, 27, 0, 2, 8, 0, 3, 26, 0, 2, 10, 0, 3, 28, 0, 2, 11, 0, 3, 29, 0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 16, 0, -2, 17, 0, -3, 25, 0, -4, 7, 0, -5, 2, 0, 0, 2, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, -5, 22, 0, -6, 23, 0, -7, 24, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -5, 26, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -5, 27, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -5, 28, 0, -1, 10, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -5, 29, 0, -1, 11, 0, 0, 7, 0, 1, 12, 0, 1, 12, 0, 1, 12, 0, 1, 12, 0, 4, 8, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 4, 9, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 4, 10, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 4, 11, 0, 0, 16, 0, 0, 16, 0, 4, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 13, 1, 3, 7, 7, 4, 7, 7, 5, 7, 7, 6, 7, 7, 104], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 26, 26, 26, 26, 27, 27, 27, 27, 27, 28, 28, 28, 28, 28, 29, 29, 29, 29, 29], [5, 6, 6, 5, 6, 5, 6, 5, 6, 5, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12], [5, 6, 0, 7, 0, 8, 0, 9, 0, 10, 1, 11, 2, 3, 4, 1, 12, 2, 3, 4, 1, 13, 2, 3, 4, 1, 14, 2, 3, 4]], [[{"name": "jungle", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2664}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2664}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1332, 0, 2000, -1332, 0, -2000, 1332, 0, 2000, 1332, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2664, 4000, 2664, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1332, "z": 0}, "maxPos": {"x": 2000, "y": 1332, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [7], 0, [0], [14], [15]]]]