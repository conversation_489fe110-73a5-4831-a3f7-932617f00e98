System.register("chunks:///_virtual/builtin-pipeline-settings.ts",["./rollupPluginModLoBabelHelpers.js","cc","./builtin-pipeline-types.ts"],(function(t){var e,o,i,r,s,n,a,p,l,g,c,y,d,b;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.initializerDefineProperty},function(t){i=t.cclegacy,r=t.Camera,s=t.CCBoolean,n=t.CCInteger,a=t.CCFloat,p=t.Material,l=t.Texture2D,g=t._decorator,c=t.Component,y=t.rendering},function(t){d=t.fillRequiredPipelineSettings,b=t.makePipelineSettings}],execute:function(){var m,h,u,P,_,M,S,f,E,O,w,G,C,D,A,j,x,v,F,B,R,k,I,T,L,X,z,H;i._RF.push({},"de1c2EHcMhAIYRZY5nyTQHG","builtin-pipeline-settings",void 0);const{ccclass:q,disallowMultiple:Y,executeInEditMode:N,menu:Q,property:Z,requireComponent:J,type:K}=g;t("BuiltinPipelineSettings",(m=q("BuiltinPipelineSettings"),h=Q("Rendering/BuiltinPipelineSettings"),u=J(r),P=Z(s),_=Z({displayName:"Editor Preview (Experimental)",type:s}),M=Z({group:{id:"MSAA",name:"Multisample Anti-Aliasing"},type:s}),S=Z({group:{id:"MSAA",name:"Multisample Anti-Aliasing",style:"section"},type:n,range:[2,4,2]}),f=Z({group:{id:"ShadingScale",name:"ShadingScale",style:"section"},type:s}),E=Z({tooltip:"i18n:postprocess.shadingScale",group:{id:"ShadingScale",name:"ShadingScale"},type:a,range:[.01,4,.01],slide:!0}),O=Z({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:s}),w=Z({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:p}),G=Z({tooltip:"i18n:bloom.enableAlphaMask",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:s}),C=Z({tooltip:"i18n:bloom.iterations",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:n,range:[1,6,1],slide:!0}),D=Z({tooltip:"i18n:bloom.threshold",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:a,min:0}),A=Z({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:s}),j=Z({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:p}),x=Z({tooltip:"i18n:color_grading.contribute",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:a,range:[0,1,.01],slide:!0}),v=Z({tooltip:"i18n:color_grading.originalMap",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:l}),F=Z({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:s}),B=Z({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:p}),R=Z({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:s}),k=Z({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:p}),I=Z({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:a,range:[0,1,.01],slide:!0}),T=Z({group:{id:"ToneMapping",name:"ToneMapping",style:"section"},type:p}),m(L=h(L=u(L=Y(L=N((z=e((X=class extends c{constructor(...t){super(...t),o(this,"_settings",z,this),o(this,"_editorPreview",H,this)}getPipelineSettings(){return this._settings}onEnable(){d(this._settings);this.getComponent(r).camera.pipelineSettings=this._settings}onDisable(){this.getComponent(r).camera.pipelineSettings=null}get editorPreview(){return this._editorPreview}set editorPreview(t){this._editorPreview=t}_tryEnableEditorPreview(){void 0!==y&&(this._editorPreview?y.setEditorPipelineSettings(this._settings):this._disableEditorPreview())}_disableEditorPreview(){if(void 0===y)return;y.getEditorPipelineSettings()===this._settings&&y.setEditorPipelineSettings(null)}get MsaaEnable(){return this._settings.msaa.enabled}set MsaaEnable(t){this._settings.msaa.enabled=t}set msaaSampleCount(t){t=2**Math.ceil(Math.log2(Math.max(t,2))),t=Math.min(t,4),this._settings.msaa.sampleCount=t}get msaaSampleCount(){return this._settings.msaa.sampleCount}set shadingScaleEnable(t){this._settings.enableShadingScale=t}get shadingScaleEnable(){return this._settings.enableShadingScale}set shadingScale(t){this._settings.shadingScale=t}get shadingScale(){return this._settings.shadingScale}set bloomEnable(t){this._settings.bloom.enabled=t}get bloomEnable(){return this._settings.bloom.enabled}set bloomMaterial(t){this._settings.bloom.material!==t&&(this._settings.bloom.material=t)}get bloomMaterial(){return this._settings.bloom.material}set bloomEnableAlphaMask(t){this._settings.bloom.enableAlphaMask=t}get bloomEnableAlphaMask(){return this._settings.bloom.enableAlphaMask}set bloomIterations(t){this._settings.bloom.iterations=t}get bloomIterations(){return this._settings.bloom.iterations}set bloomThreshold(t){this._settings.bloom.threshold=t}get bloomThreshold(){return this._settings.bloom.threshold}set bloomIntensity(t){this._settings.bloom.intensity=t}get bloomIntensity(){return this._settings.bloom.intensity}set colorGradingEnable(t){this._settings.colorGrading.enabled=t}get colorGradingEnable(){return this._settings.colorGrading.enabled}set colorGradingMaterial(t){this._settings.colorGrading.material!==t&&(this._settings.colorGrading.material=t)}get colorGradingMaterial(){return this._settings.colorGrading.material}set colorGradingContribute(t){this._settings.colorGrading.contribute=t}get colorGradingContribute(){return this._settings.colorGrading.contribute}set colorGradingMap(t){this._settings.colorGrading.colorGradingMap=t}get colorGradingMap(){return this._settings.colorGrading.colorGradingMap}set fxaaEnable(t){this._settings.fxaa.enabled=t}get fxaaEnable(){return this._settings.fxaa.enabled}set fxaaMaterial(t){this._settings.fxaa.material!==t&&(this._settings.fxaa.material=t)}get fxaaMaterial(){return this._settings.fxaa.material}set fsrEnable(t){this._settings.fsr.enabled=t}get fsrEnable(){return this._settings.fsr.enabled}set fsrMaterial(t){this._settings.fsr.material!==t&&(this._settings.fsr.material=t)}get fsrMaterial(){return this._settings.fsr.material}set fsrSharpness(t){this._settings.fsr.sharpness=t}get fsrSharpness(){return this._settings.fsr.sharpness}set toneMappingMaterial(t){this._settings.toneMapping.material!==t&&(this._settings.toneMapping.material=t)}get toneMappingMaterial(){return this._settings.toneMapping.material}}).prototype,"_settings",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return b()}}),H=e(X.prototype,"_editorPreview",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),e(X.prototype,"editorPreview",[_],Object.getOwnPropertyDescriptor(X.prototype,"editorPreview"),X.prototype),e(X.prototype,"MsaaEnable",[M],Object.getOwnPropertyDescriptor(X.prototype,"MsaaEnable"),X.prototype),e(X.prototype,"msaaSampleCount",[S],Object.getOwnPropertyDescriptor(X.prototype,"msaaSampleCount"),X.prototype),e(X.prototype,"shadingScaleEnable",[f],Object.getOwnPropertyDescriptor(X.prototype,"shadingScaleEnable"),X.prototype),e(X.prototype,"shadingScale",[E],Object.getOwnPropertyDescriptor(X.prototype,"shadingScale"),X.prototype),e(X.prototype,"bloomEnable",[O],Object.getOwnPropertyDescriptor(X.prototype,"bloomEnable"),X.prototype),e(X.prototype,"bloomMaterial",[w],Object.getOwnPropertyDescriptor(X.prototype,"bloomMaterial"),X.prototype),e(X.prototype,"bloomEnableAlphaMask",[G],Object.getOwnPropertyDescriptor(X.prototype,"bloomEnableAlphaMask"),X.prototype),e(X.prototype,"bloomIterations",[C],Object.getOwnPropertyDescriptor(X.prototype,"bloomIterations"),X.prototype),e(X.prototype,"bloomThreshold",[D],Object.getOwnPropertyDescriptor(X.prototype,"bloomThreshold"),X.prototype),e(X.prototype,"colorGradingEnable",[A],Object.getOwnPropertyDescriptor(X.prototype,"colorGradingEnable"),X.prototype),e(X.prototype,"colorGradingMaterial",[j],Object.getOwnPropertyDescriptor(X.prototype,"colorGradingMaterial"),X.prototype),e(X.prototype,"colorGradingContribute",[x],Object.getOwnPropertyDescriptor(X.prototype,"colorGradingContribute"),X.prototype),e(X.prototype,"colorGradingMap",[v],Object.getOwnPropertyDescriptor(X.prototype,"colorGradingMap"),X.prototype),e(X.prototype,"fxaaEnable",[F],Object.getOwnPropertyDescriptor(X.prototype,"fxaaEnable"),X.prototype),e(X.prototype,"fxaaMaterial",[B],Object.getOwnPropertyDescriptor(X.prototype,"fxaaMaterial"),X.prototype),e(X.prototype,"fsrEnable",[R],Object.getOwnPropertyDescriptor(X.prototype,"fsrEnable"),X.prototype),e(X.prototype,"fsrMaterial",[k],Object.getOwnPropertyDescriptor(X.prototype,"fsrMaterial"),X.prototype),e(X.prototype,"fsrSharpness",[I],Object.getOwnPropertyDescriptor(X.prototype,"fsrSharpness"),X.prototype),e(X.prototype,"toneMappingMaterial",[T],Object.getOwnPropertyDescriptor(X.prototype,"toneMappingMaterial"),X.prototype),L=X))||L)||L)||L)||L)||L));i._RF.pop()}}}));

System.register("chunks:///_virtual/builtin-pipeline-types.ts",["cc"],(function(e){var a,n;return{setters:[function(e){a=e.cclegacy,n=e.gfx}],execute:function(){e({fillRequiredBloom:o,fillRequiredColorGrading:u,fillRequiredFSR:c,fillRequiredFXAA:m,fillRequiredHBAO:function(e){void 0===e.enabled&&(e.enabled=!1);void 0===e.radiusScale&&(e.radiusScale=1);void 0===e.angleBiasDegree&&(e.angleBiasDegree=10);void 0===e.blurSharpness&&(e.blurSharpness=3);void 0===e.aoSaturation&&(e.aoSaturation=1);void 0===e.needBlur&&(e.needBlur=!1)},fillRequiredMSAA:r,fillRequiredPipelineSettings:function(e){e.msaa?r(e.msaa):e.msaa=i();void 0===e.enableShadingScale&&(e.enableShadingScale=!1);void 0===e.shadingScale&&(e.shadingScale=.5);e.bloom?o(e.bloom):e.bloom={enabled:!1,material:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:2.3};e.toneMapping?f(e.toneMapping):e.toneMapping={material:null};e.colorGrading?u(e.colorGrading):e.colorGrading={enabled:!1,material:null,contribute:1,colorGradingMap:null};e.fsr?c(e.fsr):e.fsr={enabled:!1,material:null,sharpness:.8};e.fxaa?m(e.fxaa):e.fxaa={enabled:!1,material:null}},fillRequiredToneMapping:f,makeBloom:t,makeColorGrading:d,makeFSR:s,makeFXAA:b,makeHBAO:function(){return{enabled:!1,radiusScale:1,angleBiasDegree:10,blurSharpness:3,aoSaturation:1,needBlur:!1}},makeMSAA:i,makePipelineSettings:function(){return{msaa:i(),enableShadingScale:!1,shadingScale:.5,bloom:{enabled:!1,material:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:2.3},toneMapping:{material:null},colorGrading:{enabled:!1,material:null,contribute:1,colorGradingMap:null},fsr:{enabled:!1,material:null,sharpness:.8},fxaa:{enabled:!1,material:null}}},makeToneMapping:p}),a._RF.push({},"cbf30kCUX9A3K+QpVC6wnzx","builtin-pipeline-types",void 0);const{SampleCount:l}=n;function i(){return{enabled:!1,sampleCount:l.X4}}function r(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.sampleCount&&(e.sampleCount=l.X4)}function t(){return{enabled:!1,material:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:2.3}}function o(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.enableAlphaMask&&(e.enableAlphaMask=!1),void 0===e.iterations&&(e.iterations=3),void 0===e.threshold&&(e.threshold=.8),void 0===e.intensity&&(e.intensity=2.3)}function d(){return{enabled:!1,material:null,contribute:1,colorGradingMap:null}}function u(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.contribute&&(e.contribute=1),void 0===e.colorGradingMap&&(e.colorGradingMap=null)}function s(){return{enabled:!1,material:null,sharpness:.8}}function c(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.sharpness&&(e.sharpness=.8)}function b(){return{enabled:!1,material:null}}function m(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null)}function p(){return{material:null}}function f(e){void 0===e.material&&(e.material=null)}a._RF.pop()}}}));

System.register("chunks:///_virtual/builtin-pipeline.ts",["cc","./env","./builtin-pipeline-types.ts"],(function(e){var a,t,i,s,n,o,r,d,l,h,c,g,p,u,m,S,_;return{setters:[function(e){a=e.cclegacy,t=e.Vec2,i=e.Vec4,s=e.gfx,n=e.Vec3,o=e.rendering,r=e.assert,d=e.renderer,l=e.clamp,h=e.Material,c=e.Layers,g=e.PipelineEventType,p=e.geometry,u=e.sys,m=e.pipeline},function(e){S=e.DEBUG},function(e){_=e.makePipelineSettings}],execute:function(){e("getPingPongRenderTarget",W),a._RF.push({},"ff9b0GZzgRM/obMbHGfCNbk","builtin-pipeline",void 0);const{AABB:f,Sphere:w,intersect:b}=p,{ClearFlagBit:P,Color:R,Format:T,FormatFeatureBit:M,LoadOp:E,StoreOp:C,TextureType:A,Viewport:x}=s,{scene:L}=d,{CameraUsage:D,CSMLevel:F,LightType:N}=L;function O(e){return!!(e.clearFlag&(P.COLOR|P.STENCIL<<1))}function Q(e,a,t,i,s,n){e.shadowFixedArea||e.csmLevel===F.LEVEL_1?(s.left=0,s.top=0,s.width=Math.trunc(a),s.height=Math.trunc(t)):(s.left=Math.trunc(i%2*.5*a),s.top=n>0?Math.trunc(.5*(1-Math.floor(i/2))*t):Math.trunc(.5*Math.floor(i/2)*t),s.width=Math.trunc(.5*a),s.height=Math.trunc(.5*t)),s.left=Math.max(0,s.left),s.top=Math.max(0,s.top),s.width=Math.max(1,s.width),s.height=Math.max(1,s.height)}class B{constructor(){this.isWeb=!1,this.isWebGL1=!1,this.isWebGPU=!1,this.isMobile=!1,this.isHDR=!1,this.useFloatOutput=!1,this.toneMappingType=0,this.shadowEnabled=!1,this.shadowMapFormat=T.R32F,this.shadowMapSize=new t(1,1),this.usePlanarShadow=!1,this.screenSpaceSignY=1,this.supportDepthSample=!1,this.mobileMaxSpotLightShadowMaps=1,this.platform=new i(0,0,0,0)}}function H(e,a){const t=M.SAMPLED_TEXTURE|M.LINEAR_FILTER,i=e.device;a.isWeb=!u.isNative,a.isWebGL1=i.gfxAPI===s.API.WEBGL,a.isWebGPU=i.gfxAPI===s.API.WEBGPU,a.isMobile=u.isMobile,a.isHDR=e.pipelineSceneData.isHDR,a.useFloatOutput=e.getMacroBool("CC_USE_FLOAT_OUTPUT"),a.toneMappingType=e.pipelineSceneData.postSettings.toneMappingType;const n=e.pipelineSceneData.shadows;a.shadowEnabled=n.enabled,a.shadowMapFormat=m.supportsR32FloatTexture(e.device)?T.R32F:T.RGBA8,a.shadowMapSize.set(n.size),a.usePlanarShadow=n.enabled&&n.type===d.scene.ShadowType.Planar,a.screenSpaceSignY=e.device.capabilities.screenSpaceSignY,a.supportDepthSample=(e.device.getFormatFeatures(T.DEPTH_STENCIL)&t)===t;const o=i.capabilities.screenSpaceSignY;a.platform.x=a.isMobile?1:0,a.platform.w=.5*o+.5<<1|.5*i.capabilities.clipSpaceSignY+.5}e("PipelineConfigs",B);const y=_();class z{constructor(){this.settings=y,this.isMainGameWindow=!1,this.renderWindowId=0,this.colorName="",this.depthStencilName="",this.enableFullPipeline=!1,this.enableProfiler=!1,this.remainingPasses=0,this.enableShadingScale=!1,this.shadingScale=1,this.nativeWidth=1,this.nativeHeight=1,this.width=1,this.height=1,this.enableHDR=!1,this.radianceFormat=s.Format.RGBA8,this.copyAndTonemapMaterial=null,this.enableStoreSceneDepth=!1}}e("CameraConfigs",z);const v=new R(0,0,0,0);function I(e,a,t,i){r(!!t.copyAndTonemapMaterial);const s=e.addRenderPass(t.nativeWidth,t.nativeHeight,"cc-tone-mapping");return s.addRenderTarget(t.colorName,E.CLEAR,C.STORE,v),s.addTexture(i,"inputTexture"),s.setVec4("g_platform",a.platform),s.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(t.copyAndTonemapMaterial,1),s}function W(e,a,t){return e.startsWith(a)?`${a}${1-Number(e.charAt(a.length))}_${t}`:`${a}0_${t}`}class G{constructor(){this.lights=[],this.shadowEnabledSpotLights=[],this._sphere=w.create(0,0,0,1),this._boundingBox=new f,this._rangedDirLightBoundingBox=new f(0,0,0,.5,.5,.5)}cullLights(e,a,t){this.lights.length=0,this.shadowEnabledSpotLights.length=0;for(const t of e.spotLights)t.baked||(w.set(this._sphere,t.position.x,t.position.y,t.position.z,t.range),b.sphereFrustum(this._sphere,a)&&(t.shadowEnabled?this.shadowEnabledSpotLights.push(t):this.lights.push(t)));for(const t of e.sphereLights)t.baked||(w.set(this._sphere,t.position.x,t.position.y,t.position.z,t.range),b.sphereFrustum(this._sphere,a)&&this.lights.push(t));for(const t of e.pointLights)t.baked||(w.set(this._sphere,t.position.x,t.position.y,t.position.z,t.range),b.sphereFrustum(this._sphere,a)&&this.lights.push(t));for(const t of e.rangedDirLights)f.transform(this._boundingBox,this._rangedDirLightBoundingBox,t.node.getWorldMatrix()),b.aabbFrustum(this._boundingBox,a)&&this.lights.push(t);t&&this.shadowEnabledSpotLights.sort(((e,a)=>n.squaredDistance(t,e.position)-n.squaredDistance(t,a.position)))}_addLightQueues(e,a){for(const t of this.lights){const i=a.addQueue(o.QueueHint.BLEND,"forward-add");switch(t.type){case N.SPHERE:i.name="sphere-light";break;case N.SPOT:i.name="spot-light";break;case N.POINT:i.name="point-light";break;case N.RANGED_DIRECTIONAL:i.name="ranged-directional-light";break;default:i.name="unknown-light"}i.addScene(e,o.SceneFlags.BLEND,t)}}addSpotlightShadowPasses(e,a,t){let i=0;for(const s of this.shadowEnabledSpotLights){const n=e.pipelineSceneData.shadows.size,r=e.addRenderPass(n.x,n.y,"default");if(r.name=`SpotLightShadowPass${i}`,r.addRenderTarget(`SpotShadowMap${i}`,E.CLEAR,C.STORE,new R(1,1,1,1)),r.addDepthStencil(`SpotShadowDepth${i}`,E.CLEAR,C.DISCARD),r.addQueue(o.QueueHint.NONE,"shadow-caster").addScene(a,o.SceneFlags.OPAQUE|o.SceneFlags.MASK|o.SceneFlags.SHADOW_CASTER).useLightFrustum(s),++i,i>=t)break}}addLightQueues(e,a,t){this._addLightQueues(a,e);let i=0;for(const s of this.shadowEnabledSpotLights){e.addTexture(`SpotShadowMap${i}`,"cc_spotShadowMap");if(e.addQueue(o.QueueHint.BLEND,"forward-add").addScene(a,o.SceneFlags.BLEND,s),++i,i>=t)break}}addLightPasses(e,a,t,i,s,n,r,d,l,h){this._addLightQueues(r,h);let c=0;const g=l.pipelineSceneData.shadows.size;for(const p of this.shadowEnabledSpotLights){const u=l.addRenderPass(g.x,g.y,"default");u.name="SpotlightShadowPass",u.addRenderTarget(`ShadowMap${i}`,E.CLEAR,C.STORE,new R(1,1,1,1)),u.addDepthStencil(`ShadowDepth${i}`,E.CLEAR,C.DISCARD),u.addQueue(o.QueueHint.NONE,"shadow-caster").addScene(r,o.SceneFlags.OPAQUE|o.SceneFlags.MASK|o.SceneFlags.SHADOW_CASTER).useLightFrustum(p),++c;const m=c===this.shadowEnabledSpotLights.length?t:C.STORE;(h=l.addRenderPass(s,n,"default")).name="SpotlightWithShadowMap",h.setViewport(d),h.addRenderTarget(e,E.LOAD),h.addDepthStencil(a,E.LOAD,m),h.addTexture(`ShadowMap${i}`,"cc_spotShadowMap");h.addQueue(o.QueueHint.BLEND,"forward-add").addScene(r,o.SceneFlags.BLEND,p)}return h}isMultipleLightPassesNeeded(){return this.shadowEnabledSpotLights.length>0}}class ${constructor(){this.forwardLighting=new G,this._viewport=new x,this._clearColor=new R(0,0,0,1),this._reflectionProbeClearColor=new n(0,0,0)}getConfigOrder(){return $.ConfigOrder}getRenderOrder(){return $.RenderOrder}configCamera(e,a,t){t.enableMainLightShadowMap=a.shadowEnabled&&!a.usePlanarShadow&&!!e.scene&&!!e.scene.mainLight&&e.scene.mainLight.shadowEnabled,t.enableMainLightPlanarShadowMap=a.shadowEnabled&&a.usePlanarShadow&&!!e.scene&&!!e.scene.mainLight&&e.scene.mainLight.shadowEnabled,t.enablePlanarReflectionProbe=t.isMainGameWindow||e.cameraUsage===D.SCENE_VIEW||e.cameraUsage===D.GAME_VIEW,t.enableMSAA=t.settings.msaa.enabled&&!t.enableStoreSceneDepth&&!a.isWeb&&!a.isWebGL1,t.enableSingleForwardPass=a.isMobile||t.enableMSAA,++t.remainingPasses}windowResize(e,a,t,i,s,n,r){const d=o.ResourceFlags,l=o.ResourceResidency,h=i.renderWindowId,c=t.settings,g=t.enableShadingScale?Math.max(Math.floor(n*t.shadingScale),1):n,p=t.enableShadingScale?Math.max(Math.floor(r*t.shadingScale),1):r;if(t.enableMSAA&&(t.enableHDR?e.addTexture(`MsaaRadiance${h}`,A.TEX2D,t.radianceFormat,g,p,1,1,1,c.msaa.sampleCount,d.COLOR_ATTACHMENT,l.MEMORYLESS):e.addTexture(`MsaaRadiance${h}`,A.TEX2D,T.RGBA8,g,p,1,1,1,c.msaa.sampleCount,d.COLOR_ATTACHMENT,l.MEMORYLESS),e.addTexture(`MsaaDepthStencil${h}`,A.TEX2D,T.DEPTH_STENCIL,g,p,1,1,1,c.msaa.sampleCount,d.DEPTH_STENCIL_ATTACHMENT,l.MEMORYLESS)),e.addRenderTarget(`ShadowMap${h}`,a.shadowMapFormat,a.shadowMapSize.x,a.shadowMapSize.y),e.addDepthStencil(`ShadowDepth${h}`,T.DEPTH_STENCIL,a.shadowMapSize.x,a.shadowMapSize.y),t.enableSingleForwardPass){const t=a.mobileMaxSpotLightShadowMaps;for(let i=0;i!==t;++i)e.addRenderTarget(`SpotShadowMap${i}`,a.shadowMapFormat,a.shadowMapSize.x,a.shadowMapSize.y),e.addDepthStencil(`SpotShadowDepth${i}`,T.DEPTH_STENCIL,a.shadowMapSize.x,a.shadowMapSize.y)}}setup(e,a,t,i,s){const n=i.window.renderWindowId,o=i.scene,d=o.mainLight;--t.remainingPasses,r(t.remainingPasses>=0),this.forwardLighting.cullLights(o,i.frustum),t.enableMainLightShadowMap&&(r(!!d),this._addCascadedShadowMapPass(e,a,n,d,i)),t.enableSingleForwardPass&&this.forwardLighting.addSpotlightShadowPasses(e,i,a.mobileMaxSpotLightShadowMaps),this._tryAddReflectionProbePasses(e,t,n,d,i.scene),t.remainingPasses>0||t.enableShadingScale?(s.colorName=t.enableShadingScale?`ScaledRadiance0_${n}`:`Radiance0_${n}`,s.depthStencilName=t.enableShadingScale?`ScaledSceneDepth_${n}`:`SceneDepth_${n}`):(s.colorName=t.colorName,s.depthStencilName=t.depthStencilName);const l=this._addForwardRadiancePasses(e,a,t,n,i,t.width,t.height,d,s.colorName,s.depthStencilName,!t.enableMSAA,t.enableStoreSceneDepth?C.STORE:C.DISCARD);return t.enableStoreSceneDepth||(s.depthStencilName=""),0===t.remainingPasses&&t.enableShadingScale?I(e,a,t,s.colorName):l}_addCascadedShadowMapPass(e,a,t,i,s){const n=o.QueueHint,r=o.SceneFlags,d=e.pipelineSceneData.shadows.size,l=d.x,h=d.y,c=this._viewport;c.left=c.top=0,c.width=l,c.height=h;const g=e.addRenderPass(l,h,"default");g.name="CascadedShadowMap",g.addRenderTarget(`ShadowMap${t}`,E.CLEAR,C.STORE,new R(1,1,1,1)),g.addDepthStencil(`ShadowDepth${t}`,E.CLEAR,C.DISCARD);const p=e.pipelineSceneData.csmSupported?i.csmLevel:1;for(let e=0;e!==p;++e){Q(i,l,h,e,this._viewport,a.screenSpaceSignY);const t=g.addQueue(n.NONE,"shadow-caster");a.isWebGPU||t.setViewport(this._viewport),t.addScene(s,r.OPAQUE|r.MASK|r.SHADOW_CASTER).useLightFrustum(i,e)}}_tryAddReflectionProbePasses(e,t,i,n,r){const l=a.internal.reflectionProbeManager;if(!l)return;const h=o.ResourceResidency,c=l.getProbes();let g=0;for(const a of c){if(!a.needRender)continue;const o=a.renderArea(),l=Math.max(Math.floor(o.x),1),c=Math.max(Math.floor(o.y),1);if(a.probeType===d.scene.ProbeType.PLANAR){if(!t.enablePlanarReflectionProbe)continue;const o=a.realtimePlanarTexture.window,d=`PlanarProbeRT${g}`,p=`PlanarProbeDS${g}`;e.addRenderWindow(d,t.radianceFormat,l,c,o),e.addDepthStencil(p,s.Format.DEPTH_STENCIL,l,c,h.MEMORYLESS);const u=e.addRenderPass(l,c,"default");u.name=`PlanarReflectionProbe${g}`,this._buildReflectionProbePass(u,t,i,a.camera,d,p,n,r)}if(++g,4===g)break}}_buildReflectionProbePass(e,a,t,i,s,n,r,d=null){const l=o.QueueHint,h=o.SceneFlags,c=a.enableMSAA?C.DISCARD:C.STORE;if(O(i)){this._reflectionProbeClearColor.x=i.clearColor.x,this._reflectionProbeClearColor.y=i.clearColor.y,this._reflectionProbeClearColor.z=i.clearColor.z;const a=o.packRGBE(this._reflectionProbeClearColor);this._clearColor.x=a.x,this._clearColor.y=a.y,this._clearColor.z=a.z,this._clearColor.w=a.w,e.addRenderTarget(s,E.CLEAR,c,this._clearColor)}else e.addRenderTarget(s,E.LOAD,c);i.clearFlag&P.DEPTH_STENCIL?e.addDepthStencil(n,E.CLEAR,C.DISCARD,i.clearDepth,i.clearStencil,i.clearFlag&P.DEPTH_STENCIL):e.addDepthStencil(n,E.LOAD,C.DISCARD),a.enableMainLightShadowMap&&e.addTexture(`ShadowMap${t}`,"cc_shadowMap"),e.addQueue(l.NONE,"reflect-map").addScene(i,h.OPAQUE|h.MASK|h.REFLECTION_PROBE,r||void 0,d||void 0)}_addForwardRadiancePasses(e,a,t,i,s,n,d,l,h,c,g=!1,p=C.DISCARD){const u=o.QueueHint,m=o.SceneFlags,S=s.clearColor;this._clearColor.x=S.x,this._clearColor.y=S.y,this._clearColor.z=S.z,this._clearColor.w=S.w;const _=s.viewport;this._viewport.left=Math.round(_.x*n),this._viewport.top=Math.round(_.y*d),this._viewport.width=Math.max(Math.round(_.width*n),1),this._viewport.height=Math.max(Math.round(_.height*d),1);const f=!g&&t.enableMSAA;r(!f||t.enableSingleForwardPass);const w=t.enableSingleForwardPass?this._addForwardSingleRadiancePass(e,a,t,i,s,f,n,d,l,h,c,p):this._addForwardMultipleRadiancePasses(e,t,i,s,n,d,l,h,c,p);t.enableMainLightPlanarShadowMap&&this._addPlanarShadowQueue(s,l,w);const b=m.BLEND|(s.geometryRenderer?m.GEOMETRY:m.NONE);return w.addQueue(u.BLEND).addScene(s,b,l||void 0),w}_addForwardSingleRadiancePass(e,a,t,i,s,n,o,d,l,h,c,g){let p;if(r(t.enableSingleForwardPass),n){const a=`MsaaRadiance${i}`,n=`MsaaDepthStencil${i}`,r=t.settings.msaa.sampleCount,c=e.addMultisampleRenderPass(o,d,r,0,"default");c.name="MsaaForwardPass",this._buildForwardMainLightPass(c,t,i,s,a,n,C.DISCARD,l),c.resolveRenderTarget(a,h),p=c}else p=e.addRenderPass(o,d,"default"),p.name="ForwardPass",this._buildForwardMainLightPass(p,t,i,s,h,c,g,l);return r(void 0!==p),this.forwardLighting.addLightQueues(p,s,a.mobileMaxSpotLightShadowMaps),p}_addForwardMultipleRadiancePasses(e,a,t,i,s,n,o,d,l,h){r(!a.enableSingleForwardPass);let c=e.addRenderPass(s,n,"default");c.name="ForwardPass";const g=this.forwardLighting.isMultipleLightPassesNeeded()?C.STORE:h;return this._buildForwardMainLightPass(c,a,t,i,d,l,g,o),c=this.forwardLighting.addLightPasses(d,l,h,t,s,n,i,this._viewport,e,c),c}_buildForwardMainLightPass(e,a,t,i,s,n,r,d,l=null){const h=o.QueueHint,c=o.SceneFlags;e.setViewport(this._viewport);const g=a.enableMSAA?C.DISCARD:C.STORE;O(i)?e.addRenderTarget(s,E.CLEAR,g,this._clearColor):e.addRenderTarget(s,E.LOAD,g),i.clearFlag&P.DEPTH_STENCIL?e.addDepthStencil(n,E.CLEAR,r,i.clearDepth,i.clearStencil,i.clearFlag&P.DEPTH_STENCIL):e.addDepthStencil(n,E.LOAD,r),a.enableMainLightShadowMap&&e.addTexture(`ShadowMap${t}`,"cc_shadowMap"),e.addQueue(h.NONE).addScene(i,c.OPAQUE|c.MASK,d||void 0,l||void 0)}_addPlanarShadowQueue(e,a,t){const i=o.QueueHint,s=o.SceneFlags;t.addQueue(i.BLEND,"planar-shadow").addScene(e,s.SHADOW_CASTER|s.PLANAR_SHADOW|s.BLEND,a||void 0)}}e("BuiltinForwardPassBuilder",$),$.ConfigOrder=100,$.RenderOrder=100;class U{constructor(){this._clearColorTransparentBlack=new R(0,0,0,0),this._bloomParams=new i(0,0,0,0),this._bloomTexSize=new i(0,0,0,0),this._bloomWidths=[],this._bloomHeights=[],this._bloomTexNames=[]}getConfigOrder(){return 0}getRenderOrder(){return 200}configCamera(e,a,t){t.enableBloom=t.settings.bloom.enabled&&!!t.settings.bloom.material,t.enableBloom&&++t.remainingPasses}windowResize(e,a,t,i){if(t.enableBloom){const a=i.renderWindowId;let s=t.width,n=t.height;for(let i=0;i!==t.settings.bloom.iterations+1;++i)s=Math.max(Math.floor(s/2),1),n=Math.max(Math.floor(n/2),1),e.addRenderTarget(`BloomTex${a}_${i}`,t.radianceFormat,s,n)}}setup(e,a,t,i,s,n){if(!t.enableBloom)return n;--t.remainingPasses,r(t.remainingPasses>=0);const o=i.window.renderWindowId;return r(!!t.settings.bloom.material),this._addKawaseDualFilterBloomPasses(e,a,t,t.settings,t.settings.bloom.material,o,t.width,t.height,s.colorName)}_addKawaseDualFilterBloomPasses(e,a,t,i,s,n,r,d,l){const h=o.QueueHint,c=i.bloom.iterations,g=c+1;this._bloomWidths.length=g,this._bloomHeights.length=g,this._bloomWidths[0]=Math.max(Math.floor(r/2),1),this._bloomHeights[0]=Math.max(Math.floor(d/2),1);for(let e=1;e!==g;++e)this._bloomWidths[e]=Math.max(Math.floor(this._bloomWidths[e-1]/2),1),this._bloomHeights[e]=Math.max(Math.floor(this._bloomHeights[e-1]/2),1);this._bloomTexNames.length=g;for(let e=0;e!==g;++e)this._bloomTexNames[e]=`BloomTex${n}_${e}`;this._bloomParams.x=a.useFloatOutput?1:0,this._bloomParams.x=0,this._bloomParams.z=i.bloom.threshold,this._bloomParams.w=i.bloom.enableAlphaMask?1:0;const p=e.addRenderPass(this._bloomWidths[0],this._bloomHeights[0],"cc-bloom-prefilter");p.addRenderTarget(this._bloomTexNames[0],E.CLEAR,C.STORE,this._clearColorTransparentBlack),p.addTexture(l,"inputTexture"),p.setVec4("g_platform",a.platform),p.setVec4("bloomParams",this._bloomParams),p.addQueue(h.OPAQUE).addFullscreenQuad(s,0);for(let t=1;t!==g;++t){const i=e.addRenderPass(this._bloomWidths[t],this._bloomHeights[t],"cc-bloom-downsample");i.addRenderTarget(this._bloomTexNames[t],E.CLEAR,C.STORE,this._clearColorTransparentBlack),i.addTexture(this._bloomTexNames[t-1],"bloomTexture"),this._bloomTexSize.x=this._bloomWidths[t-1],this._bloomTexSize.y=this._bloomHeights[t-1],i.setVec4("g_platform",a.platform),i.setVec4("bloomTexSize",this._bloomTexSize),i.addQueue(h.OPAQUE).addFullscreenQuad(s,1)}for(let t=c;t-- >0;){const i=e.addRenderPass(this._bloomWidths[t],this._bloomHeights[t],"cc-bloom-upsample");i.addRenderTarget(this._bloomTexNames[t],E.CLEAR,C.STORE,this._clearColorTransparentBlack),i.addTexture(this._bloomTexNames[t+1],"bloomTexture"),this._bloomTexSize.x=this._bloomWidths[t+1],this._bloomTexSize.y=this._bloomHeights[t+1],i.setVec4("g_platform",a.platform),i.setVec4("bloomTexSize",this._bloomTexSize),i.addQueue(h.OPAQUE).addFullscreenQuad(s,2)}const u=e.addRenderPass(r,d,"cc-bloom-combine");return u.addRenderTarget(l,E.LOAD,C.STORE),u.addTexture(this._bloomTexNames[0],"bloomTexture"),u.setVec4("g_platform",a.platform),u.setVec4("bloomParams",this._bloomParams),u.addQueue(h.BLEND).addFullscreenQuad(s,3),0===t.remainingPasses?I(e,a,t,l):u}}e("BuiltinBloomPassBuilder",U);class V{constructor(){this._colorGradingTexSize=new t(0,0)}getConfigOrder(){return 0}getRenderOrder(){return 300}configCamera(e,a,t){const i=t.settings;t.enableColorGrading=i.colorGrading.enabled&&!!i.colorGrading.material&&!!i.colorGrading.colorGradingMap,t.enableToneMapping=t.enableHDR||t.enableColorGrading,t.enableToneMapping&&++t.remainingPasses}windowResize(e,a,t){t.enableColorGrading&&(r(!!t.settings.colorGrading.material),t.settings.colorGrading.material.setProperty("colorGradingMap",t.settings.colorGrading.colorGradingMap))}setup(e,a,t,i,s,n){if(!t.enableToneMapping)return n;if(--t.remainingPasses,r(t.remainingPasses>=0),0===t.remainingPasses)return this._addCopyAndTonemapPass(e,a,t,t.width,t.height,s.colorName,t.colorName);{const i=t.renderWindowId,n=t.enableShadingScale?"ScaledLdrColor":"LdrColor",o=W(s.colorName,n,i),r=s.colorName;return s.colorName=o,this._addCopyAndTonemapPass(e,a,t,t.width,t.height,r,o)}}_addCopyAndTonemapPass(e,a,t,i,s,n,d){let l;const h=t.settings;if(t.enableColorGrading){r(!!h.colorGrading.material),r(!!h.colorGrading.colorGradingMap);const t=h.colorGrading.colorGradingMap;this._colorGradingTexSize.x=t.width,this._colorGradingTexSize.y=t.height;const c=t.width===t.height;l=c?e.addRenderPass(i,s,"cc-color-grading-8x8"):e.addRenderPass(i,s,"cc-color-grading-nx1"),l.addRenderTarget(d,E.CLEAR,C.STORE,v),l.addTexture(n,"sceneColorMap"),l.setVec4("g_platform",a.platform),l.setVec2("lutTextureSize",this._colorGradingTexSize),l.setFloat("contribute",h.colorGrading.contribute),l.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(h.colorGrading.material,c?1:0)}else l=e.addRenderPass(i,s,"cc-tone-mapping"),l.addRenderTarget(d,E.CLEAR,C.STORE,v),l.addTexture(n,"inputTexture"),l.setVec4("g_platform",a.platform),h.toneMapping.material?l.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(h.toneMapping.material,0):(r(!!t.copyAndTonemapMaterial),l.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(t.copyAndTonemapMaterial,0));return l}}e("BuiltinToneMappingPassBuilder",V);class k{constructor(){this._fxaaParams=new i(0,0,0,0)}getConfigOrder(){return 0}getRenderOrder(){return 400}configCamera(e,a,t){t.enableFXAA=t.settings.fxaa.enabled&&!!t.settings.fxaa.material,t.enableFXAA&&++t.remainingPasses}setup(e,a,t,i,s,n){if(!t.enableFXAA)return n;--t.remainingPasses,r(t.remainingPasses>=0);const o=t.renderWindowId,d=t.enableShadingScale?"ScaledLdrColor":"LdrColor",l=W(s.colorName,d,o);if(r(!!t.settings.fxaa.material),0===t.remainingPasses)return t.enableShadingScale?(this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,s.colorName,l),I(e,a,t,l)):(r(t.width===t.nativeWidth),r(t.height===t.nativeHeight),this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,s.colorName,t.colorName));{const i=s.colorName;s.colorName=l;return this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,i,l)}}_addFxaaPass(e,a,t,i,s,n,r){this._fxaaParams.x=i,this._fxaaParams.y=s,this._fxaaParams.z=1/i,this._fxaaParams.w=1/s;const d=e.addRenderPass(i,s,"cc-fxaa");return d.addRenderTarget(r,E.CLEAR,C.STORE,v),d.addTexture(n,"sceneColorMap"),d.setVec4("g_platform",a.platform),d.setVec4("texSize",this._fxaaParams),d.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(t,0),d}}e("BuiltinFXAAPassBuilder",k);class Y{constructor(){this._fsrParams=new i(0,0,0,0),this._fsrTexSize=new i(0,0,0,0)}getConfigOrder(){return 0}getRenderOrder(){return 500}configCamera(e,a,t){t.enableFSR=t.settings.fsr.enabled&&!!t.settings.fsr.material&&t.enableShadingScale&&t.shadingScale<1,t.enableFSR&&++t.remainingPasses}setup(e,a,t,i,s,n){if(!t.enableFSR)return n;--t.remainingPasses;const o=s.colorName,d=0===t.remainingPasses?t.colorName:W(s.colorName,"UiColor",t.renderWindowId);return s.colorName=d,r(!!t.settings.fsr.material),this._addFsrPass(e,a,t,t.settings,t.settings.fsr.material,t.renderWindowId,t.width,t.height,o,t.nativeWidth,t.nativeHeight,d)}_addFsrPass(e,a,t,i,s,n,r,d,h,c,g,p){this._fsrTexSize.x=r,this._fsrTexSize.y=d,this._fsrTexSize.z=c,this._fsrTexSize.w=g,this._fsrParams.x=l(1-i.fsr.sharpness,.02,.98);const u=W(p,"UiColor",n),m=e.addRenderPass(c,g,"cc-fsr-easu");m.addRenderTarget(u,E.CLEAR,C.STORE,v),m.addTexture(h,"outputResultMap"),m.setVec4("g_platform",a.platform),m.setVec4("fsrTexSize",this._fsrTexSize),m.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(s,0);const S=e.addRenderPass(c,g,"cc-fsr-rcas");return S.addRenderTarget(p,E.CLEAR,C.STORE,v),S.addTexture(u,"outputResultMap"),S.setVec4("g_platform",a.platform),S.setVec4("fsrTexSize",this._fsrTexSize),S.setVec4("fsrParams",this._fsrParams),S.addQueue(o.QueueHint.OPAQUE).addFullscreenQuad(s,1),S}}e("BuiltinFsrPassBuilder",Y);class X{getConfigOrder(){return 0}getRenderOrder(){return 1e3}setup(e,a,t,i,s,n){r(!!n);let d=o.SceneFlags.UI;return t.enableProfiler&&(d|=o.SceneFlags.PROFILER,n.showStatistics=!0),n.addQueue(o.QueueHint.BLEND,"default","default").addScene(i,d),n}}if(e("BuiltinUiPassBuilder",X),o){const{QueueHint:e,SceneFlags:t}=o;class i{constructor(){this._pipelineEvent=a.director.root.pipelineEvent,this._forwardPass=new $,this._bloomPass=new U,this._toneMappingPass=new V,this._fxaaPass=new k,this._fsrPass=new Y,this._uiPass=new X,this._clearColor=new R(0,0,0,1),this._viewport=new x,this._configs=new B,this._cameraConfigs=new z,this._copyAndTonemapMaterial=new h,this._initialized=!1,this._passBuilders=[]}_setupPipelinePreview(e,a){if(e.cameraUsage===D.SCENE_VIEW||e.cameraUsage===D.PREVIEW){const e=o.getEditorPipelineSettings();a.settings=e||y}else e.pipelineSettings?a.settings=e.pipelineSettings:a.settings=y}_preparePipelinePasses(e){const a=this._passBuilders;a.length=0;const t=e.settings;if(t._passes){for(const e of t._passes)a.push(e);r(a.length===t._passes.length)}a.push(this._forwardPass),t.bloom.enabled&&a.push(this._bloomPass),a.push(this._toneMappingPass),t.fxaa.enabled&&a.push(this._fxaaPass),t.fsr.enabled&&a.push(this._fsrPass),a.push(this._uiPass)}_setupBuiltinCameraConfigs(e,a,t){const i=e.window,n=e.cameraUsage===D.GAME&&!!i.swapchain;t.isMainGameWindow=n,t.renderWindowId=i.renderWindowId,t.colorName=i.colorName,t.depthStencilName=i.depthStencilName,t.enableFullPipeline=0!=(e.visibility&c.Enum.DEFAULT),t.enableProfiler=S,t.remainingPasses=0,t.shadingScale=t.settings.shadingScale,t.enableShadingScale=t.settings.enableShadingScale&&1!==t.shadingScale,t.nativeWidth=Math.max(Math.floor(i.width),1),t.nativeHeight=Math.max(Math.floor(i.height),1),t.width=t.enableShadingScale?Math.max(Math.floor(t.nativeWidth*t.shadingScale),1):t.nativeWidth,t.height=t.enableShadingScale?Math.max(Math.floor(t.nativeHeight*t.shadingScale),1):t.nativeHeight,t.enableHDR=t.enableFullPipeline&&a.useFloatOutput,t.radianceFormat=t.enableHDR?s.Format.RGBA16F:s.Format.RGBA8,t.copyAndTonemapMaterial=this._copyAndTonemapMaterial,t.enableStoreSceneDepth=!1}_setupCameraConfigs(e,a,t){this._setupPipelinePreview(e,t),this._preparePipelinePasses(t),this._passBuilders.sort(((e,a)=>e.getConfigOrder()-a.getConfigOrder())),this._setupBuiltinCameraConfigs(e,a,t);for(const i of this._passBuilders)i.configCamera&&i.configCamera(e,a,t)}windowResize(e,a,t,i,s){H(e,this._configs),this._setupCameraConfigs(t,this._configs,this._cameraConfigs);const n=a.renderWindowId;e.addRenderWindow(this._cameraConfigs.colorName,T.RGBA8,i,s,a,this._cameraConfigs.depthStencilName);const o=this._cameraConfigs.width,r=this._cameraConfigs.height;this._cameraConfigs.enableShadingScale?(e.addDepthStencil(`ScaledSceneDepth_${n}`,T.DEPTH_STENCIL,o,r),e.addRenderTarget(`ScaledRadiance0_${n}`,this._cameraConfigs.radianceFormat,o,r),e.addRenderTarget(`ScaledRadiance1_${n}`,this._cameraConfigs.radianceFormat,o,r),e.addRenderTarget(`ScaledLdrColor0_${n}`,T.RGBA8,o,r),e.addRenderTarget(`ScaledLdrColor1_${n}`,T.RGBA8,o,r)):(e.addDepthStencil(`SceneDepth_${n}`,T.DEPTH_STENCIL,o,r),e.addRenderTarget(`Radiance0_${n}`,this._cameraConfigs.radianceFormat,o,r),e.addRenderTarget(`Radiance1_${n}`,this._cameraConfigs.radianceFormat,o,r),e.addRenderTarget(`LdrColor0_${n}`,T.RGBA8,o,r),e.addRenderTarget(`LdrColor1_${n}`,T.RGBA8,o,r)),e.addRenderTarget(`UiColor0_${n}`,T.RGBA8,i,s),e.addRenderTarget(`UiColor1_${n}`,T.RGBA8,i,s);for(const n of this._passBuilders)n.windowResize&&n.windowResize(e,this._configs,this._cameraConfigs,a,t,i,s)}setup(e,a){if(!this._initMaterials(a))for(const t of e)t.scene&&t.window&&(this._setupCameraConfigs(t,this._configs,this._cameraConfigs),this._pipelineEvent.emit(g.RENDER_CAMERA_BEGIN,t),this._cameraConfigs.enableFullPipeline?this._buildForwardPipeline(a,t,t.scene,this._passBuilders):this._buildSimplePipeline(a,t),this._pipelineEvent.emit(g.RENDER_CAMERA_END,t))}_buildSimplePipeline(a,i){const s=Math.max(Math.floor(i.window.width),1),n=Math.max(Math.floor(i.window.height),1),o=this._cameraConfigs.colorName,r=this._cameraConfigs.depthStencilName,d=i.viewport;this._viewport.left=Math.round(d.x*s),this._viewport.top=Math.round(d.y*n),this._viewport.width=Math.max(Math.round(d.width*s),1),this._viewport.height=Math.max(Math.round(d.height*n),1);const l=i.clearColor;this._clearColor.x=l.x,this._clearColor.y=l.y,this._clearColor.z=l.z,this._clearColor.w=l.w;const h=a.addRenderPass(s,n,"default");O(i)?h.addRenderTarget(o,E.CLEAR,C.STORE,this._clearColor):h.addRenderTarget(o,E.LOAD,C.STORE),i.clearFlag&P.DEPTH_STENCIL?h.addDepthStencil(r,E.CLEAR,C.DISCARD,i.clearDepth,i.clearStencil,i.clearFlag&P.DEPTH_STENCIL):h.addDepthStencil(r,E.LOAD,C.DISCARD),h.setViewport(this._viewport),h.addQueue(e.OPAQUE).addScene(i,t.OPAQUE);let c=t.BLEND|t.UI;this._cameraConfigs.enableProfiler&&(c|=t.PROFILER,h.showStatistics=!0),h.addQueue(e.BLEND).addScene(i,c)}_buildForwardPipeline(e,a,t,i){!function(e){e.sort(((e,a)=>e.getRenderOrder()-a.getRenderOrder()))}(i);const s={colorName:"",depthStencilName:""};let n;for(const t of i)t.setup&&(n=t.setup(e,this._configs,this._cameraConfigs,a,s,n));r(0===this._cameraConfigs.remainingPasses)}_initMaterials(e){return this._initialized?0:(H(e,this._configs),this._copyAndTonemapMaterial._uuid="builtin-pipeline-tone-mapping-material",this._copyAndTonemapMaterial.initialize({effectName:"pipeline/post-process/tone-mapping"}),this._copyAndTonemapMaterial.effectAsset&&(this._initialized=!0),this._initialized?0:1)}}o.setCustomPipeline("Builtin",new i)}a._RF.pop()}}}));

System.register("chunks:///_virtual/internal",["./builtin-pipeline-settings.ts","./builtin-pipeline-types.ts","./builtin-pipeline.ts"],(function(){return{setters:[null,null,null],execute:function(){}}}));

(function(r) {
  r('virtual:///prerequisite-imports/internal', 'chunks:///_virtual/internal'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});