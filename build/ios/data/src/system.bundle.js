!function(){"use strict";function e(e,t){return(t||"")+" (SystemJS https://git.io/JvFET#"+e+")"}var t,n="undefined"!=typeof Symbol,r="undefined"!=typeof self,i="undefined"!=typeof document,o=r?self:global;if(i){var u=document.querySelector("base[href]");u&&(t=u.href)}if(!t&&"undefined"!=typeof location){var s=(t=location.href.split("#")[0].split("?")[0]).lastIndexOf("/");-1!==s&&(t=t.slice(0,s+1))}if(!t&&"undefined"!=typeof process){var l=process.cwd();t="file://"+("/"===l[0]?"":"/")+l.replace(/\\/g,"/")+"/"}var c=/\\/g;function f(e,t){if(-1!==e.indexOf("\\")&&(e=e.replace(c,"/")),"/"===e[0]&&"/"===e[1])return t.slice(0,t.indexOf(":")+1)+e;if("."===e[0]&&("/"===e[1]||"."===e[1]&&("/"===e[2]||2===e.length&&(e+="/"))||1===e.length&&(e+="/"))||"/"===e[0]){var n,r=t.slice(0,t.indexOf(":")+1);if(n="/"===t[r.length+1]?"file:"!==r?(n=t.slice(r.length+2)).slice(n.indexOf("/")+1):t.slice(8):t.slice(r.length+("/"===t[r.length])),"/"===e[0])return t.slice(0,t.length-n.length-1)+e;for(var i=n.slice(0,n.lastIndexOf("/")+1)+e,o=[],u=-1,s=0;s<i.length;s++)-1!==u?"/"===i[s]&&(o.push(i.slice(u,s+1)),u=-1):"."===i[s]?"."!==i[s+1]||"/"!==i[s+2]&&s+2!==i.length?"/"===i[s+1]||s+1===i.length?s+=1:u=s:(o.pop(),s+=2):u=s;return-1!==u&&o.push(i.slice(u)),t.slice(0,t.length-n.length)+o.join("")}}function a(e,t){return f(e,t)||(-1!==e.indexOf(":")?e:f("./"+e,t))}function h(e,t,n,r,i){for(var o in e){var u=f(o,n)||o,s=e[o];if("string"==typeof s){var l=m(r,f(s,n)||s,i);l?t[u]=l:g("W1",o,s)}}}function v(e,t,n){var r;for(r in e.imports&&h(e.imports,n.imports,t,n,null),e.scopes||{}){var i=a(r,t);h(e.scopes[r],n.scopes[i]||(n.scopes[i]={}),t,n,i)}for(r in e.depcache||{})n.depcache[a(r,t)]=e.depcache[r];for(r in e.integrity||{})n.integrity[a(r,t)]=e.integrity[r]}function p(e,t){if(t[e])return e;var n=e.length;do{var r=e.slice(0,n+1);if(r in t)return r}while(-1!==(n=e.lastIndexOf("/",n-1)))}function d(e,t){var n=p(e,t);if(n){var r=t[n];if(null===r)return;if(!(e.length>n.length&&"/"!==r[r.length-1]))return r+e.slice(n.length);g("W2",n,r)}}function g(t,n,r,i){console.warn(e(t,[r,n].join(", ")))}function m(e,t,n){for(var r=e.scopes,i=n&&p(n,r);i;){var o=d(t,r[i]);if(o)return o;i=p(i.slice(0,i.lastIndexOf("/")),r)}return d(t,e.imports)||-1!==t.indexOf(":")&&t}var y=n&&Symbol.toStringTag,b=n?Symbol():"@";function O(){this[b]={}}var w,E=O.prototype;function P(t,n,r){var i=t[b][n];if(i)return i;var o=[],u=Object.create(null);y&&Object.defineProperty(u,y,{value:"Module"});var s=Promise.resolve().then((function(){return t.instantiate(n,r)})).then((function(r){if(!r)throw Error(e(2,n));var s=r[1]((function(e,t){i.h=!0;var n=!1;if("string"==typeof e)e in u&&u[e]===t||(u[e]=t,n=!0);else{for(var r in e){t=e[r];r in u&&u[r]===t||(u[r]=t,n=!0)}e.__esModule&&(u.__esModule=e.__esModule)}if(n)for(var s=0;s<o.length;s++){var l=o[s];l&&l(u)}return t}),2===r[1].length?{import:function(e){return t.import(e,n)},meta:t.createContext(n)}:void 0);return i.e=s.execute||function(){},[r[0],s.setters||[]]}),(function(e){throw i.e=null,i.er=e,e})),l=s.then((function(e){return Promise.all(e[0].map((function(r,i){var o=e[1][i];return Promise.resolve(t.resolve(r,n)).then((function(e){var r=P(t,e,n);return Promise.resolve(r.I).then((function(){return o&&(r.i.push(o),!r.h&&r.I||o(r.n)),r}))}))}))).then((function(e){i.d=e}))}));return l.catch((function(){})),i=t[b][n]={id:n,i:o,n:u,I:s,L:l,h:!1,d:void 0,e:void 0,er:void 0,E:void 0,C:void 0,p:void 0}}function S(e,t,n,r){if(!r[t.id])return r[t.id]=!0,Promise.resolve(t.L).then((function(){return t.p&&null!==t.p.e||(t.p=n),Promise.all(t.d.map((function(t){return S(e,t,n,r)})))})).catch((function(e){if(t.er)throw e;throw t.e=null,e}))}E.import=function(e,t){var n=this;return Promise.resolve(n.prepareImport()).then((function(){return n.resolve(e,t)})).then((function(e){var t=P(n,e);return t.C||function(e,t){return t.C=S(e,t,t,{}).then((function(){return R(e,t,{})})).then((function(){return t.n}))}(n,t)}))},E.createContext=function(e){var t=this;return{url:e,resolve:function(n,r){return Promise.resolve(t.resolve(n,r||e))}}},E.register=function(e,t){w=[e,t]},E.getRegister=function(){var e=w;return w=void 0,e};var x=Object.freeze(Object.create(null)),j=Promise.prototype.finally||function(e){if("function"!=typeof e)return this.then(e,e);const t=this.constructor||Promise;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))};function R(e,t,n){if(n[t.id])return t.E;if(n[t.id]=!0,!t.e){if(t.er)throw t.er;return t.E?t.E:void 0}const r=t.e;var i;if(t.e=null,t.d.forEach((function(r){try{var o=R(e,r,n);o&&(i=i||[]).push(o)}catch(e){throw t.er=e,e}})),i)return t.E=j.call(Promise.all(i).then(u),(function(){t.E=null}));var o=u();if(o)return t.E=j.call(o,(function(){t.E=null}));function u(){try{var e=r.call(x);if(e)return e=e.then((function(){t.C=t.n}),(function(e){throw t.er=e,e}));t.C=t.n,t.L=t.I=void 0}catch(e){throw t.er=e,e}}}o.System=new O;const I="undefined"!=typeof $global?$global:"function"==typeof getApp?getApp().GameGlobal:void 0,M=(void 0!==I?I.System:System).constructor.prototype;M.instantiate=function(e,t){throw new Error(`Unable to instantiate ${e} from ${t}`)};var $="undefined"!=typeof Symbol&&Symbol.toStringTag;E.get=function(e){var t=this[b][e];if(t&&null===t.e&&!t.E)return t.er?null:t.n},E.set=function(e,t){var n;$&&"Module"===t[$]?n=t:(n=Object.assign(Object.create(null),t),$&&Object.defineProperty(n,$,{value:"Module"}));var r=Promise.resolve(n),i=this[b][e]||(this[b][e]={id:e,i:[],h:!1,d:[],e:null,er:void 0,E:void 0});return!i.e&&!i.E&&(Object.assign(i,{n:n,I:void 0,L:void 0,C:r}),n)},E.has=function(e){return!!this[b][e]},E.delete=function(e){var t=this[b],n=t[e];if(!n||n.p&&null!==n.p.e||n.E)return!1;var r=n.i;return n.d&&n.d.forEach((function(e){var t=e.i.indexOf(n);-1!==t&&e.i.splice(t,1)})),delete t[e],function(){var n=t[e];if(!n||!r||null!==n.e||n.E)return!1;r.forEach((function(e){n.i.push(e),e(n.n)})),r=null}};var C="undefined"!=typeof Symbol&&Symbol.iterator;E.entries=function(){var e,t,n=this,r=Object.keys(n[b]),i=0,o={next:function(){for(;void 0!==(t=r[i++])&&void 0===(e=n.get(t)););return{done:void 0===t,value:void 0!==t&&[t,e]}}};return o[C]=function(){return this},o};let _=t;const L={imports:{},scopes:{}};function A(e,t){v(e,t||_,L)}function U(e,t){v(e,f(t,_)||t,L)}function T(e,t){let n,r=t;r.startsWith("/")&&(r=r.slice(1));const i=function(e){const t=L.imports;for(const n in t){const r=t[n];if(r&&(e===r||`no-schema:/${e}`===r))return n}return null}(r);return i&&(r=i),e.registerRegistry&&(n=e.registerRegistry[r])&&(e.registerRegistry[r]=null),n}function W(e){return function(t){const n=this,r=T(n,t);if(r)return r;let i;try{i=e(t)}catch(e){return Promise.reject(e)}return o=i,Boolean(o&&"function"==typeof o.then)?new Promise((e=>i.then((()=>{e(n.getRegister())})))):n.getRegister();var o}}function k(e,t){const n=M.instantiate;M.instantiate=function(r,i){const o=r.substr(0,e.length)===e?r.substr(e.length):null;return null===o?n.call(this,r,i):t.call(this,o,i)}}M.resolve=function(e,t){return m(L,f(e,t=t||_)||e,t)||function(e,t){throw new Error(`Unresolved id: ${e} from parentUrl: ${t}`)}(e,t)},M.prepareImport=function(){return Promise.resolve()},M.warmup=function({pathname:e="/",importMap:t,importMapUrl:n,importMapList:r,defaultHandler:i,handlers:o}){const u="no-schema:";if(_=`no-schema:${e}`,n&&t&&A(t,`no-schema:/${n}`),Array.isArray(r))for(const e of r)U(e.map,e.location);if(i&&k(u,W(i)),o)for(const e of Object.keys(o))k(e,W(o[e]))},function(e){var t=e.System;u(t);var n,r=t.constructor.prototype,i=t.constructor,o=function(){i.call(this),u(this)};function u(e){e.registerRegistry=Object.create(null)}o.prototype=r,t.constructor=o;var s=r.register;r.register=function(e,t,r){if("string"!=typeof e)return s.apply(this,arguments);var i=[t,r];return this.registerRegistry[e]=i,n||(n=i,Promise.resolve().then((function(){n=null}))),s.apply(this,arguments)};var l=r.resolve;r.resolve=function(e,t){try{return l.call(this,e,t)}catch(t){if(e in this.registerRegistry)return e;throw t}};var c=r.instantiate;r.instantiate=function(e,t){var n=this.registerRegistry[e];return n?(this.registerRegistry[e]=null,n):c.call(this,e,t)};var f=r.getRegister;r.getRegister=function(){var e=f.call(this),t=n||e;return n=null,t}}("undefined"!=typeof self?self:global)}();
