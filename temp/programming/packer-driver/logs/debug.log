12:48:46.442 debug: 2025/8/24 12:48:46
12:48:46.442 debug: Project: /Users/<USER>/projects/cocos_project/SuperSplash
12:48:46.442 debug: Targets: editor,preview
12:48:46.443 debug: Incremental file seems great.
12:48:46.443 debug: Engine path: /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine
12:48:46.447 debug: Initializing target [Editor]
12:48:46.447 debug: Loading cache
12:48:46.449 debug: Loading cache costs 1.4263339999997697ms.
12:48:46.449 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
12:48:46.449 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
12:48:46.449 debug: Initializing target [Preview]
12:48:46.449 debug: Loading cache
12:48:46.450 debug: Loading cache costs 1.0345000000002074ms.
12:48:46.450 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
12:48:46.467 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,mask,particle-2d,physics-2d-box2d,profiler,rich-text,spine-3.8,tiled-map,tween,ui,video,webview,custom-pipeline
12:48:46.468 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets"
  },
  {
    "root": "db://assets/",
    "physical": "/Users/<USER>/projects/cocos_project/SuperSplash/assets"
  }
]
12:48:46.468 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/"
  }
}
12:48:46.469 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/"
  }
}
12:48:46.469 debug: Pulling asset-db.
12:48:46.472 debug: Fetch asset-db cost: 2.9843330000003334ms.
12:48:46.472 debug: Build iteration starts.
Number of accumulated asset changes: 28
Feature changed: false
12:48:46.472 debug: Target(editor) build started.
12:48:46.473 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
12:48:46.473 debug: Inspect cce:/internal/x/cc
12:48:46.490 debug: transform url: 'cce:/internal/x/cc' costs: 17.20 ms
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
12:48:46.491 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
12:48:46.491 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
12:48:46.491 debug: Inspect cce:/internal/x/prerequisite-imports
12:48:46.497 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 5.70 ms
12:48:46.498 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
12:48:46.498 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
12:48:46.498 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.498 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.498 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
12:48:46.498 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
12:48:46.498 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
12:48:46.499 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.499 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.499 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Sun Aug 24 2025 12:30:13 GMT+0800 (中国标准时间), Current mtime: Sun Aug 24 2025 12:48:43 GMT+0800 (中国标准时间)
12:48:46.499 debug: Inspect cce:/internal/code-quality/cr.mjs
12:48:46.502 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 2.80 ms
12:48:46.502 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
12:48:46.502 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
12:48:46.502 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
12:48:46.502 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
12:48:46.502 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.503 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
12:48:46.503 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
12:48:46.503 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
12:48:46.503 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.503 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
12:48:46.503 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
12:48:46.503 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
12:48:46.503 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.503 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
12:48:46.504 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
12:48:46.504 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
12:48:46.504 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.504 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.504 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
12:48:46.504 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
12:48:46.504 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
12:48:46.504 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.504 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
12:48:46.504 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.504 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.504 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.504 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.504 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.505 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.505 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
12:48:46.505 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
12:48:46.505 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
12:48:46.505 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.505 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.505 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.505 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.505 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.505 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.505 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.505 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.505 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.505 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.505 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.505 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
12:48:46.505 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.506 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.506 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
12:48:46.506 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.506 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.506 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.506 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
12:48:46.506 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
12:48:46.511 debug: Target(editor) ends with cost 38.58558300000004ms.
12:48:46.511 debug: Target(preview) build started.
12:48:46.511 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
12:48:46.511 debug: Inspect cce:/internal/x/cc
12:48:46.519 debug: transform url: 'cce:/internal/x/cc' costs: 7.80 ms
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
12:48:46.520 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
12:48:46.520 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
12:48:46.520 debug: Inspect cce:/internal/x/prerequisite-imports
12:48:46.524 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.80 ms
12:48:46.524 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
12:48:46.524 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
12:48:46.524 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.524 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.524 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
12:48:46.524 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
12:48:46.524 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.524 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.524 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.524 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
12:48:46.524 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
12:48:46.525 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.525 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.525 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Sun Aug 24 2025 12:30:13 GMT+0800 (中国标准时间), Current mtime: Sun Aug 24 2025 12:48:43 GMT+0800 (中国标准时间)
12:48:46.525 debug: Inspect cce:/internal/code-quality/cr.mjs
12:48:46.529 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 3.90 ms
12:48:46.529 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
12:48:46.529 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
12:48:46.529 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
12:48:46.529 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
12:48:46.529 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.530 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
12:48:46.530 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
12:48:46.530 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
12:48:46.530 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.530 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
12:48:46.530 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
12:48:46.530 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.530 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.530 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.530 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
12:48:46.530 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.530 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
12:48:46.530 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.530 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.530 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.530 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.530 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.530 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.530 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
12:48:46.530 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
12:48:46.531 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
12:48:46.531 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.531 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.531 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.531 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.531 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.531 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.531 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.531 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.531 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.531 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.531 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.531 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.531 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
12:48:46.531 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.532 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.532 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.532 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
12:48:46.532 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.532 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.532 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.532 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
12:48:46.532 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
12:48:46.536 debug: Target(preview) ends with cost 24.765082999999777ms.
12:48:46.765 debug: Pulling asset-db.
12:48:46.975 debug: Fetch asset-db cost: 209.73779100000002ms.
12:48:46.976 debug: Build iteration starts.
Number of accumulated asset changes: 28
Feature changed: false
12:48:46.976 debug: Target(editor) build started.
12:48:46.978 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
12:48:46.978 debug: Inspect cce:/internal/x/prerequisite-imports
12:48:46.981 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.70 ms
12:48:46.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
12:48:46.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
12:48:46.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
12:48:46.982 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
12:48:46.982 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.983 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.983 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
12:48:46.983 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.986 debug: Target(editor) ends with cost 9.713332999999693ms.
12:48:46.986 debug: Target(preview) build started.
12:48:46.987 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
12:48:46.987 debug: Inspect cce:/internal/x/prerequisite-imports
12:48:46.990 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.30 ms
12:48:46.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
12:48:46.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
12:48:46.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
12:48:46.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
12:48:46.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
12:48:46.991 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
12:48:46.991 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
12:48:46.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
12:48:46.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
12:48:46.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
12:48:46.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
12:48:46.992 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
12:48:46.995 debug: Target(preview) ends with cost 8.858124999999745ms.
