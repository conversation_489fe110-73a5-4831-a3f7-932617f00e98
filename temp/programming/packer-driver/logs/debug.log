00:26:09.585 debug: 2025/8/21 00:26:09
00:26:09.585 debug: Project: /Users/<USER>/projects/cocos_project/SuperSplash
00:26:09.585 debug: Targets: editor,preview
00:26:09.586 debug: Incremental file seems great.
00:26:09.587 debug: Engine path: /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine
00:26:09.591 debug: Initializing target [Editor]
00:26:09.591 debug: Loading cache
00:26:09.592 debug: Loading cache costs 1.2097920000001068ms.
00:26:09.592 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
00:26:09.592 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
00:26:09.592 debug: Initializing target [Preview]
00:26:09.592 debug: Loading cache
00:26:09.593 debug: Loading cache costs 0.8733330000000024ms.
00:26:09.593 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
00:26:09.613 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,mask,particle-2d,physics-2d-box2d,profiler,rich-text,spine-3.8,tiled-map,tween,ui,video,webview,custom-pipeline
00:26:09.615 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets"
  },
  {
    "root": "db://assets/",
    "physical": "/Users/<USER>/projects/cocos_project/SuperSplash/assets"
  }
]
00:26:09.615 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/"
  }
}
00:26:09.615 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/"
  }
}
00:26:09.615 debug: Pulling asset-db.
00:26:09.618 debug: Fetch asset-db cost: 3.0253749999997126ms.
00:26:09.618 debug: Build iteration starts.
Number of accumulated asset changes: 28
Feature changed: false
00:26:09.619 debug: Target(editor) build started.
00:26:09.620 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
00:26:09.620 debug: Inspect cce:/internal/x/cc
00:26:09.636 debug: transform url: 'cce:/internal/x/cc' costs: 16.70 ms
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
00:26:09.637 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
00:26:09.638 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
00:26:09.638 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:06 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
00:26:09.638 debug: Inspect cce:/internal/x/prerequisite-imports
00:26:09.643 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 5.70 ms
00:26:09.644 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
00:26:09.644 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
00:26:09.644 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:09.644 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:09.644 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
00:26:09.644 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
00:26:09.644 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
00:26:09.645 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.645 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.645 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Thu Aug 21 2025 00:11:27 GMT+0800 (中国标准时间), Current mtime: Thu Aug 21 2025 00:26:06 GMT+0800 (中国标准时间)
00:26:09.645 debug: Inspect cce:/internal/code-quality/cr.mjs
00:26:09.647 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 2.60 ms
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
00:26:09.648 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:09.648 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
00:26:09.648 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
00:26:09.648 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
00:26:09.648 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:09.648 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
00:26:09.648 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
00:26:09.648 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
00:26:09.649 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:09.649 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.649 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:09.649 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
00:26:09.649 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.649 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:09.649 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.649 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.649 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.649 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.649 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
00:26:09.649 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
00:26:09.649 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
00:26:09.649 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
00:26:09.649 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.649 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.649 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.649 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.649 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.649 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
00:26:09.650 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
00:26:09.650 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
00:26:09.650 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.650 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.650 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:09.650 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.650 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.650 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.651 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.651 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.651 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.651 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.651 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
00:26:09.651 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
00:26:09.652 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:09.652 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.652 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.652 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
00:26:09.652 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
00:26:09.655 debug: Target(editor) ends with cost 36.80774999999994ms.
00:26:09.655 debug: Target(preview) build started.
00:26:09.656 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
00:26:09.656 debug: Inspect cce:/internal/x/cc
00:26:09.664 debug: transform url: 'cce:/internal/x/cc' costs: 7.60 ms
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
00:26:09.664 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
00:26:09.664 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:06 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间)
00:26:09.664 debug: Inspect cce:/internal/x/prerequisite-imports
00:26:09.668 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.60 ms
00:26:09.668 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
00:26:09.668 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
00:26:09.668 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:09.668 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:09.668 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
00:26:09.668 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
00:26:09.668 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
00:26:09.669 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.669 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.669 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Thu Aug 21 2025 00:11:27 GMT+0800 (中国标准时间), Current mtime: Thu Aug 21 2025 00:26:06 GMT+0800 (中国标准时间)
00:26:09.669 debug: Inspect cce:/internal/code-quality/cr.mjs
00:26:09.672 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 3.50 ms
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
00:26:09.673 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:09.673 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
00:26:09.673 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
00:26:09.673 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
00:26:09.673 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:09.673 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
00:26:09.673 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
00:26:09.673 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.673 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.673 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.673 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
00:26:09.673 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:09.673 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
00:26:09.673 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.673 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:09.674 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.674 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.674 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.674 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.674 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
00:26:09.674 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
00:26:09.674 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
00:26:09.674 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.674 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.674 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.674 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.674 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.674 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.674 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:09.674 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:09.674 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.674 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:09.674 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:09.674 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.674 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
00:26:09.674 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.674 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.675 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.675 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:09.675 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:09.675 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
00:26:09.675 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:09.675 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:09.675 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:09.675 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
00:26:09.675 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
00:26:09.679 debug: Target(preview) ends with cost 23.207084000000123ms.
00:26:09.938 debug: Pulling asset-db.
00:26:10.165 debug: Fetch asset-db cost: 226.67404199999964ms.
00:26:10.165 debug: Build iteration starts.
Number of accumulated asset changes: 28
Feature changed: false
00:26:10.165 debug: Target(editor) build started.
00:26:10.166 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
00:26:10.166 debug: Inspect cce:/internal/x/prerequisite-imports
00:26:10.170 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 4.40 ms
00:26:10.171 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
00:26:10.171 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
00:26:10.171 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:10.171 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:10.171 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
00:26:10.171 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
00:26:10.171 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
00:26:10.172 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:10.176 debug: Target(editor) ends with cost 10.330375000000004ms.
00:26:10.176 debug: Target(preview) build started.
00:26:10.176 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
00:26:10.176 debug: Inspect cce:/internal/x/prerequisite-imports
00:26:10.180 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.30 ms
00:26:10.180 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
00:26:10.180 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
00:26:10.180 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
00:26:10.180 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
00:26:10.180 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
00:26:10.181 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
00:26:10.181 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
00:26:10.185 debug: Target(preview) ends with cost 9.179291999999805ms.
07:41:24.921 debug: Dispatch build request for time accumulated 6 asset changes.
07:41:24.922 debug: Build iteration starts.
Number of accumulated asset changes: 6
Feature changed: false
07:41:24.923 debug: Target(editor) build started.
07:41:24.924 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 15:15:15 GMT+0800 (中国标准时间)
07:41:24.925 debug: Inspect cce:/internal/x/prerequisite-imports
07:41:24.938 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 13.50 ms
07:41:24.940 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:24.940 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts
07:41:24.950 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts' costs: 10.00 ms
07:41:24.950 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts.
07:41:24.950 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts
07:41:24.963 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts' costs: 13.00 ms
07:41:24.964 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts.
07:41:24.964 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts
07:41:24.969 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts' costs: 5.40 ms
07:41:24.970 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts.
07:41:24.970 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts
07:41:24.975 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts' costs: 5.40 ms
07:41:24.975 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts.
07:41:24.975 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts
07:41:24.980 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts' costs: 4.10 ms
07:41:24.980 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts.
07:41:24.980 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts
07:41:24.984 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts' costs: 4.30 ms
07:41:24.985 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
07:41:24.985 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:24.985 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:24.985 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:24.985 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:24.985 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:24.985 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:24.986 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
07:41:24.986 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:24.986 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:24.986 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:24.986 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts as cce:/internal/x/cc.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:24.986 debug: Failed to resolve './ad_event' from 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts'(module type: esm). Reason: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts as cce:/internal/x/cc.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts as cce:/internal/x/cc.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts as cce:/internal/x/cc.
07:41:24.986 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts as cce:/internal/x/cc.
07:41:24.991 debug: Target(editor) ends with cost 68.49316700175405ms.
07:41:24.991 debug: Target(preview) build started.
07:41:24.992 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 15:15:16 GMT+0800 (中国标准时间)
07:41:24.992 debug: Inspect cce:/internal/x/prerequisite-imports
07:41:24.997 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 5.10 ms
07:41:24.998 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:24.998 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts
07:41:25.005 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts' costs: 6.90 ms
07:41:25.005 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts.
07:41:25.005 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts
07:41:25.016 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts' costs: 11.10 ms
07:41:25.017 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts.
07:41:25.017 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts
07:41:25.022 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts' costs: 5.70 ms
07:41:25.023 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts.
07:41:25.023 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts
07:41:25.029 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts' costs: 5.70 ms
07:41:25.029 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts.
07:41:25.029 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts
07:41:25.034 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts' costs: 4.80 ms
07:41:25.034 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts.
07:41:25.034 debug: Inspect file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts
07:41:25.040 debug: transform url: 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts' costs: 5.40 ms
07:41:25.040 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
07:41:25.040 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:25.040 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.040 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.040 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:25.040 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
07:41:25.040 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.040 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:25.041 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts as cce:/internal/x/cc.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.042 debug: Failed to resolve './ad_event' from 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts'(module type: esm). Reason: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts as cce:/internal/x/cc.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts as cce:/internal/x/cc.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts as cce:/internal/x/cc.
07:41:25.042 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts as cce:/internal/x/cc.
07:41:25.046 debug: Target(preview) ends with cost 54.70754200220108ms.
07:41:25.157 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets"
  },
  {
    "root": "db://assets/",
    "physical": "/Users/<USER>/projects/cocos_project/SuperSplash/assets"
  },
  {
    "root": "db://adsense-h5g-plugin/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api",
    "jail": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api"
  }
]
07:41:25.158 debug: Build iteration starts.
Number of accumulated asset changes: 6
Feature changed: false
07:41:25.158 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/",
    "db://adsense-h5g-plugin/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/"
  }
}
07:41:25.158 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/",
    "db://adsense-h5g-plugin/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/"
  }
}
07:41:25.158 debug: Target(editor) build started.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
07:41:25.159 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
07:41:25.159 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 15:15:15 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 15:15:16 GMT+0800 (中国标准时间)
07:41:25.159 debug: Inspect cce:/internal/x/prerequisite-imports
07:41:25.163 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 4.00 ms
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:25.164 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
07:41:25.164 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:25.165 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve ./ad_event from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
07:41:25.165 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.165 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:25.165 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:25.165 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
07:41:25.165 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:25.165 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.166 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
07:41:25.166 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.166 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
07:41:25.166 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.166 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.166 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.166 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:25.166 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.166 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.166 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.166 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.166 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.166 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.166 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:25.166 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:25.166 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:25.166 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.166 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.166 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.166 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.166 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:25.166 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.166 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.167 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.167 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.167 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.167 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.167 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.167 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.167 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.167 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:25.167 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:25.168 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:25.168 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.168 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.168 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.168 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:25.168 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:25.172 debug: Target(editor) ends with cost 13.566208001226187ms.
07:41:25.172 debug: Target(preview) build started.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
07:41:25.173 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
07:41:25.173 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 15:15:16 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 15:15:16 GMT+0800 (中国标准时间)
07:41:25.173 debug: Inspect cce:/internal/x/prerequisite-imports
07:41:25.176 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.40 ms
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:25.177 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:25.177 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:25.178 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve ./ad_event from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
07:41:25.178 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.178 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:25.178 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:25.178 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
07:41:25.178 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:25.178 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:25.179 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
07:41:25.179 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.179 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
07:41:25.179 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:25.179 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.179 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:25.179 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.179 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.180 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:25.180 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.180 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.180 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.180 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.180 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.180 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.180 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:25.180 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:25.180 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:25.180 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.180 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.180 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.180 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.180 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:25.180 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.180 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.181 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:25.181 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:25.181 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.181 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.181 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:25.181 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:25.181 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:25.181 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:25.181 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:25.181 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.182 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.182 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:25.182 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.183 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:25.183 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:25.183 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:25.183 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:25.183 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:25.183 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:25.183 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:25.183 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:25.219 debug: Target(preview) ends with cost 47.2657909989357ms.
07:41:26.545 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets"
  },
  {
    "root": "db://assets/",
    "physical": "/Users/<USER>/projects/cocos_project/SuperSplash/assets"
  }
]
07:41:26.545 debug: Build iteration starts.
Number of accumulated asset changes: 0
Feature changed: false
07:41:26.545 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/"
  }
}
07:41:26.546 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/"
  }
}
07:41:26.546 debug: Target(editor) build started.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
07:41:26.546 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
07:41:26.547 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:26.547 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
07:41:26.547 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:26.548 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:26.548 debug: Failed to resolve './ad_event' from 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts'(module type: esm). Reason: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
07:41:26.548 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:26.548 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:26.548 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:26.548 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
07:41:26.548 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:26.548 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
07:41:26.548 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
07:41:26.548 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.548 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:26.548 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.549 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:26.549 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:26.549 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.549 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:26.549 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.549 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.549 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.549 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.549 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:26.549 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:26.549 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:26.549 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.549 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.549 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.549 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.549 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.549 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.549 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:26.549 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.549 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.549 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:26.549 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.549 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.549 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.549 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.550 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.550 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:26.550 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:26.550 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.550 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.550 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:26.550 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:26.554 debug: Target(editor) ends with cost 8.508166000247002ms.
07:41:26.554 debug: Target(preview) build started.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
07:41:26.555 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts.
07:41:26.555 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
07:41:26.556 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:26.556 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:26.556 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:26.556 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:26.556 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:26.556 debug: Resolve file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts as cce:/internal/x/cc.
07:41:26.556 debug: Failed to resolve './ad_event' from 'file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts'(module type: esm). Reason: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
07:41:26.556 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
07:41:26.557 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:26.557 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
07:41:26.557 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
07:41:26.557 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
07:41:26.557 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
07:41:26.557 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
07:41:26.557 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
07:41:26.557 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.557 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:26.557 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts.
07:41:26.557 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.557 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:26.557 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.557 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.557 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.557 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.557 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts.
07:41:26.557 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts.
07:41:26.557 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts.
07:41:26.557 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:26.557 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.558 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.558 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.558 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.558 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.558 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts.
07:41:26.558 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts.
07:41:26.558 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts.
07:41:26.558 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts.
07:41:26.558 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.558 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
07:41:26.558 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.558 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts.
07:41:26.559 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts.
07:41:26.559 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
07:41:26.559 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts.
07:41:26.559 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts.
07:41:26.559 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts.
07:41:26.559 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts.
07:41:26.559 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts.
07:41:26.563 debug: Target(preview) ends with cost 9.05170900002122ms.
