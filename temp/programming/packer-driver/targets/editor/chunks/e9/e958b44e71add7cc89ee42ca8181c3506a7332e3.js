System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts at runtime.
      throw new Error(`Error: 在加载模块文件 /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts 时发生错误：Error: ENOENT: no such file or directory, open '/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts'`);
    }
  };
});
//# sourceMappingURL=e958b44e71add7cc89ee42ca8181c3506a7332e3.js.map