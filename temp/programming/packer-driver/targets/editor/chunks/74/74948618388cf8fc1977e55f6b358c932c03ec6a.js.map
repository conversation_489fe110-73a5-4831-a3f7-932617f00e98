{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts"], "names": ["_decorator", "Component", "director", "UIOpacity", "tween", "Widget", "ccclass", "SceneTransition", "uiOpacity", "isTransitioning", "getInstance", "instance", "loadScene", "scene<PERSON><PERSON>", "fadeOutDuration", "fadeInDuration", "loadSceneWithTransition", "console", "warn", "onLoad", "node", "destroy", "addPersistRootNode", "getComponent", "addComponent", "setupFullScreenOverlay", "fadeIn", "log", "widget", "isAlignTop", "isAlignBottom", "isAlignLeft", "isAlignRight", "top", "bottom", "left", "right", "alignMode", "AlignMode", "ON_WINDOW_RESIZE", "updateAlignment", "setSiblingIndex", "duration", "opacity", "to", "call", "start", "fadeOut", "onComplete", "onDestroy"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;;;;;;;;OACtD;AAAEC,QAAAA;AAAF,O,GAAcN,U;AAEpB;AACA;AACA;AACA;;iCAEaO,e,WADZD,OAAO,CAAC,iBAAD,C,2BAAR,MACaC,eADb,SACqCN,SADrC,CAC+C;AAAA;AAAA;AAAA,eAEnCO,SAFmC,GAEL,IAFK;AAAA,eAGnCC,eAHmC,GAGR,KAHQ;AAAA;;AAK3C;AACJ;AACA;AAC6B,eAAXC,WAAW,GAA2B;AAChD,iBAAOH,eAAe,CAACI,QAAvB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AAC2B,eAATC,SAAS,CAACC,SAAD,EAAoBC,eAAuB,GAAG,GAA9C,EAAmDC,cAAsB,GAAG,GAA5E,EAAuF;AAC1G,gBAAMJ,QAAQ,GAAGJ,eAAe,CAACG,WAAhB,EAAjB;;AACA,cAAIC,QAAQ,IAAI,CAACA,QAAQ,CAACF,eAA1B,EAA2C;AACvCE,YAAAA,QAAQ,CAACK,uBAAT,CAAiCH,SAAjC,EAA4CC,eAA5C,EAA6DC,cAA7D;AACH,WAFD,MAEO;AACH;AACAE,YAAAA,OAAO,CAACC,IAAR,CAAa,uDAAb;AACAhB,YAAAA,QAAQ,CAACU,SAAT,CAAmBC,SAAnB;AACH;AACJ;;AAEDM,QAAAA,MAAM,GAAG;AACL;AACA,cAAIZ,eAAe,CAACI,QAApB,EAA8B;AAC1B,iBAAKS,IAAL,CAAUC,OAAV;AACA;AACH;;AAEDd,UAAAA,eAAe,CAACI,QAAhB,GAA2B,IAA3B,CAPK,CASL;;AACAT,UAAAA,QAAQ,CAACoB,kBAAT,CAA4B,KAAKF,IAAjC,EAVK,CAYL;;AACA,eAAKZ,SAAL,GAAiB,KAAKe,YAAL,CAAkBpB,SAAlB,CAAjB;;AACA,cAAI,CAAC,KAAKK,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKY,IAAL,CAAUI,YAAV,CAAuBrB,SAAvB,CAAjB;AACH,WAhBI,CAkBL;;;AACA,eAAKsB,sBAAL,GAnBK,CAqBL;;AACA,eAAKC,MAAL;AAEAT,UAAAA,OAAO,CAACU,GAAR,CAAY,6BAAZ;AACH;AAID;AACJ;AACA;;;AACYF,QAAAA,sBAAsB,GAAS;AACnC;AACA,gBAAMG,MAAM,GAAG,KAAKR,IAAL,CAAUG,YAAV,CAAuBlB,MAAvB,KAAkC,KAAKe,IAAL,CAAUI,YAAV,CAAuBnB,MAAvB,CAAjD;AACAuB,UAAAA,MAAM,CAACC,UAAP,GAAoB,IAApB;AACAD,UAAAA,MAAM,CAACE,aAAP,GAAuB,IAAvB;AACAF,UAAAA,MAAM,CAACG,WAAP,GAAqB,IAArB;AACAH,UAAAA,MAAM,CAACI,YAAP,GAAsB,IAAtB;AACAJ,UAAAA,MAAM,CAACK,GAAP,GAAa,CAAb;AACAL,UAAAA,MAAM,CAACM,MAAP,GAAgB,CAAhB;AACAN,UAAAA,MAAM,CAACO,IAAP,GAAc,CAAd;AACAP,UAAAA,MAAM,CAACQ,KAAP,GAAe,CAAf;AACAR,UAAAA,MAAM,CAACS,SAAP,GAAmBhC,MAAM,CAACiC,SAAP,CAAiBC,gBAApC;AACAX,UAAAA,MAAM,CAACY,eAAP,GAZmC,CAcnC;;AACA,eAAKpB,IAAL,CAAUqB,eAAV,CAA0B,MAA1B;AACH;AAED;AACJ;AACA;;;AACWf,QAAAA,MAAM,CAACgB,QAAgB,GAAG,GAApB,EAA+B;AACxC,cAAI,CAAC,KAAKlC,SAAN,IAAmB,KAAKC,eAA5B,EAA6C;AAE7C,eAAKA,eAAL,GAAuB,IAAvB;AACA,eAAKD,SAAL,CAAemC,OAAf,GAAyB,GAAzB;AAEAvC,UAAAA,KAAK,CAAC,KAAKI,SAAN,CAAL,CACKoC,EADL,CACQF,QADR,EACkB;AAAEC,YAAAA,OAAO,EAAE;AAAX,WADlB,EAEKE,IAFL,CAEU,MAAM;AACR,iBAAKpC,eAAL,GAAuB,KAAvB;AACH,WAJL,EAKKqC,KALL;AAMH;AAED;AACJ;AACA;;;AACWC,QAAAA,OAAO,CAACC,UAAD,EAA0BN,QAAgB,GAAG,GAA7C,EAAwD;AAClE,cAAI,CAAC,KAAKlC,SAAN,IAAmB,KAAKC,eAA5B,EAA6C;AACzC,gBAAIuC,UAAJ,EAAgBA,UAAU;AAC1B;AACH;;AAED,eAAKvC,eAAL,GAAuB,IAAvB;AACA,eAAKD,SAAL,CAAemC,OAAf,GAAyB,CAAzB;AAEAvC,UAAAA,KAAK,CAAC,KAAKI,SAAN,CAAL,CACKoC,EADL,CACQF,QADR,EACkB;AAAEC,YAAAA,OAAO,EAAE;AAAX,WADlB,EAEKE,IAFL,CAEU,MAAM;AACR,gBAAIG,UAAJ,EAAgBA,UAAU,GADlB,CAER;AACH,WALL,EAMKF,KANL;AAOH;AAED;AACJ;AACA;;;AACW9B,QAAAA,uBAAuB,CAACH,SAAD,EAAoBC,eAAuB,GAAG,GAA9C,EAAmDC,cAAsB,GAAG,GAA5E,EAAuF;AACjH,cAAI,KAAKN,eAAT,EAA0B;AACtBQ,YAAAA,OAAO,CAACC,IAAR,CAAa,sCAAb;AACA;AACH;;AAEDD,UAAAA,OAAO,CAACU,GAAR,CAAa,iCAAgCd,SAAU,EAAvD;AAEA,eAAKkC,OAAL,CAAa,MAAM;AACf7C,YAAAA,QAAQ,CAACU,SAAT,CAAmBC,SAAnB,EAA8B,MAAM;AAChC;AACAI,cAAAA,OAAO,CAACU,GAAR,CAAa,uBAAsBd,SAAU,YAA7C;AACH,aAHD;AAIH,WALD,EAKGC,eALH;AAMH;;AAEDmC,QAAAA,SAAS,GAAG;AACR,cAAI1C,eAAe,CAACI,QAAhB,KAA6B,IAAjC,EAAuC;AACnCJ,YAAAA,eAAe,CAACI,QAAhB,GAA2B,IAA3B;AACH;AACJ;;AA5I0C,O,UAC5BA,Q,GAAmC,I", "sourcesContent": ["import { _decorator, Component, director, UIOpacity, tween, Widget } from 'cc';\nconst { ccclass } = _decorator;\n\n/**\n * 场景过渡管理器\n * 提供渐入渐出的场景切换效果\n */\n@ccclass('SceneTransition')\nexport class SceneTransition extends Component {\n    private static instance: SceneTransition | null = null;\n    private uiOpacity: UIOpacity | null = null;\n    private isTransitioning: boolean = false;\n\n    /**\n     * 获取单例实例\n     */\n    public static getInstance(): SceneTransition | null {\n        return SceneTransition.instance;\n    }\n\n    /**\n     * 静态方法：使用渐变效果加载场景\n     * @param sceneName 场景名称\n     * @param fadeOutDuration 渐出时间（默认0.5秒）\n     * @param fadeInDuration 渐入时间（默认0.5秒）\n     */\n    public static loadScene(sceneName: string, fadeOutDuration: number = 0.5, fadeInDuration: number = 0.5): void {\n        const instance = SceneTransition.getInstance();\n        if (instance && !instance.isTransitioning) {\n            instance.loadSceneWithTransition(sceneName, fadeOutDuration, fadeInDuration);\n        } else {\n            // 如果没有实例或正在过渡中，直接加载场景\n            console.warn('SceneTransition not available, loading scene directly');\n            director.loadScene(sceneName);\n        }\n    }\n\n    onLoad() {\n        // 确保只有一个实例\n        if (SceneTransition.instance) {\n            this.node.destroy();\n            return;\n        }\n\n        SceneTransition.instance = this;\n\n        // 设置为常驻节点，不会在场景切换时被销毁\n        director.addPersistRootNode(this.node);\n\n        // 获取UIOpacity组件\n        this.uiOpacity = this.getComponent(UIOpacity);\n        if (!this.uiOpacity) {\n            this.uiOpacity = this.node.addComponent(UIOpacity);\n        }\n\n        // 设置全屏覆盖\n        this.setupFullScreenOverlay();\n\n        // 场景加载完成后执行渐入\n        this.fadeIn();\n\n        console.log('SceneTransition initialized');\n    }\n\n\n\n    /**\n     * 设置全屏遮罩\n     */\n    private setupFullScreenOverlay(): void {\n        // 添加Widget组件实现全屏覆盖\n        const widget = this.node.getComponent(Widget) || this.node.addComponent(Widget);\n        widget.isAlignTop = true;\n        widget.isAlignBottom = true;\n        widget.isAlignLeft = true;\n        widget.isAlignRight = true;\n        widget.top = 0;\n        widget.bottom = 0;\n        widget.left = 0;\n        widget.right = 0;\n        widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;\n        widget.updateAlignment();\n\n        // 设置渲染层级最高\n        this.node.setSiblingIndex(999999);\n    }\n\n    /**\n     * 渐入效果（从黑屏到透明）\n     */\n    public fadeIn(duration: number = 0.5): void {\n        if (!this.uiOpacity || this.isTransitioning) return;\n        \n        this.isTransitioning = true;\n        this.uiOpacity.opacity = 255;\n        \n        tween(this.uiOpacity)\n            .to(duration, { opacity: 0 })\n            .call(() => {\n                this.isTransitioning = false;\n            })\n            .start();\n    }\n\n    /**\n     * 渐出效果（从透明到黑屏）\n     */\n    public fadeOut(onComplete?: () => void, duration: number = 0.5): void {\n        if (!this.uiOpacity || this.isTransitioning) {\n            if (onComplete) onComplete();\n            return;\n        }\n        \n        this.isTransitioning = true;\n        this.uiOpacity.opacity = 0;\n        \n        tween(this.uiOpacity)\n            .to(duration, { opacity: 255 })\n            .call(() => {\n                if (onComplete) onComplete();\n                // 注意：这里不设置isTransitioning = false，因为场景切换后会重新初始化\n            })\n            .start();\n    }\n\n    /**\n     * 带渐变效果的场景加载\n     */\n    public loadSceneWithTransition(sceneName: string, fadeOutDuration: number = 0.5, fadeInDuration: number = 0.5): void {\n        if (this.isTransitioning) {\n            console.warn('Scene transition already in progress');\n            return;\n        }\n\n        console.log(`Starting scene transition to: ${sceneName}`);\n        \n        this.fadeOut(() => {\n            director.loadScene(sceneName, () => {\n                // 场景加载完成后，新场景的SceneTransition实例会自动执行fadeIn\n                console.log(`Scene transition to ${sceneName} completed`);\n            });\n        }, fadeOutDuration);\n    }\n\n    onDestroy() {\n        if (SceneTransition.instance === this) {\n            SceneTransition.instance = null;\n        }\n    }\n}\n"]}