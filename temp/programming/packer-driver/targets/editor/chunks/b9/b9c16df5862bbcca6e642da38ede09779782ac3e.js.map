{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "ToggleContainer", "Toggle", "Sprite", "Color", "Label", "Node", "find", "TempData", "<PERSON><PERSON><PERSON><PERSON>", "SceneTransition", "CarPropertyDisplay", "PurchasePanel", "ccclass", "property", "SelectManager", "type", "tooltip", "carPrices", "carInfos", "insufficientMoneyTimer", "pendingCarId", "onLoad", "updateLevelToggles", "updateCarToggles", "setupCarPurchaseButtons", "setupCarSelectionListener", "insufficientMoneyLabel", "node", "active", "autoFindCarPropertyDisplay", "<PERSON><PERSON><PERSON><PERSON>", "instance", "console", "log", "levelToggleGroup", "toggleItems", "for<PERSON>ach", "toggle", "levelId", "name", "isUnlocked", "isLevelUnlocked", "interactable", "sprite", "getComponent", "lock", "getChildByName", "color", "WHITE", "BLACK", "updateLevelGradeDisplay", "levelNode", "gradeText", "getLevelGradeText", "gradeLabel", "getComponentInChildren", "label", "string", "progress", "getLevelProgress", "colorHex", "getLevelGradeColor", "grade", "hexToColor", "hex", "r", "parseInt", "slice", "g", "b", "unlockedCars", "player<PERSON><PERSON>", "carToggleGroup", "carId", "indexOf", "updateCarPurchaseButton", "start", "startButton", "on", "EventType", "CLICK", "onStartGame", "backButton", "onBackButton", "levelToggle", "t", "isChecked", "carToggle", "showMessage", "selectedLevel", "selectedCar", "loadScene", "carNode", "purchaseButton", "undefined", "button", "off", "showPurchasePanel", "price", "info", "purchasePanelNode", "error", "purchasePanel", "show", "purchasePrice", "processPurchase", "money", "spendMoney", "unlockCar", "savePlayerData", "showInsufficientMoneyMessage", "update", "deltaTime", "getCarPrice", "setCarPrice", "carPropertyDisplay", "carPropertyNode", "warn", "TOGGLE", "onCarToggleChanged", "checkInitialCarSelection", "showCarProperties", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,e,OAAAA,e;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AACpFC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,kB,iBAAAA,kB;;AAKAC,MAAAA,a,iBAAAA,a;;;;;;;;;AAJT;OACM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U,GAE9B;;AAGA;+BAUaiB,a,WADZF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACb,eAAD,C,UAGRa,QAAQ,CAACb,eAAD,C,UAGRa,QAAQ,CAACd,MAAD,C,UAGRc,QAAQ,CAACd,MAAD,C,UAGRc,QAAQ,CAACT,KAAD,C,UAIRS,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEV,IADA;AAENW,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAORH,QAAQ,CAAC;AACNE,QAAAA,IAAI;AAAA;AAAA,oDADE;AAENC,QAAAA,OAAO,EAAE;AAFH,OAAD,C,2BAzBb,MACaF,aADb,SACmChB,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAcF;AAEvC;AAhByC;;AAuBzC;AAvByC;;AA8BzC;AA9ByC,eA+BjCmB,SA/BiC,GA+BL;AAChC,qBAAS,CADuB;AACf;AACjB,qBAAS,GAFuB;AAEf;AACjB,qBAAS,GAHuB;AAGhB;AAChB,qBAAS,GAJuB;AAIhB;AAChB,qBAAS,GALuB,CAKhB;;AALgB,WA/BK;AAuCzC;AAvCyC,eAwCjCC,QAxCiC,GAwCP;AAC9B,qBAAS,oJADqB;AAE9B,qBAAS,gKAFqB;AAG9B,qBAAS,oKAHqB;AAI9B,qBAAS,gKAJqB;AAK9B,qBAAS;AALqB,WAxCO;AAAA,eAgDjCC,sBAhDiC,GAgDA,CAhDA;AAgDG;AAhDH,eAiDjCC,YAjDiC,GAiDV,IAjDU;AAAA;;AAmDzCC,QAAAA,MAAM,GAAG;AACL,eAAKC,kBAAL;AACA,eAAKC,gBAAL;AACA,eAAKC,uBAAL;AACA,eAAKC,yBAAL,GAJK,CAML;;AACA,cAAI,KAAKC,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,KAA1C;AACH,WATI,CAWL;;;AACA,eAAKC,0BAAL;AACH;;AAEDP,QAAAA,kBAAkB,GAAG;AACjB,gBAAMQ,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AAEA,eAAKC,gBAAL,CAAsBC,WAAtB,CAAkCC,OAAlC,CAA2CC,MAAD,IAAoB;AAC1D,kBAAMC,OAAO,GAAGD,MAAM,CAACV,IAAP,CAAYY,IAA5B;AACA,kBAAMC,UAAU,GAAGV,aAAa,CAACW,eAAd,CAA8BH,OAA9B,CAAnB;AAEAN,YAAAA,OAAO,CAACC,GAAR,CAAa,MAAKK,OAAQ,YAAWE,UAAW,EAAhD,EAJ0D,CAM1D;;AACAH,YAAAA,MAAM,CAACK,YAAP,GAAsBF,UAAtB;AACA,kBAAMG,MAAM,GAAGN,MAAM,CAACV,IAAP,CAAYiB,YAAZ,CAAyB1C,MAAzB,CAAf;AACA,kBAAM2C,IAAI,GAAGR,MAAM,CAACV,IAAP,CAAYmB,cAAZ,CAA2B,MAA3B,CAAb;;AAEA,gBAAIH,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACI,KAAP,GAAeP,UAAU,GAAGrC,KAAK,CAAC6C,KAAT,GAAiB7C,KAAK,CAAC8C,KAAhD;AACH;;AACD,gBAAIJ,IAAJ,EAAU;AACNA,cAAAA,IAAI,CAACjB,MAAL,GAAc,CAACY,UAAf;AACH,aAhByD,CAkB1D;;;AACA,iBAAKU,uBAAL,CAA6Bb,MAAM,CAACV,IAApC,EAA0CW,OAA1C;AACH,WApBD;AAqBH;AAED;AACJ;AACA;;;AACIY,QAAAA,uBAAuB,CAACC,SAAD,EAAkBb,OAAlB,EAAmC;AACtD,gBAAMR,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AACA,gBAAMqB,SAAS,GAAGtB,aAAa,CAACuB,iBAAd,CAAgCf,OAAhC,CAAlB,CAFsD,CAItD;;AACA,cAAIgB,UAAU,GAAGH,SAAS,CAACL,cAAV,CAAyB,YAAzB,CAAjB;;AACA,cAAI,CAACQ,UAAL,EAAiB;AAAA;;AACb;AACAA,YAAAA,UAAU,4BAAGH,SAAS,CAACI,sBAAV,CAAiCnD,KAAjC,CAAH,qBAAG,sBAAyCuB,IAAtD;AACH;;AAED,cAAI2B,UAAJ,EAAgB;AACZ,kBAAME,KAAK,GAAGF,UAAU,CAACV,YAAX,CAAwBxC,KAAxB,CAAd;;AACA,gBAAIoD,KAAJ,EAAW;AACP,kBAAIJ,SAAJ,EAAe;AACXI,gBAAAA,KAAK,CAACC,MAAN,GAAeL,SAAf;AACAI,gBAAAA,KAAK,CAAC7B,IAAN,CAAWC,MAAX,GAAoB,IAApB,CAFW,CAIX;;AACA,sBAAM8B,QAAQ,GAAG5B,aAAa,CAAC6B,gBAAd,CAA+BrB,OAA/B,CAAjB;;AACA,oBAAIoB,QAAJ,EAAc;AACV,wBAAME,QAAQ,GAAG9B,aAAa,CAAC+B,kBAAd,CAAiCH,QAAQ,CAACI,KAA1C,CAAjB;AACAN,kBAAAA,KAAK,CAACT,KAAN,GAAc,KAAKgB,UAAL,CAAgBH,QAAhB,CAAd;AACH;AACJ,eAVD,MAUO;AACHJ,gBAAAA,KAAK,CAACC,MAAN,GAAe,EAAf;AACAD,gBAAAA,KAAK,CAAC7B,IAAN,CAAWC,MAAX,GAAoB,KAApB;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACYmC,QAAAA,UAAU,CAACC,GAAD,EAAqB;AACnC,gBAAMC,CAAC,GAAGC,QAAQ,CAACF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;AACA,gBAAMC,CAAC,GAAGF,QAAQ,CAACF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;AACA,gBAAME,CAAC,GAAGH,QAAQ,CAACF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;AACA,iBAAO,IAAIhE,KAAJ,CAAU8D,CAAV,EAAaG,CAAb,EAAgBC,CAAhB,EAAmB,GAAnB,CAAP;AACH;;AAED9C,QAAAA,gBAAgB,GAAG;AACf,gBAAM+C,YAAY,GAAG;AAAA;AAAA,8CAAcvC,QAAd,CAAuBwC,UAAvB,CAAkCD,YAAvD;AACA,eAAKE,cAAL,CAAoBrC,WAApB,CAAgCC,OAAhC,CAAyCC,MAAD,IAAoB;AACxD,kBAAMoC,KAAK,GAAGpC,MAAM,CAACV,IAAP,CAAYY,IAA1B;AACA,kBAAMC,UAAU,GAAG8B,YAAY,CAACI,OAAb,CAAqBD,KAArB,MAAgC,CAAC,CAApD,CAFwD,CAIxD;;AACApC,YAAAA,MAAM,CAACK,YAAP,GAAsBF,UAAtB;AACA,kBAAMG,MAAM,GAAGN,MAAM,CAACV,IAAP,CAAYiB,YAAZ,CAAyB1C,MAAzB,CAAf;;AACA,gBAAIyC,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACI,KAAP,GAAeP,UAAU,GAAGrC,KAAK,CAAC6C,KAAT,GAAiB7C,KAAK,CAAC8C,KAAhD;AACH,aATuD,CAWxD;;;AACA,iBAAK0B,uBAAL,CAA6BtC,MAAM,CAACV,IAApC,EAA0C8C,KAA1C,EAAiDjC,UAAjD;AACH,WAbD;AAcH;;AAEDoC,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBlD,IAAjB,CAAsBmD,EAAtB,CAAyB/E,MAAM,CAACgF,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,WAAtD,EAAmE,IAAnE;AACH;;AACA,cAAI,KAAKC,UAAT,EAAqB;AAClB,iBAAKA,UAAL,CAAgBvD,IAAhB,CAAqBmD,EAArB,CAAwB/E,MAAM,CAACgF,SAAP,CAAiBC,KAAzC,EAAgD,KAAKG,YAArD,EAAmE,IAAnE;AACH;AACJ;;AAEDF,QAAAA,WAAW,GAAG;AACV;AACA,gBAAMG,WAAW,GAAG,KAAKlD,gBAAL,CAAsBC,WAAtB,CAAkC7B,IAAlC,CAAwC+E,CAAD,IAAYA,CAAC,CAACC,SAArD,CAApB,CAFU,CAGV;;AACA,gBAAMC,SAAS,GAAG,KAAKf,cAAL,CAAoBrC,WAApB,CAAgC7B,IAAhC,CAAsC+E,CAAD,IAAYA,CAAC,CAACC,SAAnD,CAAlB;;AAEA,cAAI,CAACF,WAAD,IAAgB,CAACG,SAArB,EAAgC;AAC5B,iBAAKC,WAAL,CAAiB,+BAAjB,EAD4B,CAE5B;;AACA;AACH,WAVS,CAYV;;;AACA;AAAA;AAAA,oCAASC,aAAT,GAAyBL,WAAW,CAACzD,IAAZ,CAAiBY,IAA1C;AACA;AAAA;AAAA,oCAASmD,WAAT,GAAuBH,SAAS,CAAC5D,IAAV,CAAeY,IAAtC;AAEAP,UAAAA,OAAO,CAACC,GAAR,CAAYmD,WAAW,CAACzD,IAAZ,CAAiBY,IAA7B,EAAkCgD,SAAS,CAAC5D,IAAV,CAAeY,IAAjD,EAhBU,CAkBV;;AACA;AAAA;AAAA,kDAAgBoD,SAAhB,CAA0B,WAA1B;AACH;;AAEDR,QAAAA,YAAY,GACZ;AACI;AAAA;AAAA,kDAAgBQ,SAAhB,CAA0B,UAA1B;AACH;AAID;AACJ;AACA;;;AACInE,QAAAA,uBAAuB,GAAG;AACtB,eAAKgD,cAAL,CAAoBrC,WAApB,CAAgCC,OAAhC,CAAyCC,MAAD,IAAoB;AACxD,kBAAMoC,KAAK,GAAGpC,MAAM,CAACV,IAAP,CAAYY,IAA1B;AACA,kBAAMC,UAAU,GAAG;AAAA;AAAA,gDAAcT,QAAd,CAAuBwC,UAAvB,CAAkCD,YAAlC,CAA+CI,OAA/C,CAAuDD,KAAvD,MAAkE,CAAC,CAAtF;AACA,iBAAKE,uBAAL,CAA6BtC,MAAM,CAACV,IAApC,EAA0C8C,KAA1C,EAAiDjC,UAAjD;AACH,WAJD;AAKH;AAED;AACJ;AACA;;;AACImC,QAAAA,uBAAuB,CAACiB,OAAD,EAAgBnB,KAAhB,EAA+BjC,UAA/B,EAAoD;AACvE;AACA,cAAIqD,cAAc,GAAGD,OAAO,CAAC9C,cAAR,CAAuB,gBAAvB,CAArB;;AAEA,cAAI,CAACN,UAAD,IAAe,KAAKvB,SAAL,CAAewD,KAAf,MAA0BqB,SAA7C,EAAwD;AACpD;AACA,gBAAI,CAACD,cAAL,EAAqB;AACjB;AACAA,cAAAA,cAAc,GAAGD,OAAO,CAAC9C,cAAR,CAAuB,gBAAvB,CAAjB;AACH;;AAED,gBAAI+C,cAAJ,EAAoB;AAChBA,cAAAA,cAAc,CAACjE,MAAf,GAAwB,IAAxB,CADgB,CAGhB;AACA;AACA;AACA;AACA;AAEA;;AACA,oBAAMmE,MAAM,GAAGF,cAAc,CAACjD,YAAf,CAA4B7C,MAA5B,CAAf;;AACA,kBAAIgG,MAAJ,EAAY;AACRA,gBAAAA,MAAM,CAACpE,IAAP,CAAYqE,GAAZ,CAAgBjG,MAAM,CAACgF,SAAP,CAAiBC,KAAjC;AACAe,gBAAAA,MAAM,CAACpE,IAAP,CAAYmD,EAAZ,CAAe/E,MAAM,CAACgF,SAAP,CAAiBC,KAAhC,EAAuC,MAAM;AACzC,uBAAK5D,YAAL,GAAoBqD,KAApB;AACA,uBAAKwB,iBAAL,CAAuB,KAAKhF,SAAL,CAAewD,KAAf,CAAvB,EAA8C,KAAKvD,QAAL,CAAcuD,KAAd,CAA9C;AACH,iBAHD,EAGG,IAHH;AAIH;AACJ;AACJ,WA1BD,MA0BO;AACH;AACA,gBAAIoB,cAAJ,EAAoB;AAChBA,cAAAA,cAAc,CAACjE,MAAf,GAAwB,KAAxB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYqE,QAAAA,iBAAiB,CAACC,KAAD,EAAgBC,IAAhB,EAA8B;AACnD,cAAI,CAAC,KAAKC,iBAAV,EAA6B;AACzBpE,YAAAA,OAAO,CAACqE,KAAR,CAAc,WAAd;AACA;AACH;;AAED,gBAAMC,aAAa,GAAG,KAAKF,iBAAL,CAAuBxD,YAAvB;AAAA;AAAA,6CAAtB;;AACA,cAAI,CAAC0D,aAAL,EAAoB;AAChBtE,YAAAA,OAAO,CAACqE,KAAR,CAAc,WAAd;AACA;AACH,WAVkD,CAYnD;AACA;AAEA;;;AACAC,UAAAA,aAAa,CAACC,IAAd,CAAmBL,KAAnB,EAA0BC,IAA1B,EAAiCK,aAAD,IAAmB;AAC/C;AACA,iBAAKC,eAAL,CAAqBD,aAArB;AACH,WAHD;AAIH;AAED;AACJ;AACA;;;AACYC,QAAAA,eAAe,CAACP,KAAD,EAAgB;AACnC,cAAI,CAAC,KAAK9E,YAAV,EAAwB;AACpB;AACH;;AAED,gBAAMqD,KAAK,GAAG,KAAKrD,YAAnB;AACA,gBAAMU,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC,CANmC,CAQnC;;AACA,cAAID,aAAa,CAACyC,UAAd,CAAyBmC,KAAzB,IAAkCR,KAAtC,EAA6C;AACzC;AACA,gBAAIpE,aAAa,CAAC6E,UAAd,CAAyBT,KAAzB,CAAJ,EAAqC;AACjCpE,cAAAA,aAAa,CAAC8E,SAAd,CAAwBnC,KAAxB;AAEAzC,cAAAA,OAAO,CAACC,GAAR,CAAa,UAASwC,KAAM,OAAMyB,KAAM,KAAxC,EAHiC,CAKjC;;AACA,mBAAK3E,gBAAL,GANiC,CAQjC;;AACAO,cAAAA,aAAa,CAAC+E,cAAd;AACH;AACJ,WAbD,MAaO;AACH;AACA,iBAAKC,4BAAL;AACH,WAzBkC,CA2BnC;;;AACA,eAAK1F,YAAL,GAAoB,IAApB;AACH;AAED;AACJ;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACI0F,QAAAA,4BAA4B,GAAG;AAC3B,cAAI,KAAKpF,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4B+B,MAA5B,GAAqC,kCAArC;AACA,iBAAK/B,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,IAA1C;AACA,iBAAKT,sBAAL,GAA8B,GAA9B,CAH6B,CAGM;AACtC;AACJ;;AACDqE,QAAAA,WAAW,CAAC/B,MAAD,EAAiB;AACxB,cAAI,KAAK/B,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4B+B,MAA5B,GAAqCA,MAArC;AACA,iBAAK/B,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,IAA1C;AACA,iBAAKT,sBAAL,GAA8B,GAA9B,CAH6B,CAGM;AACtC;AACJ;AAED;AACJ;AACA;;;AACI4F,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAK7F,sBAAL,GAA8B,CAAlC,EAAqC;AACjC,iBAAKA,sBAAL,IAA+B6F,SAA/B;;AACA,gBAAI,KAAK7F,sBAAL,IAA+B,CAAnC,EAAsC;AAClC,kBAAI,KAAKO,sBAAT,EAAiC;AAC7B,qBAAKA,sBAAL,CAA4BC,IAA5B,CAAiCC,MAAjC,GAA0C,KAA1C;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACIqF,QAAAA,WAAW,CAACxC,KAAD,EAAwB;AAC/B,iBAAO,KAAKxD,SAAL,CAAewD,KAAf,KAAyB,CAAhC;AACH;AAED;AACJ;AACA;;;AACIyC,QAAAA,WAAW,CAACzC,KAAD,EAAgByB,KAAhB,EAA+B;AACtC,eAAKjF,SAAL,CAAewD,KAAf,IAAwByB,KAAxB;AACH;AAED;AACJ;AACA;;;AACYrE,QAAAA,0BAA0B,GAAS;AACvC,cAAI,CAAC,KAAKsF,kBAAV,EAA8B;AAC1B;AACA,kBAAMC,eAAe,GAAG9G,IAAI,CAAC,qBAAD,CAAJ,IACDA,IAAI,CAAC,cAAD,CADH,IAED,KAAKqB,IAAL,CAAUmB,cAAV,CAAyB,cAAzB,CAFvB;;AAIA,gBAAIsE,eAAJ,EAAqB;AACjB,mBAAKD,kBAAL,GAA0BC,eAAe,CAACxE,YAAhB;AAAA;AAAA,2DAA1B;;AACA,kBAAI,CAAC,KAAKuE,kBAAV,EAA8B;AAC1BnF,gBAAAA,OAAO,CAACqF,IAAR,CAAa,2CAAb;AACH;AACJ,aALD,MAKO;AACHrF,cAAAA,OAAO,CAACqF,IAAR,CAAa,+BAAb;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACY5F,QAAAA,yBAAyB,GAAS;AACtC,cAAI,CAAC,KAAK+C,cAAV,EAA0B;AACtBxC,YAAAA,OAAO,CAACqF,IAAR,CAAa,mBAAb;AACA;AACH,WAJqC,CAMtC;;;AACA,eAAK7C,cAAL,CAAoBrC,WAApB,CAAgCC,OAAhC,CAAyCC,MAAD,IAAoB;AACxDA,YAAAA,MAAM,CAACV,IAAP,CAAYmD,EAAZ,CAAe7E,MAAM,CAAC8E,SAAP,CAAiBuC,MAAhC,EAAwC,KAAKC,kBAA7C,EAAiE,IAAjE;AACH,WAFD,EAPsC,CAWtC;;AACA,eAAKC,wBAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,kBAAkB,CAAClF,MAAD,EAAuB;AAC7C,cAAIA,MAAM,CAACiD,SAAX,EAAsB;AAClB,kBAAMb,KAAK,GAAGpC,MAAM,CAACV,IAAP,CAAYY,IAA1B;AACAP,YAAAA,OAAO,CAACC,GAAR,CAAa,SAAQwC,KAAM,EAA3B;AACA,iBAAKgD,iBAAL,CAAuBhD,KAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACYgD,QAAAA,iBAAiB,CAAChD,KAAD,EAAsB;AAC3C,cAAI,KAAK0C,kBAAT,EAA6B;AACzB,iBAAKA,kBAAL,CAAwBM,iBAAxB,CAA0ChD,KAA1C;AACH,WAFD,MAEO;AACHzC,YAAAA,OAAO,CAACqF,IAAR,CAAa,kCAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYG,QAAAA,wBAAwB,GAAS;AACrC,gBAAME,cAAc,GAAG,KAAKlD,cAAL,CAAoBrC,WAApB,CAAgC7B,IAAhC,CAAsC+B,MAAD,IAAoBA,MAAM,CAACiD,SAAhE,CAAvB;;AACA,cAAIoC,cAAJ,EAAoB;AAChB,kBAAMjD,KAAK,GAAGiD,cAAc,CAAC/F,IAAf,CAAoBY,IAAlC;AACA,iBAAKkF,iBAAL,CAAuBhD,KAAvB;AACH,WALoC,CAMrC;AACA;AACA;AACA;AACA;AACA;;AACH;;AA/cwC,O;;;;;iBAEL,I;;;;;;;iBAGF,I;;;;;;;iBAGZ,I;;;;;;;iBAGD,I;;;;;;;iBAGW,I;;;;;;;iBAOE,I;;;;;;;iBAOO,I", "sourcesContent": ["import { _decorator, Component, Button, ToggleContainer, Toggle, Sprite, Color, Label, Node, find } from 'cc';\nimport { TempData } from './TempData';\nimport { PlayerManager } from './PlayerManager';\nimport { SceneTransition } from './SceneTransition';\nimport { CarPropertyDisplay } from './CarPropertyDisplay';\n// @ts-ignore\nconst { ccclass, property } = _decorator;\n\n// 添加PurchasePanel引用\nimport { PurchasePanel } from './PurchasePanel';\n\n// 车辆价格配置\ninterface CarPriceConfig {\n    [carId: string]: number;\n}\n\ninterface CarinfoConfig {\n    [carId: string]: string;\n}\n\n@ccclass('SelectManager')\nexport class SelectManager extends Component {\n    @property(ToggleContainer)\n    levelToggleGroup: ToggleContainer = null!;\n\n    @property(ToggleContainer)\n    carToggleGroup: ToggleContainer = null!;\n\n    @property(Button)\n    startButton: Button = null!;\n\n    @property(Button)\n    backButton: Button = null!;\n\n    @property(Label)\n    insufficientMoneyLabel: Label = null!; // 金币不足提示标签\n\n    // 购买面板相关属性\n    @property({\n        type: Node,\n        tooltip: '场景中的购买面板节点',\n    })\n    private purchasePanelNode: Node = null!;\n\n    // 车辆属性显示相关属性\n    @property({\n        type: CarPropertyDisplay,\n        tooltip: 'car-property节点上的CarPropertyDisplay组件',\n    })\n    carPropertyDisplay: CarPropertyDisplay = null!;\n\n    // 车辆价格配置\n    private carPrices: CarPriceConfig = {\n        'car-1': 0,      // 默认车辆免费\n        'car-2': 300,    // 第二辆车500金币\n        'car-3': 500,   // 第三辆车1000金币\n        'car-4': 700,   // 第四辆车1500金币\n        'car-5': 800,   // 第五辆车2000金币\n    };\n\n    // 车辆价格配置\n    private carInfos: CarinfoConfig = {\n        'car-1': '操控性超强小车，武器配备为子弹发射器，击中对手你可造成伤害 \\n This super maneuverable car is equipped with a bullet launcher. When you hit your opponent, you can cause damage.',      \n        'car-2': '经典跑车,具有坚固的车身,武器配备为火箭炮，爆炸后会清除附近的颜料 \\nClassic sports car, with a sturdy body, equipped with  a rocket launcher. After explosion, it will clear the nearby paint.',    \n        'car-3': '现代化的超级跑车，速度与转向均衡，配备武器为飞镖，攻守兼备 \\nA modern supercar with balanced speed and steering, equipped with a dart launcher. it is a good choice for both attack and defense',   \n        'car-4': '甩尾加速犹如闪电，武器配备为火箭炮，爆炸后会清除附近的颜料\\nThe drift and acceleration is like lightning, equipped with a rocket launcher. After explosion, it will clear the nearby paint.',   \n        'car-5': '送豆腐专用，即使在狭窄的山路也灵活穿梭，武器配备为飞镖，攻守兼备\\n It is specially designed for delivering tofu and can move flexibly even on narrow mountain roads. equipped with a dart launcher. ',   \n    };\n\n    private insufficientMoneyTimer: number = 0; // 金币不足提示计时器\n    private pendingCarId: string = null!;\n\n    onLoad() {\n        this.updateLevelToggles();\n        this.updateCarToggles();\n        this.setupCarPurchaseButtons();\n        this.setupCarSelectionListener();\n\n        // 隐藏金币不足提示\n        if (this.insufficientMoneyLabel) {\n            this.insufficientMoneyLabel.node.active = false;\n        }\n\n        // 自动查找车辆属性显示组件（如果没有手动设置）\n        this.autoFindCarPropertyDisplay();\n    }\n\n    updateLevelToggles() {\n        const playerManager = PlayerManager.instance;\n        console.log('更新关卡显示');\n\n        this.levelToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            const levelId = toggle.node.name;\n            const isUnlocked = playerManager.isLevelUnlocked(levelId);\n\n            console.log(`关卡 ${levelId}: 解锁状态 = ${isUnlocked}`);\n\n            // 设置交互性和颜色\n            toggle.interactable = isUnlocked;\n            const sprite = toggle.node.getComponent(Sprite);\n            const lock = toggle.node.getChildByName('lock');\n\n            if (sprite) {\n                sprite.color = isUnlocked ? Color.WHITE : Color.BLACK;\n            }\n            if (lock) {\n                lock.active = !isUnlocked;\n            }\n\n            // 更新评级显示\n            this.updateLevelGradeDisplay(toggle.node, levelId);\n        });\n    }\n\n    /**\n     * 更新关卡评级显示\n     */\n    updateLevelGradeDisplay(levelNode: Node, levelId: string) {\n        const playerManager = PlayerManager.instance;\n        const gradeText = playerManager.getLevelGradeText(levelId);\n\n        // 查找或创建评级标签\n        let gradeLabel = levelNode.getChildByName('GradeLabel');\n        if (!gradeLabel) {\n            // 如果没有评级标签节点，尝试查找现有的Label子节点\n            gradeLabel = levelNode.getComponentInChildren(Label)?.node;\n        }\n\n        if (gradeLabel) {\n            const label = gradeLabel.getComponent(Label);\n            if (label) {\n                if (gradeText) {\n                    label.string = gradeText;\n                    label.node.active = true;\n\n                    // 设置评级颜色\n                    const progress = playerManager.getLevelProgress(levelId);\n                    if (progress) {\n                        const colorHex = playerManager.getLevelGradeColor(progress.grade);\n                        label.color = this.hexToColor(colorHex);\n                    }\n                } else {\n                    label.string = '';\n                    label.node.active = false;\n                }\n            }\n        }\n    }\n\n    /**\n     * 将十六进制颜色转换为Cocos Color\n     */\n    private hexToColor(hex: string): Color {\n        const r = parseInt(hex.slice(1, 3), 16);\n        const g = parseInt(hex.slice(3, 5), 16);\n        const b = parseInt(hex.slice(5, 7), 16);\n        return new Color(r, g, b, 255);\n    }\n\n    updateCarToggles() {\n        const unlockedCars = PlayerManager.instance.playerData.unlockedCars;\n        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            const carId = toggle.node.name;\n            const isUnlocked = unlockedCars.indexOf(carId) !== -1;\n\n            // 设置车辆图标的交互性和颜色\n            toggle.interactable = isUnlocked;\n            const sprite = toggle.node.getComponent(Sprite);\n            if (sprite) {\n                sprite.color = isUnlocked ? Color.WHITE : Color.BLACK;\n            }\n\n            // 处理购买按钮的显示\n            this.updateCarPurchaseButton(toggle.node, carId, isUnlocked);\n        });\n    }\n\n    start() {\n        if (this.startButton) {\n            this.startButton.node.on(Button.EventType.CLICK, this.onStartGame, this);\n        }\n         if (this.backButton) {\n            this.backButton.node.on(Button.EventType.CLICK, this.onBackButton, this);\n        }\n    }\n\n    onStartGame() {\n        // 获取当前选中的level\n        const levelToggle = this.levelToggleGroup.toggleItems.find((t: any) => t.isChecked);\n        // 获取当前选中的car\n        const carToggle = this.carToggleGroup.toggleItems.find((t: any) => t.isChecked);\n\n        if (!levelToggle || !carToggle) {\n            this.showMessage(\"请选择车辆！\\n please select a car!\");\n            // 你可以在这里弹窗提示\"请选择关卡和车辆\"\n            return;\n        }\n\n        // 记录选择到TempData\n        TempData.selectedLevel = levelToggle.node.name;\n        TempData.selectedCar = carToggle.node.name;\n\n        console.log(levelToggle.node.name,carToggle.node.name)\n\n        // 切换到游戏场景\n        SceneTransition.loadScene('gamescene');\n    }\n\n    onBackButton()\n    {\n        SceneTransition.loadScene('mainmenu');\n    }\n\n\n\n    /**\n     * 设置车辆购买按钮\n     */\n    setupCarPurchaseButtons() {\n        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            const carId = toggle.node.name;\n            const isUnlocked = PlayerManager.instance.playerData.unlockedCars.indexOf(carId) !== -1;\n            this.updateCarPurchaseButton(toggle.node, carId, isUnlocked);\n        });\n    }\n\n    /**\n     * 更新单个车辆的购买按钮\n     */\n    updateCarPurchaseButton(carNode: Node, carId: string, isUnlocked: boolean) {\n        // 查找或创建购买按钮\n        let purchaseButton = carNode.getChildByName('PurchaseButton');\n\n        if (!isUnlocked && this.carPrices[carId] !== undefined) {\n            // 车辆未解锁且有价格配置，显示购买按钮\n            if (!purchaseButton) {\n                // 创建购买按钮（这里假设场景中已经有购买按钮节点）\n                purchaseButton = carNode.getChildByName('PurchaseButton');\n            }\n\n            if (purchaseButton) {\n                purchaseButton.active = true;\n\n                // 设置按钮文本\n                // const buttonLabel = purchaseButton.getChildByName('Label')?.getComponent(Label);\n                // if (buttonLabel) {\n                //     buttonLabel.string = `购买 ${this.carPrices[carId]}`;\n                // }\n\n                // 绑定点击事件\n                const button = purchaseButton.getComponent(Button);\n                if (button) {\n                    button.node.off(Button.EventType.CLICK);\n                    button.node.on(Button.EventType.CLICK, () => {\n                        this.pendingCarId = carId;\n                        this.showPurchasePanel(this.carPrices[carId], this.carInfos[carId]);\n                    }, this);\n                }\n            }\n        } else {\n            // 车辆已解锁或免费，隐藏购买按钮\n            if (purchaseButton) {\n                purchaseButton.active = false;\n            }\n        }\n    }\n\n    /**\n     * 显示购买面板\n     */\n    private showPurchasePanel(price: number, info: string) {\n        if (!this.purchasePanelNode) {\n            console.error('购买面板节点未配置');\n            return;\n        }\n\n        const purchasePanel = this.purchasePanelNode.getComponent<PurchasePanel>(PurchasePanel);\n        if (!purchasePanel) {\n            console.error('购买面板组件未找到');\n            return;\n        }\n\n        // 确保面板在最上层\n        // this.purchasePanelNode.setSiblingIndex(Number.MAX_SAFE_INTEGER);\n\n        // 显示面板\n        purchasePanel.show(price, info, (purchasePrice) => {\n            // 确认购买后的回调\n            this.processPurchase(purchasePrice);\n        });\n    }\n\n    /**\n     * 处理实际购买逻辑\n     */\n    private processPurchase(price: number) {\n        if (!this.pendingCarId) {\n            return;\n        }\n\n        const carId = this.pendingCarId;\n        const playerManager = PlayerManager.instance;\n\n        // 检查玩家金币是否足够（再次检查，因为用户可能在面板显示期间改变了金币）\n        if (playerManager.playerData.money >= price) {\n            // 扣除金币并解锁车辆\n            if (playerManager.spendMoney(price)) {\n                playerManager.unlockCar(carId);\n\n                console.log(`成功购买车辆 ${carId}，花费 ${price} 金币`);\n\n                // 更新UI显示\n                this.updateCarToggles();\n\n                // 保存数据\n                playerManager.savePlayerData();\n            }\n        } else {\n            // 金币不足，显示提示\n            this.showInsufficientMoneyMessage();\n        }\n\n        // 重置待购买车辆ID\n        this.pendingCarId = null;\n    }\n\n    /**\n     * 购买车辆\n     */\n    // onPurchaseCar(carId: string) {\n    //     const price = this.carPrices[carId];\n    //     if (price === undefined) {\n    //         console.warn(`车辆 ${carId} 没有配置价格`);\n    //         return;\n    //     }\n\n    //     const playerManager = PlayerManager.instance;\n    //     if (!playerManager) {\n    //         console.error('PlayerManager 实例不存在');\n    //         return;\n    //     }\n\n    //     // 检查玩家金币是否足够\n    //     if (playerManager.playerData.money >= price) {\n    //         // 扣除金币并解锁车辆\n    //         if (playerManager.spendMoney(price)) {\n    //             playerManager.unlockCar(carId);\n\n    //             console.log(`成功购买车辆 ${carId}，花费 ${price} 金币`);\n\n    //             // 更新UI显示\n    //             this.updateCarToggles();\n\n    //             // 保存数据\n    //             playerManager.savePlayerData();\n    //         }\n    //     } else {\n    //         // 金币不足，显示提示\n    //         this.showInsufficientMoneyMessage();\n    //     }\n    // }\n\n    /**\n     * 显示金币不足提示\n     */\n    showInsufficientMoneyMessage() {\n        if (this.insufficientMoneyLabel) {\n            this.insufficientMoneyLabel.string = '金币不足！\\n your money is not enough';\n            this.insufficientMoneyLabel.node.active = true;\n            this.insufficientMoneyTimer = 3.0; // 3秒后隐藏\n        }\n    }\n    showMessage(string: string) {\n        if (this.insufficientMoneyLabel) {\n            this.insufficientMoneyLabel.string = string;\n            this.insufficientMoneyLabel.node.active = true;\n            this.insufficientMoneyTimer = 3.0; // 3秒后隐藏\n        }\n    }\n\n    /**\n     * 更新方法，处理金币不足提示的计时\n     */\n    update(deltaTime: number) {\n        if (this.insufficientMoneyTimer > 0) {\n            this.insufficientMoneyTimer -= deltaTime;\n            if (this.insufficientMoneyTimer <= 0) {\n                if (this.insufficientMoneyLabel) {\n                    this.insufficientMoneyLabel.node.active = false;\n                }\n            }\n        }\n    }\n\n    /**\n     * 获取车辆价格\n     */\n    getCarPrice(carId: string): number {\n        return this.carPrices[carId] || 0;\n    }\n\n    /**\n     * 设置车辆价格\n     */\n    setCarPrice(carId: string, price: number) {\n        this.carPrices[carId] = price;\n    }\n\n    /**\n     * 自动查找车辆属性显示组件\n     */\n    private autoFindCarPropertyDisplay(): void {\n        if (!this.carPropertyDisplay) {\n            // 尝试在场景中查找car-property节点\n            const carPropertyNode = find('Canvas/car-property') ||\n                                   find('car-property') ||\n                                   this.node.getChildByName('car-property');\n\n            if (carPropertyNode) {\n                this.carPropertyDisplay = carPropertyNode.getComponent(CarPropertyDisplay);\n                if (!this.carPropertyDisplay) {\n                    console.warn('car-property节点找到了，但没有CarPropertyDisplay组件');\n                }\n            } else {\n                console.warn('未找到car-property节点，请确保场景中存在该节点');\n            }\n        }\n    }\n\n    /**\n     * 设置车辆选择监听器\n     */\n    private setupCarSelectionListener(): void {\n        if (!this.carToggleGroup) {\n            console.warn('carToggleGroup未设置');\n            return;\n        }\n\n        // 为每个车辆Toggle添加选择监听\n        this.carToggleGroup.toggleItems.forEach((toggle: Toggle) => {\n            toggle.node.on(Toggle.EventType.TOGGLE, this.onCarToggleChanged, this);\n        });\n\n        // 检查是否有默认选中的车辆\n        this.checkInitialCarSelection();\n    }\n\n    /**\n     * 车辆Toggle状态改变时的回调\n     */\n    private onCarToggleChanged(toggle: Toggle): void {\n        if (toggle.isChecked) {\n            const carId = toggle.node.name;\n            console.log(`选中车辆: ${carId}`);\n            this.showCarProperties(carId);\n        }\n    }\n\n    /**\n     * 显示车辆属性\n     */\n    private showCarProperties(carId: string): void {\n        if (this.carPropertyDisplay) {\n            this.carPropertyDisplay.showCarProperties(carId);\n        } else {\n            console.warn('CarPropertyDisplay组件未找到，无法显示车辆属性');\n        }\n    }\n\n    /**\n     * 检查初始车辆选择\n     */\n    private checkInitialCarSelection(): void {\n        const selectedToggle = this.carToggleGroup.toggleItems.find((toggle: Toggle) => toggle.isChecked);\n        if (selectedToggle) {\n            const carId = selectedToggle.node.name;\n            this.showCarProperties(carId);\n        } \n        // else {\n        //     // 如果没有选中的车辆，隐藏属性显示\n        //     if (this.carPropertyDisplay) {\n        //         this.carPropertyDisplay.hideAllProperties();\n        //     }\n        // }\n    }\n}\n\n"]}