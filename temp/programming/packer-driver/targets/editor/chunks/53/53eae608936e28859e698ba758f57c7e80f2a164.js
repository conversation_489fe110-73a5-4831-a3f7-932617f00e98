System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, InterstitialType;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _cclegacy._RF.push({}, "e7eca5A3bFKwYq/A4Lp1A8C", "interstitial_type", undefined);

      /**
       * Interstitial Type for AdBreak
       */
      _export("InterstitialType", InterstitialType = /*#__PURE__*/function (InterstitialType) {
        InterstitialType["START"] = "start";
        InterstitialType["PAUSE"] = "pause";
        InterstitialType["NEXT"] = "next";
        InterstitialType["BROWSE"] = "browse";
        return InterstitialType;
      }({}));

      _cclegacy._RF.pop();
    }
  };
});
//# sourceMappingURL=53eae608936e28859e698ba758f57c7e80f2a164.js.map