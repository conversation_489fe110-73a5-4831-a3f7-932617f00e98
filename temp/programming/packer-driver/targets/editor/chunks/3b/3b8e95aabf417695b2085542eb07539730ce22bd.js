System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts at runtime.
      throw new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts: Unterminated string constant. (194:29)

  192 |
  193 |         if (!levelToggle || !carToggle) {
> 194 |             this.showMessage("请选择车辆！\n please select a car！
      |                              ^
  195 |             // 你可以在这里弹窗提示"请选择关卡和车辆"
  196 |             return;
  197 |         }`);
    }
  };
});
//# sourceMappingURL=3b8e95aabf417695b2085542eb07539730ce22bd.js.map