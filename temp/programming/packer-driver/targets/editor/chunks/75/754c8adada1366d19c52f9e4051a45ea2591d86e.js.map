{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts"], "names": ["_decorator", "Component", "Vec2", "GameManager", "ccclass", "property", "AIState", "AIController", "aiStates", "canvasWorldPos", "boundaryTurnTimers", "boundaryTurnTargetAngles", "boundaryTurnDirections", "boundaryTurnStartAngles", "lastAngleChanges", "boundaryTurnCooldowns", "aiPlayers", "start", "onScenePrefabLoaded", "console", "log", "getInstance", "getAIPlayers", "map", "FreeDrive", "length", "refreshAIPlayers", "update", "dt", "i", "ai", "BoundaryTurning", "handleBoundaryTurning", "freeDrive", "Math", "random", "setDirection", "setAccel", "pos", "node", "worldPosition", "relativeX", "x", "relativeY", "y", "margin", "nearLeftBoundary", "sceneMinX", "nearRightBoundary", "sceneMaxX", "nearBottomBoundary", "sceneMinY", "nearTopBoundary", "sceneMaxY", "requiredCooldown", "abs", "boundaryInfo", "startBoundaryTurning", "nearLeft", "nearRight", "nearBottom", "nearTop", "getCurrentAngle", "targetAngle", "turnDirection", "currentAngle", "<PERSON><PERSON><PERSON><PERSON>", "angleDiff", "setTargetAngle", "startAngle", "angleTurned", "angleToTarget", "angleChange", "sign", "absAngleToTarget", "pulseInterval", "pulseOn", "timeInPulse", "setAIHealth", "aiIndex", "health", "<PERSON><PERSON><PERSON><PERSON>", "setHealth", "damageAI", "damage", "takeDamage", "healAI", "amount", "heal", "getAIHealth", "getHealth", "getAIMaxHealth", "getMaxHealth", "isAIDead", "isDead", "isAIBoundaryTurning", "aiPlayer", "index", "indexOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;;AAE7BC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;AAEzBM,MAAAA,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;QAAAA,O;;8BAMQC,Y,WADZH,OAAO,CAAC,cAAD,C,2BAAR,MACaG,YADb,SACkCN,SADlC,CAC4C;AAAA;AAAA;;AACxC;AACA;AAFwC;;AAKX;AALW;;AAOX;AAPW;;AASX;AATW;;AAWX;AAXW,eAahCO,QAbgC,GAaV,EAbU;AAAA,eAchCC,cAdgC,GAcT,IAAIP,IAAJ,EAdS;AAAA,eAehCQ,kBAfgC,GAeD,EAfC;AAeG;AAfH,eAgBhCC,wBAhBgC,GAgBK,EAhBL;AAgBS;AAhBT,eAiBhCC,sBAjBgC,GAiBG,EAjBH;AAiBO;AAjBP,eAkBhCC,uBAlBgC,GAkBI,EAlBJ;AAkBQ;AAlBR,eAmBhCC,gBAnBgC,GAmBH,EAnBG;AAmBC;AAnBD,eAoBhCC,qBApBgC,GAoBE,EApBF;AAoBM;AApBN,eAqBhCC,SArBgC,GAqBR,EArBQ;AAAA;;AAuBxCC,QAAAA,KAAK,GAAG,CACJ;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,mBAAmB,GAAG;AACzBC,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACA,eAAKJ,SAAL,GAAiB;AAAA;AAAA,0CAAYK,WAAZ,GAA0BC,YAA1B,EAAjB,CAFyB,CAGzB;;AACA,eAAKd,QAAL,GAAgB,KAAKQ,SAAL,CAAeO,GAAf,CAAmB,MAAMjB,OAAO,CAACkB,SAAjC,CAAhB;AACA,eAAKd,kBAAL,GAA0B,KAAKM,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA1B;AACA,eAAKZ,wBAAL,GAAgC,KAAKK,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAAhC;AACA,eAAKX,sBAAL,GAA8B,KAAKI,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA9B;AACA,eAAKV,uBAAL,GAA+B,KAAKG,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA/B;AACA,eAAKT,gBAAL,GAAwB,KAAKE,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAAxB;AACA,eAAKR,qBAAL,GAA6B,KAAKC,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA7B;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAa,eAAc,KAAKJ,SAAL,CAAeS,MAAO,QAAjD;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,gBAAgB,GAAG;AACtB,eAAKV,SAAL,GAAiB;AAAA;AAAA,0CAAYK,WAAZ,GAA0BC,YAA1B,EAAjB,CADsB,CAGtB;;AACA,eAAKd,QAAL,GAAgB,KAAKQ,SAAL,CAAeO,GAAf,CAAmB,MAAMjB,OAAO,CAACkB,SAAjC,CAAhB;AACA,eAAKd,kBAAL,GAA0B,KAAKM,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA1B;AACA,eAAKZ,wBAAL,GAAgC,KAAKK,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAAhC;AACA,eAAKX,sBAAL,GAA8B,KAAKI,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA9B;AACA,eAAKV,uBAAL,GAA+B,KAAKG,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA/B;AACA,eAAKT,gBAAL,GAAwB,KAAKE,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAAxB;AACA,eAAKR,qBAAL,GAA6B,KAAKC,SAAL,CAAeO,GAAf,CAAmB,MAAM,CAAzB,CAA7B;AAEAJ,UAAAA,OAAO,CAACC,GAAR,CAAa,kBAAiB,KAAKJ,SAAL,CAAeS,MAAO,QAApD;AACH;;AAEDE,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKb,SAAL,CAAeS,MAAnC,EAA2CI,CAAC,EAA5C,EAAgD;AAC5C,kBAAMC,EAAE,GAAG,KAAKd,SAAL,CAAea,CAAf,CAAX;AACA,gBAAI,CAACC,EAAL,EAAS;AAET,iBAAKpB,kBAAL,CAAwBmB,CAAxB,KAA8BD,EAA9B;AACA,iBAAKb,qBAAL,CAA2Bc,CAA3B,KAAiCD,EAAjC,CAL4C,CAO5C;;AACA,gBAAI,KAAKpB,QAAL,CAAcqB,CAAd,MAAqBvB,OAAO,CAACyB,eAAjC,EAAkD;AAC9C,mBAAKC,qBAAL,CAA2BF,EAA3B,EAA+BD,CAA/B,EAAkCD,EAAlC;AACA,uBAF8C,CAEpC;AACb;;AACD,gBAAI,KAAKZ,SAAL,CAAeS,MAAf,IAAyB,CAA7B,EAAgC;AAC5B;AACC;AACD,mBAAKQ,SAAL,CAAeH,EAAf,EAAmBD,CAAnB;AACH;AAEJ;AACJ;;AAEDI,QAAAA,SAAS,CAACH,EAAD,EAAeD,CAAf,EAA0B;AAC/B;AACA;AACA,cAAIK,IAAI,CAACC,MAAL,KAAgB,KAApB,EAA2B;AAAE;AACzBL,YAAAA,EAAE,CAACM,YAAH,CAAgBF,IAAI,CAACC,MAAL,KAAgB,GAAhB,GAAsB,CAAC,CAAvB,GAA2B,CAA3C;AACH,WAL8B,CAO/B;;;AACA,cAAID,IAAI,CAACC,MAAL,KAAgB,IAApB,EAA0B;AAAE;AACxBL,YAAAA,EAAE,CAACO,QAAH,CAAYH,IAAI,CAACC,MAAL,KAAgB,GAAhB,GAAsB,CAAtB,GAA0B,CAAtC;AACH,WAV8B,CAY/B;;;AACA,gBAAMG,GAAG,GAAGR,EAAE,CAACS,IAAH,CAAQC,aAApB,CAb+B,CAe/B;;AACA,gBAAMC,SAAS,GAAGH,GAAG,CAACI,CAAJ,GAAQ,KAAKjC,cAAL,CAAoBiC,CAA9C;AACA,gBAAMC,SAAS,GAAGL,GAAG,CAACM,CAAJ,GAAQ,KAAKnC,cAAL,CAAoBmC,CAA9C,CAjB+B,CAmB/B;;AACA,gBAAMC,MAAM,GAAG,GAAf,CApB+B,CAoBX;AAEpB;;AACA,gBAAMC,gBAAgB,GAAGL,SAAS,GAAG,KAAKM,SAAL,GAAiBF,MAAtD;AACA,gBAAMG,iBAAiB,GAAGP,SAAS,GAAG,KAAKQ,SAAL,GAAiBJ,MAAvD;AACA,gBAAMK,kBAAkB,GAAGP,SAAS,GAAG,KAAKQ,SAAL,GAAiBN,MAAxD;AACA,gBAAMO,eAAe,GAAGT,SAAS,GAAG,KAAKU,SAAL,GAAiBR,MAArD,CA1B+B,CA4B/B;;AACA,cAAIC,gBAAgB,IAAIE,iBAApB,IAAyCE,kBAAzC,IAA+DE,eAAnE,EAAoF;AAChF;AACA,kBAAME,gBAAgB,GAAG,KAAKvC,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAhC,GAAoCK,IAAI,CAACqB,GAAL,CAAS,KAAKxC,qBAAL,CAA2Bc,CAA3B,CAAT,CAApC,GAA8E,GAAvG;;AACA,gBAAI,KAAKd,qBAAL,CAA2Bc,CAA3B,KAAiCyB,gBAArC,EAAuD;AAAE;AACrD;AACA,kBAAIE,YAAY,GAAG,QAAnB;AACA,kBAAIV,gBAAJ,EAAsBU,YAAY,IAAI,MAAhB;AACtB,kBAAIR,iBAAJ,EAAuBQ,YAAY,IAAI,MAAhB;AACvB,kBAAIN,kBAAJ,EAAwBM,YAAY,IAAI,MAAhB;AACxB,kBAAIJ,eAAJ,EAAqBI,YAAY,IAAI,MAAhB,CAN8B,CAOnD;;AAEA,mBAAKC,oBAAL,CAA0B3B,EAA1B,EAA8BD,CAA9B,EAAiCiB,gBAAjC,EAAmDE,iBAAnD,EAAsEE,kBAAtE,EAA0FE,eAA1F;AACH,aAVD,MAUO;AACH;AACA,oBAAME,gBAAgB,GAAG,KAAKvC,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAhC,GAAoCK,IAAI,CAACqB,GAAL,CAAS,KAAKxC,qBAAL,CAA2Bc,CAA3B,CAAT,CAApC,GAA8E,GAAvG,CAFG,CAGH;;AACAC,cAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACH;AACJ,WAnBD,MAmBO;AACH;AACAP,YAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACH;AACJ;;AAEOoB,QAAAA,oBAAoB,CAAC3B,EAAD,EAAeD,CAAf,EAA0B6B,QAA1B,EAA6CC,SAA7C,EAAiEC,UAAjE,EAAsFC,OAAtF,EAAwG;AAChI,eAAKrD,QAAL,CAAcqB,CAAd,IAAmBvB,OAAO,CAACyB,eAA3B;AACA,eAAKrB,kBAAL,CAAwBmB,CAAxB,IAA6B,CAA7B;AACA,eAAKhB,uBAAL,CAA6BgB,CAA7B,IAAkCC,EAAE,CAACgC,eAAH,EAAlC;AACA,eAAK/C,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAhC,CAJgI,CAI7F;AAEnC;;AACA,cAAIkC,WAAW,GAAG,CAAlB;AACA,cAAIC,aAAa,GAAG,CAApB;AACA,gBAAMC,YAAY,GAAGnC,EAAE,CAACgC,eAAH,EAArB,CATgI,CAWhI;;AACA,gBAAMI,QAAQ,GAAIR,QAAQ,IAAIE,UAAb,IAA6BF,QAAQ,IAAIG,OAAzC,IACAF,SAAS,IAAIC,UADb,IAC6BD,SAAS,IAAIE,OAD3D;;AAGA,cAAIK,QAAJ,EAAc;AACV;AACAH,YAAAA,WAAW,GAAG,CAACE,YAAY,GAAG,GAAhB,IAAuB,GAArC,CAFU,CAEgC;AAC1C;AACA;;AACA,iBAAKlD,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAC,GAAjC,CALU,CAK4B;AACzC,WAND,MAMO;AACH;AACA,gBAAI6B,QAAJ,EAAc;AACV;AACA,kBAAIO,YAAY,GAAG,GAAnB,EAAwB;AACpBF,gBAAAA,WAAW,GAAG7B,IAAI,CAACC,MAAL,KAAgB,EAA9B,CADoB,CACc;AACrC,eAFD,MAEO;AACH4B,gBAAAA,WAAW,GAAG,MAAM7B,IAAI,CAACC,MAAL,KAAgB,EAApC,CADG,CACqC;AAC3C;AACJ,aAPD,MAOO,IAAIwB,SAAJ,EAAe;AAClB;AACA,kBAAIM,YAAY,GAAG,GAAnB,EAAwB;AACpBF,gBAAAA,WAAW,GAAG,MAAM7B,IAAI,CAACC,MAAL,KAAgB,GAApC,CADoB,CACqB;AAC5C,eAFD,MAEO;AACH4B,gBAAAA,WAAW,GAAG7B,IAAI,CAACC,MAAL,KAAgB,GAA9B,CADG,CACgC;AACtC;AACJ,aAPM,MAOA,IAAIyB,UAAJ,EAAgB;AACnB;AACA,kBAAIK,YAAY,GAAG,EAAf,IAAqBA,YAAY,GAAG,GAAxC,EAA6C;AACzCF,gBAAAA,WAAW,GAAG7B,IAAI,CAACC,MAAL,KAAgB,EAA9B,CADyC,CACP;AACrC,eAFD,MAEO;AACH4B,gBAAAA,WAAW,GAAG,MAAM7B,IAAI,CAACC,MAAL,KAAgB,EAApC,CADG,CACqC;AAC3C;AACJ,aAPM,MAOA,IAAI0B,OAAJ,EAAa;AAChB;AACA,kBAAII,YAAY,GAAG,EAAf,IAAqBA,YAAY,GAAG,GAAxC,EAA6C;AACzCF,gBAAAA,WAAW,GAAG,KAAK7B,IAAI,CAACC,MAAL,KAAgB,GAAnC,CADyC,CACD;AAC3C,eAFD,MAEO;AACH4B,gBAAAA,WAAW,GAAG7B,IAAI,CAACC,MAAL,KAAgB,EAAhB,GAAqB,GAAnC,CADG,CACqC;AAC3C;AACJ;AACJ,WApD+H,CAsDhI;;;AACA4B,UAAAA,WAAW,GAAGA,WAAW,GAAG,GAA5B;AACA,cAAIA,WAAW,GAAG,CAAlB,EAAqBA,WAAW,IAAI,GAAf,CAxD2G,CA0DhI;;AACA,cAAII,SAAS,GAAGJ,WAAW,GAAGE,YAA9B;;AACA,iBAAOE,SAAS,GAAG,GAAnB,EAAwBA,SAAS,IAAI,GAAb;;AACxB,iBAAOA,SAAS,GAAG,CAAC,GAApB,EAAyBA,SAAS,IAAI,GAAb,CA7DuG,CA+DhI;;;AACAH,UAAAA,aAAa,GAAGG,SAAS,GAAG,CAAZ,GAAgB,CAAhB,GAAoB,CAAC,CAArC;AAEA,eAAKxD,wBAAL,CAA8BkB,CAA9B,IAAmCkC,WAAnC;AACA,eAAKnD,sBAAL,CAA4BiB,CAA5B,IAAiCmC,aAAjC,CAnEgI,CAqEhI;;AACAlC,UAAAA,EAAE,CAACsC,cAAH,CAAkBL,WAAlB;AACAjC,UAAAA,EAAE,CAACM,YAAH,CAAgB4B,aAAhB;AACAlC,UAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ,EAxEgI,CAwEhH;AAEhB;AACH;;AAEOL,QAAAA,qBAAqB,CAACF,EAAD,EAAeD,CAAf,EAA0BD,EAA1B,EAAsC;AAC/D,gBAAMqC,YAAY,GAAGnC,EAAE,CAACgC,eAAH,EAArB;AACA,gBAAMC,WAAW,GAAG,KAAKpD,wBAAL,CAA8BkB,CAA9B,CAApB;AACA,gBAAMwC,UAAU,GAAG,KAAKxD,uBAAL,CAA6BgB,CAA7B,CAAnB,CAH+D,CAK/D;;AACA,cAAIyC,WAAW,GAAGL,YAAY,GAAGI,UAAjC;;AACA,iBAAOC,WAAW,GAAG,GAArB,EAA0BA,WAAW,IAAI,GAAf;;AAC1B,iBAAOA,WAAW,GAAG,CAAC,GAAtB,EAA2BA,WAAW,IAAI,GAAf,CARoC,CAU/D;;;AACA,cAAIC,aAAa,GAAGR,WAAW,GAAGE,YAAlC;;AACA,iBAAOM,aAAa,GAAG,GAAvB,EAA4BA,aAAa,IAAI,GAAjB;;AAC5B,iBAAOA,aAAa,GAAG,CAAC,GAAxB,EAA6BA,aAAa,IAAI,GAAjB,CAbkC,CAe/D;;;AACA,gBAAMC,WAAW,GAAGtC,IAAI,CAACqB,GAAL,CAASgB,aAAa,GAAG,KAAKzD,gBAAL,CAAsBe,CAAtB,CAAzB,CAApB;;AACA,cAAI2C,WAAW,GAAG,CAAd,IAAmBtC,IAAI,CAACuC,IAAL,CAAUF,aAAV,MAA6BrC,IAAI,CAACuC,IAAL,CAAU,KAAK3D,gBAAL,CAAsBe,CAAtB,CAAV,CAApD,EAAyF;AACrFC,YAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACAN,YAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACA,iBAAK7B,QAAL,CAAcqB,CAAd,IAAmBvB,OAAO,CAACkB,SAA3B;AACA,iBAAKT,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAhC,CAJqF,CAIlD;AACnC;;AACA;AACH;;AACD,eAAKf,gBAAL,CAAsBe,CAAtB,IAA2B0C,aAA3B,CAzB+D,CA2B/D;;AACA,cAAIrC,IAAI,CAACqB,GAAL,CAASgB,aAAT,IAA0B,EAA9B,EAAkC;AAAE;AAChCzC,YAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACAN,YAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACA,iBAAK7B,QAAL,CAAcqB,CAAd,IAAmBvB,OAAO,CAACkB,SAA3B;AACA,iBAAKT,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAhC,CAJ8B,CAIK;AACnC;;AACA;AACH,WAnC8D,CAqC/D;;;AACA,cAAIK,IAAI,CAACqB,GAAL,CAASe,WAAT,KAAyB,EAA7B,EAAiC;AAC7B;AACA,kBAAMhC,GAAG,GAAGR,EAAE,CAACS,IAAH,CAAQC,aAApB;AACA,kBAAMC,SAAS,GAAGH,GAAG,CAACI,CAAJ,GAAQ,KAAKjC,cAAL,CAAoBiC,CAA9C;AACA,kBAAMC,SAAS,GAAGL,GAAG,CAACM,CAAJ,GAAQ,KAAKnC,cAAL,CAAoBmC,CAA9C,CAJ6B,CAM7B;;AACA,gBAAIH,SAAS,GAAG,KAAKM,SAAL,GAAiB,GAA7B,IAAoCN,SAAS,GAAG,KAAKQ,SAAL,GAAiB,GAAjE,IACAN,SAAS,GAAG,KAAKQ,SAAL,GAAiB,GAD7B,IACoCR,SAAS,GAAG,KAAKU,SAAL,GAAiB,GADrE,EAC0E;AACtEvB,cAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACAN,cAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACA,mBAAK7B,QAAL,CAAcqB,CAAd,IAAmBvB,OAAO,CAACkB,SAA3B;AACA,mBAAKT,qBAAL,CAA2Bc,CAA3B,IAAgC,CAAhC,CAJsE,CAInC;AACnC;;AACA;AACH;AACJ,WAtD8D,CAwD/D;;;AACA,cAAI,KAAKnB,kBAAL,CAAwBmB,CAAxB,IAA6B,CAAjC,EAAoC;AAChCC,YAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACAN,YAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACA,iBAAK7B,QAAL,CAAcqB,CAAd,IAAmBvB,OAAO,CAACkB,SAA3B,CAHgC,CAIhC;;AACA;AACH,WA/D8D,CAiE/D;;;AACA,gBAAMkD,gBAAgB,GAAGxC,IAAI,CAACqB,GAAL,CAASgB,aAAT,CAAzB,CAlE+D,CAoE/D;AACA;AACA;AACA;;AAEA,cAAIG,gBAAgB,GAAG,EAAvB,EAA2B;AACvB;AACA5C,YAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACAN,YAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACH,WAJD,MAIO;AACH;AACA,gBAAIqC,gBAAgB,GAAG,EAAvB,EAA2B;AACvB;AACA5C,cAAAA,EAAE,CAACM,YAAH,CAAgB,KAAKxB,sBAAL,CAA4BiB,CAA5B,CAAhB;AACH,aAHD,MAGO,IAAI6C,gBAAgB,GAAG,EAAvB,EAA2B;AAC9B;AACA5C,cAAAA,EAAE,CAACM,YAAH,CAAgB,KAAKxB,sBAAL,CAA4BiB,CAA5B,CAAhB;AACH,aAHM,MAGA,IAAI6C,gBAAgB,GAAG,EAAvB,EAA2B;AAC9B;AACA,oBAAMC,aAAa,GAAG,IAAtB,CAF8B,CAEF;;AAC5B,oBAAMC,OAAO,GAAG,IAAhB,CAH8B,CAGR;;AACtB,oBAAMC,WAAW,GAAG,KAAKnE,kBAAL,CAAwBmB,CAAxB,IAA6B8C,aAAjD;;AAEA,kBAAIE,WAAW,GAAGD,OAAlB,EAA2B;AACvB9C,gBAAAA,EAAE,CAACM,YAAH,CAAgB,KAAKxB,sBAAL,CAA4BiB,CAA5B,CAAhB;AACH,eAFD,MAEO;AACHC,gBAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACH;AACJ,aAXM,MAWA;AACH;AACAN,cAAAA,EAAE,CAACM,YAAH,CAAgB,CAAhB;AACH;;AACDN,YAAAA,EAAE,CAACO,QAAH,CAAY,CAAZ;AACH;AACJ,SA9TuC,CAgUxC;;AACA;AACJ;AACA;;;AACWyC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,MAAlB,EAAkC;AAChD,cAAID,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,KAAK/D,SAAL,CAAeS,MAA7C,EAAqD;AACjD,kBAAMK,EAAE,GAAG,KAAKd,SAAL,CAAe+D,OAAf,CAAX;;AACA,gBAAIjD,EAAE,IAAIA,EAAE,CAACS,IAAT,IAAiBT,EAAE,CAACS,IAAH,CAAQ0C,OAA7B,EAAsC;AAClCnD,cAAAA,EAAE,CAACoD,SAAH,CAAaF,MAAb;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWG,QAAAA,QAAQ,CAACJ,OAAD,EAAkBK,MAAlB,EAAkC;AAC7C,cAAIL,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,KAAK/D,SAAL,CAAeS,MAA7C,EAAqD;AACjD,kBAAMK,EAAE,GAAG,KAAKd,SAAL,CAAe+D,OAAf,CAAX;;AACA,gBAAIjD,EAAE,IAAIA,EAAE,CAACS,IAAT,IAAiBT,EAAE,CAACS,IAAH,CAAQ0C,OAA7B,EAAsC;AAClCnD,cAAAA,EAAE,CAACuD,UAAH,CAAcD,MAAd;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWE,QAAAA,MAAM,CAACP,OAAD,EAAkBQ,MAAlB,EAAkC;AAC3C,cAAIR,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,KAAK/D,SAAL,CAAeS,MAA7C,EAAqD;AACjD,kBAAMK,EAAE,GAAG,KAAKd,SAAL,CAAe+D,OAAf,CAAX;;AACA,gBAAIjD,EAAE,IAAIA,EAAE,CAACS,IAAT,IAAiBT,EAAE,CAACS,IAAH,CAAQ0C,OAA7B,EAAsC;AAClCnD,cAAAA,EAAE,CAAC0D,IAAH,CAAQD,MAAR;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWE,QAAAA,WAAW,CAACV,OAAD,EAA0B;AACxC,cAAIA,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,KAAK/D,SAAL,CAAeS,MAA7C,EAAqD;AACjD,kBAAMK,EAAE,GAAG,KAAKd,SAAL,CAAe+D,OAAf,CAAX;;AACA,gBAAIjD,EAAE,IAAIA,EAAE,CAACS,IAAT,IAAiBT,EAAE,CAACS,IAAH,CAAQ0C,OAA7B,EAAsC;AAClC,qBAAOnD,EAAE,CAAC4D,SAAH,EAAP;AACH;AACJ;;AACD,iBAAO,CAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,cAAc,CAACZ,OAAD,EAA0B;AAC3C,cAAIA,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,KAAK/D,SAAL,CAAeS,MAA7C,EAAqD;AACjD,kBAAMK,EAAE,GAAG,KAAKd,SAAL,CAAe+D,OAAf,CAAX;;AACA,gBAAIjD,EAAE,IAAIA,EAAE,CAACS,IAAT,IAAiBT,EAAE,CAACS,IAAH,CAAQ0C,OAA7B,EAAsC;AAClC,qBAAOnD,EAAE,CAAC8D,YAAH,EAAP;AACH;AACJ;;AACD,iBAAO,CAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,QAAQ,CAACd,OAAD,EAA2B;AACtC,cAAIA,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,KAAK/D,SAAL,CAAeS,MAA7C,EAAqD;AACjD,kBAAMK,EAAE,GAAG,KAAKd,SAAL,CAAe+D,OAAf,CAAX;;AACA,gBAAIjD,EAAE,IAAIA,EAAE,CAACS,IAAT,IAAiBT,EAAE,CAACS,IAAH,CAAQ0C,OAA7B,EAAsC;AAClC,qBAAOnD,EAAE,CAACgE,MAAH,EAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,mBAAmB,CAACC,QAAD,EAA8B;AACpD,gBAAMC,KAAK,GAAG,KAAKjF,SAAL,CAAekF,OAAf,CAAuBF,QAAvB,CAAd;;AACA,cAAIC,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,mBAAO,KAAP;AACH;;AACD,iBAAO,KAAKzF,QAAL,CAAcyF,KAAd,MAAyB3F,OAAO,CAACyB,eAAxC;AACH;;AAvZuC,O,4EAIvC1B,Q;;;;;iBACmB,CAAC,M;;oFACpBA,Q;;;;;iBACmB,M;;oFACnBA,Q;;;;;iBACmB,CAAC,K;;oFACpBA,Q;;;;;iBACmB,M", "sourcesContent": ["import { _decorator, Component, Node, Vec2, math, SkeletalAnimationComponent } from 'cc';\nimport { AIPlayer } from './AIPlayer';\nimport { GameManager } from './GameManager';\nconst { ccclass, property } = _decorator;\n\nenum AIState {\n    FreeDrive,\n    BoundaryTurning\n}\n\n@ccclass('AIController')\nexport class AIController extends Component {\n    // @property([AIPlayer])\n    // aiPlayers: AIPlayer[] = [];\n\n    @property\n    sceneMinX: number = -1048.5; // 左边界：640 - 3377/2\n    @property\n    sceneMaxX: number = 2328.5;  // 右边界：640 + 3377/2\n    @property\n    sceneMinY: number = -692.5;  // 下边界：360 - 2105/2\n    @property\n    sceneMaxY: number = 1412.5;  // 上边界：360 + 2105/2\n\n    private aiStates: AIState[] = [];\n    private canvasWorldPos: Vec2 = new Vec2();\n    private boundaryTurnTimers: number[] = []; // 边界转向计时器\n    private boundaryTurnTargetAngles: number[] = []; // 目标转向角度\n    private boundaryTurnDirections: number[] = []; // 转向方向\n    private boundaryTurnStartAngles: number[] = []; // 开始转向时的角度\n    private lastAngleChanges: number[] = []; // 记录上次角度变化，防止抖动\n    private boundaryTurnCooldowns: number[] = []; // 边界转向冷却时间\n    private aiPlayers: AIPlayer[] = [];\n\n    start() {\n        // 不再自动查找AI车辆，全部通过GameManager单例获取\n    }\n\n    /**\n     * 场景预制体加载完成后调用此方法\n     * 由GameManager在场景预制体加载完成后调用\n     */\n    public onScenePrefabLoaded() {\n        console.log('场景预制体加载完成，通过GameManager获取AI车辆列表...');\n        this.aiPlayers = GameManager.getInstance().getAIPlayers();\n        // 初始化状态数组\n        this.aiStates = this.aiPlayers.map(() => AIState.FreeDrive);\n        this.boundaryTurnTimers = this.aiPlayers.map(() => 0);\n        this.boundaryTurnTargetAngles = this.aiPlayers.map(() => 0);\n        this.boundaryTurnDirections = this.aiPlayers.map(() => 0);\n        this.boundaryTurnStartAngles = this.aiPlayers.map(() => 0);\n        this.lastAngleChanges = this.aiPlayers.map(() => 0);\n        this.boundaryTurnCooldowns = this.aiPlayers.map(() => 0);\n        console.log(`AI车辆查找完成，找到 ${this.aiPlayers.length} 个AI车辆`);\n    }\n\n    /**\n     * 公共方法：重新查找AI车辆（用于动态加载场景时）\n     */\n    public refreshAIPlayers() {\n        this.aiPlayers = GameManager.getInstance().getAIPlayers();\n        \n        // 重新初始化状态数组\n        this.aiStates = this.aiPlayers.map(() => AIState.FreeDrive);\n        this.boundaryTurnTimers = this.aiPlayers.map(() => 0);\n        this.boundaryTurnTargetAngles = this.aiPlayers.map(() => 0);\n        this.boundaryTurnDirections = this.aiPlayers.map(() => 0);\n        this.boundaryTurnStartAngles = this.aiPlayers.map(() => 0);\n        this.lastAngleChanges = this.aiPlayers.map(() => 0);\n        this.boundaryTurnCooldowns = this.aiPlayers.map(() => 0);\n        \n        console.log(`AI车辆列表已刷新，当前共有 ${this.aiPlayers.length} 个AI车辆`);\n    }\n\n    update(dt: number) {\n        for (let i = 0; i < this.aiPlayers.length; i++) {\n            const ai = this.aiPlayers[i];\n            if (!ai) continue;\n\n            this.boundaryTurnTimers[i] += dt;\n            this.boundaryTurnCooldowns[i] += dt;\n\n            // 处理边界转向状态\n            if (this.aiStates[i] === AIState.BoundaryTurning) {\n                this.handleBoundaryTurning(ai, i, dt);\n                continue; // 跳过其他状态处理\n            }\n            if (this.aiPlayers.length != 0) {\n                // console.log(\"AI车辆数量：\",this.aiPlayers.length);\n                 // 自由驾驶状态\n                this.freeDrive(ai, i);\n            }\n           \n        }\n    }\n\n    freeDrive(ai: AIPlayer, i: number) {\n        // console.log(ai.getCurrentAngle());\n        // 随机改变方向\n        if (Math.random() < 0.005) { // 降低频率\n            ai.setDirection(Math.random() < 0.5 ? -1 : 1);\n        }\n        \n        // 随机加速\n        if (Math.random() < 0.01) { // 降低频率\n            ai.setAccel(Math.random() < 0.8 ? 1 : 0);\n        }\n        \n        // 边界检测和转向\n        const pos = ai.node.worldPosition;\n        \n        // 计算相对于Canvas中心的位置\n        const relativeX = pos.x - this.canvasWorldPos.x;\n        const relativeY = pos.y - this.canvasWorldPos.y;\n        \n        // 计算到边界的距离\n        const margin = 400; // 边界检测距离\n        \n        // 检查是否接近边界\n        const nearLeftBoundary = relativeX < this.sceneMinX + margin;\n        const nearRightBoundary = relativeX > this.sceneMaxX - margin;\n        const nearBottomBoundary = relativeY < this.sceneMinY + margin;\n        const nearTopBoundary = relativeY > this.sceneMaxY - margin;\n        \n        // 如果接近边界，开始边界转向（需要检查冷却时间）\n        if (nearLeftBoundary || nearRightBoundary || nearBottomBoundary || nearTopBoundary) {\n            // 检查冷却时间，避免频繁触发转向\n            const requiredCooldown = this.boundaryTurnCooldowns[i] < 0 ? Math.abs(this.boundaryTurnCooldowns[i]) : 3.0;\n            if (this.boundaryTurnCooldowns[i] >= requiredCooldown) { // 动态冷却时间\n                // 打印触发的边界位置\n                let boundaryInfo = \"触发边界: \";\n                if (nearLeftBoundary) boundaryInfo += \"左边界 \";\n                if (nearRightBoundary) boundaryInfo += \"右边界 \";\n                if (nearBottomBoundary) boundaryInfo += \"下边界 \";\n                if (nearTopBoundary) boundaryInfo += \"上边界 \";\n                // console.log(boundaryInfo + `位置(${relativeX.toFixed(1)}, ${relativeY.toFixed(1)})`);\n                \n                this.startBoundaryTurning(ai, i, nearLeftBoundary, nearRightBoundary, nearBottomBoundary, nearTopBoundary);\n            } else {\n                // 在冷却时间内，继续正常行驶\n                const requiredCooldown = this.boundaryTurnCooldowns[i] < 0 ? Math.abs(this.boundaryTurnCooldowns[i]) : 3.0;\n                // console.log(`冷却中: ${this.boundaryTurnCooldowns[i].toFixed(1)}s/${requiredCooldown.toFixed(1)}s`);\n                ai.setAccel(1);\n            }\n        } else {\n            // 不在边界附近，正常行驶\n            ai.setAccel(1);\n        }\n    }\n    \n    private startBoundaryTurning(ai: AIPlayer, i: number, nearLeft: boolean, nearRight: boolean, nearBottom: boolean, nearTop: boolean) {\n        this.aiStates[i] = AIState.BoundaryTurning;\n        this.boundaryTurnTimers[i] = 0;\n        this.boundaryTurnStartAngles[i] = ai.getCurrentAngle();\n        this.boundaryTurnCooldowns[i] = 0; // 重置冷却时间\n        \n        // 根据触发的边界确定目标方向\n        let targetAngle = 0;\n        let turnDirection = 0;\n        const currentAngle = ai.getCurrentAngle();\n        \n        // 优先处理角落情况，避免判断混乱\n        const isCorner = (nearLeft && nearBottom) || (nearLeft && nearTop) || \n                        (nearRight && nearBottom) || (nearRight && nearTop);\n        \n        if (isCorner) {\n            // 角落情况：转向原朝向的反方向\n            targetAngle = (currentAngle + 180) % 360; // 直接转向反方向\n            // console.log(`检测到角落，当前角度=${currentAngle.toFixed(1)}°，转向反方向=${targetAngle.toFixed(1)}°`);\n            // 角落转向需要更长的冷却时间\n            this.boundaryTurnCooldowns[i] = -5.0; // 3秒冷却时间\n        } else {\n            // 单边界情况：更智能的目标角度选择，考虑当前朝向\n            if (nearLeft) {\n                // 左边界：转向右方，避免选择与当前角度相近的方向\n                if (currentAngle > 180) {\n                    targetAngle = Math.random() * 90; // 0-90度\n                } else {\n                    targetAngle = 270 + Math.random() * 90; // 270-360度\n                }\n            } else if (nearRight) {\n                // 右边界：转向左方，避免选择与当前角度相近的方向\n                if (currentAngle < 180) {\n                    targetAngle = 180 + Math.random() * 180; // 180-360度\n                } else {\n                    targetAngle = Math.random() * 180; // 0-180度\n                }\n            } else if (nearBottom) {\n                // 下边界：转向上方，避免选择与当前角度相近的方向\n                if (currentAngle > 90 && currentAngle < 270) {\n                    targetAngle = Math.random() * 90; // 0-90度\n                } else {\n                    targetAngle = 270 + Math.random() * 90; // 270-360度\n                }\n            } else if (nearTop) {\n                // 上边界：转向下方，避免选择与当前角度相近的方向\n                if (currentAngle < 90 || currentAngle > 270) {\n                    targetAngle = 90 + Math.random() * 180; // 90-270度\n                } else {\n                    targetAngle = Math.random() * 90 + 270; // 270-360度\n                }\n            }\n        }\n        \n        // 确保目标角度在0-360范围内\n        targetAngle = targetAngle % 360;\n        if (targetAngle < 0) targetAngle += 360;\n        \n        // 计算当前角度到目标角度的最短路径\n        let angleDiff = targetAngle - currentAngle;\n        while (angleDiff > 180) angleDiff -= 360;\n        while (angleDiff < -180) angleDiff += 360;\n        \n        // 确定转向方向\n        turnDirection = angleDiff > 0 ? 1 : -1;\n        \n        this.boundaryTurnTargetAngles[i] = targetAngle;\n        this.boundaryTurnDirections[i] = turnDirection;\n        \n        // 直接设置目标角度，让车辆立即朝向安全方向\n        ai.setTargetAngle(targetAngle);\n        ai.setDirection(turnDirection);\n        ai.setAccel(1); // 保持全速，产生自然漂移\n        \n        // console.log(`开始边界转向: 当前角度=${currentAngle.toFixed(1)}°, 目标角度=${targetAngle.toFixed(1)}°, 角度差=${angleDiff.toFixed(1)}°, 方向=${turnDirection > 0 ? '左' : '右'}`);\n    }\n    \n    private handleBoundaryTurning(ai: AIPlayer, i: number, dt: number) {\n        const currentAngle = ai.getCurrentAngle();\n        const targetAngle = this.boundaryTurnTargetAngles[i];\n        const startAngle = this.boundaryTurnStartAngles[i];\n        \n        // 计算已经转向的角度\n        let angleTurned = currentAngle - startAngle;\n        while (angleTurned > 180) angleTurned -= 360;\n        while (angleTurned < -180) angleTurned += 360;\n        \n        // 计算到目标角度的距离\n        let angleToTarget = targetAngle - currentAngle;\n        while (angleToTarget > 180) angleToTarget -= 360;\n        while (angleToTarget < -180) angleToTarget += 360;\n        \n        // 防抖动：如果角度变化很小且与上次变化方向相反，则停止转向\n        const angleChange = Math.abs(angleToTarget - this.lastAngleChanges[i]);\n        if (angleChange < 5 && Math.sign(angleToTarget) !== Math.sign(this.lastAngleChanges[i])) {\n            ai.setDirection(0);\n            ai.setAccel(1);\n            this.aiStates[i] = AIState.FreeDrive;\n            this.boundaryTurnCooldowns[i] = 0; // 重置冷却时间\n            // console.log(`边界转向完成: 防抖动触发，角度变化=${angleChange.toFixed(1)}°`);\n            return;\n        }\n        this.lastAngleChanges[i] = angleToTarget;\n        \n        // 检查是否达到目标角度（更宽松的容差）\n        if (Math.abs(angleToTarget) < 30) { // 30度容差，更宽松\n            ai.setDirection(0);\n            ai.setAccel(1);\n            this.aiStates[i] = AIState.FreeDrive;\n            this.boundaryTurnCooldowns[i] = 0; // 重置冷却时间\n            // console.log(`边界转向完成: 到达目标角度=${targetAngle.toFixed(1)}°, 当前角度=${currentAngle.toFixed(1)}°`);\n            return;\n        }\n        \n        // 检查是否已经转向足够的角度（至少30度）\n        if (Math.abs(angleTurned) >= 30) {\n            // 检查当前朝向是否安全（远离边界）\n            const pos = ai.node.worldPosition;\n            const relativeX = pos.x - this.canvasWorldPos.x;\n            const relativeY = pos.y - this.canvasWorldPos.y;\n            \n            // 如果已经转向足够角度且位置安全，停止转向\n            if (relativeX > this.sceneMinX + 400 && relativeX < this.sceneMaxX - 400 &&\n                relativeY > this.sceneMinY + 400 && relativeY < this.sceneMaxY - 400) {\n                ai.setDirection(0);\n                ai.setAccel(1);\n                this.aiStates[i] = AIState.FreeDrive;\n                this.boundaryTurnCooldowns[i] = 0; // 重置冷却时间\n                // console.log(`边界转向完成: 已转向${Math.abs(angleTurned).toFixed(1)}°, 位置安全`);\n                return;\n            }\n        }\n        \n        // 防止转向时间过长（超过3秒）\n        if (this.boundaryTurnTimers[i] > 3) {\n            ai.setDirection(0);\n            ai.setAccel(1);\n            this.aiStates[i] = AIState.FreeDrive;\n            // console.log('边界转向超时，强制完成');\n            return;\n        }\n        \n        // 智能转向控制：根据角度差动态调整转向\n        const absAngleToTarget = Math.abs(angleToTarget);\n        \n        // 添加调试信息\n        // if (this.boundaryTurnTimers[i] % 0.5 < 0.1) { // 每0.5秒打印一次调试信息\n        //     console.log(`转向调试: 当前=${currentAngle.toFixed(1)}°, 目标=${targetAngle.toFixed(1)}°, 角度差=${angleToTarget.toFixed(1)}°, 已转向=${angleTurned.toFixed(1)}°`);\n        // }\n        \n        if (absAngleToTarget < 30) {\n            // 接近目标角度时，停止转向，让车辆自然朝向目标\n            ai.setDirection(0);\n            ai.setAccel(1);\n        } else {\n            // 改进的转向策略：根据角度差调整转向强度\n            if (absAngleToTarget > 90) {\n                // 大角度差：持续转向\n                ai.setDirection(this.boundaryTurnDirections[i]);\n            } else if (absAngleToTarget > 60) {\n                // 中等角度差：持续转向，但稍微减弱\n                ai.setDirection(this.boundaryTurnDirections[i]);\n            } else if (absAngleToTarget > 30) {\n                // 小角度差：脉冲式转向，但更稳定\n                const pulseInterval = 0.12; // 120ms脉冲\n                const pulseOn = 0.08; // 80ms开启\n                const timeInPulse = this.boundaryTurnTimers[i] % pulseInterval;\n                \n                if (timeInPulse < pulseOn) {\n                    ai.setDirection(this.boundaryTurnDirections[i]);\n                } else {\n                    ai.setDirection(0);\n                }\n            } else {\n                // 很小角度差：停止转向，避免来回摆动\n                ai.setDirection(0);\n            }\n            ai.setAccel(1);\n        }\n    }\n\n    // 血量管理接口\n    /**\n     * 为指定AI车辆设置生命值\n     */\n    public setAIHealth(aiIndex: number, health: number) {\n        if (aiIndex >= 0 && aiIndex < this.aiPlayers.length) {\n            const ai = this.aiPlayers[aiIndex];\n            if (ai && ai.node && ai.node.isValid) {\n                ai.setHealth(health);\n            }\n        }\n    }\n\n    /**\n     * 为指定AI车辆造成伤害\n     */\n    public damageAI(aiIndex: number, damage: number) {\n        if (aiIndex >= 0 && aiIndex < this.aiPlayers.length) {\n            const ai = this.aiPlayers[aiIndex];\n            if (ai && ai.node && ai.node.isValid) {\n                ai.takeDamage(damage);\n            }\n        }\n    }\n\n    /**\n     * 为指定AI车辆恢复生命值\n     */\n    public healAI(aiIndex: number, amount: number) {\n        if (aiIndex >= 0 && aiIndex < this.aiPlayers.length) {\n            const ai = this.aiPlayers[aiIndex];\n            if (ai && ai.node && ai.node.isValid) {\n                ai.heal(amount);\n            }\n        }\n    }\n\n    /**\n     * 获取指定AI车辆的生命值\n     */\n    public getAIHealth(aiIndex: number): number {\n        if (aiIndex >= 0 && aiIndex < this.aiPlayers.length) {\n            const ai = this.aiPlayers[aiIndex];\n            if (ai && ai.node && ai.node.isValid) {\n                return ai.getHealth();\n            }\n        }\n        return 0;\n    }\n\n    /**\n     * 获取指定AI车辆的最大生命值\n     */\n    public getAIMaxHealth(aiIndex: number): number {\n        if (aiIndex >= 0 && aiIndex < this.aiPlayers.length) {\n            const ai = this.aiPlayers[aiIndex];\n            if (ai && ai.node && ai.node.isValid) {\n                return ai.getMaxHealth();\n            }\n        }\n        return 0;\n    }\n\n    /**\n     * 检查指定AI车辆是否死亡\n     */\n    public isAIDead(aiIndex: number): boolean {\n        if (aiIndex >= 0 && aiIndex < this.aiPlayers.length) {\n            const ai = this.aiPlayers[aiIndex];\n            if (ai && ai.node && ai.node.isValid) {\n                return ai.isDead();\n            }\n        }\n        return true;\n    }\n\n    /**\n     * 检查指定AI是否处于边界转向状态\n     * @param aiPlayer AI玩家实例\n     * @returns 是否处于边界转向状态\n     */\n    public isAIBoundaryTurning(aiPlayer: AIPlayer): boolean {\n        const index = this.aiPlayers.indexOf(aiPlayer);\n        if (index === -1) {\n            return false;\n        }\n        return this.aiStates[index] === AIState.BoundaryTurning;\n    }\n} "]}