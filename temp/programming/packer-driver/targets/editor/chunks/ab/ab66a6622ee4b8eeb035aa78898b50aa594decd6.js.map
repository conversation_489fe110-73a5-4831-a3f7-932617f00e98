{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"], "names": ["_decorator", "Component", "AudioSource", "director", "sys", "ccclass", "property", "SoundManager", "type", "tooltip", "allAudioSources", "instance", "_instance", "onLoad", "destroy", "addPersistRootNode", "node", "push", "bgmAudioSource", "buttonClickAudioSource", "carCollisionAudioSource", "carDestructionAudioSource", "carStartAudioSource", "carAccelerateAudioSource", "carDriftAudioSource", "loadState", "start", "playBGM", "play", "playSoundEffect", "soundName", "sourceToPlay", "toggleAudio", "muted", "isMuted", "newVolume", "for<PERSON>ach", "source", "volume", "saveState", "state", "localStorage", "setItem", "JSON", "stringify", "stateStr", "getItem", "parse"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA4BC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,G,OAAAA,G;;;;;;;;;OAClE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;8BAGjBO,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,sCAtCb,MACaF,YADb,SACkCN,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA2ChCS,eA3CgC,GA2CC,EA3CD;AAAA;;AA+Cd,mBAARC,QAAQ,GAAiB;AACvC,iBAAO,KAAKC,SAAZ;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAIN,YAAY,CAACK,SAAjB,EAA4B;AACxB,iBAAKE,OAAL;AACA;AACH;;AACDP,UAAAA,YAAY,CAACK,SAAb,GAAyB,IAAzB;AACAT,UAAAA,QAAQ,CAACY,kBAAT,CAA4B,KAAKC,IAAjC,EANK,CAQL;;AACA,eAAKN,eAAL,CAAqBO,IAArB,CAA0B,KAAKC,cAA/B;AACA,eAAKR,eAAL,CAAqBO,IAArB,CAA0B,KAAKE,sBAA/B;AACA,eAAKT,eAAL,CAAqBO,IAArB,CAA0B,KAAKG,uBAA/B;AACA,eAAKV,eAAL,CAAqBO,IAArB,CAA0B,KAAKI,yBAA/B;AACA,eAAKX,eAAL,CAAqBO,IAArB,CAA0B,KAAKK,mBAA/B;AACA,eAAKZ,eAAL,CAAqBO,IAArB,CAA0B,KAAKM,wBAA/B;AACA,eAAKb,eAAL,CAAqBO,IAArB,CAA0B,KAAKO,mBAA/B;AAEA,eAAKC,SAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKC,OAAL;AACH;;AAEDA,QAAAA,OAAO,GAAG;AACN,cAAI,KAAKT,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBU,IAApB;AACH;AACJ;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAIC,YAAgC,GAAG,IAAvC;;AACA,kBAAQD,SAAR;AACI,iBAAK,aAAL;AACIC,cAAAA,YAAY,GAAG,KAAKZ,sBAApB;AACA;;AACJ,iBAAK,cAAL;AACIY,cAAAA,YAAY,GAAG,KAAKX,uBAApB;AACA;;AACJ,iBAAK,gBAAL;AACIW,cAAAA,YAAY,GAAG,KAAKV,yBAApB;AACA;;AACJ,iBAAK,UAAL;AACIU,cAAAA,YAAY,GAAG,KAAKT,mBAApB;AACA;;AACJ,iBAAK,eAAL;AACIS,cAAAA,YAAY,GAAG,KAAKR,wBAApB;AACA;;AACJ,iBAAK,UAAL;AACIQ,cAAAA,YAAY,GAAG,KAAKP,mBAApB;AACA;AAlBR;;AAqBA,cAAIO,YAAJ,EAAkB;AACdA,YAAAA,YAAY,CAACH,IAAb;AACH;AACJ;;AAEDI,QAAAA,WAAW,GAAG;AACV,gBAAMC,KAAK,GAAG,KAAKC,OAAL,EAAd;AACA,gBAAMC,SAAS,GAAGF,KAAK,GAAG,CAAH,GAAO,CAA9B;AACA,eAAKvB,eAAL,CAAqB0B,OAArB,CAA6BC,MAAM,IAAI;AACnC,gBAAIA,MAAM,KAAG,KAAKnB,cAAlB,EAAkC;AAC1BmB,cAAAA,MAAM,CAACC,MAAP,GAAgBH,SAAS,GAAG,GAA5B;AACH,aAFL,MAGS,IAAIE,MAAJ,EAAY;AACbA,cAAAA,MAAM,CAACC,MAAP,GAAgBH,SAAhB;AACH;AACR,WAPD;AAQA,eAAKI,SAAL;AACH;;AAEDL,QAAAA,OAAO,GAAY;AACf;AACA,iBAAO,KAAKhB,cAAL,GAAsB,KAAKA,cAAL,CAAoBoB,MAApB,KAA+B,CAArD,GAAyD,KAAhE;AACH;;AAEOC,QAAAA,SAAS,GAAG;AAChB,gBAAMC,KAAK,GAAG;AACVP,YAAAA,KAAK,EAAE,KAAKC,OAAL;AADG,WAAd;AAGA9B,UAAAA,GAAG,CAACqC,YAAJ,CAAiBC,OAAjB,CAAyB,YAAzB,EAAuCC,IAAI,CAACC,SAAL,CAAeJ,KAAf,CAAvC;AACH;;AAEOf,QAAAA,SAAS,GAAG;AAChB,gBAAMoB,QAAQ,GAAGzC,GAAG,CAACqC,YAAJ,CAAiBK,OAAjB,CAAyB,YAAzB,CAAjB;;AACA,cAAID,QAAJ,EAAc;AACV,kBAAML,KAAK,GAAGG,IAAI,CAACI,KAAL,CAAWF,QAAX,CAAd;AACA,kBAAMP,MAAM,GAAGE,KAAK,CAACP,KAAN,GAAc,CAAd,GAAkB,CAAjC;AACA,iBAAKvB,eAAL,CAAqB0B,OAArB,CAA6BC,MAAM,IAAI;AACnC,kBAAIA,MAAM,KAAG,KAAKnB,cAAlB,EAAkC;AAC9BmB,gBAAAA,MAAM,CAACC,MAAP,GAAgBA,MAAM,GAAG,GAAzB;AACH,eAFD,MAGK,IAAID,MAAJ,EAAY;AACbA,gBAAAA,MAAM,CAACC,MAAP,GAAgBA,MAAhB;AACH;AACJ,aAPD;AAQH;AACJ;;AArJuC,O,UA6CzB1B,S,GAA0B,I;;;;;iBAxCJ,I;;;;;;;iBAMQ,I;;;;;;;iBAMC,I;;;;;;;iBAME,I;;;;;;;iBAMN,I;;;;;;;iBAMK,I;;;;;;;iBAML,I", "sourcesContent": ["import { _decorator, Component, Node, AudioClip, AudioSource, director, sys } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('SoundManager')\nexport class SoundManager extends Component {\n    @property({\n        type: AudioSource,\n        tooltip: \"背景音乐\"\n    })\n    public bgmAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"按钮点击音效\"\n    })\n    public buttonClickAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆碰撞音效\"\n    })\n    public carCollisionAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆毁坏音效\"\n    })\n    public carDestructionAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆启动音效\"\n    })\n    public carStartAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆加速音效\"\n    })\n    public carAccelerateAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆漂移音效\"\n    })\n    public carDriftAudioSource: AudioSource = null!;\n\n    private allAudioSources: AudioSource[] = [];\n\n    private static _instance: SoundManager = null!;\n\n    public static get instance(): SoundManager {\n        return this._instance;\n    }\n\n    onLoad() {\n        if (SoundManager._instance) {\n            this.destroy();\n            return;\n        }\n        SoundManager._instance = this;\n        director.addPersistRootNode(this.node);\n\n        // 将所有音源收集到一个数组中，方便统一管理\n        this.allAudioSources.push(this.bgmAudioSource);\n        this.allAudioSources.push(this.buttonClickAudioSource);\n        this.allAudioSources.push(this.carCollisionAudioSource);\n        this.allAudioSources.push(this.carDestructionAudioSource);\n        this.allAudioSources.push(this.carStartAudioSource);\n        this.allAudioSources.push(this.carAccelerateAudioSource);\n        this.allAudioSources.push(this.carDriftAudioSource);\n\n        this.loadState();\n    }\n\n    start() {\n        this.playBGM();\n    }\n\n    playBGM() {\n        if (this.bgmAudioSource) {\n            this.bgmAudioSource.play();\n        }\n    }\n\n    playSoundEffect(soundName: string) {\n        let sourceToPlay: AudioSource | null = null;\n        switch (soundName) {\n            case 'buttonClick':\n                sourceToPlay = this.buttonClickAudioSource;\n                break;\n            case 'carCollision':\n                sourceToPlay = this.carCollisionAudioSource;\n                break;\n            case 'carDestruction':\n                sourceToPlay = this.carDestructionAudioSource;\n                break;\n            case 'carStart':\n                sourceToPlay = this.carStartAudioSource;\n                break;\n            case 'carAccelerate':\n                sourceToPlay = this.carAccelerateAudioSource;\n                break;\n            case 'carDrift':\n                sourceToPlay = this.carDriftAudioSource;\n                break;\n        }\n\n        if (sourceToPlay) {\n            sourceToPlay.play();\n        }\n    }\n\n    toggleAudio() {\n        const muted = this.isMuted();\n        const newVolume = muted ? 1 : 0;\n        this.allAudioSources.forEach(source => {\n            if (source===this.bgmAudioSource) {\n                    source.volume = newVolume * 0.3;\n                }\n                else if (source) {\n                    source.volume = newVolume;\n                }\n        });\n        this.saveState();\n    }\n\n    isMuted(): boolean {\n        // 检查BGM音源的音量作为代表\n        return this.bgmAudioSource ? this.bgmAudioSource.volume === 0 : false;\n    }\n\n    private saveState() {\n        const state = {\n            muted: this.isMuted()\n        };\n        sys.localStorage.setItem('soundState', JSON.stringify(state));\n    }\n\n    private loadState() {\n        const stateStr = sys.localStorage.getItem('soundState');\n        if (stateStr) {\n            const state = JSON.parse(stateStr);\n            const volume = state.muted ? 0 : 1;\n            this.allAudioSources.forEach(source => {\n                if (source===this.bgmAudioSource) {\n                    source.volume = volume * 0.3;\n                }\n                else if (source) {\n                    source.volume = volume;\n                }\n            });\n        }\n    }\n}"]}