{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"], "names": ["_decorator", "Camera", "CCBoolean", "CCFloat", "CCInteger", "Component", "Material", "rendering", "Texture2D", "EDITOR", "fillRequiredPipelineSettings", "makePipelineSettings", "ccclass", "disallowMultiple", "executeInEditMode", "menu", "property", "requireComponent", "type", "BuiltinPipelineSettings", "displayName", "group", "id", "name", "style", "range", "tooltip", "slide", "min", "getPipelineSettings", "_settings", "onEnable", "cameraComponent", "getComponent", "camera", "pipelineSettings", "_tryEnableEditorPreview", "onDisable", "_disableEditorPreview", "editorPreview", "_editorPreview", "v", "undefined", "setEditorPipelineSettings", "current", "getEditorPipelineSettings", "Msaa<PERSON>nable", "msaa", "enabled", "value", "msaaSampleCount", "Math", "ceil", "log2", "max", "sampleCount", "shadingScaleEnable", "enableShadingScale", "shadingScale", "bloomEnable", "bloom", "bloomMaterial", "material", "bloomEnableAlphaMask", "enableAlphaMask", "bloomIterations", "iterations", "bloom<PERSON><PERSON><PERSON>old", "threshold", "bloomIntensity", "intensity", "colorGradingEnable", "colorGrading", "colorGradingMaterial", "colorGradingContribute", "contribute", "colorGradingMap", "val", "fxaaEnable", "fxaa", "fxaaMaterial", "fsrEnable", "fsr", "fsrMaterial", "fsrSharpness", "sharpness", "toneMappingMaterial", "toneMapping"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBIA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AACnDC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAGhBC,MAAAA,M,UAAAA,M;;AAGLC,MAAAA,4B,iBAAAA,4B;AAA8BC,MAAAA,oB,iBAAAA,oB;;;;;;AAhClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAaM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,gBAAX;AAA6BC,QAAAA,iBAA7B;AAAgDC,QAAAA,IAAhD;AAAsDC,QAAAA,QAAtD;AAAgEC,QAAAA,gBAAhE;AAAkFC,QAAAA;AAAlF,O,GAA2FlB,U;;yCAOpFmB,uB,WALZP,OAAO,CAAC,yBAAD,C,UACPG,IAAI,CAAC,mCAAD,C,UACJE,gBAAgB,CAAChB,MAAD,C,UAiCZe,QAAQ,CAACd,SAAD,C,UAGRc,QAAQ,CAAC;AACNI,QAAAA,WAAW,EAAE,+BADP;AAENF,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAkCRc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE;AAApB,SADD;AAENL,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAcRc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE,2BAApB;AAAiDC,UAAAA,KAAK,EAAE;AAAxD,SADD;AAENN,QAAAA,IAAI,EAAEd,SAFA;AAGNqB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAHD,OAAD,C,UAkBRT,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,cAA5B;AAA4CC,UAAAA,KAAK,EAAE;AAAnD,SADD;AAENN,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAcRc,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,+BADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE;AAA5B,SAFD;AAGNL,QAAAA,IAAI,EAAEf,OAHA;AAINsB,QAAAA,KAAK,EAAE,CAAC,IAAD,EAAO,CAAP,EAAU,IAAV,CAJD;AAKNE,QAAAA,KAAK,EAAE;AALD,OAAD,C,WAkBRX,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SADD;AAENN,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,WAcRc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SADD;AAENN,QAAAA,IAAI,EAAEZ;AAFA,OAAD,C,WAiBRU,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,4BADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SAFD;AAGNN,QAAAA,IAAI,EAAEhB;AAHA,OAAD,C,WAeRc,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,uBADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SAFD;AAGNN,QAAAA,IAAI,EAAEd,SAHA;AAINqB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAJD;AAKNE,QAAAA,KAAK,EAAE;AALD,OAAD,C,WAiBRX,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,sBADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SAFD;AAGNN,QAAAA,IAAI,EAAEf,OAHA;AAINyB,QAAAA,GAAG,EAAE;AAJC,OAAD,C,WAqBRZ,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SADD;AAENN,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,WAcRc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SADD;AAENN,QAAAA,IAAI,EAAEZ;AAFA,OAAD,C,WAiBRU,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,+BADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SAFD;AAGNN,QAAAA,IAAI,EAAEf,OAHA;AAINsB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,IAAP,CAJD;AAKNE,QAAAA,KAAK,EAAE;AALD,OAAD,C,WAcRX,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,gCADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SAFD;AAGNN,QAAAA,IAAI,EAAEV;AAHA,OAAD,C,WAgBRQ,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE,iDAApB;AAAuEC,UAAAA,KAAK,EAAE;AAA9E,SADD;AAENN,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,WAcRc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE,iDAApB;AAAuEC,UAAAA,KAAK,EAAE;AAA9E,SADD;AAENN,QAAAA,IAAI,EAAEZ;AAFA,OAAD,C,WAkBRU,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,KAAN;AAAaC,UAAAA,IAAI,EAAE,6BAAnB;AAAkDC,UAAAA,KAAK,EAAE;AAAzD,SADD;AAENN,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,WAcRc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,KAAN;AAAaC,UAAAA,IAAI,EAAE,6BAAnB;AAAkDC,UAAAA,KAAK,EAAE;AAAzD,SADD;AAENN,QAAAA,IAAI,EAAEZ;AAFA,OAAD,C,WAiBRU,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,KAAN;AAAaC,UAAAA,IAAI,EAAE,6BAAnB;AAAkDC,UAAAA,KAAK,EAAE;AAAzD,SADD;AAENN,QAAAA,IAAI,EAAEf,OAFA;AAGNsB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,IAAP,CAHD;AAINE,QAAAA,KAAK,EAAE;AAJD,OAAD,C,WAaRX,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,aAAN;AAAqBC,UAAAA,IAAI,EAAE,aAA3B;AAA0CC,UAAAA,KAAK,EAAE;AAAjD,SADD;AAENN,QAAAA,IAAI,EAAEZ;AAFA,OAAD,C,8CAlWZO,gB,UACAC,iB,qBAJD,MAKaK,uBALb,SAK6Cd,SAL7C,CAKuD;AAAA;AAAA;;AAAA;;AA6BnD;AA7BmD;AAAA;;AAInDwB,QAAAA,mBAAmB,GAAqB;AACpC,iBAAO,KAAKC,SAAZ;AACH,SANkD,CAQnD;;;AACAC,QAAAA,QAAQ,GAAS;AACb;AAAA;AAAA,4EAA6B,KAAKD,SAAlC;AACA,gBAAME,eAAe,GAAG,KAAKC,YAAL,CAAkBhC,MAAlB,CAAxB;AACA,gBAAMiC,MAAM,GAAGF,eAAe,CAACE,MAA/B;AACAA,UAAAA,MAAM,CAACC,gBAAP,GAA0B,KAAKL,SAA/B;;AAEA,cAAIrB,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACDC,QAAAA,SAAS,GAAS;AACd,gBAAML,eAAe,GAAG,KAAKC,YAAL,CAAkBhC,MAAlB,CAAxB;AACA,gBAAMiC,MAAM,GAAGF,eAAe,CAACE,MAA/B;AACAA,UAAAA,MAAM,CAACC,gBAAP,GAA0B,IAA1B;;AAEA,cAAI1B,MAAJ,EAAY;AACR,iBAAK6B,qBAAL;AACH;AACJ;;AAUgB,YAAbC,aAAa,GAAY;AACzB,iBAAO,KAAKC,cAAZ;AACH;;AACgB,YAAbD,aAAa,CAACE,CAAD,EAAa;AAC1B,eAAKD,cAAL,GAAsBC,CAAtB;;AACA,cAAIhC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACMA,QAAAA,uBAAuB,GAAS;AACnC,cAAI7B,SAAS,KAAKmC,SAAlB,EAA6B;AACzB;AACH;;AACD,cAAI,KAAKF,cAAT,EAAyB;AACrBjC,YAAAA,SAAS,CAACoC,yBAAV,CAAoC,KAAKb,SAAzC;AACH,WAFD,MAEO;AACH,iBAAKQ,qBAAL;AACH;AACJ;;AACMA,QAAAA,qBAAqB,GAAS;AACjC,cAAI/B,SAAS,KAAKmC,SAAlB,EAA6B;AACzB;AACH;;AACD,gBAAME,OAAO,GAAGrC,SAAS,CAACsC,yBAAV,EAAhB;;AACA,cAAID,OAAO,KAAK,KAAKd,SAArB,EAAgC;AAC5BvB,YAAAA,SAAS,CAACoC,yBAAV,CAAoC,IAApC;AACH;AACJ,SAhEkD,CAkEnD;;;AAKc,YAAVG,UAAU,GAAY;AACtB,iBAAO,KAAKhB,SAAL,CAAeiB,IAAf,CAAoBC,OAA3B;AACH;;AACa,YAAVF,UAAU,CAACG,KAAD,EAAiB;AAC3B,eAAKnB,SAAL,CAAeiB,IAAf,CAAoBC,OAApB,GAA8BC,KAA9B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AAOkB,YAAfc,eAAe,CAACD,KAAD,EAAgB;AAC/BA,UAAAA,KAAK,GAAG,KAAKE,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,IAAL,CAAUF,IAAI,CAACG,GAAL,CAASL,KAAT,EAAgB,CAAhB,CAAV,CAAV,CAAb;AACAA,UAAAA,KAAK,GAAGE,IAAI,CAACvB,GAAL,CAASqB,KAAT,EAAgB,CAAhB,CAAR;AACA,eAAKnB,SAAL,CAAeiB,IAAf,CAAoBQ,WAApB,GAAkCN,KAAlC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACkB,YAAfc,eAAe,GAAW;AAC1B,iBAAO,KAAKpB,SAAL,CAAeiB,IAAf,CAAoBQ,WAA3B;AACH,SAhGkD,CAkGnD;;;AAKsB,YAAlBC,kBAAkB,CAACP,KAAD,EAAiB;AACnC,eAAKnB,SAAL,CAAe2B,kBAAf,GAAoCR,KAApC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACqB,YAAlBoB,kBAAkB,GAAY;AAC9B,iBAAO,KAAK1B,SAAL,CAAe2B,kBAAtB;AACH;;AASe,YAAZC,YAAY,CAACT,KAAD,EAAgB;AAC5B,eAAKnB,SAAL,CAAe4B,YAAf,GAA8BT,KAA9B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACe,YAAZsB,YAAY,GAAW;AACvB,iBAAO,KAAK5B,SAAL,CAAe4B,YAAtB;AACH,SAhIkD,CAkInD;;;AAKe,YAAXC,WAAW,CAACV,KAAD,EAAiB;AAC5B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBZ,OAArB,GAA+BC,KAA/B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACc,YAAXuB,WAAW,GAAY;AACvB,iBAAO,KAAK7B,SAAL,CAAe8B,KAAf,CAAqBZ,OAA5B;AACH;;AAMgB,YAAba,aAAa,CAACZ,KAAD,EAAkB;AAC/B,cAAI,KAAKnB,SAAL,CAAe8B,KAAf,CAAqBE,QAArB,KAAkCb,KAAtC,EAA6C;AACzC;AACH;;AACD,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBE,QAArB,GAAgCb,KAAhC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACgB,YAAbyB,aAAa,GAAa;AAC1B,iBAAO,KAAK/B,SAAL,CAAe8B,KAAf,CAAqBE,QAA5B;AACH;;AAOuB,YAApBC,oBAAoB,CAACd,KAAD,EAAiB;AACrC,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBI,eAArB,GAAuCf,KAAvC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACuB,YAApB2B,oBAAoB,GAAY;AAChC,iBAAO,KAAKjC,SAAL,CAAe8B,KAAf,CAAqBI,eAA5B;AACH;;AASkB,YAAfC,eAAe,CAAChB,KAAD,EAAgB;AAC/B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBM,UAArB,GAAkCjB,KAAlC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACkB,YAAf6B,eAAe,GAAW;AAC1B,iBAAO,KAAKnC,SAAL,CAAe8B,KAAf,CAAqBM,UAA5B;AACH;;AAQiB,YAAdC,cAAc,CAAClB,KAAD,EAAgB;AAC9B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBQ,SAArB,GAAiCnB,KAAjC;AACH;;AACiB,YAAdkB,cAAc,GAAW;AACzB,iBAAO,KAAKrC,SAAL,CAAe8B,KAAf,CAAqBQ,SAA5B;AACH;;AAEiB,YAAdC,cAAc,CAACpB,KAAD,EAAgB;AAC9B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBU,SAArB,GAAiCrB,KAAjC;AACH;;AACiB,YAAdoB,cAAc,GAAW;AACzB,iBAAO,KAAKvC,SAAL,CAAe8B,KAAf,CAAqBU,SAA5B;AACH,SApNkD,CAsNnD;;;AAKsB,YAAlBC,kBAAkB,CAACtB,KAAD,EAAiB;AACnC,eAAKnB,SAAL,CAAe0C,YAAf,CAA4BxB,OAA5B,GAAsCC,KAAtC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACqB,YAAlBmC,kBAAkB,GAAY;AAC9B,iBAAO,KAAKzC,SAAL,CAAe0C,YAAf,CAA4BxB,OAAnC;AACH;;AAMuB,YAApByB,oBAAoB,CAACxB,KAAD,EAAkB;AACtC,cAAI,KAAKnB,SAAL,CAAe0C,YAAf,CAA4BV,QAA5B,KAAyCb,KAA7C,EAAoD;AAChD;AACH;;AACD,eAAKnB,SAAL,CAAe0C,YAAf,CAA4BV,QAA5B,GAAuCb,KAAvC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACuB,YAApBqC,oBAAoB,GAAa;AACjC,iBAAO,KAAK3C,SAAL,CAAe0C,YAAf,CAA4BV,QAAnC;AACH;;AASyB,YAAtBY,sBAAsB,CAACzB,KAAD,EAAgB;AACtC,eAAKnB,SAAL,CAAe0C,YAAf,CAA4BG,UAA5B,GAAyC1B,KAAzC;AACH;;AACyB,YAAtByB,sBAAsB,GAAW;AACjC,iBAAO,KAAK5C,SAAL,CAAe0C,YAAf,CAA4BG,UAAnC;AACH;;AAOkB,YAAfC,eAAe,CAACC,GAAD,EAAiB;AAChC,eAAK/C,SAAL,CAAe0C,YAAf,CAA4BI,eAA5B,GAA8CC,GAA9C;;AACA,cAAIpE,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACkB,YAAfwC,eAAe,GAAc;AAC7B,iBAAO,KAAK9C,SAAL,CAAe0C,YAAf,CAA4BI,eAAnC;AACH,SAjRkD,CAmRnD;;;AAKc,YAAVE,UAAU,CAAC7B,KAAD,EAAiB;AAC3B,eAAKnB,SAAL,CAAeiD,IAAf,CAAoB/B,OAApB,GAA8BC,KAA9B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACa,YAAV0C,UAAU,GAAY;AACtB,iBAAO,KAAKhD,SAAL,CAAeiD,IAAf,CAAoB/B,OAA3B;AACH;;AAMe,YAAZgC,YAAY,CAAC/B,KAAD,EAAkB;AAC9B,cAAI,KAAKnB,SAAL,CAAeiD,IAAf,CAAoBjB,QAApB,KAAiCb,KAArC,EAA4C;AACxC;AACH;;AACD,eAAKnB,SAAL,CAAeiD,IAAf,CAAoBjB,QAApB,GAA+Bb,KAA/B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACe,YAAZ4C,YAAY,GAAa;AACzB,iBAAO,KAAKlD,SAAL,CAAeiD,IAAf,CAAoBjB,QAA3B;AACH,SAjTkD,CAmTnD;;;AAKa,YAATmB,SAAS,CAAChC,KAAD,EAAiB;AAC1B,eAAKnB,SAAL,CAAeoD,GAAf,CAAmBlC,OAAnB,GAA6BC,KAA7B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACY,YAAT6C,SAAS,GAAY;AACrB,iBAAO,KAAKnD,SAAL,CAAeoD,GAAf,CAAmBlC,OAA1B;AACH;;AAMc,YAAXmC,WAAW,CAAClC,KAAD,EAAkB;AAC7B,cAAI,KAAKnB,SAAL,CAAeoD,GAAf,CAAmBpB,QAAnB,KAAgCb,KAApC,EAA2C;AACvC;AACH;;AACD,eAAKnB,SAAL,CAAeoD,GAAf,CAAmBpB,QAAnB,GAA8Bb,KAA9B;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACc,YAAX+C,WAAW,GAAa;AACxB,iBAAO,KAAKrD,SAAL,CAAeoD,GAAf,CAAmBpB,QAA1B;AACH;;AAQe,YAAZsB,YAAY,CAACnC,KAAD,EAAgB;AAC5B,eAAKnB,SAAL,CAAeoD,GAAf,CAAmBG,SAAnB,GAA+BpC,KAA/B;AACH;;AACe,YAAZmC,YAAY,GAAW;AACvB,iBAAO,KAAKtD,SAAL,CAAeoD,GAAf,CAAmBG,SAA1B;AACH;;AAMsB,YAAnBC,mBAAmB,CAACrC,KAAD,EAAkB;AACrC,cAAI,KAAKnB,SAAL,CAAeyD,WAAf,CAA2BzB,QAA3B,KAAwCb,KAA5C,EAAmD;AAC/C;AACH;;AACD,eAAKnB,SAAL,CAAeyD,WAAf,CAA2BzB,QAA3B,GAAsCb,KAAtC;;AACA,cAAIxC,MAAJ,EAAY;AACR,iBAAK2B,uBAAL;AACH;AACJ;;AACsB,YAAnBkD,mBAAmB,GAAa;AAChC,iBAAO,KAAKxD,SAAL,CAAeyD,WAAf,CAA2BzB,QAAlC;AACH;;AA/WkD,O,4EAClD9C,Q;;;;;iBAC8C;AAAA;AAAA,6D;;;;;;;iBA6BpB,K", "sourcesContent": ["/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com/\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\nimport {\n    _decorator, Camera, CCBoolean, CCFloat, CCInteger, Component,\n    Material, rendering, Texture2D,\n} from 'cc';\n\nimport { EDITOR } from 'cc/env';\n\nimport {\n    fillRequiredPipelineSettings, makePipelineSettings, PipelineSettings,\n} from './builtin-pipeline-types';\n\nconst { ccclass, disallowMultiple, executeInEditMode, menu, property, requireComponent, type } = _decorator;\n\n@ccclass('BuiltinPipelineSettings')\n@menu('Rendering/BuiltinPipelineSettings')\n@requireComponent(Camera)\n@disallowMultiple\n@executeInEditMode\nexport class BuiltinPipelineSettings extends Component {\n    @property\n    private readonly _settings: PipelineSettings = makePipelineSettings();\n\n    getPipelineSettings(): PipelineSettings {\n        return this._settings;\n    }\n\n    // Enable/Disable\n    onEnable(): void {\n        fillRequiredPipelineSettings(this._settings);\n        const cameraComponent = this.getComponent(Camera)!;\n        const camera = cameraComponent.camera;\n        camera.pipelineSettings = this._settings;\n\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    onDisable(): void {\n        const cameraComponent = this.getComponent(Camera)!;\n        const camera = cameraComponent.camera;\n        camera.pipelineSettings = null;\n\n        if (EDITOR) {\n            this._disableEditorPreview();\n        }\n    }\n\n    // Editor Preview\n    @property(CCBoolean)\n    protected _editorPreview = false;\n\n    @property({\n        displayName: 'Editor Preview (Experimental)',\n        type: CCBoolean,\n    })\n    get editorPreview(): boolean {\n        return this._editorPreview;\n    }\n    set editorPreview(v: boolean) {\n        this._editorPreview = v;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    public _tryEnableEditorPreview(): void {\n        if (rendering === undefined) {\n            return;\n        }\n        if (this._editorPreview) {\n            rendering.setEditorPipelineSettings(this._settings);\n        } else {\n            this._disableEditorPreview();\n        }\n    }\n    public _disableEditorPreview(): void {\n        if (rendering === undefined) {\n            return;\n        }\n        const current = rendering.getEditorPipelineSettings() as PipelineSettings | null;\n        if (current === this._settings) {\n            rendering.setEditorPipelineSettings(null);\n        }\n    }\n\n    // MSAA\n    @property({\n        group: { id: 'MSAA', name: 'Multisample Anti-Aliasing' },\n        type: CCBoolean,\n    })\n    get MsaaEnable(): boolean {\n        return this._settings.msaa.enabled;\n    }\n    set MsaaEnable(value: boolean) {\n        this._settings.msaa.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n\n    @property({\n        group: { id: 'MSAA', name: 'Multisample Anti-Aliasing', style: 'section' },\n        type: CCInteger,\n        range: [2, 4, 2],\n    })\n    set msaaSampleCount(value: number) {\n        value = 2 ** Math.ceil(Math.log2(Math.max(value, 2)));\n        value = Math.min(value, 4);\n        this._settings.msaa.sampleCount = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get msaaSampleCount(): number {\n        return this._settings.msaa.sampleCount;\n    }\n\n    // Shading Scale\n    @property({\n        group: { id: 'ShadingScale', name: 'ShadingScale', style: 'section' },\n        type: CCBoolean,\n    })\n    set shadingScaleEnable(value: boolean) {\n        this._settings.enableShadingScale = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get shadingScaleEnable(): boolean {\n        return this._settings.enableShadingScale;\n    }\n\n    @property({\n        tooltip: 'i18n:postprocess.shadingScale',\n        group: { id: 'ShadingScale', name: 'ShadingScale' },\n        type: CCFloat,\n        range: [0.01, 4, 0.01],\n        slide: true,\n    })\n    set shadingScale(value: number) {\n        this._settings.shadingScale = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get shadingScale(): number {\n        return this._settings.shadingScale;\n    }\n\n    // Bloom\n    @property({\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set bloomEnable(value: boolean) {\n        this._settings.bloom.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomEnable(): boolean {\n        return this._settings.bloom.enabled;\n    }\n\n    @property({\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: Material,\n    })\n    set bloomMaterial(value: Material) {\n        if (this._settings.bloom.material === value) {\n            return;\n        }\n        this._settings.bloom.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomMaterial(): Material {\n        return this._settings.bloom.material!;\n    }\n\n    @property({\n        tooltip: 'i18n:bloom.enableAlphaMask',\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set bloomEnableAlphaMask(value: boolean) {\n        this._settings.bloom.enableAlphaMask = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomEnableAlphaMask(): boolean {\n        return this._settings.bloom.enableAlphaMask;\n    }\n\n    @property({\n        tooltip: 'i18n:bloom.iterations',\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCInteger,\n        range: [1, 6, 1],\n        slide: true,\n    })\n    set bloomIterations(value: number) {\n        this._settings.bloom.iterations = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomIterations(): number {\n        return this._settings.bloom.iterations;\n    }\n\n    @property({\n        tooltip: 'i18n:bloom.threshold',\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        min: 0,\n    })\n    set bloomThreshold(value: number) {\n        this._settings.bloom.threshold = value;\n    }\n    get bloomThreshold(): number {\n        return this._settings.bloom.threshold;\n    }\n\n    set bloomIntensity(value: number) {\n        this._settings.bloom.intensity = value;\n    }\n    get bloomIntensity(): number {\n        return this._settings.bloom.intensity;\n    }\n\n    // Color Grading (LDR)\n    @property({\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set colorGradingEnable(value: boolean) {\n        this._settings.colorGrading.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get colorGradingEnable(): boolean {\n        return this._settings.colorGrading.enabled;\n    }\n\n    @property({\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: Material,\n    })\n    set colorGradingMaterial(value: Material) {\n        if (this._settings.colorGrading.material === value) {\n            return;\n        }\n        this._settings.colorGrading.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get colorGradingMaterial(): Material {\n        return this._settings.colorGrading.material!;\n    }\n\n    @property({\n        tooltip: 'i18n:color_grading.contribute',\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        range: [0, 1, 0.01],\n        slide: true,\n    })\n    set colorGradingContribute(value: number) {\n        this._settings.colorGrading.contribute = value;\n    }\n    get colorGradingContribute(): number {\n        return this._settings.colorGrading.contribute;\n    }\n\n    @property({\n        tooltip: 'i18n:color_grading.originalMap',\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: Texture2D,\n    })\n    set colorGradingMap(val: Texture2D) {\n        this._settings.colorGrading.colorGradingMap = val;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get colorGradingMap(): Texture2D {\n        return this._settings.colorGrading.colorGradingMap!;\n    }\n\n    // FXAA\n    @property({\n        group: { id: 'FXAA', name: 'Fast Approximate Anti-Aliasing (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set fxaaEnable(value: boolean) {\n        this._settings.fxaa.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fxaaEnable(): boolean {\n        return this._settings.fxaa.enabled;\n    }\n\n    @property({\n        group: { id: 'FXAA', name: 'Fast Approximate Anti-Aliasing (PostProcessing)', style: 'section' },\n        type: Material,\n    })\n    set fxaaMaterial(value: Material) {\n        if (this._settings.fxaa.material === value) {\n            return;\n        }\n        this._settings.fxaa.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fxaaMaterial(): Material {\n        return this._settings.fxaa.material!;\n    }\n\n    // FSR\n    @property({\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\n        type: CCBoolean,\n    })\n    set fsrEnable(value: boolean) {\n        this._settings.fsr.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fsrEnable(): boolean {\n        return this._settings.fsr.enabled;\n    }\n\n    @property({\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\n        type: Material,\n    })\n    set fsrMaterial(value: Material) {\n        if (this._settings.fsr.material === value) {\n            return;\n        }\n        this._settings.fsr.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fsrMaterial(): Material {\n        return this._settings.fsr.material!;\n    }\n\n    @property({\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\n        type: CCFloat,\n        range: [0, 1, 0.01],\n        slide: true,\n    })\n    set fsrSharpness(value: number) {\n        this._settings.fsr.sharpness = value;\n    }\n    get fsrSharpness(): number {\n        return this._settings.fsr.sharpness;\n    }\n\n    @property({\n        group: { id: 'ToneMapping', name: 'ToneMapping', style: 'section' },\n        type: Material,\n    })\n    set toneMappingMaterial(value: Material) {\n        if (this._settings.toneMapping.material === value) {\n            return;\n        }\n        this._settings.toneMapping.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get toneMappingMaterial(): Material {\n        return this._settings.toneMapping.material!;\n    }\n}\n"]}