{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Label", "KeyCode", "input", "Input", "GameManager", "GameState", "ccclass", "property", "GameManagerExample", "gameManager", "onLoad", "getInstance", "start", "bindButtonEvents", "bindKeyboardEvents", "testPauseB<PERSON>on", "node", "on", "EventType", "CLICK", "onTestPauseClick", "testGameOverWinButton", "onTestGameOverWinClick", "testGameOverLoseButton", "onTestGameOverLoseClick", "testDamageButton", "onTestDamageClick", "KEY_DOWN", "onKeyDown", "event", "keyCode", "KEY_P", "getCurrentState", "RUNNING", "pauseGame", "PAUSED", "resumeGame", "KEY_R", "restartGame", "KEY_M", "returnToMainMenu", "gameOver", "reducePlayerHP", "update", "updateUI", "statusLabel", "state", "stateText", "GAME_OVER", "string", "gameTimeLabel", "gameTime", "getGameTime", "toFixed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playerHP", "getPlayerHP", "maxHP", "getPlayerMaxHP", "enemyCountLabel", "enemyCount", "getEnemyCount", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAC5DC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,S,iBAAAA,S;;;;;;;;;OAChB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;;oCAEaW,kB,WADZF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,2BAvBb,MACaS,kBADb,SACwCV,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAElB;AAFkB;;AAKhB;AALgB;;AAQhB;AARgB;;AAWd;AAXc;;AAcb;AAda;;AAiBP;AAjBO;;AAoBN;AApBM;;AAuBZ;AAvBY,eAyBtCW,WAzBsC,GAyBX,IAzBW;AAAA;;AA2B9CC,QAAAA,MAAM,GAAG;AACL;AACA,eAAKD,WAAL,GAAmB;AAAA;AAAA,0CAAYE,WAAZ,EAAnB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKC,gBAAL;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,GAAG;AACvB,cAAI,KAAKE,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBC,IAArB,CAA0BC,EAA1B,CAA6BlB,MAAM,CAACmB,SAAP,CAAiBC,KAA9C,EAAqD,KAAKC,gBAA1D,EAA4E,IAA5E;AACH;;AAED,cAAI,KAAKC,qBAAT,EAAgC;AAC5B,iBAAKA,qBAAL,CAA2BL,IAA3B,CAAgCC,EAAhC,CAAmClB,MAAM,CAACmB,SAAP,CAAiBC,KAApD,EAA2D,KAAKG,sBAAhE,EAAwF,IAAxF;AACH;;AAED,cAAI,KAAKC,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4BP,IAA5B,CAAiCC,EAAjC,CAAoClB,MAAM,CAACmB,SAAP,CAAiBC,KAArD,EAA4D,KAAKK,uBAAjE,EAA0F,IAA1F;AACH;;AAED,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBT,IAAtB,CAA2BC,EAA3B,CAA8BlB,MAAM,CAACmB,SAAP,CAAiBC,KAA/C,EAAsD,KAAKO,iBAA3D,EAA8E,IAA9E;AACH;AACJ;AAED;AACJ;AACA;;;AACYZ,QAAAA,kBAAkB,GAAG;AACzBZ,UAAAA,KAAK,CAACe,EAAN,CAASd,KAAK,CAACe,SAAN,CAAgBS,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,SAAS,CAACC,KAAD,EAAuB;AACpC,cAAI,CAAC,KAAKpB,WAAV,EAAuB;;AAEvB,kBAAQoB,KAAK,CAACC,OAAd;AACI,iBAAK7B,OAAO,CAAC8B,KAAb;AAAoB;AAChB,kBAAI,KAAKtB,WAAL,CAAiBuB,eAAjB,OAAuC;AAAA;AAAA,0CAAUC,OAArD,EAA8D;AAC1D,qBAAKxB,WAAL,CAAiByB,SAAjB;AACH,eAFD,MAEO,IAAI,KAAKzB,WAAL,CAAiBuB,eAAjB,OAAuC;AAAA;AAAA,0CAAUG,MAArD,EAA6D;AAChE,qBAAK1B,WAAL,CAAiB2B,UAAjB;AACH;;AACD;;AACJ,iBAAKnC,OAAO,CAACoC,KAAb;AAAoB;AAChB,mBAAK5B,WAAL,CAAiB6B,WAAjB;AACA;;AACJ,iBAAKrC,OAAO,CAACsC,KAAb;AAAoB;AAChB,mBAAK9B,WAAL,CAAiB+B,gBAAjB;AACA;AAbR;AAeH;AAED;AACJ;AACA;;;AACYpB,QAAAA,gBAAgB,GAAG;AACvB,cAAI,CAAC,KAAKX,WAAV,EAAuB;;AAEvB,cAAI,KAAKA,WAAL,CAAiBuB,eAAjB,OAAuC;AAAA;AAAA,sCAAUC,OAArD,EAA8D;AAC1D,iBAAKxB,WAAL,CAAiByB,SAAjB;AACH,WAFD,MAEO,IAAI,KAAKzB,WAAL,CAAiBuB,eAAjB,OAAuC;AAAA;AAAA,sCAAUG,MAArD,EAA6D;AAChE,iBAAK1B,WAAL,CAAiB2B,UAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACYd,QAAAA,sBAAsB,GAAG;AAC7B,cAAI,KAAKb,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBgC,QAAjB,CAA0B,IAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYjB,QAAAA,uBAAuB,GAAG;AAC9B,cAAI,KAAKf,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBgC,QAAjB,CAA0B,KAA1B;AACH;AACJ;AAED;AACJ;AACA;;;AACYf,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAKjB,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBiC,cAAjB,CAAgC,EAAhC,EADkB,CACmB;AACxC;AACJ;;AAEDC,QAAAA,MAAM,GAAG;AACL,eAAKC,QAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,QAAQ,GAAG;AACf,cAAI,CAAC,KAAKnC,WAAV,EAAuB,OADR,CAGf;;AACA,cAAI,KAAKoC,WAAT,EAAsB;AAClB,kBAAMC,KAAK,GAAG,KAAKrC,WAAL,CAAiBuB,eAAjB,EAAd;AACA,gBAAIe,SAAS,GAAG,EAAhB;;AACA,oBAAQD,KAAR;AACI,mBAAK;AAAA;AAAA,0CAAUb,OAAf;AACIc,gBAAAA,SAAS,GAAG,KAAZ;AACA;;AACJ,mBAAK;AAAA;AAAA,0CAAUZ,MAAf;AACIY,gBAAAA,SAAS,GAAG,KAAZ;AACA;;AACJ,mBAAK;AAAA;AAAA,0CAAUC,SAAf;AACID,gBAAAA,SAAS,GAAG,MAAZ;AACA;AATR;;AAWA,iBAAKF,WAAL,CAAiBI,MAAjB,GAA2B,SAAQF,SAAU,EAA7C;AACH,WAnBc,CAqBf;;;AACA,cAAI,KAAKG,aAAT,EAAwB;AACpB,kBAAMC,QAAQ,GAAG,KAAK1C,WAAL,CAAiB2C,WAAjB,EAAjB;AACA,iBAAKF,aAAL,CAAmBD,MAAnB,GAA6B,SAAQE,QAAQ,CAACE,OAAT,CAAiB,CAAjB,CAAoB,GAAzD;AACH,WAzBc,CA2Bf;;;AACA,cAAI,KAAKC,aAAT,EAAwB;AACpB,kBAAMC,QAAQ,GAAG,KAAK9C,WAAL,CAAiB+C,WAAjB,EAAjB;AACA,kBAAMC,KAAK,GAAG,KAAKhD,WAAL,CAAiBiD,cAAjB,EAAd;AACA,iBAAKJ,aAAL,CAAmBL,MAAnB,GAA6B,QAAOM,QAAS,IAAGE,KAAM,EAAtD;AACH,WAhCc,CAkCf;;;AACA,cAAI,KAAKE,eAAT,EAA0B;AACtB,kBAAMC,UAAU,GAAG,KAAKnD,WAAL,CAAiBoD,aAAjB,EAAnB;AACA,iBAAKF,eAAL,CAAqBV,MAArB,GAA+B,SAAQW,UAAW,EAAlD;AACH;AACJ;;AAEDE,QAAAA,SAAS,GAAG;AACR;AACA5D,UAAAA,KAAK,CAAC6D,GAAN,CAAU5D,KAAK,CAACe,SAAN,CAAgBS,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;;AAEA,cAAI,KAAKb,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBC,IAArB,CAA0B+C,GAA1B,CAA8BhE,MAAM,CAACmB,SAAP,CAAiBC,KAA/C,EAAsD,KAAKC,gBAA3D,EAA6E,IAA7E;AACH;;AACD,cAAI,KAAKC,qBAAT,EAAgC;AAC5B,iBAAKA,qBAAL,CAA2BL,IAA3B,CAAgC+C,GAAhC,CAAoChE,MAAM,CAACmB,SAAP,CAAiBC,KAArD,EAA4D,KAAKG,sBAAjE,EAAyF,IAAzF;AACH;;AACD,cAAI,KAAKC,sBAAT,EAAiC;AAC7B,iBAAKA,sBAAL,CAA4BP,IAA5B,CAAiC+C,GAAjC,CAAqChE,MAAM,CAACmB,SAAP,CAAiBC,KAAtD,EAA6D,KAAKK,uBAAlE,EAA2F,IAA3F;AACH;;AACD,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBT,IAAtB,CAA2B+C,GAA3B,CAA+BhE,MAAM,CAACmB,SAAP,CAAiBC,KAAhD,EAAuD,KAAKO,iBAA5D,EAA+E,IAA/E;AACH;AACJ;;AAhM6C,O;;;;;iBAEzB,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I;;;;;;;iBAGE,I;;;;;;;iBAGC,I;;;;;;;iBAGM,I;;;;;;;iBAGC,I;;;;;;;iBAGN,I", "sourcesContent": ["import { _decorator, Component, Node, Button, Label, KeyCode, input, Input, EventKeyboard } from 'cc';\nimport { GameManager, GameState } from './GameManager';\nconst { ccclass, property } = _decorator;\n\n/**\n * GameManager使用示例\n * 这个组件展示了如何使用GameManager的游戏状态管理功能\n */\n@ccclass('GameManagerExample')\nexport class GameManagerExample extends Component {\n    @property(Label)\n    statusLabel: Label = null!; // 状态显示标签\n\n    @property(Label)\n    gameTimeLabel: Label = null!; // 游戏时间显示标签\n\n    @property(Label)\n    playerHPLabel: Label = null!; // 玩家生命值显示标签\n\n    @property(Label)\n    enemyCountLabel: Label = null!; // 敌人数量显示标签\n\n    @property(Button)\n    testPauseButton: Button = null!; // 测试暂停按钮\n\n    @property(Button)\n    testGameOverWinButton: Button = null!; // 测试胜利按钮\n\n    @property(Button)\n    testGameOverLoseButton: Button = null!; // 测试失败按钮\n\n    @property(Button)\n    testDamageButton: Button = null!; // 测试伤害按钮\n\n    private gameManager: GameManager = null!;\n\n    onLoad() {\n        // 获取GameManager实例\n        this.gameManager = GameManager.getInstance();\n    }\n\n    start() {\n        this.bindButtonEvents();\n        this.bindKeyboardEvents();\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        if (this.testPauseButton) {\n            this.testPauseButton.node.on(Button.EventType.CLICK, this.onTestPauseClick, this);\n        }\n\n        if (this.testGameOverWinButton) {\n            this.testGameOverWinButton.node.on(Button.EventType.CLICK, this.onTestGameOverWinClick, this);\n        }\n\n        if (this.testGameOverLoseButton) {\n            this.testGameOverLoseButton.node.on(Button.EventType.CLICK, this.onTestGameOverLoseClick, this);\n        }\n\n        if (this.testDamageButton) {\n            this.testDamageButton.node.on(Button.EventType.CLICK, this.onTestDamageClick, this);\n        }\n    }\n\n    /**\n     * 绑定键盘事件\n     */\n    private bindKeyboardEvents() {\n        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n    }\n\n    /**\n     * 键盘按下事件\n     */\n    private onKeyDown(event: EventKeyboard) {\n        if (!this.gameManager) return;\n\n        switch (event.keyCode) {\n            case KeyCode.KEY_P: // P键暂停/继续\n                if (this.gameManager.getCurrentState() === GameState.RUNNING) {\n                    this.gameManager.pauseGame();\n                } else if (this.gameManager.getCurrentState() === GameState.PAUSED) {\n                    this.gameManager.resumeGame();\n                }\n                break;\n            case KeyCode.KEY_R: // R键重新开始\n                this.gameManager.restartGame();\n                break;\n            case KeyCode.KEY_M: // M键返回主菜单\n                this.gameManager.returnToMainMenu();\n                break;\n        }\n    }\n\n    /**\n     * 测试暂停按钮点击\n     */\n    private onTestPauseClick() {\n        if (!this.gameManager) return;\n\n        if (this.gameManager.getCurrentState() === GameState.RUNNING) {\n            this.gameManager.pauseGame();\n        } else if (this.gameManager.getCurrentState() === GameState.PAUSED) {\n            this.gameManager.resumeGame();\n        }\n    }\n\n    /**\n     * 测试胜利按钮点击\n     */\n    private onTestGameOverWinClick() {\n        if (this.gameManager) {\n            this.gameManager.gameOver(true);\n        }\n    }\n\n    /**\n     * 测试失败按钮点击\n     */\n    private onTestGameOverLoseClick() {\n        if (this.gameManager) {\n            this.gameManager.gameOver(false);\n        }\n    }\n\n    /**\n     * 测试伤害按钮点击\n     */\n    private onTestDamageClick() {\n        if (this.gameManager) {\n            this.gameManager.reducePlayerHP(20); // 减少20点生命值\n        }\n    }\n\n    update() {\n        this.updateUI();\n    }\n\n    /**\n     * 更新UI显示\n     */\n    private updateUI() {\n        if (!this.gameManager) return;\n\n        // 更新状态显示\n        if (this.statusLabel) {\n            const state = this.gameManager.getCurrentState();\n            let stateText = '';\n            switch (state) {\n                case GameState.RUNNING:\n                    stateText = '运行中';\n                    break;\n                case GameState.PAUSED:\n                    stateText = '已暂停';\n                    break;\n                case GameState.GAME_OVER:\n                    stateText = '游戏结束';\n                    break;\n            }\n            this.statusLabel.string = `游戏状态: ${stateText}`;\n        }\n\n        // 更新游戏时间\n        if (this.gameTimeLabel) {\n            const gameTime = this.gameManager.getGameTime();\n            this.gameTimeLabel.string = `游戏时长: ${gameTime.toFixed(1)}秒`;\n        }\n\n        // 更新玩家生命值\n        if (this.playerHPLabel) {\n            const playerHP = this.gameManager.getPlayerHP();\n            const maxHP = this.gameManager.getPlayerMaxHP();\n            this.playerHPLabel.string = `生命值: ${playerHP}/${maxHP}`;\n        }\n\n        // 更新敌人数量\n        if (this.enemyCountLabel) {\n            const enemyCount = this.gameManager.getEnemyCount();\n            this.enemyCountLabel.string = `敌人剩余: ${enemyCount}`;\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        \n        if (this.testPauseButton) {\n            this.testPauseButton.node.off(Button.EventType.CLICK, this.onTestPauseClick, this);\n        }\n        if (this.testGameOverWinButton) {\n            this.testGameOverWinButton.node.off(Button.EventType.CLICK, this.onTestGameOverWinClick, this);\n        }\n        if (this.testGameOverLoseButton) {\n            this.testGameOverLoseButton.node.off(Button.EventType.CLICK, this.onTestGameOverLoseClick, this);\n        }\n        if (this.testDamageButton) {\n            this.testDamageButton.node.off(Button.EventType.CLICK, this.onTestDamageClick, this);\n        }\n    }\n}\n"]}