System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts at runtime.
      throw new Error(`Error: 在加载模块文件 /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts 时发生错误：Error: ENOENT: no such file or directory, open '/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts'`);
    }
  };
});
//# sourceMappingURL=7052b35dfbe13ea1766793c74299b451255e5fb1.js.map