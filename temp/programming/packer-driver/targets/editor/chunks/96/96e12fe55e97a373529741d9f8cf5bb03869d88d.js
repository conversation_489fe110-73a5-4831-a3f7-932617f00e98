System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts at runtime.
      throw new Error(`Error: 在加载模块文件 /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts 时发生错误：Error: ENOENT: no such file or directory, open '/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts'`);
    }
  };
});
//# sourceMappingURL=96e12fe55e97a373529741d9f8cf5bb03869d88d.js.map