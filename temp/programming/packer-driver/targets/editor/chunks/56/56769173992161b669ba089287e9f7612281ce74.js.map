{"version": 3, "sources": ["data:text/javascript,%0A%2F%2F%20This%20module%20is%20auto-generated%20to%20report%20error%20emitted%20when%20try%20to%20load%20module%20file%3A%2F%2F%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIPlayer.ts%20at%20runtime.%0Athrow%20new%20Error(%60SyntaxError%3A%20%2Ffile%3A%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIPlayer.ts%3A%20Unexpected%20token%20(540%3A0)%0A%0A%20%20538%20%7C%20%20%20%20%20%7D%0A%20%20539%20%7C%20%7D%0A%3E%20540%20%7C%20%7D%0A%20%20%20%20%20%20%7C%20%5E%60)%3B%0A%20%20%20%20%20%20%20%20"], "names": ["Error"], "mappings": ";;;;;;AACA;AACA,YAAM,IAAIA,KAAJ,CAAW;AACjB;AACA;AACA;AACA;AACA,UALM,CAAN", "sourcesContent": ["\n// This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts at runtime.\nthrow new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts: Unexpected token (540:0)\n\n  538 |     }\n  539 | }\n> 540 | }\n      | ^`);\n        "]}