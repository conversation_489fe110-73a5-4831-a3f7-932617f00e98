System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts at runtime.
      throw new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts: Unexpected token (540:0)

  538 |     }
  539 | }
> 540 | }
      | ^`);
    }
  };
});
//# sourceMappingURL=56769173992161b669ba089287e9f7612281ce74.js.map