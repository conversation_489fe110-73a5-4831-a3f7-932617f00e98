{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts"], "names": ["_decorator", "Component", "Label", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "PlayerInfoUI", "_player<PERSON><PERSON><PERSON>", "onLoad", "instance", "addDataChangeListener", "updateUI", "bind", "player<PERSON><PERSON>", "onDestroy", "removeDataChangeListener", "data", "levelLabel", "string", "level", "moneyLabel", "money"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AACvBC,MAAAA,a,iBAAAA,a;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;8BAGjBM,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAACH,KAAD,C,UAGRG,QAAQ,CAACH,KAAD,C,2BALb,MACaI,YADb,SACkCL,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAEb;AAFa;;AAKb;AALa,eAOhCM,cAPgC,GAOA,IAPA;AAAA;;AASxCC,QAAAA,MAAM,GAAG;AACL,eAAKD,cAAL,GAAsB;AAAA;AAAA,8CAAcE,QAApC,CADK,CAEL;;AACA,eAAKF,cAAL,CAAoBG,qBAApB,CAA0C,KAAKC,QAAL,CAAcC,IAAd,CAAmB,IAAnB,CAA1C;;AACA,eAAKD,QAAL,CAAc,KAAKJ,cAAL,CAAoBM,UAAlC;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKP,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBQ,wBAApB,CAA6C,KAAKJ,QAAL,CAAcC,IAAd,CAAmB,IAAnB,CAA7C;AACH;AACJ;;AAEDD,QAAAA,QAAQ,CAACK,IAAD,EAAmB;AACvB,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAA0B,UAASF,IAAI,CAACG,KAAM,EAA9C;AACH;;AACD,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBF,MAAhB,GAA0B,UAASF,IAAI,CAACK,KAAM,EAA9C;AACH;AACJ;;AA7BuC,O;;;;;iBAEpB,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Label } from 'cc';\nimport { PlayerManager, PlayerData } from './PlayerManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlayerInfoUI')\nexport class PlayerInfoUI extends Component {\n    @property(Label)\n    levelLabel: Label = null!; // 拖拽等级Label\n\n    @property(Label)\n    moneyLabel: Label = null!; // 拖拽金钱Label\n\n    private _playerManager: PlayerManager = null!;\n\n    onLoad() {\n        this._playerManager = PlayerManager.instance;\n        // 监听数据变化，自动刷新UI\n        this._playerManager.addDataChangeListener(this.updateUI.bind(this));\n        this.updateUI(this._playerManager.playerData);\n    }\n\n    onDestroy() {\n        if (this._playerManager) {\n            this._playerManager.removeDataChangeListener(this.updateUI.bind(this));\n        }\n    }\n\n    updateUI(data: PlayerData) {\n        if (this.levelLabel) {\n            this.levelLabel.string = `level: ${data.level}`;\n        }\n        if (this.moneyLabel) {\n            this.moneyLabel.string = `money: ${data.money}`;\n        }\n    }\n} "]}