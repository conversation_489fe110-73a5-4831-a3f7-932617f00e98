System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _cclegacy._RF.push({}, "e0c4eLHWCNPuoCVatWpgw2m", "rewarded_callback", undefined);
      /**
       * Interface for Rewarded AdBreak API Callback
       */


      _cclegacy._RF.pop();
    }
  };
});
//# sourceMappingURL=b69cf2900308d0879070a4914a7efa2857266e96.js.map