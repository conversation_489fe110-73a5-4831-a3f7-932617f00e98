{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA", "sourcesContent": ["/**\n * Interface for Rewarded AdBreak API Callback\n */\nexport interface RewardedCallback {\n  type: string;\n  name: string;\n  beforeAd: () => void;\n  afterAd: () => void;\n  beforeReward: (showAdFn: () => void) => void;\n  adDismissed: () => void;\n  adViewed: () => void;\n  adBreakDone: (placementInfo: unknown) => void;\n}"]}