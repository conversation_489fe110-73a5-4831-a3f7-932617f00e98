{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts"], "names": ["_decorator", "Component", "Vec2", "Vec3", "RigidBody2D", "ERigidBody2DType", "BoxCollider2D", "Contact2DType", "ProgressBar", "Sprite", "SpriteFrame", "tween", "Prefab", "Layers", "find", "player", "GameManager", "AIController", "WeaponType", "SoundManager", "ccclass", "property", "AIPlayer", "type", "tooltip", "_rigidBody", "_direction", "_accel", "_angle", "_targetAngle", "_lastValidPosition", "_currentHealth", "_isDestroyed", "_paintTimer", "_vehicleId", "_blockCollisionCooldown", "_blockCollisionCooldownDuration", "_canFire", "_fireTimer", "onLoad", "node", "name", "Date", "now", "start", "getComponent", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "Dynamic", "allowSleep", "gravityScale", "linearDamping", "angularDamping", "fixedRotation", "worldPosition", "x", "y", "initAngle", "setRotationFromEuler", "initHealthBar", "collider", "log", "on", "BEGIN_CONTACT", "onCollisionEnter", "maxHealth", "updateHealthBar", "healthBar", "progress", "update", "deltaTime", "currentVelocity", "linearVelocity", "currentSpeed", "length", "currentPos", "turnAmount", "turnSpeed", "angleDiff", "Math", "abs", "rad", "PI", "force", "cos", "acceleration", "sin", "applyForce", "forward", "dot", "brakeForce", "clone", "multiplyScalar", "brakeDeceleration", "reverseForce", "frictionForce", "friction", "maxSpeed", "normalizedVelocity", "normalize", "distanceToLastPos", "distance", "setWorldPosition", "z", "updatePaintSpray", "updateWeaponSystem", "setAccel", "accel", "setDirection", "direction", "setTargetAngle", "angle", "getCurrentAngle", "init", "setHealth", "health", "max", "min", "takeDamage", "damage", "destroyVehicle", "heal", "amount", "getHealth", "getMaxHealth", "isDead", "self", "other", "<PERSON><PERSON><PERSON><PERSON>", "layer", "blockLayer", "name<PERSON>o<PERSON><PERSON><PERSON>", "aiControllerNode", "aiController", "isAIBoundaryTurning", "turnDirection", "random", "turnAngle", "currentAngle", "targetAngle", "playerComponent", "playerRigidBody", "getRigidBody", "isDestroyed", "impactForce", "instance", "playSoundEffect", "destroyedSprite", "sprite", "spriteFrame", "active", "startDestroyAnimation", "updateEnemyCount", "scheduleRemoveNode", "gameManager", "getInstance", "allAIPlayers", "getAIPlayers", "aliveCount", "filter", "ai", "refreshEnemyCount", "scheduleOnce", "removeVehicleNode", "<PERSON><PERSON><PERSON><PERSON>", "aiPlayers", "index", "indexOf", "splice", "removeFromParent", "to", "scale", "getVehicleId", "paintPrefab", "paintSprayInterval", "sprayPaint", "warn", "getWorldPosition", "fireInterval", "fireRate", "checkAndShoot", "getPlayerComponent", "<PERSON><PERSON><PERSON>", "aiPos", "toPlayer", "angleToPlayer", "atan2", "ai<PERSON>orward<PERSON>ngle", "shoot", "bulletPrefab", "weaponType", "NORMAL", "normalBulletPrefab", "DART", "dartPrefab", "ROCKET", "rocketPrefab", "vehicleWorldPos", "offsetDistance", "bulletStartPos", "fireBullet"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;;AACzJC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AAEAC,MAAAA,Y,iBAAAA,Y;;;;;;;wNAHsC;;;AACR;OACjC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;0BAIjBsB,Q,WADZF,OAAO,CAAC,UAAD,C,UAkBHC,QAAQ,CAACb,WAAD,C,UAGRa,QAAQ,CAACX,WAAD,C,UAORW,QAAQ,CAACT,MAAD,C,UAORS,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEX,MADA;AAENY,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEX,MADA;AAENY,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEX,MADA;AAENY,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNG,QAAAA,OAAO,EAAE;AADH,OAAD,C,UAKRH,QAAQ,CAAC;AACNE,QAAAA,IAAI;AAAA;AAAA,oCADE;AAENC,QAAAA,OAAO,EAAE;AAFH,OAAD,C,2BA1Db,MACaF,QADb,SAC8BrB,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAeZ;AAfY;;AAkBJ;AAlBI;;AAqBE;AArBF;;AAwBT;AAE3B;AA1BoC;;AA4BP;AA5BO;;AA+BF;AAElC;AAjCoC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA+D5BwB,UA/D4B,GA+DF,IA/DE;AAAA,eAgE5BC,UAhE4B,GAgEP,CAhEO;AAgEJ;AAhEI,eAiE5BC,MAjE4B,GAiEX,CAjEW;AAiER;AAjEQ,eAkE5BC,MAlE4B,GAkEX,CAlEW;AAAA,eAmE5BC,YAnE4B,GAmEL,CAnEK;AAAA,eAoE5BC,kBApE4B,GAoED,IAAI5B,IAAJ,EApEC;AAAA,eAqE5B6B,cArE4B,GAqEH,GArEG;AAqEE;AAEtC;AAvEoC,eAwE5BC,YAxE4B,GAwEJ,KAxEI;AAwEG;AAEvC;AA1EoC,eA2E5BC,WA3E4B,GA2EN,CA3EM;AA2EH;AA3EG,eA4E5BC,UA5E4B,GA4EP,EA5EO;AA4EH;AAEjC;AA9EoC,eA+E5BC,uBA/E4B,GA+EM,CA/EN;AA+ES;AA/ET,eAgF5BC,+BAhF4B,GAgFc,GAhFd;AAgFmB;AAEvD;AAlFoC,eAmF5BC,QAnF4B,GAmFR,IAnFQ;AAmFF;AAnFE,eAoF5BC,UApF4B,GAoFP,CApFO;AAAA;;AAoFJ;AAEhCC,QAAAA,MAAM,GAAG;AACL,eAAKd,UAAL,GAAkB,IAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,CAAd;AACA,eAAKC,MAAL,GAAc,CAAd;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKC,kBAAL,GAA0B,IAAI5B,IAAJ,EAA1B,CANK,CAQL;;AACA,eAAK8B,YAAL,GAAoB,KAApB,CATK,CAWL;;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAmB,MAAK,KAAKM,IAAL,CAAUC,IAAK,IAAGC,IAAI,CAACC,GAAL,EAAW,EAArD,CAbK,CAamD;AAExD;;AACA,eAAKR,uBAAL,GAA+B,CAA/B,CAhBK,CAkBL;;AACA,eAAKE,QAAL,GAAgB,IAAhB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACH;;AAEDM,QAAAA,KAAK,GAAG;AACJ,eAAKnB,UAAL,GAAkB,KAAKoB,YAAL,CAAkBzC,WAAlB,CAAlB;;AACA,cAAI,CAAC,KAAKqB,UAAN,IAAoB,CAAC,KAAKe,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUM,OAAjD,EAA0D;AACtDC,YAAAA,OAAO,CAACC,KAAR,CAAc,wDAAd;AACA;AACH;;AACD,eAAKvB,UAAL,CAAgBF,IAAhB,GAAuBlB,gBAAgB,CAAC4C,OAAxC;AACA,eAAKxB,UAAL,CAAgByB,UAAhB,GAA6B,KAA7B;AACA,eAAKzB,UAAL,CAAgB0B,YAAhB,GAA+B,CAA/B;AACA,eAAK1B,UAAL,CAAgB2B,aAAhB,GAAgC,GAAhC;AACA,eAAK3B,UAAL,CAAgB4B,cAAhB,GAAiC,GAAjC;AACA,eAAK5B,UAAL,CAAgB6B,aAAhB,GAAgC,IAAhC;AACA,eAAKxB,kBAAL,GAA0B,IAAI5B,IAAJ,CAAS,KAAKsC,IAAL,CAAUe,aAAV,CAAwBC,CAAjC,EAAoC,KAAKhB,IAAL,CAAUe,aAAV,CAAwBE,CAA5D,CAA1B;AACA,eAAK7B,MAAL,GAAc,KAAK8B,SAAnB;AACA,eAAK7B,YAAL,GAAoB,KAAK6B,SAAzB;AACA,eAAKlB,IAAL,CAAUmB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKD,SAA1C,EAfI,CAiBJ;;AACA,eAAKE,aAAL,GAlBI,CAoBJ;AAEA;;AACA,gBAAMC,QAAQ,GAAG,KAAKhB,YAAL,CAAkBvC,aAAlB,CAAjB;;AACA,cAAIuD,QAAJ,EAAc;AACVd,YAAAA,OAAO,CAACe,GAAR,CAAY,wCAAZ;AACAD,YAAAA,QAAQ,CAACE,EAAT,CAAYxD,aAAa,CAACyD,aAA1B,EAAyC,KAAKC,gBAA9C,EAAgE,IAAhE;AACH,WAHD,MAGO;AACHlB,YAAAA,OAAO,CAACC,KAAR,CAAc,4CAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACYY,QAAAA,aAAa,GAAG;AACpB,eAAK7B,cAAL,GAAsB,KAAKmC,SAA3B,CADoB,CAGpB;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,eAAe,GAAG;AACtB,cAAI,KAAKC,SAAT,EAAoB;AAEhB,iBAAKA,SAAL,CAAeC,QAAf,GAA0B,KAAKtC,cAAL,GAAsB,KAAKmC,SAArD;AACAnB,YAAAA,OAAO,CAACe,GAAR,CAAY,+BAAZ,EAA6C,KAAK/B,cAAL,GAAsB,KAAKmC,SAAxE;AACH;AACJ;;AAEDI,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC,KAAK9C,UAAN,IAAoB,CAAC,KAAKe,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUM,OAAjD,EAA0D,OADpC,CAGtB;;AACA,cAAI,KAAKX,uBAAL,GAA+B,CAAnC,EAAsC;AAClC,iBAAKA,uBAAL,IAAgCoC,SAAhC;AACH,WANqB,CAQtB;;;AACA,cAAI,KAAKvC,YAAT,EAAuB;AAEnB;AACH;;AAED,gBAAMwC,eAAe,GAAG,KAAK/C,UAAL,CAAgBgD,cAAxC;AACA,gBAAMC,YAAY,GAAGF,eAAe,CAACG,MAAhB,EAArB;AACA,gBAAMC,UAAU,GAAG,IAAI1E,IAAJ,CAAS,KAAKsC,IAAL,CAAUe,aAAV,CAAwBC,CAAjC,EAAoC,KAAKhB,IAAL,CAAUe,aAAV,CAAwBE,CAA5D,CAAnB,CAhBsB,CAkBtB;;AACA,cAAI,KAAK/B,UAAL,KAAoB,CAAxB,EAA2B;AACvB,kBAAMmD,UAAU,GAAG,KAAKC,SAAL,GAAiBP,SAAjB,GAA6B,KAAK7C,UAArD;AACA,iBAAKG,YAAL,IAAqBgD,UAArB;AACH,WAtBqB,CAwBtB;;;AACA,gBAAME,SAAS,GAAG,KAAKlD,YAAL,GAAoB,KAAKD,MAA3C;;AACA,cAAIoD,IAAI,CAACC,GAAL,CAASF,SAAT,IAAsB,GAA1B,EAA+B;AAC3B,iBAAKnD,MAAL,IAAemD,SAAS,GAAG,GAA3B,CAD2B,CACK;AACnC,WAFD,MAEO;AACH,iBAAKnD,MAAL,GAAc,KAAKC,YAAnB;AACH,WA9BqB,CAgCtB;;;AACA,eAAKW,IAAL,CAAUmB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAK/B,MAA1C,EAjCsB,CAmCtB;;AACA,cAAI,KAAKD,MAAL,KAAgB,CAApB,EAAuB;AACnB;AACA,kBAAMuD,GAAG,GAAG,CAAC,KAAKtD,MAAL,GAAc,EAAf,IAAqBoD,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,kBAAMC,KAAK,GAAG,IAAIlF,IAAJ,CACV8E,IAAI,CAACK,GAAL,CAASH,GAAT,IAAgB,KAAKI,YADX,EAEVN,IAAI,CAACO,GAAL,CAASL,GAAT,IAAgB,KAAKI,YAFX,CAAd;;AAIA,iBAAK7D,UAAL,CAAgB+D,UAAhB,CAA2BJ,KAA3B,EAAkCR,UAAlC,EAA8C,IAA9C;AACH,WARD,CASA;AATA,eAUK,IAAI,KAAKjD,MAAL,KAAgB,CAAC,CAArB,EAAwB;AACzB;AACA,kBAAMuD,GAAG,GAAG,CAAC,KAAKtD,MAAL,GAAc,EAAf,IAAqBoD,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,kBAAMM,OAAO,GAAG,IAAIvF,IAAJ,CAAS8E,IAAI,CAACK,GAAL,CAASH,GAAT,CAAT,EAAwBF,IAAI,CAACO,GAAL,CAASL,GAAT,CAAxB,CAAhB;AACA,kBAAMQ,GAAG,GAAGlB,eAAe,CAACkB,GAAhB,CAAoBD,OAApB,CAAZ;;AAEA,gBAAIC,GAAG,GAAG,CAAV,EAAa;AACT;AACA,oBAAMC,UAAU,GAAGF,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKC,iBAArC,CAAnB;;AACA,mBAAKrE,UAAL,CAAgB+D,UAAhB,CAA2BG,UAA3B,EAAuCf,UAAvC,EAAmD,IAAnD;AACH,aAJD,MAIO;AACH;AACA,oBAAMmB,YAAY,GAAGN,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKP,YAAN,GAAqB,GAApD,CAArB;;AACA,mBAAK7D,UAAL,CAAgB+D,UAAhB,CAA2BO,YAA3B,EAAyCnB,UAAzC,EAAqD,IAArD;AACH;AAEJ,WAhBI,CAiBL;AAjBK,eAkBA;AACD;AACA,gBAAIF,YAAY,GAAG,CAAnB,EAAsB;AAClB,oBAAMsB,aAAa,GAAGxB,eAAe,CAACoB,KAAhB,GAAwBC,cAAxB,CAAuC,CAAC,KAAKI,QAAN,GAAiB,CAAxD,CAAtB,CADkB,CACgE;;AAClF,mBAAKxE,UAAL,CAAgB+D,UAAhB,CAA2BQ,aAA3B,EAA0CpB,UAA1C,EAAsD,IAAtD;AACH;AACJ,WAtEqB,CAwEtB;;;AACA,cAAIF,YAAY,GAAG,KAAKwB,QAAxB,EAAkC;AAC9B,kBAAMC,kBAAkB,GAAG3B,eAAe,CAACoB,KAAhB,GAAwBQ,SAAxB,EAA3B;AACA,iBAAK3E,UAAL,CAAgBgD,cAAhB,GAAiC0B,kBAAkB,CAACN,cAAnB,CAAkC,KAAKK,QAAvC,CAAjC;AACH,WA5EqB,CA8EtB;;;AACA,cAAIxB,YAAY,GAAG,GAAnB,EAAwB;AACpB;AACA,kBAAM2B,iBAAiB,GAAGnG,IAAI,CAACoG,QAAL,CAAc1B,UAAd,EAA0B,KAAK9C,kBAA/B,CAA1B;;AACA,gBAAIuE,iBAAiB,GAAG,EAAxB,EAA4B;AAAE;AAC1B,mBAAK7D,IAAL,CAAU+D,gBAAV,CAA2B,KAAKzE,kBAAL,CAAwB0B,CAAnD,EAAsD,KAAK1B,kBAAL,CAAwB2B,CAA9E,EAAiF,KAAKjB,IAAL,CAAUe,aAAV,CAAwBiD,CAAzG;AACA,mBAAK/E,UAAL,CAAgBgD,cAAhB,GAAiC,IAAIvE,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAjC;AACH;AACJ,WAPD,MAOO;AACH;AACA,iBAAK4B,kBAAL,GAA0B8C,UAAU,CAACgB,KAAX,EAA1B;AACH,WAzFqB,CA2FtB;;;AACA,cAAIZ,IAAI,CAACC,GAAL,CAAS,KAAKrD,MAAd,IAAwB,GAA5B,EAAiC;AAC7B,iBAAKA,MAAL,GAAc,KAAKA,MAAL,GAAc,GAA5B;AACA,iBAAKC,YAAL,GAAoB,KAAKA,YAAL,GAAoB,GAAxC;AACH,WA/FqB,CAiGtB;;;AACA,eAAK4E,gBAAL,CAAsBlC,SAAtB,EAlGsB,CAoGtB;;AACA,eAAKmC,kBAAL,CAAwBnC,SAAxB;AACH,SAlRmC,CAoRpC;;;AACOoC,QAAAA,QAAQ,CAACC,KAAD,EAAgB;AAC3B,eAAKjF,MAAL,GAAciF,KAAd;AACH;;AAEMC,QAAAA,YAAY,CAACC,SAAD,EAAoB;AACnC,eAAKpF,UAAL,GAAkBoF,SAAlB;AACH;;AAEMC,QAAAA,cAAc,CAACC,KAAD,EAAgB;AACjC,eAAKnF,YAAL,GAAoBmF,KAApB;AACH;;AAEMC,QAAAA,eAAe,GAAW;AAC7B,iBAAO,KAAKrF,MAAZ;AACH;;AAEMsF,QAAAA,IAAI,CAACF,KAAD,EAAgB;AACvB,eAAKtD,SAAL,GAAiBsD,KAAjB;AACA,eAAKpF,MAAL,GAAcoF,KAAd;AACA,eAAKnF,YAAL,GAAoBmF,KAApB;AACA,eAAKxE,IAAL,CAAUmB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCqD,KAArC;AACH,SA1SmC,CA4SpC;;AACA;AACJ;AACA;;;AACWG,QAAAA,SAAS,CAACC,MAAD,EAAiB;AAC7B,eAAKrF,cAAL,GAAsBiD,IAAI,CAACqC,GAAL,CAAS,CAAT,EAAYrC,IAAI,CAACsC,GAAL,CAASF,MAAT,EAAiB,KAAKlD,SAAtB,CAAZ,CAAtB;AACA,eAAKC,eAAL;AACH;AAED;AACJ;AACA;;;AACWoD,QAAAA,UAAU,CAACC,MAAD,EAAiB;AAC9B,cAAI,KAAKxF,YAAT,EAAuB;AAEvBe,UAAAA,OAAO,CAACe,GAAR,CAAY,yBAAZ,EAAuC0D,MAAvC;AACA,eAAKL,SAAL,CAAe,KAAKpF,cAAL,GAAsByF,MAArC;AACA,eAAKrD,eAAL,GAL8B,CAO9B;;AACA,cAAI,KAAKpC,cAAL,IAAuB,CAA3B,EAA8B;AAC1B,iBAAK0F,cAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACxB,eAAKR,SAAL,CAAe,KAAKpF,cAAL,GAAsB4F,MAArC;AACA,eAAKxD,eAAL;AACH;AAED;AACJ;AACA;;;AACWyD,QAAAA,SAAS,GAAW;AACvB,iBAAO,KAAK7F,cAAZ;AACH;AAED;AACJ;AACA;;;AACW8F,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAK3D,SAAZ;AACH;AAED;AACJ;AACA;;;AACW4D,QAAAA,MAAM,GAAY;AACrB,iBAAO,KAAK/F,cAAL,IAAuB,CAA9B;AACH;AAED;AACJ;AACA;;;AACIkC,QAAAA,gBAAgB,CAAC8D,IAAD,EAAsBC,KAAtB,EAA4C;AACxD;AAEA;AACA,gBAAMC,UAAU,GAAGD,KAAK,CAACxF,IAAN,CAAW0F,KAA9B;AACA,gBAAMC,UAAU,GAAGtH,MAAM,CAACuH,WAAP,CAAmB,OAAnB,CAAnB,CALwD,CAOxD;;AACA,cAAIH,UAAU,KAAKE,UAAnB,EAA+B;AAC3B;AACA,gBAAI,KAAKhG,uBAAL,GAA+B,CAAnC,EAAsC;AAClC;AACA,qBAFkC,CAE1B;AACX,aAL0B,CAO3B;;;AACA,kBAAMkG,gBAAgB,GAAGvH,IAAI,CAAC,cAAD,CAA7B;;AACA,gBAAIuH,gBAAJ,EAAsB;AAClB,oBAAMC,YAAY,GAAGD,gBAAgB,CAACxF,YAAjB;AAAA;AAAA,+CAArB;;AACA,kBAAIyF,YAAJ,EAAkB;AACd;AACA,oBAAIA,YAAY,CAACC,mBAAb,CAAiC,IAAjC,CAAJ,EAA4C;AACxC;AACH,iBAJa,CAMd;AAEA;;;AACA,qBAAKpG,uBAAL,GAA+B,KAAKC,+BAApC,CATc,CAWd;;AACA,sBAAMoG,aAAa,GAAGxD,IAAI,CAACyD,MAAL,KAAgB,GAAhB,GAAsB,CAAC,CAAvB,GAA2B,CAAjD,CAZc,CAcd;;AACA,sBAAMC,SAAS,GAAG,MAAM1D,IAAI,CAACyD,MAAL,KAAgB,EAAxC,CAfc,CAiBd;;AACA,sBAAME,YAAY,GAAG,KAAK1B,eAAL,EAArB,CAlBc,CAoBd;;AACA,sBAAM2B,WAAW,GAAGJ,aAAa,GAAG,CAAhB,GAChBG,YAAY,GAAGD,SADC,GAEhBC,YAAY,GAAGD,SAFnB,CArBc,CAyBd;;AACA,qBAAK3B,cAAL,CAAoB6B,WAApB;AACA,qBAAK/B,YAAL,CAAkB2B,aAAlB;AACA,qBAAK7B,QAAL,CAAc,CAAd;AACH;AACJ;;AACD,mBA1C2B,CA0CnB;AACX;;AAED,gBAAMkC,eAAe,GAAGb,KAAK,CAACxF,IAAN,CAAWK,YAAX;AAAA;AAAA,+BAAxB;;AACA,cAAIgG,eAAJ,EAAqB;AACjB9F,YAAAA,OAAO,CAACe,GAAR,CAAY,kBAAZ;AACA,kBAAMgF,eAAe,GAAGD,eAAe,CAACE,YAAhB,EAAxB;;AACA,gBAAID,eAAe,IAAI,CAAC,KAAKE,WAA7B,EAA0C;AACtC,oBAAMC,WAAW,GAAG,IAAI/I,IAAJ,CAAS4I,eAAe,CAACrE,cAAhB,CAA+BjB,CAAxC,EAA2CsF,eAAe,CAACrE,cAAhB,CAA+BhB,CAA1E,CAApB;AACAwF,cAAAA,WAAW,CAAC7C,SAAZ,GAFsC,CAEb;;AACzB6C,cAAAA,WAAW,CAACpD,cAAZ,CAA2B,EAA3B,EAHsC,CAGN;;AAChC,mBAAKpE,UAAL,CAAgBgD,cAAhB,GAAiCwE,WAAjC;AACH;AAEJ;AACJ,SAtamC,CAwapC;;AAEA;AACJ;AACA;;;AACYxB,QAAAA,cAAc,GAAG;AACrB,cAAI,KAAKzF,YAAT,EAAuB;AAEvB,eAAKA,YAAL,GAAoB,IAApB;AACAe,UAAAA,OAAO,CAACe,GAAR,CAAY,UAAZ;AACA;AAAA;AAAA,4CAAaoF,QAAb,CAAsBC,eAAtB,CAAsC,gBAAtC,EALqB,CAOrB;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,kBAAMC,MAAM,GAAG,KAAKxG,YAAL,CAAkBpC,MAAlB,CAAf;;AACA,gBAAI4I,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,WAAP,GAAqB,KAAKF,eAA1B;AACH;AACJ,WAboB,CAerB;;;AACA,cAAI,KAAKhF,SAAL,IAAkB,KAAKA,SAAL,CAAe5B,IAArC,EAA2C;AACvC,iBAAK4B,SAAL,CAAe5B,IAAf,CAAoB+G,MAApB,GAA6B,KAA7B;AACH,WAlBoB,CAoBrB;;;AACA,eAAKC,qBAAL,GArBqB,CAuBrB;;AACA,eAAKC,gBAAL,GAxBqB,CA0BrB;;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,GAAG;AACvB,gBAAME,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACb;AACA,kBAAME,YAAY,GAAGF,WAAW,CAACG,YAAZ,EAArB;AACA,kBAAMC,UAAU,GAAGF,YAAY,CAACG,MAAb,CAAqBC,EAAD,IAAkB,CAACA,EAAE,CAACjB,WAAH,EAAvC,EAAyDrE,MAA5E;AACAgF,YAAAA,WAAW,CAACO,iBAAZ,CAA8BH,UAA9B;AACH;AACJ;AAED;AACJ;AACA;;;AACYL,QAAAA,kBAAkB,GAAG;AACzB,cAAI,KAAKlH,IAAL,IAAa,KAAKA,IAAL,CAAUM,OAA3B,EAAoC;AAChC;AACA,iBAAKqH,YAAL,CAAkB,MAAM;AACpB,mBAAKC,iBAAL;AACH,aAFD,EAEG,KAAKC,WAFR;AAGH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK5H,IAAL,IAAa,KAAKA,IAAL,CAAUM,OAA3B,EAAoC;AAChCC,YAAAA,OAAO,CAACe,GAAR,CAAY,UAAZ,EADgC,CAGhC;;AACA,kBAAM6F,WAAW,GAAG;AAAA;AAAA,4CAAYC,WAAZ,EAApB;;AACA,gBAAID,WAAJ,EAAiB;AACb,oBAAMW,SAAS,GAAGX,WAAW,CAACG,YAAZ,EAAlB;AACA,oBAAMS,KAAK,GAAGD,SAAS,CAACE,OAAV,CAAkB,IAAlB,CAAd;;AACA,kBAAID,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdD,gBAAAA,SAAS,CAACG,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH,eALY,CAOb;;;AACAZ,cAAAA,WAAW,CAACO,iBAAZ,CAA8BI,SAAS,CAAC3F,MAAxC;AACH,aAd+B,CAgBhC;;;AACA,iBAAKnC,IAAL,CAAUkI,gBAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACYlB,QAAAA,qBAAqB,GAAG;AAC5B,cAAI,KAAKhH,IAAT,EAAe;AACX;AACA7B,YAAAA,KAAK,CAAC,KAAK6B,IAAN,CAAL,CACKmI,EADL,CACQ,GADR,EACa;AACLC,cAAAA,KAAK,EAAE,IAAIzK,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,CAAnB,CADF,CAC0B;AAC/B;;AAFK,aADb,EAKKyC,KALL;AAMH;AACJ;AAED;AACJ;AACA;;AAGI;AACJ;AACA;;;AACWoG,QAAAA,WAAW,GAAY;AAC1B,iBAAO,KAAKhH,YAAZ;AACH;AAED;AACJ;AACA;;;AACW6I,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAK3I,UAAZ;AACH;AAED;AACJ;AACA;AACI;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;;AAEA;AACJ;AACA;AACA;;;AACYuE,QAAAA,gBAAgB,CAAClC,SAAD,EAA0B;AAC9C,cAAI,KAAKvC,YAAL,IAAqB,CAAC,KAAK8I,WAA/B,EAA4C,OADE,CAG9C;;AACA,eAAK7I,WAAL,IAAoBsC,SAApB,CAJ8C,CAM9C;;AACA,cAAI,KAAKtC,WAAL,IAAoB,KAAK8I,kBAA7B,EAAiD;AAC7C,iBAAKC,UAAL;AACA,iBAAK/I,WAAL,GAAmB,CAAnB,CAF6C,CAEvB;AACzB;AACJ;AAED;AACJ;AACA;;;AACY+I,QAAAA,UAAU,GAAS;AACvB,gBAAMrB,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAI,CAACD,WAAL,EAAkB;AACd5G,YAAAA,OAAO,CAACkI,IAAR,CAAa,uBAAb;AACA;AACH,WALsB,CAOvB;;;AACA,gBAAM1H,aAAa,GAAG,KAAKf,IAAL,CAAU0I,gBAAV,EAAtB,CARuB,CAUvB;;AACAvB,UAAAA,WAAW,CAACqB,UAAZ,CAAuB,KAAKF,WAA5B,EAAyCvH,aAAzC,EAAwD,KAAKrB,UAA7D;AACH,SAzmBmC,CA2mBpC;;AAEA;AACJ;AACA;AACA;;;AACYwE,QAAAA,kBAAkB,CAACnC,SAAD,EAA0B;AAChD,cAAI,KAAKvC,YAAT,EAAuB,OADyB,CAGhD;;AACA,eAAKM,UAAL,IAAmBiC,SAAnB,CAJgD,CAMhD;;AACA,gBAAM4G,YAAY,GAAG,IAAI,KAAKC,QAA9B;;AACA,cAAI,KAAK9I,UAAL,IAAmB6I,YAAvB,EAAqC;AACjC,iBAAK9I,QAAL,GAAgB,IAAhB;AACH,WAV+C,CAYhD;;;AACA,eAAKgJ,aAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,aAAa,GAAS;AAC1B,cAAI,CAAC,KAAKhJ,QAAV,EAAoB,OADM,CAG1B;;AACA,gBAAMsH,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;AACA,cAAI,CAACD,WAAL,EAAkB;AAElB,gBAAMd,eAAe,GAAGc,WAAW,CAAC2B,kBAAZ,EAAxB;AACA,cAAI,CAACzC,eAAD,IAAoB,CAACA,eAAe,CAACrG,IAAzC,EAA+C,OARrB,CAU1B;;AACA,gBAAM+I,SAAS,GAAG1C,eAAe,CAACrG,IAAhB,CAAqB0I,gBAArB,EAAlB;AACA,gBAAMM,KAAK,GAAG,KAAKhJ,IAAL,CAAU0I,gBAAV,EAAd,CAZ0B,CAc1B;;AACA,gBAAMO,QAAQ,GAAG,IAAIvL,IAAJ,CAASqL,SAAS,CAAC/H,CAAV,GAAcgI,KAAK,CAAChI,CAA7B,EAAgC+H,SAAS,CAAC9H,CAAV,GAAc+H,KAAK,CAAC/H,CAApD,CAAjB,CAf0B,CAiB1B;;AACA,gBAAMiI,aAAa,GAAG1G,IAAI,CAAC2G,KAAL,CAAWF,QAAQ,CAAChI,CAApB,EAAuBgI,QAAQ,CAACjI,CAAhC,IAAqC,GAArC,GAA2CwB,IAAI,CAACG,EAAtE,CAlB0B,CAoB1B;;AACA,gBAAMyG,cAAc,GAAG,KAAKhK,MAAL,GAAc,EAArC,CArB0B,CAuB1B;;AACA,cAAImD,SAAS,GAAG2G,aAAa,GAAGE,cAAhC,CAxB0B,CA0B1B;;AACA,iBAAO7G,SAAS,GAAG,GAAnB,EAAwBA,SAAS,IAAI,GAAb;;AACxB,iBAAOA,SAAS,GAAG,CAAC,GAApB,EAAyBA,SAAS,IAAI,GAAb,CA5BC,CA8B1B;;;AACA,cAAIC,IAAI,CAACC,GAAL,CAASF,SAAT,KAAuB,EAA3B,EAA+B;AAC3B,iBAAK8G,KAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACWA,QAAAA,KAAK,GAAS;AACjB,cAAI,CAAC,KAAKxJ,QAAN,IAAkB,KAAKL,YAA3B,EAAyC,OADxB,CAGjB;;AACA,eAAKK,QAAL,GAAgB,KAAhB;AACA,eAAKC,UAAL,GAAkB,CAAlB,CALiB,CAOjB;;AACA,cAAIwJ,YAA2B,GAAG,IAAlC;;AACA,kBAAQ,KAAKC,UAAb;AACI,iBAAK;AAAA;AAAA,0CAAWC,MAAhB;AACIF,cAAAA,YAAY,GAAG,KAAKG,kBAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,IAAhB;AACIJ,cAAAA,YAAY,GAAG,KAAKK,UAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,MAAhB;AACIN,cAAAA,YAAY,GAAG,KAAKO,YAApB;AACA;AATR,WATiB,CAqBjB;;;AACA,cAAI,CAACP,YAAL,EAAmB;AACf/I,YAAAA,OAAO,CAACkI,IAAR,CAAa,YAAb,EADe,CAEf;;AACA,iBAAK5I,QAAL,GAAgB,IAAhB;AACA;AACH,WA3BgB,CA6BjB;;;AACA,gBAAM6C,GAAG,GAAG,CAAC,KAAKtD,MAAL,GAAc,EAAf,IAAqBoD,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,gBAAM2B,SAAS,GAAG,IAAI5G,IAAJ,CAAS8E,IAAI,CAACK,GAAL,CAASH,GAAT,CAAT,EAAwBF,IAAI,CAACO,GAAL,CAASL,GAAT,CAAxB,CAAlB,CA/BiB,CAiCjB;;AACA,gBAAMoH,eAAe,GAAG,KAAK9J,IAAL,CAAUe,aAAlC;AACA,gBAAMgJ,cAAc,GAAG,EAAvB,CAnCiB,CAmCU;;AAC3B,gBAAMC,cAAc,GAAG,IAAIrM,IAAJ,CACnBmM,eAAe,CAAC9I,CAAhB,GAAoBsD,SAAS,CAACtD,CAAV,GAAc+I,cADf,EAEnBD,eAAe,CAAC7I,CAAhB,GAAoBqD,SAAS,CAACrD,CAAV,GAAc8I,cAFf,EAGnBD,eAAe,CAAC9F,CAHG,CAAvB,CApCiB,CA0CjB;;AACA,gBAAMmD,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAAC8C,UAAZ,CAAuBX,YAAvB,EAAqCU,cAArC,EAAqD1F,SAArD,EAAgE,KAAK5E,UAArE,EAAiF,KAAK6J,UAAtF;AACH,WA9CgB,CAgDjB;AACA;;AACH;;AA7tBmC,O,2EACnC1K,Q;;;;;iBACkB,E;;uFAClBA,Q;;;;;iBACsB,E;;4FACtBA,Q;;;;;iBAC2B,G;;oFAC3BA,Q;;;;;iBACmB,E;;mFACnBA,Q;;;;;iBACkB,G;;oFAClBA,Q;;;;;iBACmB,C;;oFAEnBA,Q;;;;;iBACmB,E;;;;;;;iBAGK,I;;;;;;;iBAGM,I;;uFAE9BA,Q;;;;;iBACqB,G;;;;;;;iBAIA,I;;8FAErBA,Q;;;;;iBAC4B,G;;;;;;;iBAOA,I;;;;;;;iBAMR,I;;;;;;;iBAME,I;;;;;;;iBAKJ,G;;;;;;;iBAMM;AAAA;AAAA,wCAAW2K,M", "sourcesContent": ["import { _decorator, Component, Vec2, Vec3, RigidBody2D, ERigidBody2DType, BoxCollider2D, Contact2DType, ProgressBar, Sprite, SpriteFrame, tween, Prefab, Layers, find } from 'cc';\nimport { player } from './player';\nimport { GameManager } from './GameManager';\nimport { AIController } from './AIController'; // 添加AIController导入\nimport { WeaponType } from './Bullet'; // 导入武器类型枚举\nconst { ccclass, property } = _decorator;\nimport { SoundManager } from './SoundManager';\n\n@ccclass('AIPlayer')\nexport class AIPlayer extends Component {\n    @property\n    maxSpeed: number = 50;\n    @property\n    acceleration: number = 50;\n    @property\n    brakeDeceleration: number = 200;\n    @property\n    turnSpeed: number = 50;\n    @property\n    friction: number = 1.5;\n    @property\n    initAngle: number = 0;\n\n    @property\n    maxHealth: number = 30; // 最大生命值\n\n    @property(ProgressBar)\n    healthBar: ProgressBar = null!; // 血条UI组件\n\n    @property(SpriteFrame)\n    destroyedSprite: SpriteFrame = null!; // 摧毁状态的精灵图\n\n    @property\n    removeDelay: number = 3.0; // 摧毁后移除节点的延迟时间（秒）\n\n    // 颜料喷洒相关属性\n    @property(Prefab)\n    paintPrefab: Prefab = null!; // 颜料预制体\n\n    @property\n    paintSprayInterval: number = 0.2; // 颜料喷洒间隔（秒）\n\n    // 武器系统相关属性\n    @property({\n        type: Prefab,\n        tooltip: \"普通子弹预制体\"\n    })\n    normalBulletPrefab: Prefab = null!;\n\n    @property({\n        type: Prefab,\n        tooltip: \"飞镖预制体\"\n    })\n    dartPrefab: Prefab = null!;\n\n    @property({\n        type: Prefab,\n        tooltip: \"火箭弹预制体\"\n    })\n    rocketPrefab: Prefab = null!;\n\n    @property({\n        tooltip: \"射速（发/秒）\"\n    })\n    fireRate: number = 1.5;\n\n    @property({\n        type: WeaponType,\n        tooltip: \"武器类型\"\n    })\n    weaponType: WeaponType = WeaponType.NORMAL;\n\n    private _rigidBody: RigidBody2D = null!;\n    private _direction: number = 0; // -1:左, 0:不转, 1:右\n    private _accel: number = 0; // -1:刹车, 0:无, 1:加速\n    private _angle: number = 0;\n    private _targetAngle: number = 0;\n    private _lastValidPosition: Vec2 = new Vec2();\n    private _currentHealth: number = 100; // 当前生命值\n\n    // 摧毁相关\n    private _isDestroyed: boolean = false; // 是否已摧毁\n\n    // 颜料喷洒相关私有变量\n    private _paintTimer: number = 0; // 颜料喷洒计时器\n    private _vehicleId: string = ''; // 车辆唯一ID\n\n    // Block碰撞冷却时间相关\n    private _blockCollisionCooldown: number = 0; // Block碰撞冷却时间计时器\n    private _blockCollisionCooldownDuration: number = 3.0; // Block碰撞冷却时间(秒)\n\n    // 武器系统相关私有变量\n    private _canFire: boolean = true; // 是否可以射击\n    private _fireTimer: number = 0; // 射击计时器\n\n    onLoad() {\n        this._rigidBody = null!;\n        this._direction = 0;\n        this._accel = 0;\n        this._angle = 0;\n        this._targetAngle = 0;\n        this._lastValidPosition = new Vec2();\n\n        // 初始化摧毁状态\n        this._isDestroyed = false;\n\n        // 初始化颜料喷洒相关\n        this._paintTimer = 0;\n        this._vehicleId = `ai_${this.node.name}_${Date.now()}`; // 生成唯一ID\n        \n        // 初始化Block碰撞冷却时间\n        this._blockCollisionCooldown = 0;\n\n        // 初始化武器系统相关\n        this._canFire = true;\n        this._fireTimer = 0;\n    }\n\n    start() {\n        this._rigidBody = this.getComponent(RigidBody2D)!;\n        if (!this._rigidBody || !this.node || !this.node.isValid) {\n            console.error('AIPlayer requires RigidBody2D component and valid node');\n            return;\n        }\n        this._rigidBody.type = ERigidBody2DType.Dynamic;\n        this._rigidBody.allowSleep = false;\n        this._rigidBody.gravityScale = 0;\n        this._rigidBody.linearDamping = 0.3;\n        this._rigidBody.angularDamping = 0.9;\n        this._rigidBody.fixedRotation = true;\n        this._lastValidPosition = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n        this._angle = this.initAngle;\n        this._targetAngle = this.initAngle;\n        this.node.setRotationFromEuler(0, 0, this.initAngle);\n\n        // 初始化血条\n        this.initHealthBar();\n\n        // 原始精灵图保存留作未来扩展\n\n        // 检查BoxCollider2D组件是否存在\n        const collider = this.getComponent(BoxCollider2D);\n        if (collider) {\n            console.log('AIPlayer BoxCollider2D component found');\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);\n        } else {\n            console.error('AIPlayer BoxCollider2D component not found');\n        }\n    }\n\n    /**\n     * 初始化血条\n     */\n    private initHealthBar() {\n        this._currentHealth = this.maxHealth;\n        \n        // 如果没有手动设置血条UI，尝试自动查找\n        // if (!this.healthBarUI) {\n        //     this.healthBarUI = this.node.getComponentInChildren(HealthBarUI);\n        // }\n        \n        // if (this.healthBarUI) {\n        //     // 设置血条的目标为当前AI车辆\n        //     this.healthBarUI.setTarget(this.node);\n        //     this.updateHealthBar();\n        // } else {\n        //     console.warn('AIPlayer: 未找到HealthBarUI组件');\n        // }\n    }\n\n    /**\n     * 更新血条显示\n     */\n    private updateHealthBar() {\n        if (this.healthBar) {\n            \n            this.healthBar.progress = this._currentHealth / this.maxHealth;\n            console.log('AIPlayer updating health bar:', this._currentHealth / this.maxHealth);\n        }\n    }\n\n    update(deltaTime: number) {\n        if (!this._rigidBody || !this.node || !this.node.isValid) return;\n\n        // 更新Block碰撞冷却时间计时器\n        if (this._blockCollisionCooldown > 0) {\n            this._blockCollisionCooldown -= deltaTime;\n        }\n\n        // 如果车辆已摧毁，执行摧毁动画逻辑\n        if (this._isDestroyed) {\n\n            return;\n        }\n        \n        const currentVelocity = this._rigidBody.linearVelocity;\n        const currentSpeed = currentVelocity.length();\n        const currentPos = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n\n        // 更新目标角度（转向）\n        if (this._direction !== 0) {\n            const turnAmount = this.turnSpeed * deltaTime * this._direction;\n            this._targetAngle -= turnAmount;\n        }\n        \n        // 平滑角度插值，防止突然转向\n        const angleDiff = this._targetAngle - this._angle;\n        if (Math.abs(angleDiff) > 0.1) {\n            this._angle += angleDiff * 0.1; // 平滑插值\n        } else {\n            this._angle = this._targetAngle;\n        }\n        \n        // 设置节点旋转\n        this.node.setRotationFromEuler(0, 0, this._angle);\n\n        // 前进\n        if (this._accel === 1) {\n            // 正常加速\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const force = new Vec2(\n                Math.cos(rad) * this.acceleration,\n                Math.sin(rad) * this.acceleration\n            );\n            this._rigidBody.applyForce(force, currentPos, true);\n        }\n        // 刹车\n        else if (this._accel === -1) {\n            // 如果当前速度方向与车辆朝向一致，施加反向力（刹车）\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const forward = new Vec2(Math.cos(rad), Math.sin(rad));\n            const dot = currentVelocity.dot(forward);\n            \n            if (dot > 0) {\n                // 施加强力反向力（刹车）\n                const brakeForce = forward.clone().multiplyScalar(-this.brakeDeceleration);\n                this._rigidBody.applyForce(brakeForce, currentPos, true);\n            } else {\n                // 允许倒车\n                const reverseForce = forward.clone().multiplyScalar(-this.acceleration * 0.5);\n                this._rigidBody.applyForce(reverseForce, currentPos, true);\n            }\n\n        }\n        // 松开加速/刹车键\n        else {\n            // 增大摩擦力，让车辆更快停下来\n            if (currentSpeed > 1) {\n                const frictionForce = currentVelocity.clone().multiplyScalar(-this.friction * 2); // 2倍摩擦\n                this._rigidBody.applyForce(frictionForce, currentPos, true);\n            }\n        }\n\n        // 限制最大速度\n        if (currentSpeed > this.maxSpeed) {\n            const normalizedVelocity = currentVelocity.clone().normalize();\n            this._rigidBody.linearVelocity = normalizedVelocity.multiplyScalar(this.maxSpeed);\n        }\n\n        // 防止车辆卡住或异常位置\n        if (currentSpeed < 0.1) {\n            // 如果速度很小，重置到上次有效位置附近\n            const distanceToLastPos = Vec2.distance(currentPos, this._lastValidPosition);\n            if (distanceToLastPos > 50) { // 如果偏离太远\n                this.node.setWorldPosition(this._lastValidPosition.x, this._lastValidPosition.y, this.node.worldPosition.z);\n                this._rigidBody.linearVelocity = new Vec2(0, 0);\n            }\n        } else {\n            // 更新有效位置\n            this._lastValidPosition = currentPos.clone();\n        }\n\n        // 防止车辆旋转过度\n        if (Math.abs(this._angle) > 360) {\n            this._angle = this._angle % 360;\n            this._targetAngle = this._targetAngle % 360;\n        }\n\n        // 更新颜料喷洒\n        this.updatePaintSpray(deltaTime);\n\n        // 更新武器系统\n        this.updateWeaponSystem(deltaTime);\n    }\n\n    // 供AI控制器调用的接口\n    public setAccel(accel: number) {\n        this._accel = accel;\n    }\n    \n    public setDirection(direction: number) {\n        this._direction = direction;\n    }\n    \n    public setTargetAngle(angle: number) {\n        this._targetAngle = angle;\n    }\n    \n    public getCurrentAngle(): number {\n        return this._angle;\n    }\n    \n    public init(angle: number) {\n        this.initAngle = angle;\n        this._angle = angle;\n        this._targetAngle = angle;\n        this.node.setRotationFromEuler(0, 0, angle);\n    }\n\n    // 血量管理接口\n    /**\n     * 设置当前生命值\n     */\n    public setHealth(health: number) {\n        this._currentHealth = Math.max(0, Math.min(health, this.maxHealth));\n        this.updateHealthBar();\n    }\n\n    /**\n     * 减少生命值\n     */\n    public takeDamage(damage: number) {\n        if (this._isDestroyed) return;\n\n        console.log('AIPlayer taking damage:', damage);\n        this.setHealth(this._currentHealth - damage);\n        this.updateHealthBar();\n\n        // 检查是否死亡\n        if (this._currentHealth <= 0) {\n            this.destroyVehicle();\n        }\n    }\n\n    /**\n     * 恢复生命值\n     */\n    public heal(amount: number) {\n        this.setHealth(this._currentHealth + amount);\n        this.updateHealthBar();\n    }\n\n    /**\n     * 获取当前生命值\n     */\n    public getHealth(): number {\n        return this._currentHealth;\n    }\n\n    /**\n     * 获取最大生命值\n     */\n    public getMaxHealth(): number {\n        return this.maxHealth;\n    }\n\n    /**\n     * 检查是否死亡\n     */\n    public isDead(): boolean {\n        return this._currentHealth <= 0;\n    }\n\n    /**\n     * 碰撞事件处理\n     */\n    onCollisionEnter(self: BoxCollider2D, other: BoxCollider2D) {\n        // console.log('AIPlayer collided with something', other.node.name);\n        \n        // 获取碰撞对象的层级\n        const otherLayer = other.node.layer;\n        const blockLayer = Layers.nameToLayer('Block');\n        \n        // 检查是否与Block层碰撞\n        if (otherLayer === blockLayer) {\n            // 检查冷却时间\n            if (this._blockCollisionCooldown > 0) {\n                // console.log('AIPlayer collided with Block but is in cooldown');\n                return; // 冷却时间内，不执行任何操作\n            }\n            \n            // 获取AIController实例\n            const aiControllerNode = find('AIController');\n            if (aiControllerNode) {\n                const aiController = aiControllerNode.getComponent(AIController);\n                if (aiController) {\n                    // 检查AI是否处于边界转向状态，如果是则不处理Block碰撞\n                    if (aiController.isAIBoundaryTurning(this)) {\n                        return;\n                    }\n                    \n                    // console.log('AIPlayer collided with Block, turning around');\n                    \n                    // 设置冷却时间\n                    this._blockCollisionCooldown = this._blockCollisionCooldownDuration;\n                    \n                    // 随机选择向左或向右掉头\n                    const turnDirection = Math.random() < 0.5 ? -1 : 1;\n                    \n                    // 随机选择掉头角度(130-180度)\n                    const turnAngle = 130 + Math.random() * 50;\n                    \n                    // 计算当前角度\n                    const currentAngle = this.getCurrentAngle();\n                    \n                    // 计算目标角度\n                    const targetAngle = turnDirection > 0 ? \n                        currentAngle + turnAngle : \n                        currentAngle - turnAngle;\n                        \n                    // 设置目标角度\n                    this.setTargetAngle(targetAngle);\n                    this.setDirection(turnDirection);\n                    this.setAccel(1);\n                }\n            }\n            return; // Block碰撞处理完成，直接返回\n        }\n        \n        const playerComponent = other.node.getComponent<player>(player);\n        if (playerComponent) {\n            console.log('AIPlayer 被玩家车辆撞击');\n            const playerRigidBody = playerComponent.getRigidBody();\n            if (playerRigidBody && !this.isDestroyed) {\n                const impactForce = new Vec2(playerRigidBody.linearVelocity.x, playerRigidBody.linearVelocity.y);\n                impactForce.normalize(); // 归一化方向\n                impactForce.multiplyScalar(20); // 增加冲力强度\n                this._rigidBody.linearVelocity = impactForce;\n            }\n\n        }\n    }\n\n    // ==================== 摧毁系统 ====================\n\n    /**\n     * 摧毁车辆\n     */\n    private destroyVehicle() {\n        if (this._isDestroyed) return;\n\n        this._isDestroyed = true;\n        console.log('AI车辆被摧毁！');\n        SoundManager.instance.playSoundEffect('carDestruction');\n\n        // 切换到摧毁状态的精灵图\n        if (this.destroyedSprite) {\n            const sprite = this.getComponent(Sprite);\n            if (sprite) {\n                sprite.spriteFrame = this.destroyedSprite;\n            }\n        }\n\n        // 隐藏血条\n        if (this.healthBar && this.healthBar.node) {\n            this.healthBar.node.active = false;\n        }\n\n        // 开始摧毁动画\n        this.startDestroyAnimation();\n\n        // 立即更新敌人数量（不等待节点移除）\n        this.updateEnemyCount();\n\n        // 延迟移除节点\n        this.scheduleRemoveNode();\n    }\n\n    /**\n     * 更新敌人数量\n     */\n    private updateEnemyCount() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            // 计算当前存活的AI数量\n            const allAIPlayers = gameManager.getAIPlayers();\n            const aliveCount = allAIPlayers.filter((ai: AIPlayer) => !ai.isDestroyed()).length;\n            gameManager.refreshEnemyCount(aliveCount);\n        }\n    }\n\n    /**\n     * 安排移除节点\n     */\n    private scheduleRemoveNode() {\n        if (this.node && this.node.isValid) {\n            // 使用scheduleOnce在指定时间后执行移除\n            this.scheduleOnce(() => {\n                this.removeVehicleNode();\n            }, this.removeDelay);\n        }\n    }\n\n    /**\n     * 移除车辆节点\n     */\n    private removeVehicleNode() {\n        if (this.node && this.node.isValid) {\n            console.log('移除AI车辆节点');\n\n            // 从GameManager的AI列表中移除\n            const gameManager = GameManager.getInstance();\n            if (gameManager) {\n                const aiPlayers = gameManager.getAIPlayers();\n                const index = aiPlayers.indexOf(this);\n                if (index !== -1) {\n                    aiPlayers.splice(index, 1);\n                }\n\n                // 再次更新敌人数量（基于实际存在的AI数量）\n                gameManager.refreshEnemyCount(aiPlayers.length);\n            }\n\n            // 移除节点\n            this.node.removeFromParent();\n        }\n    }\n\n    /**\n     * 开始摧毁动画\n     */\n    private startDestroyAnimation() {\n        if (this.node) {\n            // 添加摧毁动画效果\n            tween(this.node)\n                .to(2.0, {\n                    scale: new Vec3(1.1, 1.1, 1),  // 稍微缩小\n                    // angle: this.node.angle + 180 // 旋转180度\n                })\n                .start();\n        }\n    }\n\n    /**\n     * 更新摧毁动画\n     */\n   \n\n    /**\n     * 是否已摧毁\n     */\n    public isDestroyed(): boolean {\n        return this._isDestroyed;\n    }\n\n    /**\n     * 获取车辆ID\n     */\n    public getVehicleId(): string {\n        return this._vehicleId;\n    }\n\n    /**\n     * 恢复车辆（用于重新开始游戏）\n     */\n    // public restoreVehicle() {\n    //     // 取消移除节点的计划\n    //     this.unschedule(this.removeVehicleNode);\n\n    //     this._isDestroyed = false;\n    //     this._currentHealth = this.maxHealth;\n\n    //     // 恢复原始精灵图\n    //     if (this._originalSprite) {\n    //         const sprite = this.getComponent(Sprite);\n    //         if (sprite) {\n    //             sprite.spriteFrame = this._originalSprite;\n    //         }\n    //     }\n\n    //     // 显示血条\n    //     if (this.healthBar && this.healthBar.node) {\n    //         this.healthBar.node.active = true;\n    //     }\n\n    //     // 更新血条\n    //     this.updateHealthBar();\n\n    //     // 恢复节点状态\n    //     if (this.node) {\n    //         this.node.setScale(1, 1);\n    //         this.node.angle = this.initAngle;\n    //     }\n\n    //     // 重置速度\n    //     if (this._rigidBody) {\n    //         this._rigidBody.linearVelocity = Vec2.ZERO;\n    //     }\n\n    //     console.log('AI车辆已恢复');\n    // }\n\n    // ==================== 颜料喷洒系统 ====================\n\n    /**\n     * 更新颜料喷洒\n     * @param deltaTime 帧时间间隔\n     */\n    private updatePaintSpray(deltaTime: number): void {\n        if (this._isDestroyed || !this.paintPrefab) return;\n\n        // 更新计时器\n        this._paintTimer += deltaTime;\n\n        // 检查是否到了喷洒时间\n        if (this._paintTimer >= this.paintSprayInterval) {\n            this.sprayPaint();\n            this._paintTimer = 0; // 重置计时器\n        }\n    }\n\n    /**\n     * 喷洒颜料\n     */\n    private sprayPaint(): void {\n        const gameManager = GameManager.getInstance();\n        if (!gameManager) {\n            console.warn('GameManager未找到，无法喷洒颜料');\n            return;\n        }\n\n        // 获取当前车辆的世界位置\n        const worldPosition = this.node.getWorldPosition();\n\n        // 通过GameManager喷洒颜料\n        gameManager.sprayPaint(this.paintPrefab, worldPosition, this._vehicleId);\n    }\n\n    // ==================== 武器系统 ====================\n\n    /**\n     * 更新武器系统\n     * @param deltaTime 帧时间间隔\n     */\n    private updateWeaponSystem(deltaTime: number): void {\n        if (this._isDestroyed) return;\n\n        // 更新射击计时器\n        this._fireTimer += deltaTime;\n\n        // 检查是否可以射击\n        const fireInterval = 1 / this.fireRate;\n        if (this._fireTimer >= fireInterval) {\n            this._canFire = true;\n        }\n\n        // 检查是否应该射击\n        this.checkAndShoot();\n    }\n\n    /**\n     * 检查是否应该射击\n     */\n    private checkAndShoot(): void {\n        if (!this._canFire) return;\n\n        // 获取玩家位置\n        const gameManager = GameManager.getInstance();\n        if (!gameManager) return;\n\n        const playerComponent = gameManager.getPlayerComponent();\n        if (!playerComponent || !playerComponent.node) return;\n\n        // 获取玩家和AI的位置\n        const playerPos = playerComponent.node.getWorldPosition();\n        const aiPos = this.node.getWorldPosition();\n\n        // 计算玩家相对于AI的方向向量\n        const toPlayer = new Vec2(playerPos.x - aiPos.x, playerPos.y - aiPos.y);\n\n        // 计算玩家相对于AI的角度\n        const angleToPlayer = Math.atan2(toPlayer.y, toPlayer.x) * 180 / Math.PI;\n\n        // 获取AI车辆的正前方角度（AI的角度+90度，因为车辆默认朝向是-90度）\n        const aiForwardAngle = this._angle + 90;\n\n        // 计算角度差\n        let angleDiff = angleToPlayer - aiForwardAngle;\n\n        // 将角度差标准化到-180到180度范围内\n        while (angleDiff > 180) angleDiff -= 360;\n        while (angleDiff < -180) angleDiff += 360;\n\n        // 检查玩家是否在AI车辆正前方的-90度到90度范围内\n        if (Math.abs(angleDiff) <= 90) {\n            this.shoot();\n        }\n    }\n\n    /**\n     * 射击方法\n     */\n    public shoot(): void {\n        if (!this._canFire || this._isDestroyed) return;\n\n        // 重置射击状态\n        this._canFire = false;\n        this._fireTimer = 0;\n\n        // 根据武器类型选择子弹预制体\n        let bulletPrefab: Prefab | null = null;\n        switch (this.weaponType) {\n            case WeaponType.NORMAL:\n                bulletPrefab = this.normalBulletPrefab;\n                break;\n            case WeaponType.DART:\n                bulletPrefab = this.dartPrefab;\n                break;\n            case WeaponType.ROCKET:\n                bulletPrefab = this.rocketPrefab;\n                break;\n        }\n\n        // 检查预制体是否存在\n        if (!bulletPrefab) {\n            console.warn('AI子弹预制体未设置');\n            // 允许重新射击\n            this._canFire = true;\n            return;\n        }\n\n        // 获取当前车辆的朝向\n        const rad = (this._angle + 90) * Math.PI / 180;\n        const direction = new Vec2(Math.cos(rad), Math.sin(rad));\n\n        // 计算子弹发射位置（车辆正前方）\n        const vehicleWorldPos = this.node.worldPosition;\n        const offsetDistance = 70; // 子弹发射偏移距离（像素）\n        const bulletStartPos = new Vec3(\n            vehicleWorldPos.x + direction.x * offsetDistance,\n            vehicleWorldPos.y + direction.y * offsetDistance,\n            vehicleWorldPos.z\n        );\n\n        // 获取GameManager实例并发射子弹\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.fireBullet(bulletPrefab, bulletStartPos, direction, this._vehicleId, this.weaponType);\n        }\n\n        // 播放射击音效\n        // SoundManager.instance.playSoundEffect('weaponFire');\n    }\n\n}"]}