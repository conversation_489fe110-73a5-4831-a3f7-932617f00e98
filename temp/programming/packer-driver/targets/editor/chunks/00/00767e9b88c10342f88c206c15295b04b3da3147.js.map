{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts"], "names": ["_decorator", "Component", "input", "Input", "KeyCode", "<PERSON><PERSON><PERSON><PERSON>", "LevelGrade", "SelectManager", "ccclass", "property", "LevelGradeTest", "onLoad", "on", "EventType", "KEY_DOWN", "onKeyDown", "onDestroy", "off", "event", "<PERSON><PERSON><PERSON><PERSON>", "instance", "console", "error", "keyCode", "KEY_Q", "testLevelCompletion", "KEY_W", "KEY_E", "KEY_R", "KEY_T", "KEY_Y", "KEY_U", "KEY_I", "showAllLevelStatus", "KEY_O", "resetPlayerData", "KEY_P", "unlockAllLevels", "levelId", "time", "stars", "log", "isLevelUnlocked", "unlockLevel", "updateLevelProgress", "progress", "getLevelProgress", "grade", "bestTime", "attempts", "unlockedLevels", "player<PERSON><PERSON>", "join", "selectManager", "updateLevelToggles", "savePlayerData", "i", "isUnlocked", "completed", "getGradeDescription", "S", "A", "B", "C", "D", "F"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;;AACrCC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,U,iBAAAA,U;;AACfC,MAAAA,a,iBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;gCAEaU,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ;AAAA;AAAA,yC,2BAFb,MACaC,cADb,SACoCT,SADpC,CAC8C;AAAA;AAAA;;AAAA;AAAA;;AAI1CU,QAAAA,MAAM,GAAG;AACLT,UAAAA,KAAK,CAACU,EAAN,CAAST,KAAK,CAACU,SAAN,CAAgBC,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACRd,UAAAA,KAAK,CAACe,GAAN,CAAUd,KAAK,CAACU,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACH;;AAEDA,QAAAA,SAAS,CAACG,KAAD,EAAa;AAClB,gBAAMC,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;;AACA,cAAI,CAACD,aAAL,EAAoB;AAChBE,YAAAA,OAAO,CAACC,KAAR,CAAc,qBAAd;AACA;AACH;;AAED,kBAAQJ,KAAK,CAACK,OAAd;AACI,iBAAKnB,OAAO,CAACoB,KAAb;AACI,mBAAKC,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAACsB,KAAb;AACI,mBAAKD,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAACuB,KAAb;AACI,mBAAKF,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAACwB,KAAb;AACI,mBAAKH,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAACyB,KAAb;AACI,mBAAKJ,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAAC0B,KAAb;AACI,mBAAKL,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAAC2B,KAAb;AACI,mBAAKN,mBAAL,CAAyB,SAAzB,EAAoC,KAApC,EAA2C,CAA3C,EADJ,CACmD;;AAC/C;;AACJ,iBAAKrB,OAAO,CAAC4B,KAAb;AACI,mBAAKC,kBAAL;AACA;;AACJ,iBAAK7B,OAAO,CAAC8B,KAAb;AACI,mBAAKC,eAAL;AACA;;AACJ,iBAAK/B,OAAO,CAACgC,KAAb;AACI,mBAAKC,eAAL;AACA;AA9BR;AAgCH;AAED;AACJ;AACA;;;AACIZ,QAAAA,mBAAmB,CAACa,OAAD,EAAkBC,IAAlB,EAAgCC,KAAhC,EAA+C;AAC9D,gBAAMrB,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AAEAC,UAAAA,OAAO,CAACoB,GAAR,CAAa,kBAAb;AACApB,UAAAA,OAAO,CAACoB,GAAR,CAAa,OAAMH,OAAQ,EAA3B;AACAjB,UAAAA,OAAO,CAACoB,GAAR,CAAa,OAAMF,IAAK,OAAMA,IAAI,GAAC,IAAK,IAAxC;AACAlB,UAAAA,OAAO,CAACoB,GAAR,CAAa,OAAMD,KAAM,EAAzB,EAN8D,CAQ9D;;AACA,cAAI,CAACrB,aAAa,CAACuB,eAAd,CAA8BJ,OAA9B,CAAL,EAA6C;AACzCnB,YAAAA,aAAa,CAACwB,WAAd,CAA0BL,OAA1B;AACAjB,YAAAA,OAAO,CAACoB,GAAR,CAAa,WAAUH,OAAQ,EAA/B;AACH,WAZ6D,CAc9D;;;AACAnB,UAAAA,aAAa,CAACyB,mBAAd,CAAkCN,OAAlC,EAA2CC,IAA3C,EAAiDC,KAAjD,EAf8D,CAiB9D;;AACA,gBAAMK,QAAQ,GAAG1B,aAAa,CAAC2B,gBAAd,CAA+BR,OAA/B,CAAjB;;AACA,cAAIO,QAAJ,EAAc;AACVxB,YAAAA,OAAO,CAACoB,GAAR,CAAa,OAAMI,QAAQ,CAACE,KAAM,EAAlC;AACA1B,YAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQI,QAAQ,CAACG,QAAS,IAAvC;AACA3B,YAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQI,QAAQ,CAACI,QAAS,EAAvC;AACH,WAvB6D,CAyB9D;;;AACA,gBAAMC,cAAc,GAAG/B,aAAa,CAACgC,UAAd,CAAyBD,cAAhD;AACA7B,UAAAA,OAAO,CAACoB,GAAR,CAAa,WAAUS,cAAc,CAACE,IAAf,CAAoB,IAApB,CAA0B,EAAjD,EA3B8D,CA6B9D;;AACA,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,kBAAnB;AACH,WAhC6D,CAkC9D;;;AACAnC,UAAAA,aAAa,CAACoC,cAAd;AACH;AAED;AACJ;AACA;;;AACItB,QAAAA,kBAAkB,GAAG;AACjB,gBAAMd,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AAEAC,UAAAA,OAAO,CAACoB,GAAR,CAAa,kBAAb;AACApB,UAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQtB,aAAa,CAACgC,UAAd,CAAyBD,cAAzB,CAAwCE,IAAxC,CAA6C,IAA7C,CAAmD,EAAxE,EAJiB,CAMjB;;AACA,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,kBAAMlB,OAAO,GAAI,SAAQkB,CAAE,EAA3B;AACA,kBAAMC,UAAU,GAAGtC,aAAa,CAACuB,eAAd,CAA8BJ,OAA9B,CAAnB;AACA,kBAAMO,QAAQ,GAAG1B,aAAa,CAAC2B,gBAAd,CAA+BR,OAA/B,CAAjB;AAEAjB,YAAAA,OAAO,CAACoB,GAAR,CAAa,KAAIH,OAAQ,GAAzB;AACAjB,YAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQgB,UAAW,EAAhC;;AAEA,gBAAIZ,QAAQ,IAAIA,QAAQ,CAACa,SAAzB,EAAoC;AAChCrC,cAAAA,OAAO,CAACoB,GAAR,CAAa,UAAb;AACApB,cAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQI,QAAQ,CAACE,KAAM,EAApC;AACA1B,cAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQI,QAAQ,CAACL,KAAM,EAApC;AACAnB,cAAAA,OAAO,CAACoB,GAAR,CAAa,WAAUI,QAAQ,CAACG,QAAS,OAAMH,QAAQ,CAACG,QAAT,GAAkB,IAAK,IAAtE;AACA3B,cAAAA,OAAO,CAACoB,GAAR,CAAa,WAAUI,QAAQ,CAACI,QAAS,EAAzC;AACH,aAND,MAMO;AACH5B,cAAAA,OAAO,CAACoB,GAAR,CAAa,UAAb;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIN,QAAAA,eAAe,GAAG;AACd,gBAAMhB,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AACAD,UAAAA,aAAa,CAACgB,eAAd;AAEAd,UAAAA,OAAO,CAACoB,GAAR,CAAY,mBAAZ,EAJc,CAMd;;AACA,cAAI,KAAKY,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,kBAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACIjB,QAAAA,eAAe,GAAG;AACd,gBAAMlB,aAAa,GAAG;AAAA;AAAA,8CAAcC,QAApC;AAEAC,UAAAA,OAAO,CAACoB,GAAR,CAAY,kBAAZ;;AAEA,eAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,kBAAMlB,OAAO,GAAI,SAAQkB,CAAE,EAA3B;;AACA,gBAAI,CAACrC,aAAa,CAACuB,eAAd,CAA8BJ,OAA9B,CAAL,EAA6C;AACzCnB,cAAAA,aAAa,CAACwB,WAAd,CAA0BL,OAA1B;AACAjB,cAAAA,OAAO,CAACoB,GAAR,CAAa,SAAQH,OAAQ,EAA7B;AACH;AACJ,WAXa,CAad;;;AACA,cAAI,KAAKe,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,kBAAnB;AACH,WAhBa,CAkBd;;;AACAnC,UAAAA,aAAa,CAACoC,cAAd;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,mBAAmB,CAACZ,KAAD,EAA4B;AAC3C,kBAAQA,KAAR;AACI,iBAAK;AAAA;AAAA,0CAAWa,CAAhB;AAAmB,qBAAO,YAAP;;AACnB,iBAAK;AAAA;AAAA,0CAAWC,CAAhB;AAAmB,qBAAO,YAAP;;AACnB,iBAAK;AAAA;AAAA,0CAAWC,CAAhB;AAAmB,qBAAO,YAAP;;AACnB,iBAAK;AAAA;AAAA,0CAAWC,CAAhB;AAAmB,qBAAO,WAAP;;AACnB,iBAAK;AAAA;AAAA,0CAAWC,CAAhB;AAAmB,qBAAO,WAAP;;AACnB,iBAAK;AAAA;AAAA,0CAAWC,CAAhB;AAAmB,qBAAO,WAAP;;AACnB;AAAS,qBAAO,MAAP;AAPb;AASH;;AAjLyC,O;;;;;iBAEX,I", "sourcesContent": ["import { _decorator, Component, input, Input, KeyCode } from 'cc';\nimport { PlayerManager, LevelGrade } from './PlayerManager';\nimport { SelectManager } from './SelectManager';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 关卡评级系统测试脚本\n * 测试键位：\n * - Q: 完成level-1，获得S级评价（25秒，3星）\n * - W: 完成level-1，获得A级评价（40秒，3星）\n * - E: 完成level-1，获得B级评价（50秒，3星）\n * - R: 完成level-1，获得C级评价（50秒，2星）\n * - T: 完成level-1，获得D级评价（70秒，2星）\n * - Y: 完成level-1，获得F级评价（60秒，0星）\n * - U: 完成level-2，获得D级评价（解锁level-3）\n * - I: 显示所有关卡状态\n * - O: 重置玩家数据\n * - P: 解锁所有关卡（测试用）\n */\n@ccclass('LevelGradeTest')\nexport class LevelGradeTest extends Component {\n    @property(SelectManager)\n    selectManager: SelectManager = null!;\n\n    onLoad() {\n        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n    }\n\n    onDestroy() {\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n    }\n\n    onKeyDown(event: any) {\n        const playerManager = PlayerManager.instance;\n        if (!playerManager) {\n            console.error('PlayerManager 实例不存在');\n            return;\n        }\n\n        switch (event.keyCode) {\n            case KeyCode.KEY_Q:\n                this.testLevelCompletion('level-1', 25000, 3); // S级\n                break;\n            case KeyCode.KEY_W:\n                this.testLevelCompletion('level-1', 40000, 3); // A级\n                break;\n            case KeyCode.KEY_E:\n                this.testLevelCompletion('level-1', 50000, 3); // B级\n                break;\n            case KeyCode.KEY_R:\n                this.testLevelCompletion('level-1', 50000, 2); // C级\n                break;\n            case KeyCode.KEY_T:\n                this.testLevelCompletion('level-1', 70000, 2); // D级\n                break;\n            case KeyCode.KEY_Y:\n                this.testLevelCompletion('level-1', 60000, 0); // F级\n                break;\n            case KeyCode.KEY_U:\n                this.testLevelCompletion('level-2', 65000, 2); // D级，应该解锁level-3\n                break;\n            case KeyCode.KEY_I:\n                this.showAllLevelStatus();\n                break;\n            case KeyCode.KEY_O:\n                this.resetPlayerData();\n                break;\n            case KeyCode.KEY_P:\n                this.unlockAllLevels();\n                break;\n        }\n    }\n\n    /**\n     * 测试关卡完成\n     */\n    testLevelCompletion(levelId: string, time: number, stars: number) {\n        const playerManager = PlayerManager.instance;\n        \n        console.log(`\\n=== 测试关卡完成 ===`);\n        console.log(`关卡: ${levelId}`);\n        console.log(`时间: ${time}ms (${time/1000}秒)`);\n        console.log(`星星: ${stars}`);\n        \n        // 确保关卡已解锁\n        if (!playerManager.isLevelUnlocked(levelId)) {\n            playerManager.unlockLevel(levelId);\n            console.log(`自动解锁关卡: ${levelId}`);\n        }\n        \n        // 更新关卡进度\n        playerManager.updateLevelProgress(levelId, time, stars);\n        \n        // 获取评级结果\n        const progress = playerManager.getLevelProgress(levelId);\n        if (progress) {\n            console.log(`评级: ${progress.grade}`);\n            console.log(`最佳时间: ${progress.bestTime}ms`);\n            console.log(`尝试次数: ${progress.attempts}`);\n        }\n        \n        // 检查是否解锁了新关卡\n        const unlockedLevels = playerManager.playerData.unlockedLevels;\n        console.log(`当前解锁关卡: ${unlockedLevels.join(', ')}`);\n        \n        // 更新UI\n        if (this.selectManager) {\n            this.selectManager.updateLevelToggles();\n        }\n        \n        // 保存数据\n        playerManager.savePlayerData();\n    }\n\n    /**\n     * 显示所有关卡状态\n     */\n    showAllLevelStatus() {\n        const playerManager = PlayerManager.instance;\n        \n        console.log(`\\n=== 所有关卡状态 ===`);\n        console.log(`解锁关卡: ${playerManager.playerData.unlockedLevels.join(', ')}`);\n        \n        // 显示每个关卡的详细信息\n        for (let i = 1; i <= 5; i++) {\n            const levelId = `level-${i}`;\n            const isUnlocked = playerManager.isLevelUnlocked(levelId);\n            const progress = playerManager.getLevelProgress(levelId);\n            \n            console.log(`\\n${levelId}:`);\n            console.log(`  解锁: ${isUnlocked}`);\n            \n            if (progress && progress.completed) {\n                console.log(`  已完成: 是`);\n                console.log(`  评级: ${progress.grade}`);\n                console.log(`  星星: ${progress.stars}`);\n                console.log(`  最佳时间: ${progress.bestTime}ms (${progress.bestTime/1000}秒)`);\n                console.log(`  尝试次数: ${progress.attempts}`);\n            } else {\n                console.log(`  已完成: 否`);\n            }\n        }\n    }\n\n    /**\n     * 重置玩家数据\n     */\n    resetPlayerData() {\n        const playerManager = PlayerManager.instance;\n        playerManager.resetPlayerData();\n        \n        console.log('\\n=== 玩家数据已重置 ===');\n        \n        // 更新UI\n        if (this.selectManager) {\n            this.selectManager.updateLevelToggles();\n        }\n    }\n\n    /**\n     * 解锁所有关卡（测试用）\n     */\n    unlockAllLevels() {\n        const playerManager = PlayerManager.instance;\n        \n        console.log('\\n=== 解锁所有关卡 ===');\n        \n        for (let i = 1; i <= 5; i++) {\n            const levelId = `level-${i}`;\n            if (!playerManager.isLevelUnlocked(levelId)) {\n                playerManager.unlockLevel(levelId);\n                console.log(`解锁关卡: ${levelId}`);\n            }\n        }\n        \n        // 更新UI\n        if (this.selectManager) {\n            this.selectManager.updateLevelToggles();\n        }\n        \n        // 保存数据\n        playerManager.savePlayerData();\n    }\n\n    /**\n     * 获取评级说明\n     */\n    getGradeDescription(grade: LevelGrade): string {\n        switch (grade) {\n            case LevelGrade.S: return 'S级 - 完美表现！';\n            case LevelGrade.A: return 'A级 - 优秀表现！';\n            case LevelGrade.B: return 'B级 - 良好表现！';\n            case LevelGrade.C: return 'C级 - 一般表现';\n            case LevelGrade.D: return 'D级 - 勉强通过';\n            case LevelGrade.F: return 'F级 - 需要重试';\n            default: return '未知评级';\n        }\n    }\n}\n"]}