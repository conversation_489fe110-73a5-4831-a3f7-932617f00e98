{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts"], "names": ["_decorator", "Component", "sys", "director", "ccclass", "property", "LevelGrade", "<PERSON><PERSON><PERSON><PERSON>", "STORAGE_KEY", "STORAGE_KEY_BACKUP", "_playerData", "_autoSaveInterval", "_lastAutoSaveTime", "_dataChangeCallbacks", "instance", "_instance", "player<PERSON><PERSON>", "onLoad", "node", "destroy", "addPersistRootNode", "scheduleOnce", "_initPlayerData", "loadPlayerData", "console", "log", "error", "onDestroy", "update", "deltaTime", "currentTime", "Date", "now", "level", "money", "experience", "unlockedCars", "currentCar", "unlockedLevels", "currentLevel", "levelProgress", "stars", "completed", "bestTime", "grade", "F", "attempts", "settings", "soundVolume", "musicVolume", "statistics", "totalRaces", "totalWins", "totalMoneyEarned", "lastSaveTime", "createTime", "data", "jsonData", "localStorage", "getItem", "JSON", "parse", "_mergePlayerData", "_notifyDataChange", "savePlayerData", "setItem", "stringify", "defaultData", "savedData", "merged", "key", "hasOwnProperty", "Array", "isArray", "addDataChangeListener", "callback", "push", "removeDataChangeListener", "index", "indexOf", "splice", "for<PERSON>ach", "addMoney", "amount", "spendMoney", "addExperience", "exp", "expNeeded", "unlockCar", "carId", "isCarUnlocked", "setCurrentCar", "unlockLevel", "levelId", "updateLevelProgress", "performance", "S", "A", "B", "progress", "checkAndUnlockNextLevel", "currentLevelId", "currentProgress", "isGradePassable", "nextLevelId", "getNextLevelId", "match", "currentNumber", "parseInt", "getLevelProgress", "isLevelUnlocked", "getLevelGradeText", "getLevelGradeColor", "updateSettings", "updateStatistics", "updates", "resetPlayerData", "exportPlayerData", "importPlayerData"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,G,OAAAA,G;AAAWC,MAAAA,Q,OAAAA,Q;;;;;;;;;OAE3C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U,GAE9B;AA4BA;AAEA;;AASA;4BACYM,U,0BAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;eAAAA,U;cASZ;AAMA;;;AAOA;+BAGaC,a,WADZH,OAAO,CAAC,eAAD,C,2BAAR,MACaG,aADb,SACmCN,SADnC,CAC6C;AAAA;AAAA;AAGzC;AAHyC,eAIxBO,WAJwB,GAIV,sBAJU;AAAA,eAKxBC,kBALwB,GAKH,6BALG;AAOzC;AAPyC,eAQjCC,WARiC,GAQP,IARO;AAUzC;AACA;AACA;AACA;AAEA;AAfyC,eAgBjCC,iBAhBiC,GAgBL,KAhBK;AAgBE;AAhBF,eAiBjCC,iBAjBiC,GAiBL,CAjBK;AAmBzC;AAnByC,eAoBjCC,oBApBiC,GAoBsB,EApBtB;AAAA;;AAsBf,mBAARC,QAAQ,GAAkB;AACxC,iBAAOP,aAAa,CAACQ,SAArB;AACH;;AAEoB,YAAVC,UAAU,GAAe;AAChC,iBAAO,KAAKN,WAAZ;AACH,SA5BwC,CA8BzC;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAEAO,QAAAA,MAAM,GAAG;AACL;AACA,cAAIV,aAAa,CAACQ,SAAlB,EAA6B;AACzB,iBAAKG,IAAL,CAAUC,OAAV;AACA;AACH;;AAEDZ,UAAAA,aAAa,CAACQ,SAAd,GAA0B,IAA1B;;AAEA,cAAI;AACA;AACAZ,YAAAA,QAAQ,CAACiB,kBAAT,CAA4B,KAAKF,IAAjC,EAFA,CAIA;;AACA,iBAAKG,YAAL,CAAkB,MAAM;AACpB,mBAAKC,eAAL;;AACA,mBAAKC,cAAL;AACAC,cAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ;AACH,aAJD,EAIG,GAJH;AAMH,WAXD,CAWE,OAAOC,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,mCAAd,EAAmDA,KAAnD;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAIpB,aAAa,CAACQ,SAAd,KAA4B,IAAhC,EAAsC;AAClCR,YAAAA,aAAa,CAACQ,SAAd,GAA0B,IAA1B;AACH;AACJ;;AAEDa,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB;AACA,gBAAMC,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;;AACA,cAAIF,WAAW,GAAG,KAAKlB,iBAAnB,GAAuC,KAAKD,iBAAhD,EAAmE;AAC/D;AACA,iBAAKC,iBAAL,GAAyBkB,WAAzB;AACH;AACJ;AAED;AACJ;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACYR,QAAAA,eAAe,GAAG;AACtB,eAAKZ,WAAL,GAAmB;AACfuB,YAAAA,KAAK,EAAE,CADQ;AAEfC,YAAAA,KAAK,EAAE,CAFQ;AAGfC,YAAAA,UAAU,EAAE,CAHG;AAKfC,YAAAA,YAAY,EAAE,EALC;AAKG;AAClBC,YAAAA,UAAU,EAAE,EANG;AAOf;AAEAC,YAAAA,cAAc,EAAE,CAAC,SAAD,CATD;AASc;AAC7BC,YAAAA,YAAY,EAAE,SAVC;AAWfC,YAAAA,aAAa,EAAE;AACX,yBAAW;AACPC,gBAAAA,KAAK,EAAE,CADA;AAEPC,gBAAAA,SAAS,EAAE,KAFJ;AAGPC,gBAAAA,QAAQ,EAAE,CAHH;AAIPC,gBAAAA,KAAK,EAAEtC,UAAU,CAACuC,CAJX;AAKPC,gBAAAA,QAAQ,EAAE;AALH;AADA,aAXA;AAqBfC,YAAAA,QAAQ,EAAE;AACNC,cAAAA,WAAW,EAAE,GADP;AAENC,cAAAA,WAAW,EAAE;AAFP,aArBK;AA0BfC,YAAAA,UAAU,EAAE;AACRC,cAAAA,UAAU,EAAE,CADJ;AAERC,cAAAA,SAAS,EAAE,CAFH;AAGRC,cAAAA,gBAAgB,EAAE;AAHV,aA1BG;AAgCfC,YAAAA,YAAY,EAAEvB,IAAI,CAACC,GAAL,EAhCC;AAiCfuB,YAAAA,UAAU,EAAExB,IAAI,CAACC,GAAL;AAjCG,WAAnB;AAmCH;AAED;AACJ;AACA;;;AAC+B,cAAdT,cAAc,GAAkB;AACzC,cAAI;AACA,gBAAIiC,IAAS,GAAG,IAAhB,CADA,CAGA;;AACA,kBAAMC,QAAQ,GAAGvD,GAAG,CAACwD,YAAJ,CAAiBC,OAAjB,CAAyB,KAAKnD,WAA9B,CAAjB;;AACA,gBAAIiD,QAAJ,EAAc;AACVD,cAAAA,IAAI,GAAGI,IAAI,CAACC,KAAL,CAAWJ,QAAX,CAAP;AACH;;AAED,gBAAID,IAAJ,EAAU;AACN;AACA,mBAAK9C,WAAL,GAAmB,KAAKoD,gBAAL,CAAsB,KAAKpD,WAA3B,EAAwC8C,IAAxC,CAAnB;AACAhC,cAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACH,aAJD,MAIO;AACHD,cAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACH,aAfD,CAiBA;;;AACA,iBAAKsC,iBAAL;AAEH,WApBD,CAoBE,OAAOrC,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACH;AACJ;AAED;AACJ;AACA;;;AAC+B,cAAdsC,cAAc,GAAkB;AACzC,cAAI;AACA,iBAAKtD,WAAL,CAAiB4C,YAAjB,GAAgCvB,IAAI,CAACC,GAAL,EAAhC,CADA,CAGA;;AACA9B,YAAAA,GAAG,CAACwD,YAAJ,CAAiBO,OAAjB,CAAyB,KAAKzD,WAA9B,EAA2CoD,IAAI,CAACM,SAAL,CAAe,KAAKxD,WAApB,CAA3C;AAEAc,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AAEH,WARD,CAQE,OAAOC,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACH;AACJ;AAED;AACJ;AACA;;;AACYoC,QAAAA,gBAAgB,CAACK,WAAD,EAA0BC,SAA1B,EAAsD;AAC1E,gBAAMC,MAAM,GAAG,EAAE,GAAGF;AAAL,WAAf,CAD0E,CAG1E;;AACA,eAAK,MAAMG,GAAX,IAAkBF,SAAlB,EAA6B;AACzB,gBAAIA,SAAS,CAACG,cAAV,CAAyBD,GAAzB,CAAJ,EAAmC;AAC/B,kBAAI,OAAOF,SAAS,CAACE,GAAD,CAAhB,KAA0B,QAA1B,IAAsCF,SAAS,CAACE,GAAD,CAAT,KAAmB,IAAzD,IAAiE,CAACE,KAAK,CAACC,OAAN,CAAcL,SAAS,CAACE,GAAD,CAAvB,CAAtE,EAAqG;AACjGD,gBAAAA,MAAM,CAACC,GAAD,CAAN,GAAc,EAAE,GAAGD,MAAM,CAACC,GAAD,CAAX;AAAkB,qBAAGF,SAAS,CAACE,GAAD;AAA9B,iBAAd;AACH,eAFD,MAEO;AACHD,gBAAAA,MAAM,CAACC,GAAD,CAAN,GAAcF,SAAS,CAACE,GAAD,CAAvB;AACH;AACJ;AACJ;;AAED,iBAAOD,MAAP;AACH;AAED;AACJ;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACWK,QAAAA,qBAAqB,CAACC,QAAD,EAA6C;AACrE,eAAK9D,oBAAL,CAA0B+D,IAA1B,CAA+BD,QAA/B;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,wBAAwB,CAACF,QAAD,EAA6C;AACxE,gBAAMG,KAAK,GAAG,KAAKjE,oBAAL,CAA0BkE,OAA1B,CAAkCJ,QAAlC,CAAd;;AACA,cAAIG,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAKjE,oBAAL,CAA0BmE,MAA1B,CAAiCF,KAAjC,EAAwC,CAAxC;AACH;AACJ;AAED;AACJ;AACA;;;AACYf,QAAAA,iBAAiB,GAAS;AAC9B,eAAKlD,oBAAL,CAA0BoE,OAA1B,CAAkCN,QAAQ,IAAI;AAC1C,gBAAI;AACAA,cAAAA,QAAQ,CAAC,KAAKjE,WAAN,CAAR;AACH,aAFD,CAEE,OAAOgB,KAAP,EAAc;AACZF,cAAAA,OAAO,CAACE,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACH;AACJ,WAND;AAOH,SApSwC,CAsSzC;;AAEA;AACJ;AACA;;;AACWwD,QAAAA,QAAQ,CAACC,MAAD,EAAuB;AAClC,eAAKzE,WAAL,CAAiBwB,KAAjB,IAA0BiD,MAA1B;AACA,eAAKzE,WAAL,CAAiBwC,UAAjB,CAA4BG,gBAA5B,IAAgD8B,MAAhD;;AACA,eAAKpB,iBAAL;AACH;AAED;AACJ;AACA;;;AACWqB,QAAAA,UAAU,CAACD,MAAD,EAA0B;AACvC,cAAI,KAAKzE,WAAL,CAAiBwB,KAAjB,IAA0BiD,MAA9B,EAAsC;AAClC,iBAAKzE,WAAL,CAAiBwB,KAAjB,IAA0BiD,MAA1B;;AACA,iBAAKpB,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACWsB,QAAAA,aAAa,CAACC,GAAD,EAAoB;AACpC,eAAK5E,WAAL,CAAiByB,UAAjB,IAA+BmD,GAA/B,CADoC,CAGpC;;AACA,gBAAMC,SAAS,GAAG,KAAK7E,WAAL,CAAiBuB,KAAjB,GAAyB,GAA3C,CAJoC,CAIY;;AAChD,cAAI,KAAKvB,WAAL,CAAiByB,UAAjB,IAA+BoD,SAAnC,EAA8C;AAC1C,iBAAK7E,WAAL,CAAiBuB,KAAjB;AACA,iBAAKvB,WAAL,CAAiByB,UAAjB,IAA+BoD,SAA/B;AACA/D,YAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKf,WAAL,CAAiBuB,KAAM,KAA5C;AACH;;AAED,eAAK8B,iBAAL;AACH;AAED;AACJ;AACA;;;AACWyB,QAAAA,SAAS,CAACC,KAAD,EAAyB;AACrC,cAAI,KAAK/E,WAAL,CAAiB0B,YAAjB,CAA8B2C,OAA9B,CAAsCU,KAAtC,MAAiD,CAAC,CAAtD,EAAyD;AACrD,iBAAK/E,WAAL,CAAiB0B,YAAjB,CAA8BwC,IAA9B,CAAmCa,KAAnC,EADqD,CAErD;;;AACA,iBAAK1B,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACW2B,QAAAA,aAAa,CAACD,KAAD,EAAyB;AACzC,iBAAO,KAAK/E,WAAL,CAAiB0B,YAAjB,CAA8B2C,OAA9B,CAAsCU,KAAtC,MAAiD,CAAC,CAAzD;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,aAAa,CAACF,KAAD,EAAyB;AACzC,cAAI,KAAK/E,WAAL,CAAiB0B,YAAjB,CAA8B2C,OAA9B,CAAsCU,KAAtC,MAAiD,CAAC,CAAtD,EAAyD;AACrD,iBAAK/E,WAAL,CAAiB2B,UAAjB,GAA8BoD,KAA9B;;AACA,iBAAK1B,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACW6B,QAAAA,WAAW,CAACC,OAAD,EAA2B;AACzC,cAAI,KAAKnF,WAAL,CAAiB4B,cAAjB,CAAgCyC,OAAhC,CAAwCc,OAAxC,MAAqD,CAAC,CAA1D,EAA6D;AACzD,iBAAKnF,WAAL,CAAiB4B,cAAjB,CAAgCsC,IAAhC,CAAqCiB,OAArC;;AACA,iBAAKnF,WAAL,CAAiB8B,aAAjB,CAA+BqD,OAA/B,IAA0C;AACtCpD,cAAAA,KAAK,EAAE,CAD+B;AAEtCC,cAAAA,SAAS,EAAE,KAF2B;AAGtCC,cAAAA,QAAQ,EAAE,CAH4B;AAItCC,cAAAA,KAAK,EAAEtC,UAAU,CAACuC,CAJoB;AAKtCC,cAAAA,QAAQ,EAAE;AAL4B,aAA1C;;AAOA,iBAAKiB,iBAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACW+B,QAAAA,mBAAmB,CAACD,OAAD,EAAkBpD,KAAlB,EAAiCsD,WAAjC,EAA6D;AACnF,cAAInD,KAAJ,CADmF,CAGnF;;AACA,cAAImD,WAAJ,EAAiB;AACb,oBAAQA,WAAR;AACI,mBAAK,GAAL;AACInD,gBAAAA,KAAK,GAAGtC,UAAU,CAAC0F,CAAnB;AACA;;AACJ,mBAAK,GAAL;AACIpD,gBAAAA,KAAK,GAAGtC,UAAU,CAAC2F,CAAnB;AACA;;AACJ,mBAAK,GAAL;AACIrD,gBAAAA,KAAK,GAAGtC,UAAU,CAAC4F,CAAnB;AACA;;AACJ,mBAAK,GAAL;AACA,mBAAK,SAAL;AACItD,gBAAAA,KAAK,GAAGtC,UAAU,CAACuC,CAAnB;AACA;;AACJ;AACI;AACA,oBAAIJ,KAAK,IAAI,CAAb,EAAgB;AACZG,kBAAAA,KAAK,GAAGtC,UAAU,CAAC0F,CAAnB;AACH,iBAFD,MAEO,IAAIvD,KAAK,IAAI,CAAb,EAAgB;AACnBG,kBAAAA,KAAK,GAAGtC,UAAU,CAAC2F,CAAnB;AACH,iBAFM,MAEA,IAAIxD,KAAK,IAAI,CAAb,EAAgB;AACnBG,kBAAAA,KAAK,GAAGtC,UAAU,CAAC4F,CAAnB;AACH,iBAFM,MAEA;AACHtD,kBAAAA,KAAK,GAAGtC,UAAU,CAACuC,CAAnB;AACH;;AACD;AAzBR;AA2BH,WA5BD,MA4BO;AACH;AACA,gBAAIJ,KAAK,IAAI,CAAb,EAAgB;AACZG,cAAAA,KAAK,GAAGtC,UAAU,CAAC0F,CAAnB;AACH,aAFD,MAEO,IAAIvD,KAAK,IAAI,CAAb,EAAgB;AACnBG,cAAAA,KAAK,GAAGtC,UAAU,CAAC2F,CAAnB;AACH,aAFM,MAEA,IAAIxD,KAAK,IAAI,CAAb,EAAgB;AACnBG,cAAAA,KAAK,GAAGtC,UAAU,CAAC4F,CAAnB;AACH,aAFM,MAEA;AACHtD,cAAAA,KAAK,GAAGtC,UAAU,CAACuC,CAAnB;AACH;AACJ,WA3CkF,CA6CnF;;;AACA,gBAAMf,WAAW,GAAGC,IAAI,CAACC,GAAL,EAApB;;AAEA,cAAI,CAAC,KAAKtB,WAAL,CAAiB8B,aAAjB,CAA+BqD,OAA/B,CAAL,EAA8C;AAC1C,iBAAKnF,WAAL,CAAiB8B,aAAjB,CAA+BqD,OAA/B,IAA0C;AACtCpD,cAAAA,KAAK,EAAEA,KAD+B;AAEtCC,cAAAA,SAAS,EAAE,IAF2B;AAGtCC,cAAAA,QAAQ,EAAEb,WAH4B;AAGf;AACvBc,cAAAA,KAAK,EAAEA,KAJ+B;AAKtCE,cAAAA,QAAQ,EAAE;AAL4B,aAA1C;AAOH,WARD,MAQO;AACH,kBAAMqD,QAAQ,GAAG,KAAKzF,WAAL,CAAiB8B,aAAjB,CAA+BqD,OAA/B,CAAjB,CADG,CAGH;;AACA,gBAAIpD,KAAK,GAAG0D,QAAQ,CAAC1D,KAArB,EAA4B;AACxB0D,cAAAA,QAAQ,CAAC1D,KAAT,GAAiBA,KAAjB;AACA0D,cAAAA,QAAQ,CAACxD,QAAT,GAAoBb,WAApB,CAFwB,CAES;;AACjCqE,cAAAA,QAAQ,CAACvD,KAAT,GAAiBA,KAAjB;AACH;;AAEDuD,YAAAA,QAAQ,CAACzD,SAAT,GAAqB,IAArB;AACAyD,YAAAA,QAAQ,CAACrD,QAAT;AACH,WApEkF,CAsEnF;;;AACA,eAAKsD,uBAAL,CAA6BP,OAA7B;;AAEA,eAAK9B,iBAAL;AACH;AAED;AACJ;AACA;;;AACYqC,QAAAA,uBAAuB,CAACC,cAAD,EAA+B;AAC1D,gBAAMC,eAAe,GAAG,KAAK5F,WAAL,CAAiB8B,aAAjB,CAA+B6D,cAA/B,CAAxB,CAD0D,CAG1D;;AACA,cAAIC,eAAe,IAAI,KAAKC,eAAL,CAAqBD,eAAe,CAAC1D,KAArC,CAAvB,EAAoE;AAChE,kBAAM4D,WAAW,GAAG,KAAKC,cAAL,CAAoBJ,cAApB,CAApB;;AACA,gBAAIG,WAAW,IAAI,KAAK9F,WAAL,CAAiB4B,cAAjB,CAAgCyC,OAAhC,CAAwCyB,WAAxC,MAAyD,CAAC,CAA7E,EAAgF;AAC5E,mBAAKZ,WAAL,CAAiBY,WAAjB;AACAhF,cAAAA,OAAO,CAACC,GAAR,CAAa,UAAS+E,WAAY,EAAlC;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,eAAe,CAAC3D,KAAD,EAA6B;AAChD,iBAAOA,KAAK,KAAKtC,UAAU,CAACuC,CAA5B,CADgD,CACjB;AAClC;AAED;AACJ;AACA;;;AACY4D,QAAAA,cAAc,CAACJ,cAAD,EAAwC;AAC1D;AACA,gBAAMK,KAAK,GAAGL,cAAc,CAACK,KAAf,CAAqB,aAArB,CAAd;;AACA,cAAIA,KAAJ,EAAW;AACP,kBAAMC,aAAa,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,CAA9B;AACA,mBAAQ,SAAQC,aAAa,GAAG,CAAE,EAAlC;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,gBAAgB,CAAChB,OAAD,EAAwC;AAC3D,iBAAO,KAAKnF,WAAL,CAAiB8B,aAAjB,CAA+BqD,OAA/B,KAA2C,IAAlD;AACH;AAED;AACJ;AACA;;;AACWiB,QAAAA,eAAe,CAACjB,OAAD,EAA2B;AAC7C,iBAAO,KAAKnF,WAAL,CAAiB4B,cAAjB,CAAgCyC,OAAhC,CAAwCc,OAAxC,MAAqD,CAAC,CAA7D;AACH;AAED;AACJ;AACA;;;AACWkB,QAAAA,iBAAiB,CAAClB,OAAD,EAA0B;AAC9C,gBAAMM,QAAQ,GAAG,KAAKU,gBAAL,CAAsBhB,OAAtB,CAAjB;;AACA,cAAI,CAACM,QAAD,IAAa,CAACA,QAAQ,CAACzD,SAA3B,EAAsC;AAClC,mBAAO,EAAP;AACH;;AACD,iBAAOyD,QAAQ,CAACvD,KAAhB;AACH;AAED;AACJ;AACA;;;AACWoE,QAAAA,kBAAkB,CAACpE,KAAD,EAA4B;AACjD,kBAAQA,KAAR;AACI,iBAAKtC,UAAU,CAAC0F,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAK1F,UAAU,CAAC2F,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC,iBAAK3F,UAAU,CAAC4F,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;AACrC;AACA;;AACA,iBAAK5F,UAAU,CAACuC,CAAhB;AAAmB,qBAAO,SAAP;AAAkB;;AACrC;AAAS,qBAAO,SAAP;AAAkB;AAP/B;AASH;AAED;AACJ;AACA;;;AACWoE,QAAAA,cAAc,CAAClE,QAAD,EAAwC;AACzD,eAAKrC,WAAL,CAAiBqC,QAAjB,GAA4B,EAAE,GAAG,KAAKrC,WAAL,CAAiBqC,QAAtB;AAAgC,eAAGA;AAAnC,WAA5B;;AACA,eAAKgB,iBAAL;AACH;AAED;AACJ;AACA;;;AACWmD,QAAAA,gBAAgB,CAACC,OAAD,EAAyC;AAC5D,eAAKzG,WAAL,CAAiBwC,UAAjB,GAA8B,EAAE,GAAG,KAAKxC,WAAL,CAAiBwC,UAAtB;AAAkC,eAAGiE;AAArC,WAA9B;;AACA,eAAKpD,iBAAL;AACH;AAED;AACJ;AACA;;;AACWqD,QAAAA,eAAe,GAAS;AAC3B,eAAK9F,eAAL;;AACA,eAAK0C,cAAL;;AACA,eAAKD,iBAAL;;AACAvC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH;AAED;AACJ;AACA;;;AACW4F,QAAAA,gBAAgB,GAAW;AAC9B,iBAAOzD,IAAI,CAACM,SAAL,CAAe,KAAKxD,WAApB,EAAiC,IAAjC,EAAuC,CAAvC,CAAP;AACH;AAED;AACJ;AACA;;;AACW4G,QAAAA,gBAAgB,CAAC7D,QAAD,EAA4B;AAC/C,cAAI;AACA,kBAAMD,IAAI,GAAGI,IAAI,CAACC,KAAL,CAAWJ,QAAX,CAAb;AACA,iBAAK/C,WAAL,GAAmB,KAAKoD,gBAAL,CAAsB,KAAKpD,WAA3B,EAAwC8C,IAAxC,CAAnB;AACA,iBAAKQ,cAAL;;AACA,iBAAKD,iBAAL;;AACAvC,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA,mBAAO,IAAP;AACH,WAPD,CAOE,OAAOC,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,mBAAO,KAAP;AACH;AACJ;;AA9lBwC,O,UAC1BX,S,GAA2B,I", "sourcesContent": ["import { _decorator, Component, Node, sys, game, director } from 'cc';\n\nconst { ccclass, property } = _decorator;\n\n// 玩家数据结构\nexport interface PlayerData {\n    // 基础信息\n    level: number;\n    money: number;\n    experience: number;\n    \n    // 车辆相关\n    unlockedCars: string[];\n    currentCar: string;\n    // 移除车辆改装相关字段\n    \n    // 关卡相关\n    unlockedLevels: string[];\n    currentLevel: string;\n    levelProgress: { [levelId: string]: LevelProgress };\n    \n    // 设置\n    settings: GameSettings;\n    \n    // 统计信息\n    statistics: GameStatistics;\n    \n    // 时间戳\n    lastSaveTime: number;\n    createTime: number;\n}\n\n// 移除CarUpgrade接口\n\n// 关卡进度数据\nexport interface LevelProgress {\n    stars: number;       // 获得星星数 (0-3)\n    completed: boolean;  // 是否完成\n    bestTime: number;    // 最佳时间 (毫秒)\n    grade: LevelGrade;   // 评级 (S, A, B, C, D, F)\n    attempts: number;    // 尝试次数\n}\n\n// 评级枚举\nexport enum LevelGrade {\n    S = 'S',\n    A = 'A',\n    B = 'B',\n    C = 'C',\n    D = 'D',\n    F = 'F'\n}\n\n// 游戏设置\nexport interface GameSettings {\n    soundVolume: number;     // 音效音量 (0-1)\n    musicVolume: number;     // 音乐音量 (0-1)\n}\n\n// 游戏统计\nexport interface GameStatistics {\n    totalRaces: number;      // 总比赛次数\n    totalWins: number;       // 总胜利次数\n    totalMoneyEarned: number; // 总获得金钱\n}\n\n// 移除微信小游戏相关接口\n\n@ccclass('PlayerManager')\nexport class PlayerManager extends Component {\n    private static _instance: PlayerManager = null!;\n    \n    // 存储键名\n    private readonly STORAGE_KEY = 'TopRacing_PlayerData';\n    private readonly STORAGE_KEY_BACKUP = 'TopRacing_PlayerData_Backup';\n    \n    // 玩家数据\n    private _playerData: PlayerData = null!;\n    \n    // 移除微信相关字段\n    // private _wechatAPI: WeChatAPI | null = null;\n    // private _isWeChatMiniGame = false;\n    // private _userId: string = '';\n    \n    // 自动保存相关\n    private _autoSaveInterval: number = 30000; // 30秒自动保存\n    private _lastAutoSaveTime: number = 0;\n    \n    // 事件回调\n    private _dataChangeCallbacks: ((data: PlayerData) => void)[] = [];\n    \n    public static get instance(): PlayerManager {\n        return PlayerManager._instance;\n    }\n    \n    public get playerData(): PlayerData {\n        return this._playerData;\n    }\n    \n    // 移除微信相关属性\n    // public get isWeChatMiniGame(): boolean {\n    //     return this._isWeChatMiniGame;\n    // }\n    \n    // public get userId(): string {\n    //     return this._userId;\n    // }\n    \n    onLoad() {\n        // 单例模式\n        if (PlayerManager._instance) {\n            this.node.destroy();\n            return;\n        }\n\n        PlayerManager._instance = this;\n\n        try {\n            // 设置为常驻节点，不随场景切换而销毁\n            director.addPersistRootNode(this.node);\n\n            // 延迟初始化，确保系统准备就绪\n            this.scheduleOnce(() => {\n                this._initPlayerData();\n                this.loadPlayerData();\n                console.log('PlayerManager initialized successfully');\n            }, 0.1);\n\n        } catch (error) {\n            console.error('Error initializing PlayerManager:', error);\n        }\n    }\n    \n    onDestroy() {\n        if (PlayerManager._instance === this) {\n            PlayerManager._instance = null!;\n        }\n    }\n    \n    update(deltaTime: number) {\n        // 自动保存检查\n        const currentTime = Date.now();\n        if (currentTime - this._lastAutoSaveTime > this._autoSaveInterval) {\n            // this.savePlayerData();\n            this._lastAutoSaveTime = currentTime;\n        }\n    }\n    \n    /**\n     * 移除环境检测方法\n     */\n    // private _detectEnvironment() {\n    //     // 检测是否在微信小游戏环境中\n    //     if (typeof wx !== 'undefined' && wx.setStorageSync) {\n    //         this._isWeChatMiniGame = true;\n    //         this._wechatAPI = wx as any;\n    //         console.log('检测到微信小游戏环境');\n    //     } else {\n    //         this._isWeChatMiniGame = false;\n    //         console.log('检测到普通游戏环境');\n    //     }\n    // }\n    \n    /**\n     * 初始化玩家数据\n     */\n    private _initPlayerData() {\n        this._playerData = {\n            level: 1,\n            money: 0,\n            experience: 0,\n            \n            unlockedCars: [], // 默认解锁第一辆车\n            currentCar: '',\n            // 移除车辆改装相关初始化\n            \n            unlockedLevels: ['level-1'], // 默认只解锁第一关\n            currentLevel: 'level-1',\n            levelProgress: {\n                'level-1': {\n                    stars: 0,\n                    completed: false,\n                    bestTime: 0,\n                    grade: LevelGrade.F,\n                    attempts: 0\n                }\n            },\n            \n            settings: {\n                soundVolume: 0.8,\n                musicVolume: 0.6,\n            },\n            \n            statistics: {\n                totalRaces: 0,\n                totalWins: 0,\n                totalMoneyEarned: 0,\n            },\n            \n            lastSaveTime: Date.now(),\n            createTime: Date.now()\n        };\n    }\n    \n    /**\n     * 加载玩家数据\n     */\n    public async loadPlayerData(): Promise<void> {\n        try {\n            let data: any = null;\n            \n            // 移除微信相关代码，只保留普通环境的localStorage\n            const jsonData = sys.localStorage.getItem(this.STORAGE_KEY);\n            if (jsonData) {\n                data = JSON.parse(jsonData);\n            }\n            \n            if (data) {\n                // 合并数据，保留新字段的默认值\n                this._playerData = this._mergePlayerData(this._playerData, data);\n                console.log('玩家数据加载成功');\n            } else {\n                console.log('未找到存档数据，使用默认数据');\n            }\n            \n            // 触发数据变化回调\n            this._notifyDataChange();\n            \n        } catch (error) {\n            console.error('加载玩家数据失败:', error);\n        }\n    }\n    \n    /**\n     * 保存玩家数据\n     */\n    public async savePlayerData(): Promise<void> {\n        try {\n            this._playerData.lastSaveTime = Date.now();\n            \n            // 移除微信相关代码，只保留普通环境的localStorage\n            sys.localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this._playerData));\n            \n            console.log('玩家数据保存成功');\n            \n        } catch (error) {\n            console.error('保存玩家数据失败:', error);\n        }\n    }\n    \n    /**\n     * 合并玩家数据（处理版本兼容性）\n     */\n    private _mergePlayerData(defaultData: PlayerData, savedData: any): PlayerData {\n        const merged = { ...defaultData };\n        \n        // 递归合并对象\n        for (const key in savedData) {\n            if (savedData.hasOwnProperty(key)) {\n                if (typeof savedData[key] === 'object' && savedData[key] !== null && !Array.isArray(savedData[key])) {\n                    merged[key] = { ...merged[key], ...savedData[key] };\n                } else {\n                    merged[key] = savedData[key];\n                }\n            }\n        }\n        \n        return merged;\n    }\n    \n    /**\n     * 移除微信云存储相关方法\n     */\n    // private async _saveToWeChatCloud(): Promise<void> {\n    //     if (!this._wechatAPI || !this._userId) return;\n    //     \n    //     try {\n    //         await this._wechatAPI.cloudCallFunction('savePlayerData', {\n    //             userId: this._userId,\n    //             data: this._playerData\n    //         });\n    //         console.log('数据已保存到微信云存储');\n    //     } catch (error) {\n    //         console.error('微信云存储保存失败:', error);\n    //     }\n    // }\n    \n    /**\n     * 移除微信登录相关方法\n     */\n    // public async wechatLogin(): Promise<boolean> {\n    //     if (!this._isWeChatMiniGame || !this._wechatAPI) {\n    //         return false;\n    //     }\n    //     \n    //     try {\n    //         const loginResult = await this._wechatAPI.login();\n    //         this._userId = loginResult.code || '';\n    //         \n    //         if (this._userId) {\n    //             console.log('微信登录成功');\n    //             // 登录后尝试从云端加载数据\n    //             await this._loadFromWeChatCloud();\n    //             return true;\n    //         }\n    //     } catch (error) {\n    //         console.error('微信登录失败:', error);\n    //     }\n    //     \n    //     return false;\n    // }\n    \n    /**\n     * 移除微信云端加载相关方法\n     */\n    // private async _loadFromWeChatCloud(): Promise<void> {\n    //     if (!this._wechatAPI || !this._userId) return;\n    //     \n    //     try {\n    //         const result = await this._wechatAPI.cloudCallFunction('loadPlayerData', {\n    //             userId: this._userId\n    //         });\n    //         \n    //         if (result.data) {\n    //             this._playerData = this._mergePlayerData(this._playerData, result.data);\n    //             this._notifyDataChange();\n    //             console.log('从微信云端加载数据成功');\n    //         }\n    //     } catch (error) {\n    //         console.error('从微信云端加载数据失败:', error);\n    //     }\n    // }\n    \n    /**\n     * 添加数据变化监听\n     */\n    public addDataChangeListener(callback: (data: PlayerData) => void): void {\n        this._dataChangeCallbacks.push(callback);\n    }\n    \n    /**\n     * 移除数据变化监听\n     */\n    public removeDataChangeListener(callback: (data: PlayerData) => void): void {\n        const index = this._dataChangeCallbacks.indexOf(callback);\n        if (index !== -1) {\n            this._dataChangeCallbacks.splice(index, 1);\n        }\n    }\n    \n    /**\n     * 通知数据变化\n     */\n    private _notifyDataChange(): void {\n        this._dataChangeCallbacks.forEach(callback => {\n            try {\n                callback(this._playerData);\n            } catch (error) {\n                console.error('数据变化回调执行失败:', error);\n            }\n        });\n    }\n    \n    // ==================== 玩家数据操作方法 ====================\n    \n    /**\n     * 增加金钱\n     */\n    public addMoney(amount: number): void {\n        this._playerData.money += amount;\n        this._playerData.statistics.totalMoneyEarned += amount;\n        this._notifyDataChange();\n    }\n    \n    /**\n     * 消费金钱\n     */\n    public spendMoney(amount: number): boolean {\n        if (this._playerData.money >= amount) {\n            this._playerData.money -= amount;\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 增加经验\n     */\n    public addExperience(exp: number): void {\n        this._playerData.experience += exp;\n        \n        // 检查是否升级\n        const expNeeded = this._playerData.level * 100; // 每级需要100经验\n        if (this._playerData.experience >= expNeeded) {\n            this._playerData.level++;\n            this._playerData.experience -= expNeeded;\n            console.log(`玩家升级到 ${this._playerData.level} 级！`);\n        }\n        \n        this._notifyDataChange();\n    }\n    \n    /**\n     * 解锁车辆\n     */\n    public unlockCar(carId: string): boolean {\n        if (this._playerData.unlockedCars.indexOf(carId) === -1) {\n            this._playerData.unlockedCars.push(carId);\n            // 移除车辆改装相关代码\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n\n    /**\n     * 检查车辆是否已解锁\n     */\n    public isCarUnlocked(carId: string): boolean {\n        return this._playerData.unlockedCars.indexOf(carId) !== -1;\n    }\n    \n    /**\n     * 设置当前车辆\n     */\n    public setCurrentCar(carId: string): boolean {\n        if (this._playerData.unlockedCars.indexOf(carId) !== -1) {\n            this._playerData.currentCar = carId;\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 移除车辆升级相关方法\n     */\n    // public upgradeCarPart(carId: string, part: keyof CarUpgrade): boolean {\n    //     if (!this._playerData.carUpgrades[carId]) return false;\n    //     \n    //     const upgrade = this._playerData.carUpgrades[carId];\n    //     if (upgrade[part] < 5) {\n    //         upgrade[part]++;\n    //         this._notifyDataChange();\n    //         return true;\n    //     }\n    //     return false;\n    // }\n    \n    /**\n     * 解锁关卡\n     */\n    public unlockLevel(levelId: string): boolean {\n        if (this._playerData.unlockedLevels.indexOf(levelId) === -1) {\n            this._playerData.unlockedLevels.push(levelId);\n            this._playerData.levelProgress[levelId] = {\n                stars: 0,\n                completed: false,\n                bestTime: 0,\n                grade: LevelGrade.F,\n                attempts: 0\n            };\n            this._notifyDataChange();\n            return true;\n        }\n        return false;\n    }\n    \n    /**\n     * 更新关卡进度\n     * @param levelId 关卡ID\n     * @param stars 获得的星星数\n     * @param performance 表现评价（可选）\n     */\n    public updateLevelProgress(levelId: string, stars: number, performance?: string): void {\n        let grade: LevelGrade;\n        \n        // 如果提供了表现评价，则根据表现评价确定等级\n        if (performance) {\n            switch (performance) {\n                case 'S':\n                    grade = LevelGrade.S;\n                    break;\n                case 'A':\n                    grade = LevelGrade.A;\n                    break;\n                case 'B':\n                    grade = LevelGrade.B;\n                    break;\n                case 'F':\n                case 'failure':\n                    grade = LevelGrade.F;\n                    break;\n                default:\n                    // 如果提供的表现评价无法识别，根据星星数确定等级\n                    if (stars >= 3) {\n                        grade = LevelGrade.S;\n                    } else if (stars >= 2) {\n                        grade = LevelGrade.A;\n                    } else if (stars >= 1) {\n                        grade = LevelGrade.B;\n                    } else {\n                        grade = LevelGrade.F;\n                    }\n                    break;\n            }\n        } else {\n            // 没有提供表现评价时，根据星星数确定等级\n            if (stars >= 3) {\n                grade = LevelGrade.S;\n            } else if (stars >= 2) {\n                grade = LevelGrade.A;\n            } else if (stars >= 1) {\n                grade = LevelGrade.B;\n            } else {\n                grade = LevelGrade.F;\n            }\n        }\n\n        // 获取当前时间作为记录时间\n        const currentTime = Date.now();\n\n        if (!this._playerData.levelProgress[levelId]) {\n            this._playerData.levelProgress[levelId] = {\n                stars: stars,\n                completed: true,\n                bestTime: currentTime, // 使用当前时间作为记录时间\n                grade: grade,\n                attempts: 1\n            };\n        } else {\n            const progress = this._playerData.levelProgress[levelId];\n\n            // 更新最佳成绩（仅基于星星数，不再考虑时间）\n            if (stars > progress.stars) {\n                progress.stars = stars;\n                progress.bestTime = currentTime; // 更新记录时间\n                progress.grade = grade;\n            }\n\n            progress.completed = true;\n            progress.attempts++;\n        }\n\n        // 检查是否解锁下一关卡\n        this.checkAndUnlockNextLevel(levelId);\n\n        this._notifyDataChange();\n    }\n\n    /**\n     * 检查并解锁下一关卡\n     */\n    private checkAndUnlockNextLevel(currentLevelId: string): void {\n        const currentProgress = this._playerData.levelProgress[currentLevelId];\n\n        // 只有评级在D及以上时才解锁下一关\n        if (currentProgress && this.isGradePassable(currentProgress.grade)) {\n            const nextLevelId = this.getNextLevelId(currentLevelId);\n            if (nextLevelId && this._playerData.unlockedLevels.indexOf(nextLevelId) === -1) {\n                this.unlockLevel(nextLevelId);\n                console.log(`解锁新关卡: ${nextLevelId}`);\n            }\n        }\n    }\n\n    /**\n     * 检查评级是否达到解锁要求\n     */\n    private isGradePassable(grade: LevelGrade): boolean {\n        return grade !== LevelGrade.F; // D及以上都可以解锁下一关\n    }\n\n    /**\n     * 获取下一关卡ID\n     */\n    private getNextLevelId(currentLevelId: string): string | null {\n        // 假设关卡命名为 level-1, level-2, level-3...\n        const match = currentLevelId.match(/level-(\\d+)/);\n        if (match) {\n            const currentNumber = parseInt(match[1]);\n            return `level-${currentNumber + 1}`;\n        }\n        return null;\n    }\n\n    /**\n     * 获取关卡进度信息\n     */\n    public getLevelProgress(levelId: string): LevelProgress | null {\n        return this._playerData.levelProgress[levelId] || null;\n    }\n\n    /**\n     * 检查关卡是否已解锁\n     */\n    public isLevelUnlocked(levelId: string): boolean {\n        return this._playerData.unlockedLevels.indexOf(levelId) !== -1;\n    }\n\n    /**\n     * 获取关卡评级文本\n     */\n    public getLevelGradeText(levelId: string): string {\n        const progress = this.getLevelProgress(levelId);\n        if (!progress || !progress.completed) {\n            return '';\n        }\n        return progress.grade;\n    }\n\n    /**\n     * 获取关卡评级颜色\n     */\n    public getLevelGradeColor(grade: LevelGrade): string {\n        switch (grade) {\n            case LevelGrade.S: return '#FFD700'; // 金色\n            case LevelGrade.A: return '#C0C0C0'; // 银色\n            case LevelGrade.B: return '#CD7F32'; // 铜色\n            // case LevelGrade.C: return '#90EE90'; // 浅绿色\n            // case LevelGrade.D: return '#87CEEB'; // 天蓝色\n            case LevelGrade.F: return '#FF6B6B'; // 红色\n            default: return '#FFFFFF'; // 白色\n        }\n    }\n    \n    /**\n     * 更新游戏设置\n     */\n    public updateSettings(settings: Partial<GameSettings>): void {\n        this._playerData.settings = { ...this._playerData.settings, ...settings };\n        this._notifyDataChange();\n    }\n    \n    /**\n     * 更新统计数据\n     */\n    public updateStatistics(updates: Partial<GameStatistics>): void {\n        this._playerData.statistics = { ...this._playerData.statistics, ...updates };\n        this._notifyDataChange();\n    }\n    \n    /**\n     * 重置玩家数据\n     */\n    public resetPlayerData(): void {\n        this._initPlayerData();\n        this.savePlayerData();\n        this._notifyDataChange();\n        console.log('玩家数据已重置');\n    }\n    \n    /**\n     * 导出玩家数据（用于调试）\n     */\n    public exportPlayerData(): string {\n        return JSON.stringify(this._playerData, null, 2);\n    }\n    \n    /**\n     * 导入玩家数据（用于调试）\n     */\n    public importPlayerData(jsonData: string): boolean {\n        try {\n            const data = JSON.parse(jsonData);\n            this._playerData = this._mergePlayerData(this._playerData, data);\n            this.savePlayerData();\n            this._notifyDataChange();\n            console.log('玩家数据导入成功');\n            return true;\n        } catch (error) {\n            console.error('玩家数据导入失败:', error);\n            return false;\n        }\n    }\n}"]}