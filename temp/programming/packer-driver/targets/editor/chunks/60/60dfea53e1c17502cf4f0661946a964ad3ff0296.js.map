{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"], "names": ["PipelineConfigs", "CameraConfigs", "ForwardLighting", "BuiltinForwardPassBuilder", "BuiltinBloomPassBuilder", "BuiltinToneMappingPassBuilder", "BuiltinFXAAPassBuilder", "BuiltinFsrPassBuilder", "BuiltinUiPassBuilder", "forwardNeedClearColor", "camera", "clearFlag", "ClearFlagBit", "COLOR", "STENCIL", "getCsmMainLightViewport", "light", "w", "h", "level", "vp", "screenSpaceSignY", "shadowFixedArea", "csmLevel", "CSMLevel", "LEVEL_1", "left", "top", "width", "Math", "trunc", "height", "floor", "max", "setupPipelineConfigs", "ppl", "configs", "sampleFeature", "FormatFeatureBit", "SAMPLED_TEXTURE", "LINEAR_FILTER", "device", "isWeb", "sys", "isNative", "isWebGL1", "gfxAPI", "gfx", "API", "WEBGL", "isWebGPU", "WEBGPU", "isMobile", "isHDR", "pipelineSceneData", "useFloatOutput", "getMacroBool", "toneMappingType", "postSettings", "shadowInfo", "shadows", "shadowEnabled", "enabled", "shadowMapFormat", "pipeline", "supportsR32FloatTexture", "Format", "R32F", "RGBA8", "shadowMapSize", "set", "size", "usePlanarShadow", "type", "renderer", "scene", "ShadowType", "Planar", "capabilities", "supportDepthSample", "getFormatFeatures", "DEPTH_STENCIL", "platform", "x", "clipSpaceSignY", "sortPipelinePassBuildersByConfigOrder", "passBuilders", "sort", "a", "b", "getConfigOrder", "sortPipelinePassBuildersByRenderOrder", "getRenderOrder", "addCopyToScreenPass", "pplConfigs", "cameraConfigs", "input", "assert", "copyAndTonemapMaterial", "pass", "addRenderPass", "nativeWidth", "nativeHeight", "addR<PERSON><PERSON>arget", "colorName", "LoadOp", "CLEAR", "StoreOp", "STORE", "sClearColorTransparentBlack", "addTexture", "setVec4", "addQueue", "rendering", "QueueHint", "OPAQUE", "addFullscreenQuad", "getPingPongRenderTarget", "prevName", "prefix", "id", "startsWith", "Number", "char<PERSON>t", "length", "cclegacy", "clamp", "geometry", "Layers", "Material", "PipelineEventType", "Vec2", "Vec3", "Vec4", "warn", "DEBUG", "EDITOR", "makePipelineSettings", "AABB", "Sphere", "intersect", "Color", "TextureType", "Viewport", "CameraUsage", "LightType", "mobileMaxSpotLightShadowMaps", "defaultSettings", "settings", "isMainGameWindow", "renderWindowId", "depthStencilName", "enableFullPipeline", "enableProfiler", "remainingPasses", "enableShadingScale", "shadingScale", "enableHDR", "radianceFormat", "enableStoreSceneDepth", "lights", "shadowEnabledSpotLights", "_sphere", "create", "_boundingBox", "_rangedDirLightBoundingBox", "cullLights", "frustum", "cameraPos", "spotLights", "baked", "position", "y", "z", "range", "sphereFrustum", "push", "sphereLights", "pointLights", "rangedDirLights", "transform", "node", "getWorldMatrix", "aabbFrustum", "lhs", "rhs", "squaredDistance", "_addLightQueues", "queue", "BLEND", "SPHERE", "name", "SPOT", "POINT", "RANGED_DIRECTIONAL", "addScene", "SceneFlags", "addSpotlightShadowPasses", "maxNumShadowMaps", "i", "<PERSON><PERSON><PERSON>", "addDepthStencil", "DISCARD", "NONE", "MASK", "SHADOW_CASTER", "useLightFrustum", "addLightQueues", "addLightPasses", "depthStencilStoreOp", "viewport", "count", "storeOp", "setViewport", "LOAD", "isMultipleLightPassesNeeded", "forwardLighting", "_viewport", "_clearColor", "_reflectionProbeClearColor", "ConfigOrder", "RenderOrder", "configCamera", "pipelineConfigs", "enableMainLightShadowMap", "mainLight", "enableMainLightPlanarShadowMap", "enablePlanarReflectionProbe", "cameraUsage", "SCENE_VIEW", "GAME_VIEW", "enableMSAA", "msaa", "enableSingleForwardPass", "windowResize", "window", "ResourceFlags", "ResourceResidency", "TEX2D", "sampleCount", "COLOR_ATTACHMENT", "MEMORYLESS", "DEPTH_STENCIL_ATTACHMENT", "setup", "context", "_addCascadedShadowMapPass", "_tryAddReflectionProbePasses", "_addForwardRadiancePasses", "shadowSize", "csmSupported", "reflectionProbeManager", "internal", "probes", "getProbes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "probeID", "probe", "needRender", "area", "renderArea", "probeType", "ProbeType", "PLANAR", "realtimePlanarTexture", "addRenderWindow", "probePass", "_buildReflectionProbePass", "faceIdx", "bakedCubeTextures", "updateCameraDir", "colorStoreOp", "clearColor", "packRGBE", "clear<PERSON><PERSON>h", "clearStencil", "REFLECTION_PROBE", "undefined", "disableMSAA", "round", "_addForwardSingleRadiancePass", "_addForwardMultipleRadiancePasses", "_addPlanarShadowQueue", "sceneFlags", "<PERSON><PERSON><PERSON><PERSON>", "GEOMETRY", "msaaRadianceName", "msaaDepthStencilName", "msPass", "addMultisampleRenderPass", "_buildForwardMainLightPass", "resolve<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstStoreOp", "PLANAR_SHADOW", "_clearColorTransparentBlack", "_bloomParams", "_bloomTexSize", "_bloomWidths", "_bloomHeights", "_bloomTexNames", "enableBloom", "bloom", "material", "bloomWidth", "bloomHeight", "iterations", "prevRenderPass", "_addKawaseDualFilterBloomPasses", "bloomMaterial", "radianceName", "sizeCount", "threshold", "enableAlphaMask", "prefilterPass", "downPass", "upPass", "combinePass", "_colorGradingTexSize", "enableColorGrading", "colorGrading", "colorGradingMap", "enableToneMapping", "setProperty", "_addCopyAndTonemapPass", "ldrColorPrefix", "ldrColorName", "lutTex", "isSquareMap", "setVec2", "setFloat", "contribute", "toneMapping", "_fxaaParams", "enableFXAA", "fxaa", "_addFxaaPass", "inputColorName", "lastPass", "fxaaMaterial", "_fsrParams", "_fsrTexSize", "enableFSR", "fsr", "outputColorName", "_addFsrPass", "fsrMaterial", "sharpness", "uiColorPrefix", "fsrColorName", "easu<PERSON><PERSON>", "rcasPass", "flags", "UI", "PROFILER", "showStatistics", "BuiltinPipelineBuilder", "_pipelineEvent", "director", "root", "pipelineEvent", "_forwardPass", "_bloomPass", "_toneMappingPass", "_fxaaPass", "_fsrPass", "_uiPass", "_configs", "_cameraConfigs", "_copyAndTonemapMaterial", "_initialized", "_passBuilders", "_setupPipelinePreview", "isEditorView", "PREVIEW", "editorSettings", "getEditorPipelineSettings", "pipelineSettings", "_prepare<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_passes", "_setupBuiltinCameraConfigs", "GAME", "swapchain", "visibility", "Enum", "DEFAULT", "RGBA16F", "_setupCameraConfigs", "builder", "cameras", "_initMaterials", "emit", "RENDER_CAMERA_BEGIN", "_buildFor<PERSON><PERSON><PERSON><PERSON>e", "_buildSimplePipeline", "RENDER_CAMERA_END", "_uuid", "initialize", "effectName", "effectAsset", "setCustomPipeline"], "mappings": ";;;6PA2EaA,e,EAwDAC,a,EA8EPC,e,EAoMOC,yB,EAokBAC,uB,EAgLAC,6B,EA6HAC,sB,EAqGAC,qB,EAuGAC,oB;;AAx6Cb,WAASC,qBAAT,CAA+BC,MAA/B,EAAuE;AACnE,WAAO,CAAC,EAAEA,MAAM,CAACC,SAAP,IAAoBC,YAAY,CAACC,KAAb,GAAsBD,YAAY,CAACE,OAAb,IAAwB,CAAlE,CAAF,CAAR;AACH;;AAED,WAASC,uBAAT,CACIC,KADJ,EAEIC,CAFJ,EAGIC,CAHJ,EAIIC,KAJJ,EAKIC,EALJ,EAMIC,gBANJ,EAOQ;AACJ,QAAIL,KAAK,CAACM,eAAN,IAAyBN,KAAK,CAACO,QAAN,KAAmBC,QAAQ,CAACC,OAAzD,EAAkE;AAC9DL,MAAAA,EAAE,CAACM,IAAH,GAAU,CAAV;AACAN,MAAAA,EAAE,CAACO,GAAH,GAAS,CAAT;AACAP,MAAAA,EAAE,CAACQ,KAAH,GAAWC,IAAI,CAACC,KAAL,CAAWb,CAAX,CAAX;AACAG,MAAAA,EAAE,CAACW,MAAH,GAAYF,IAAI,CAACC,KAAL,CAAWZ,CAAX,CAAZ;AACH,KALD,MAKO;AACHE,MAAAA,EAAE,CAACM,IAAH,GAAUG,IAAI,CAACC,KAAL,CAAWX,KAAK,GAAG,CAAR,GAAY,GAAZ,GAAkBF,CAA7B,CAAV;;AACA,UAAII,gBAAgB,GAAG,CAAvB,EAA0B;AACtBD,QAAAA,EAAE,CAACO,GAAH,GAASE,IAAI,CAACC,KAAL,CAAW,CAAC,IAAID,IAAI,CAACG,KAAL,CAAWb,KAAK,GAAG,CAAnB,CAAL,IAA8B,GAA9B,GAAoCD,CAA/C,CAAT;AACH,OAFD,MAEO;AACHE,QAAAA,EAAE,CAACO,GAAH,GAASE,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACG,KAAL,CAAWb,KAAK,GAAG,CAAnB,IAAwB,GAAxB,GAA8BD,CAAzC,CAAT;AACH;;AACDE,MAAAA,EAAE,CAACQ,KAAH,GAAWC,IAAI,CAACC,KAAL,CAAW,MAAMb,CAAjB,CAAX;AACAG,MAAAA,EAAE,CAACW,MAAH,GAAYF,IAAI,CAACC,KAAL,CAAW,MAAMZ,CAAjB,CAAZ;AACH;;AACDE,IAAAA,EAAE,CAACM,IAAH,GAAUG,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACM,IAAf,CAAV;AACAN,IAAAA,EAAE,CAACO,GAAH,GAASE,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACO,GAAf,CAAT;AACAP,IAAAA,EAAE,CAACQ,KAAH,GAAWC,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACQ,KAAf,CAAX;AACAR,IAAAA,EAAE,CAACW,MAAH,GAAYF,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACW,MAAf,CAAZ;AACH;;AAqBD,WAASG,oBAAT,CACIC,GADJ,EAEIC,OAFJ,EAGQ;AACJ,UAAMC,aAAa,GAAGC,gBAAgB,CAACC,eAAjB,GAAmCD,gBAAgB,CAACE,aAA1E;AACA,UAAMC,MAAM,GAAGN,GAAG,CAACM,MAAnB,CAFI,CAGJ;;AACAL,IAAAA,OAAO,CAACM,KAAR,GAAgB,CAACC,GAAG,CAACC,QAArB;AACAR,IAAAA,OAAO,CAACS,QAAR,GAAmBJ,MAAM,CAACK,MAAP,KAAkBC,GAAG,CAACC,GAAJ,CAAQC,KAA7C;AACAb,IAAAA,OAAO,CAACc,QAAR,GAAmBT,MAAM,CAACK,MAAP,KAAkBC,GAAG,CAACC,GAAJ,CAAQG,MAA7C;AACAf,IAAAA,OAAO,CAACgB,QAAR,GAAmBT,GAAG,CAACS,QAAvB,CAPI,CASJ;;AACAhB,IAAAA,OAAO,CAACiB,KAAR,GAAgBlB,GAAG,CAACmB,iBAAJ,CAAsBD,KAAtC,CAVI,CAUyC;;AAC7CjB,IAAAA,OAAO,CAACmB,cAAR,GAAyBpB,GAAG,CAACqB,YAAJ,CAAiB,qBAAjB,CAAzB;AACApB,IAAAA,OAAO,CAACqB,eAAR,GAA0BtB,GAAG,CAACmB,iBAAJ,CAAsBI,YAAtB,CAAmCD,eAA7D,CAZI,CAaJ;;AACA,UAAME,UAAU,GAAGxB,GAAG,CAACmB,iBAAJ,CAAsBM,OAAzC;AACAxB,IAAAA,OAAO,CAACyB,aAAR,GAAwBF,UAAU,CAACG,OAAnC;AACA1B,IAAAA,OAAO,CAAC2B,eAAR,GAA0BC,QAAQ,CAACC,uBAAT,CAAiC9B,GAAG,CAACM,MAArC,IAA+CyB,MAAM,CAACC,IAAtD,GAA6DD,MAAM,CAACE,KAA9F;AACAhC,IAAAA,OAAO,CAACiC,aAAR,CAAsBC,GAAtB,CAA0BX,UAAU,CAACY,IAArC;AACAnC,IAAAA,OAAO,CAACoC,eAAR,GAA0Bb,UAAU,CAACG,OAAX,IAAsBH,UAAU,CAACc,IAAX,KAAoBC,QAAQ,CAACC,KAAT,CAAeC,UAAf,CAA0BC,MAA9F,CAlBI,CAmBJ;;AACAzC,IAAAA,OAAO,CAACf,gBAAR,GAA2Bc,GAAG,CAACM,MAAJ,CAAWqC,YAAX,CAAwBzD,gBAAnD;AACAe,IAAAA,OAAO,CAAC2C,kBAAR,GAA6B,CAAC5C,GAAG,CAACM,MAAJ,CAAWuC,iBAAX,CAA6Bd,MAAM,CAACe,aAApC,IAAqD5C,aAAtD,MAAyEA,aAAtG,CArBI,CAsBJ;;AACA,UAAMhB,gBAAgB,GAAGoB,MAAM,CAACqC,YAAP,CAAoBzD,gBAA7C;AACAe,IAAAA,OAAO,CAAC8C,QAAR,CAAiBC,CAAjB,GAAqB/C,OAAO,CAACgB,QAAR,GAAmB,GAAnB,GAAyB,GAA9C;AACAhB,IAAAA,OAAO,CAAC8C,QAAR,CAAiBjE,CAAjB,GAAsBI,gBAAgB,GAAG,GAAnB,GAAyB,GAA1B,IAAkC,CAAlC,GAAuCoB,MAAM,CAACqC,YAAP,CAAoBM,cAApB,GAAqC,GAArC,GAA2C,GAAvG;AACH;;AAuCD,WAASC,qCAAT,CAA+CC,YAA/C,EAAoG;AAChGA,IAAAA,YAAY,CAACC,IAAb,CAAkB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACxB,aAAOD,CAAC,CAACE,cAAF,KAAqBD,CAAC,CAACC,cAAF,EAA5B;AACH,KAFD;AAGH;;AAED,WAASC,qCAAT,CAA+CL,YAA/C,EAAoG;AAChGA,IAAAA,YAAY,CAACC,IAAb,CAAkB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACxB,aAAOD,CAAC,CAACI,cAAF,KAAqBH,CAAC,CAACG,cAAF,EAA5B;AACH,KAFD;AAGH;;AAED,WAASC,mBAAT,CACI1D,GADJ,EAEI2D,UAFJ,EAGIC,aAHJ,EAIIC,KAJJ,EAKoC;AAChCC,IAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACG,sBAAjB,CAAN;AACA,UAAMC,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CACTL,aAAa,CAACM,WADL,EAETN,aAAa,CAACO,YAFL,EAGT,iBAHS,CAAb;AAIAH,IAAAA,IAAI,CAACI,eAAL,CACIR,aAAa,CAACS,SADlB,EAEIC,MAAM,CAACC,KAFX,EAEkBC,OAAO,CAACC,KAF1B,EAGIC,2BAHJ;AAIAV,IAAAA,IAAI,CAACW,UAAL,CAAgBd,KAAhB,EAAuB,cAAvB;AACAG,IAAAA,IAAI,CAACY,OAAL,CAAa,YAAb,EAA2BjB,UAAU,CAACZ,QAAtC;AACAiB,IAAAA,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBrB,aAAa,CAACG,sBADrC,EAC6D,CAD7D;AAEA,WAAOC,IAAP;AACH;;AAEM,WAASkB,uBAAT,CAAiCC,QAAjC,EAAmDC,MAAnD,EAAmEC,EAAnE,EAAuF;AAC1F,QAAIF,QAAQ,CAACG,UAAT,CAAoBF,MAApB,CAAJ,EAAiC;AAC7B,aAAQ,GAAEA,MAAO,GAAE,IAAIG,MAAM,CAACJ,QAAQ,CAACK,MAAT,CAAgBJ,MAAM,CAACK,MAAvB,CAAD,CAAiC,IAAGJ,EAAG,EAApE;AACH,KAFD,MAEO;AACH,aAAQ,GAAED,MAAO,KAAIC,EAAG,EAAxB;AACH;AACJ;;;;;;;;;;;;;6BANeH,uB;;;;;;;;;;;;;;;;AA3KZpB,MAAAA,M,OAAAA,M;AAAQ4B,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUhF,MAAAA,G,OAAAA,G;AAAKiF,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAUjE,MAAAA,Q,OAAAA,Q;AAClCkE,MAAAA,iB,OAAAA,iB;AAA2CxD,MAAAA,Q,OAAAA,Q;AACnEuC,MAAAA,S,OAAAA,S;AAAWtE,MAAAA,G,OAAAA,G;AAAKwF,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG7BC,MAAAA,K,UAAAA,K;AAAOC,MAAAA,M,UAAAA,M;;AAGZC,MAAAA,oB,iBAAAA,oB;;;;;;AAjCJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAeM;AAAEC,QAAAA,IAAF;AAAQC,QAAAA,MAAR;AAAgBC,QAAAA;AAAhB,O,GAA8Bb,Q;OAC9B;AAAEnH,QAAAA,YAAF;AAAgBiI,QAAAA,KAAhB;AAAuB3E,QAAAA,MAAvB;AAA+B5B,QAAAA,gBAA/B;AAAiDmE,QAAAA,MAAjD;AAAyDE,QAAAA,OAAzD;AAAkEmC,QAAAA,WAAlE;AAA+EC,QAAAA;AAA/E,O,GAA4FhG,G;OAC5F;AAAE4B,QAAAA;AAAF,O,GAAYD,Q;OACZ;AAAEsE,QAAAA,WAAF;AAAexH,QAAAA,QAAf;AAAyByH,QAAAA;AAAzB,O,GAAuCtE,K;;iCAmChC3E,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAAA,eACzB0C,KADyB,GACjB,KADiB;AAAA,eAEzBG,QAFyB,GAEd,KAFc;AAAA,eAGzBK,QAHyB,GAGd,KAHc;AAAA,eAIzBE,QAJyB,GAId,KAJc;AAAA,eAKzBC,KALyB,GAKjB,KALiB;AAAA,eAMzBE,cANyB,GAMR,KANQ;AAAA,eAOzBE,eAPyB,GAOP,CAPO;AAOJ;AAPI,eAQzBI,aARyB,GAQT,KARS;AAAA,eASzBE,eATyB,GASPG,MAAM,CAACC,IATA;AAAA,eAUzBE,aAVyB,GAUT,IAAI8D,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAVS;AAAA,eAWzB3D,eAXyB,GAWP,KAXO;AAAA,eAYzBnD,gBAZyB,GAYN,CAZM;AAAA,eAazB0D,kBAbyB,GAaJ,KAbI;AAAA,eAczBmE,4BAdyB,GAcM,CAdN;AAAA,eAgBzBhE,QAhByB,GAgBd,IAAImD,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAhBc;AAAA;;AAAA,O;;AAsDvBc,MAAAA,e,GAAkB;AAAA;AAAA,yD;;+BAEXlJ,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eACvBmJ,QADuB,GACMD,eADN;AAEvB;AAFuB,eAGvBE,gBAHuB,GAGJ,KAHI;AAAA,eAIvBC,cAJuB,GAIN,CAJM;AAKvB;AALuB,eAMvB9C,SANuB,GAMX,EANW;AAAA,eAOvB+C,gBAPuB,GAOJ,EAPI;AAQvB;AARuB,eASvBC,kBATuB,GASF,KATE;AAAA,eAUvBC,cAVuB,GAUN,KAVM;AAAA,eAWvBC,eAXuB,GAWL,CAXK;AAYvB;AAZuB,eAavBC,kBAbuB,GAaF,KAbE;AAAA,eAcvBC,YAduB,GAcR,GAdQ;AAAA,eAevBvD,WAfuB,GAeT,CAfS;AAAA,eAgBvBC,YAhBuB,GAgBR,CAhBQ;AAAA,eAiBvB1E,KAjBuB,GAiBf,CAjBe;AAiBZ;AAjBY,eAkBvBG,MAlBuB,GAkBd,CAlBc;AAkBX;AACZ;AAnBuB,eAoBvB8H,SApBuB,GAoBX,KApBW;AAAA,eAqBvBC,cArBuB,GAqBN/G,GAAG,CAACmB,MAAJ,CAAWE,KArBL;AAsBvB;AAtBuB,eAuBvB8B,sBAvBuB,GAuBmB,IAvBnB;AAwBvB;;AACA;AAzBuB,eA0BvB6D,qBA1BuB,GA0BC,KA1BD;AAAA;;AAAA,O;;AA6BrBlD,MAAAA,2B,GAA8B,IAAIgC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,C;AAiD9B3I,MAAAA,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAClB;AADkB,eAED8J,MAFC,GAEgC,EAFhC;AAGlB;AAHkB,eAIDC,uBAJC,GAIqD,EAJrD;AAMlB;AANkB,eAODC,OAPC,GAOSvB,MAAM,CAACwB,MAAP,CAAc,CAAd,EAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,CAPT;AAAA,eAQDC,YARC,GAQc,IAAI1B,IAAJ,EARd;AAAA,eASD2B,0BATC,GAS4B,IAAI3B,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,EAAwB,GAAxB,EAA6B,GAA7B,EAAkC,GAAlC,CAT5B;AAAA;;AAWlB;AACA;AACA;AACO4B,QAAAA,UAAU,CAAC3F,KAAD,EAA8B4F,OAA9B,EAAyDC,SAAzD,EAAiF;AAC9F;AACA,eAAKR,MAAL,CAAYpC,MAAZ,GAAqB,CAArB;AACA,eAAKqC,uBAAL,CAA6BrC,MAA7B,GAAsC,CAAtC,CAH8F,CAI9F;;AACA,eAAK,MAAM5G,KAAX,IAAoB2D,KAAK,CAAC8F,UAA1B,EAAsC;AAClC,gBAAIzJ,KAAK,CAAC0J,KAAV,EAAiB;AACb;AACH;;AACD/B,YAAAA,MAAM,CAACrE,GAAP,CAAW,KAAK4F,OAAhB,EAAyBlJ,KAAK,CAAC2J,QAAN,CAAexF,CAAxC,EAA2CnE,KAAK,CAAC2J,QAAN,CAAeC,CAA1D,EAA6D5J,KAAK,CAAC2J,QAAN,CAAeE,CAA5E,EAA+E7J,KAAK,CAAC8J,KAArF;;AACA,gBAAIlC,SAAS,CAACmC,aAAV,CAAwB,KAAKb,OAA7B,EAAsCK,OAAtC,CAAJ,EAAoD;AAChD,kBAAIvJ,KAAK,CAAC6C,aAAV,EAAyB;AACrB,qBAAKoG,uBAAL,CAA6Be,IAA7B,CAAkChK,KAAlC;AACH,eAFD,MAEO;AACH,qBAAKgJ,MAAL,CAAYgB,IAAZ,CAAiBhK,KAAjB;AACH;AACJ;AACJ,WAjB6F,CAkB9F;;;AACA,eAAK,MAAMA,KAAX,IAAoB2D,KAAK,CAACsG,YAA1B,EAAwC;AACpC,gBAAIjK,KAAK,CAAC0J,KAAV,EAAiB;AACb;AACH;;AACD/B,YAAAA,MAAM,CAACrE,GAAP,CAAW,KAAK4F,OAAhB,EAAyBlJ,KAAK,CAAC2J,QAAN,CAAexF,CAAxC,EAA2CnE,KAAK,CAAC2J,QAAN,CAAeC,CAA1D,EAA6D5J,KAAK,CAAC2J,QAAN,CAAeE,CAA5E,EAA+E7J,KAAK,CAAC8J,KAArF;;AACA,gBAAIlC,SAAS,CAACmC,aAAV,CAAwB,KAAKb,OAA7B,EAAsCK,OAAtC,CAAJ,EAAoD;AAChD,mBAAKP,MAAL,CAAYgB,IAAZ,CAAiBhK,KAAjB;AACH;AACJ,WA3B6F,CA4B9F;;;AACA,eAAK,MAAMA,KAAX,IAAoB2D,KAAK,CAACuG,WAA1B,EAAuC;AACnC,gBAAIlK,KAAK,CAAC0J,KAAV,EAAiB;AACb;AACH;;AACD/B,YAAAA,MAAM,CAACrE,GAAP,CAAW,KAAK4F,OAAhB,EAAyBlJ,KAAK,CAAC2J,QAAN,CAAexF,CAAxC,EAA2CnE,KAAK,CAAC2J,QAAN,CAAeC,CAA1D,EAA6D5J,KAAK,CAAC2J,QAAN,CAAeE,CAA5E,EAA+E7J,KAAK,CAAC8J,KAArF;;AACA,gBAAIlC,SAAS,CAACmC,aAAV,CAAwB,KAAKb,OAA7B,EAAsCK,OAAtC,CAAJ,EAAoD;AAChD,mBAAKP,MAAL,CAAYgB,IAAZ,CAAiBhK,KAAjB;AACH;AACJ,WArC6F,CAsC9F;;;AACA,eAAK,MAAMA,KAAX,IAAoB2D,KAAK,CAACwG,eAA1B,EAA2C;AACvCzC,YAAAA,IAAI,CAAC0C,SAAL,CAAe,KAAKhB,YAApB,EAAkC,KAAKC,0BAAvC,EAAmErJ,KAAK,CAACqK,IAAN,CAAYC,cAAZ,EAAnE;;AACA,gBAAI1C,SAAS,CAAC2C,WAAV,CAAsB,KAAKnB,YAA3B,EAAyCG,OAAzC,CAAJ,EAAuD;AACnD,mBAAKP,MAAL,CAAYgB,IAAZ,CAAiBhK,KAAjB;AACH;AACJ;;AAED,cAAIwJ,SAAJ,EAAe;AACX,iBAAKP,uBAAL,CAA6B1E,IAA7B,CACI,CAACiG,GAAD,EAAMC,GAAN,KAAcrD,IAAI,CAACsD,eAAL,CAAqBlB,SAArB,EAAgCgB,GAAG,CAACb,QAApC,IAAgDvC,IAAI,CAACsD,eAAL,CAAqBlB,SAArB,EAAgCiB,GAAG,CAACd,QAApC,CADlE;AAGH;AACJ;;AACOgB,QAAAA,eAAe,CAACjL,MAAD,EAAgCyF,IAAhC,EAA8E;AACjG,eAAK,MAAMnF,KAAX,IAAoB,KAAKgJ,MAAzB,EAAiC;AAC7B,kBAAM4B,KAAK,GAAGzF,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoB2E,KAAlC,EAAyC,aAAzC,CAAd;;AACA,oBAAQ7K,KAAK,CAACyD,IAAd;AACI,mBAAKwE,SAAS,CAAC6C,MAAf;AACIF,gBAAAA,KAAK,CAACG,IAAN,GAAa,cAAb;AACA;;AACJ,mBAAK9C,SAAS,CAAC+C,IAAf;AACIJ,gBAAAA,KAAK,CAACG,IAAN,GAAa,YAAb;AACA;;AACJ,mBAAK9C,SAAS,CAACgD,KAAf;AACIL,gBAAAA,KAAK,CAACG,IAAN,GAAa,aAAb;AACA;;AACJ,mBAAK9C,SAAS,CAACiD,kBAAf;AACIN,gBAAAA,KAAK,CAACG,IAAN,GAAa,0BAAb;AACA;;AACJ;AACIH,gBAAAA,KAAK,CAACG,IAAN,GAAa,eAAb;AAdR;;AAgBAH,YAAAA,KAAK,CAACO,QAAN,CACIzL,MADJ,EAEIuG,SAAS,CAACmF,UAAV,CAAqBP,KAFzB,EAGI7K,KAHJ;AAKH;AACJ;;AACMqL,QAAAA,wBAAwB,CAC3BlK,GAD2B,EAE3BzB,MAF2B,EAG3B4L,gBAH2B,EAIvB;AACJ,cAAIC,CAAC,GAAG,CAAR;;AACA,eAAK,MAAMvL,KAAX,IAAoB,KAAKiJ,uBAAzB,EAAkD;AAC9C,kBAAM5F,aAAa,GAAGlC,GAAG,CAACmB,iBAAJ,CAAsBM,OAAtB,CAA8BW,IAApD;AACA,kBAAMiI,UAAU,GAAGrK,GAAG,CAACiE,aAAJ,CAAkB/B,aAAa,CAACc,CAAhC,EAAmCd,aAAa,CAACuG,CAAjD,EAAoD,SAApD,CAAnB;AACA4B,YAAAA,UAAU,CAACT,IAAX,GAAmB,sBAAqBQ,CAAE,EAA1C;AACAC,YAAAA,UAAU,CAACjG,eAAX,CAA4B,gBAAegG,CAAE,EAA7C,EAAgD9F,MAAM,CAACC,KAAvD,EAA8DC,OAAO,CAACC,KAAtE,EAA6E,IAAIiC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAA7E;AACA2D,YAAAA,UAAU,CAACC,eAAX,CAA4B,kBAAiBF,CAAE,EAA/C,EAAkD9F,MAAM,CAACC,KAAzD,EAAgEC,OAAO,CAAC+F,OAAxE;AACAF,YAAAA,UAAU,CAACxF,QAAX,CAAoBC,SAAS,CAACC,SAAV,CAAoByF,IAAxC,EAA8C,eAA9C,EACKR,QADL,CACczL,MADd,EACsBuG,SAAS,CAACmF,UAAV,CAAqBjF,MAArB,GAA8BF,SAAS,CAACmF,UAAV,CAAqBQ,IAAnD,GAA0D3F,SAAS,CAACmF,UAAV,CAAqBS,aADrG,EAEKC,eAFL,CAEqB9L,KAFrB;AAGA,cAAEuL,CAAF;;AACA,gBAAIA,CAAC,IAAID,gBAAT,EAA2B;AACvB;AACH;AACJ;AACJ;;AACMS,QAAAA,cAAc,CAAC5G,IAAD,EACjBzF,MADiB,EACc4L,gBADd,EAC8C;AAC/D,eAAKX,eAAL,CAAqBjL,MAArB,EAA6ByF,IAA7B;;AACA,cAAIoG,CAAC,GAAG,CAAR;;AACA,eAAK,MAAMvL,KAAX,IAAoB,KAAKiJ,uBAAzB,EAAkD;AAC9C;AACA;AACA;AACA9D,YAAAA,IAAI,CAACW,UAAL,CAAiB,gBAAeyF,CAAE,EAAlC,EAAqC,kBAArC;AACA,kBAAMX,KAAK,GAAGzF,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoB2E,KAAlC,EAAyC,aAAzC,CAAd;AACAD,YAAAA,KAAK,CAACO,QAAN,CAAezL,MAAf,EAAuBuG,SAAS,CAACmF,UAAV,CAAqBP,KAA5C,EAAmD7K,KAAnD;AACA,cAAEuL,CAAF;;AACA,gBAAIA,CAAC,IAAID,gBAAT,EAA2B;AACvB;AACH;AACJ;AACJ,SAjIiB,CAmIlB;AACA;AACA;;;AACOU,QAAAA,cAAc,CACjBxG,SADiB,EAEjB+C,gBAFiB,EAGjB0D,mBAHiB,EAIjBzF,EAJiB,EAIL;AACZ5F,QAAAA,KALiB,EAMjBG,MANiB,EAOjBrB,MAPiB,EAQjBwM,QARiB,EASjB/K,GATiB,EAUjBgE,IAViB,EAWe;AAChC,eAAKwF,eAAL,CAAqBjL,MAArB,EAA6ByF,IAA7B;;AAEA,cAAIgH,KAAK,GAAG,CAAZ;AACA,gBAAM9I,aAAa,GAAGlC,GAAG,CAACmB,iBAAJ,CAAsBM,OAAtB,CAA8BW,IAApD;;AACA,eAAK,MAAMvD,KAAX,IAAoB,KAAKiJ,uBAAzB,EAAkD;AAC9C,kBAAMuC,UAAU,GAAGrK,GAAG,CAACiE,aAAJ,CAAkB/B,aAAa,CAACc,CAAhC,EAAmCd,aAAa,CAACuG,CAAjD,EAAoD,SAApD,CAAnB;AACA4B,YAAAA,UAAU,CAACT,IAAX,GAAkB,qBAAlB,CAF8C,CAG9C;;AACAS,YAAAA,UAAU,CAACjG,eAAX,CAA4B,YAAWiB,EAAG,EAA1C,EAA6Cf,MAAM,CAACC,KAApD,EAA2DC,OAAO,CAACC,KAAnE,EAA0E,IAAIiC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAA1E;AACA2D,YAAAA,UAAU,CAACC,eAAX,CAA4B,cAAajF,EAAG,EAA5C,EAA+Cf,MAAM,CAACC,KAAtD,EAA6DC,OAAO,CAAC+F,OAArE;AACAF,YAAAA,UAAU,CAACxF,QAAX,CAAoBC,SAAS,CAACC,SAAV,CAAoByF,IAAxC,EAA8C,eAA9C,EACKR,QADL,CACczL,MADd,EACsBuG,SAAS,CAACmF,UAAV,CAAqBjF,MAArB,GAA8BF,SAAS,CAACmF,UAAV,CAAqBQ,IAAnD,GAA0D3F,SAAS,CAACmF,UAAV,CAAqBS,aADrG,EAEKC,eAFL,CAEqB9L,KAFrB,EAN8C,CAU9C;AACA;;AACA,cAAEmM,KAAF;AACA,kBAAMC,OAAO,GAAGD,KAAK,KAAK,KAAKlD,uBAAL,CAA6BrC,MAAvC,GACVqF,mBADU,GAEVtG,OAAO,CAACC,KAFd;AAIAT,YAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAP;AACAoE,YAAAA,IAAI,CAAC4F,IAAL,GAAY,wBAAZ;AACA5F,YAAAA,IAAI,CAACkH,WAAL,CAAiBH,QAAjB;AACA/G,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC6G,IAAvC;AACAnH,YAAAA,IAAI,CAACsG,eAAL,CAAqBlD,gBAArB,EAAuC9C,MAAM,CAAC6G,IAA9C,EAAoDF,OAApD;AACAjH,YAAAA,IAAI,CAACW,UAAL,CAAiB,YAAWU,EAAG,EAA/B,EAAkC,kBAAlC;AACA,kBAAMoE,KAAK,GAAGzF,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoB2E,KAAlC,EAAyC,aAAzC,CAAd;AACAD,YAAAA,KAAK,CAACO,QAAN,CACIzL,MADJ,EAEIuG,SAAS,CAACmF,UAAV,CAAqBP,KAFzB,EAGI7K,KAHJ;AAKH;;AACD,iBAAOmF,IAAP;AACH;;AAEMoH,QAAAA,2BAA2B,GAAY;AAC1C,iBAAO,KAAKtD,uBAAL,CAA6BrC,MAA7B,GAAsC,CAA7C;AACH;;AAzLiB,O;;2CAoMTzH,yB,GAAN,MAAMA,yBAAN,CAAyE;AAAA;AAAA,eA0jB3DqN,eA1jB2D,GA0jBzC,IAAItN,eAAJ,EA1jByC;AAAA,eA2jB3DuN,SA3jB2D,GA2jB/C,IAAI1E,QAAJ,EA3jB+C;AAAA,eA4jB3D2E,WA5jB2D,GA4jB7C,IAAI7E,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CA5jB6C;AAAA,eA6jB3D8E,0BA7jB2D,GA6jB9B,IAAIvF,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CA7jB8B;AAAA;;AAG5E1C,QAAAA,cAAc,GAAW;AACrB,iBAAOvF,yBAAyB,CAACyN,WAAjC;AACH;;AACDhI,QAAAA,cAAc,GAAW;AACrB,iBAAOzF,yBAAyB,CAAC0N,WAAjC;AACH;;AACDC,QAAAA,YAAY,CACRpN,MADQ,EAERqN,eAFQ,EAGRhI,aAHQ,EAGiD;AACzD;AACAA,UAAAA,aAAa,CAACiI,wBAAd,GAAyCD,eAAe,CAAClK,aAAhB,IAClC,CAACkK,eAAe,CAACvJ,eADiB,IAElC,CAAC,CAAC9D,MAAM,CAACiE,KAFyB,IAGlC,CAAC,CAACjE,MAAM,CAACiE,KAAP,CAAasJ,SAHmB,IAIlCvN,MAAM,CAACiE,KAAP,CAAasJ,SAAb,CAAuBpK,aAJ9B;AAMAkC,UAAAA,aAAa,CAACmI,8BAAd,GAA+CH,eAAe,CAAClK,aAAhB,IACxCkK,eAAe,CAACvJ,eADwB,IAExC,CAAC,CAAC9D,MAAM,CAACiE,KAF+B,IAGxC,CAAC,CAACjE,MAAM,CAACiE,KAAP,CAAasJ,SAHyB,IAIxCvN,MAAM,CAACiE,KAAP,CAAasJ,SAAb,CAAuBpK,aAJ9B,CARyD,CAczD;;AACAkC,UAAAA,aAAa,CAACoI,2BAAd,GAA4CpI,aAAa,CAACsD,gBAAd,IACrC3I,MAAM,CAAC0N,WAAP,KAAuBpF,WAAW,CAACqF,UADE,IAErC3N,MAAM,CAAC0N,WAAP,KAAuBpF,WAAW,CAACsF,SAF1C,CAfyD,CAmBzD;;AACAvI,UAAAA,aAAa,CAACwI,UAAd,GAA2BxI,aAAa,CAACqD,QAAd,CAAuBoF,IAAvB,CAA4B1K,OAA5B,IACpB,CAACiC,aAAa,CAACgE,qBADK,CACiB;AADjB,aAEpB,CAACgE,eAAe,CAACrL,KAFG,CAEG;AAFH,aAGpB,CAACqL,eAAe,CAAClL,QAHxB,CApByD,CAyBzD;;AACAkD,UAAAA,aAAa,CAAC0I,uBAAd,GACMV,eAAe,CAAC3K,QAAhB,IAA4B2C,aAAa,CAACwI,UADhD;AAGA,YAAExI,aAAa,CAAC2D,eAAhB;AACH;;AACDgF,QAAAA,YAAY,CACRvM,GADQ,EAER2D,UAFQ,EAGRC,aAHQ,EAIR4I,MAJQ,EAKRjO,MALQ,EAMR2F,WANQ,EAORC,YAPQ,EAOoB;AAC5B,gBAAMsI,aAAa,GAAG3H,SAAS,CAAC2H,aAAhC;AACA,gBAAMC,iBAAiB,GAAG5H,SAAS,CAAC4H,iBAApC;AACA,gBAAMrH,EAAE,GAAGmH,MAAM,CAACrF,cAAlB;AACA,gBAAMF,QAAQ,GAAGrD,aAAa,CAACqD,QAA/B;AAEA,gBAAMxH,KAAK,GAAGmE,aAAa,CAAC4D,kBAAd,GACR9H,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWqE,WAAW,GAAGN,aAAa,CAAC6D,YAAvC,CAAT,EAA+D,CAA/D,CADQ,GAERvD,WAFN;AAGA,gBAAMtE,MAAM,GAAGgE,aAAa,CAAC4D,kBAAd,GACT9H,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWsE,YAAY,GAAGP,aAAa,CAAC6D,YAAxC,CAAT,EAAgE,CAAhE,CADS,GAETtD,YAFN,CAT4B,CAa5B;;AACA,cAAIP,aAAa,CAACwI,UAAlB,EAA8B;AAC1B;AACA;AACA;AACA,gBAAIxI,aAAa,CAAC8D,SAAlB,EAA6B;AACzB1H,cAAAA,GAAG,CAAC2E,UAAJ,CAAgB,eAAcU,EAAG,EAAjC,EAAoCsB,WAAW,CAACgG,KAAhD,EAAuD/I,aAAa,CAAC+D,cAArE,EAAqFlI,KAArF,EAA4FG,MAA5F,EAAoG,CAApG,EAAuG,CAAvG,EAA0G,CAA1G,EACIqH,QAAQ,CAACoF,IAAT,CAAcO,WADlB,EAC+BH,aAAa,CAACI,gBAD7C,EAC+DH,iBAAiB,CAACI,UADjF;AAEH,aAHD,MAGO;AACH9M,cAAAA,GAAG,CAAC2E,UAAJ,CAAgB,eAAcU,EAAG,EAAjC,EAAoCsB,WAAW,CAACgG,KAAhD,EAAuD5K,MAAM,CAACE,KAA9D,EAAqExC,KAArE,EAA4EG,MAA5E,EAAoF,CAApF,EAAuF,CAAvF,EAA0F,CAA1F,EACIqH,QAAQ,CAACoF,IAAT,CAAcO,WADlB,EAC+BH,aAAa,CAACI,gBAD7C,EAC+DH,iBAAiB,CAACI,UADjF;AAEH;;AACD9M,YAAAA,GAAG,CAAC2E,UAAJ,CAAgB,mBAAkBU,EAAG,EAArC,EAAwCsB,WAAW,CAACgG,KAApD,EAA2D5K,MAAM,CAACe,aAAlE,EAAiFrD,KAAjF,EAAwFG,MAAxF,EAAgG,CAAhG,EAAmG,CAAnG,EAAsG,CAAtG,EACIqH,QAAQ,CAACoF,IAAT,CAAcO,WADlB,EAC+BH,aAAa,CAACM,wBAD7C,EACuEL,iBAAiB,CAACI,UADzF;AAEH,WA3B2B,CA6B5B;;;AACA9M,UAAAA,GAAG,CAACoE,eAAJ,CACK,YAAWiB,EAAG,EADnB,EAEI1B,UAAU,CAAC/B,eAFf,EAGI+B,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyBuG,CAJ7B;AAMAzI,UAAAA,GAAG,CAACsK,eAAJ,CACK,cAAajF,EAAG,EADrB,EAEItD,MAAM,CAACe,aAFX,EAGIa,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyBuG,CAJ7B,EApC4B,CA2C5B;;AACA,cAAI7E,aAAa,CAAC0I,uBAAlB,EAA2C;AACvC,kBAAMtB,KAAK,GAAGrH,UAAU,CAACoD,4BAAzB;;AACA,iBAAK,IAAIqD,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAKY,KAAtB,EAA6B,EAAEZ,CAA/B,EAAkC;AAC9BpK,cAAAA,GAAG,CAACoE,eAAJ,CACK,gBAAegG,CAAE,EADtB,EAEIzG,UAAU,CAAC/B,eAFf,EAGI+B,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyBuG,CAJ7B;AAMAzI,cAAAA,GAAG,CAACsK,eAAJ,CACK,kBAAiBF,CAAE,EADxB,EAEIrI,MAAM,CAACe,aAFX,EAGIa,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyBuG,CAJ7B;AAMH;AACJ;AACJ;;AACDuE,QAAAA,KAAK,CACDhN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD0O,OALC,EAKuE;AACxE,gBAAM5H,EAAE,GAAG9G,MAAM,CAACiO,MAAP,CAAcrF,cAAzB;AAEA,gBAAM3E,KAAK,GAAGjE,MAAM,CAACiE,KAArB;AACA,gBAAMsJ,SAAS,GAAGtJ,KAAK,CAACsJ,SAAxB;AAEA,YAAElI,aAAa,CAAC2D,eAAhB;AACAzD,UAAAA,MAAM,CAACF,aAAa,CAAC2D,eAAd,IAAiC,CAAlC,CAAN,CAPwE,CASxE;;AACA,eAAK8D,eAAL,CAAqBlD,UAArB,CAAgC3F,KAAhC,EAAuCjE,MAAM,CAAC6J,OAA9C,EAVwE,CAYxE;;AACA,cAAIxE,aAAa,CAACiI,wBAAlB,EAA4C;AACxC/H,YAAAA,MAAM,CAAC,CAAC,CAACgI,SAAH,CAAN;;AACA,iBAAKoB,yBAAL,CAA+BlN,GAA/B,EAAoC2D,UAApC,EAAgD0B,EAAhD,EAAoDyG,SAApD,EAA+DvN,MAA/D;AACH,WAhBuE,CAkBxE;;;AACA,cAAIqF,aAAa,CAAC0I,uBAAlB,EAA2C;AACvC;AACA;AACA,iBAAKjB,eAAL,CAAqBnB,wBAArB,CACIlK,GADJ,EACSzB,MADT,EACiBoF,UAAU,CAACoD,4BAD5B;AAEH;;AAED,eAAKoG,4BAAL,CAAkCnN,GAAlC,EAAuC4D,aAAvC,EAAsDyB,EAAtD,EAA0DyG,SAA1D,EAAqEvN,MAAM,CAACiE,KAA5E;;AAEA,cAAIoB,aAAa,CAAC2D,eAAd,GAAgC,CAAhC,IAAqC3D,aAAa,CAAC4D,kBAAvD,EAA2E;AACvEyF,YAAAA,OAAO,CAAC5I,SAAR,GAAoBT,aAAa,CAAC4D,kBAAd,GACb,mBAAkBnC,EAAG,EADR,GAEb,aAAYA,EAAG,EAFtB;AAGA4H,YAAAA,OAAO,CAAC7F,gBAAR,GAA2BxD,aAAa,CAAC4D,kBAAd,GACpB,oBAAmBnC,EAAG,EADF,GAEpB,cAAaA,EAAG,EAFvB;AAGH,WAPD,MAOO;AACH4H,YAAAA,OAAO,CAAC5I,SAAR,GAAoBT,aAAa,CAACS,SAAlC;AACA4I,YAAAA,OAAO,CAAC7F,gBAAR,GAA2BxD,aAAa,CAACwD,gBAAzC;AACH;;AAED,gBAAMpD,IAAI,GAAG,KAAKoJ,yBAAL,CACTpN,GADS,EACJ2D,UADI,EACQC,aADR,EACuByB,EADvB,EAC2B9G,MAD3B,EAETqF,aAAa,CAACnE,KAFL,EAEYmE,aAAa,CAAChE,MAF1B,EAEkCkM,SAFlC,EAGTmB,OAAO,CAAC5I,SAHC,EAGU4I,OAAO,CAAC7F,gBAHlB,EAIT,CAACxD,aAAa,CAACwI,UAJN,EAKTxI,aAAa,CAACgE,qBAAd,GAAsCpD,OAAO,CAACC,KAA9C,GAAsDD,OAAO,CAAC+F,OALrD,CAAb;;AAOA,cAAI,CAAC3G,aAAa,CAACgE,qBAAnB,EAA0C;AACtCqF,YAAAA,OAAO,CAAC7F,gBAAR,GAA2B,EAA3B;AACH;;AAED,cAAIxD,aAAa,CAAC2D,eAAd,KAAkC,CAAlC,IAAuC3D,aAAa,CAAC4D,kBAAzD,EAA6E;AACzE,mBAAO9D,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCqJ,OAAO,CAAC5I,SAAzC,CAA1B;AACH,WAFD,MAEO;AACH,mBAAOL,IAAP;AACH;AACJ;;AACOkJ,QAAAA,yBAAyB,CAC7BlN,GAD6B,EAE7B2D,UAF6B,EAG7B0B,EAH6B,EAI7BxG,KAJ6B,EAK7BN,MAL6B,EAMzB;AACJ,gBAAMwG,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMkF,UAAU,GAAGnF,SAAS,CAACmF,UAA7B,CAFI,CAGJ;AACA;AACA;;AACA,gBAAMoD,UAAU,GAAGrN,GAAG,CAACmB,iBAAJ,CAAsBM,OAAtB,CAA8BW,IAAjD;AACA,gBAAM3C,KAAK,GAAG4N,UAAU,CAACrK,CAAzB;AACA,gBAAMpD,MAAM,GAAGyN,UAAU,CAAC5E,CAA1B;AAEA,gBAAMsC,QAAQ,GAAG,KAAKO,SAAtB;AACAP,UAAAA,QAAQ,CAACxL,IAAT,GAAgBwL,QAAQ,CAACvL,GAAT,GAAe,CAA/B;AACAuL,UAAAA,QAAQ,CAACtL,KAAT,GAAiBA,KAAjB;AACAsL,UAAAA,QAAQ,CAACnL,MAAT,GAAkBA,MAAlB,CAbI,CAeJ;AACA;AACA;;AACA,gBAAMoE,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAb;AACAoE,UAAAA,IAAI,CAAC4F,IAAL,GAAY,mBAAZ;AACA5F,UAAAA,IAAI,CAACI,eAAL,CAAsB,YAAWiB,EAAG,EAApC,EAAuCf,MAAM,CAACC,KAA9C,EAAqDC,OAAO,CAACC,KAA7D,EAAoE,IAAIiC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAApE;AACA1C,UAAAA,IAAI,CAACsG,eAAL,CAAsB,cAAajF,EAAG,EAAtC,EAAyCf,MAAM,CAACC,KAAhD,EAAuDC,OAAO,CAAC+F,OAA/D;AACA,gBAAMnL,QAAQ,GAAGY,GAAG,CAACmB,iBAAJ,CAAsBmM,YAAtB,GAAqCzO,KAAK,CAACO,QAA3C,GAAsD,CAAvE,CAtBI,CAwBJ;;AACA,eAAK,IAAIJ,KAAK,GAAG,CAAjB,EAAoBA,KAAK,KAAKI,QAA9B,EAAwC,EAAEJ,KAA1C,EAAiD;AAC7CJ,YAAAA,uBAAuB,CAACC,KAAD,EAAQY,KAAR,EAAeG,MAAf,EAAuBZ,KAAvB,EAA8B,KAAKsM,SAAnC,EAA8C3H,UAAU,CAACzE,gBAAzD,CAAvB;AACA,kBAAMuK,KAAK,GAAGzF,IAAI,CAACa,QAAL,CAAcE,SAAS,CAACyF,IAAxB,EAA8B,eAA9B,CAAd;;AACA,gBAAI,CAAC7G,UAAU,CAAC5C,QAAhB,EAA0B;AAAE;AACxB0I,cAAAA,KAAK,CAACyB,WAAN,CAAkB,KAAKI,SAAvB;AACH;;AACD7B,YAAAA,KAAK,CACAO,QADL,CACczL,MADd,EACsB0L,UAAU,CAACjF,MAAX,GAAoBiF,UAAU,CAACQ,IAA/B,GAAsCR,UAAU,CAACS,aADvE,EAEKC,eAFL,CAEqB9L,KAFrB,EAE4BG,KAF5B;AAGH;AACJ;;AACOmO,QAAAA,4BAA4B,CAChCnN,GADgC,EAEhC4D,aAFgC,EAGhCyB,EAHgC,EAIhCyG,SAJgC,EAKhCtJ,KALgC,EAM5B;AACJ,gBAAM+K,sBAAsB,GAAG7H,QAAQ,CAAC8H,QAAT,CAAkBD,sBAAjD;;AACA,cAAI,CAACA,sBAAL,EAA6B;AACzB;AACH;;AACD,gBAAMb,iBAAiB,GAAG5H,SAAS,CAAC4H,iBAApC;AACA,gBAAMe,MAAM,GAAGF,sBAAsB,CAACG,SAAvB,EAAf;AACA,gBAAMC,aAAa,GAAG,CAAtB;AACA,cAAIC,OAAO,GAAG,CAAd;;AACA,eAAK,MAAMC,KAAX,IAAoBJ,MAApB,EAA4B;AACxB,gBAAI,CAACI,KAAK,CAACC,UAAX,EAAuB;AACnB;AACH;;AACD,kBAAMC,IAAI,GAAGF,KAAK,CAACG,UAAN,EAAb;AACA,kBAAMvO,KAAK,GAAGC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWkO,IAAI,CAAC/K,CAAhB,CAAT,EAA6B,CAA7B,CAAd;AACA,kBAAMpD,MAAM,GAAGF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWkO,IAAI,CAACtF,CAAhB,CAAT,EAA6B,CAA7B,CAAf;;AAEA,gBAAIoF,KAAK,CAACI,SAAN,KAAoB1L,QAAQ,CAACC,KAAT,CAAe0L,SAAf,CAAyBC,MAAjD,EAAyD;AACrD,kBAAI,CAACvK,aAAa,CAACoI,2BAAnB,EAAgD;AAC5C;AACH;;AACD,oBAAMQ,MAA6B,GAAGqB,KAAK,CAACO,qBAAN,CAA6B5B,MAAnE;AACA,oBAAMnI,SAAS,GAAI,gBAAeuJ,OAAQ,EAA1C;AACA,oBAAMxG,gBAAgB,GAAI,gBAAewG,OAAQ,EAAjD,CANqD,CAOrD;;AACA5N,cAAAA,GAAG,CAACqO,eAAJ,CAAoBhK,SAApB,EACIT,aAAa,CAAC+D,cADlB,EACkClI,KADlC,EACyCG,MADzC,EACiD4M,MADjD;AAEAxM,cAAAA,GAAG,CAACsK,eAAJ,CAAoBlD,gBAApB,EACIxG,GAAG,CAACmB,MAAJ,CAAWe,aADf,EAC8BrD,KAD9B,EACqCG,MADrC,EAC6C8M,iBAAiB,CAACI,UAD/D,EAVqD,CAarD;;AACA,oBAAMwB,SAAS,GAAGtO,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAlB;AACA0O,cAAAA,SAAS,CAAC1E,IAAV,GAAkB,wBAAuBgE,OAAQ,EAAjD;;AACA,mBAAKW,yBAAL,CAA+BD,SAA/B,EAA0C1K,aAA1C,EAAyDyB,EAAzD,EAA6DwI,KAAK,CAACtP,MAAnE,EACI8F,SADJ,EACe+C,gBADf,EACiC0E,SADjC,EAC4CtJ,KAD5C;AAEH,aAlBD,MAkBO,IAAI6D,MAAJ,EAAY;AACf,mBAAK,IAAImI,OAAO,GAAG,CAAnB,EAAsBA,OAAO,GAAGX,KAAK,CAACY,iBAAN,CAAwBhJ,MAAxD,EAAgE+I,OAAO,EAAvE,EAA2E;AACvEX,gBAAAA,KAAK,CAACa,eAAN,CAAsBF,OAAtB;AACA,sBAAMhC,MAA6B,GAAGqB,KAAK,CAACY,iBAAN,CAAwBD,OAAxB,EAAiChC,MAAvE;AACA,sBAAMnI,SAAS,GAAI,cAAauJ,OAAQ,GAAEY,OAAQ,EAAlD;AACA,sBAAMpH,gBAAgB,GAAI,cAAawG,OAAQ,GAAEY,OAAQ,EAAzD,CAJuE,CAKvE;;AACAxO,gBAAAA,GAAG,CAACqO,eAAJ,CAAoBhK,SAApB,EACIT,aAAa,CAAC+D,cADlB,EACkClI,KADlC,EACyCG,MADzC,EACiD4M,MADjD;AAEAxM,gBAAAA,GAAG,CAACsK,eAAJ,CAAoBlD,gBAApB,EACIxG,GAAG,CAACmB,MAAJ,CAAWe,aADf,EAC8BrD,KAD9B,EACqCG,MADrC,EAC6C8M,iBAAiB,CAACI,UAD/D,EARuE,CAWvE;;AACA,sBAAMwB,SAAS,GAAGtO,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAlB;AACA0O,gBAAAA,SAAS,CAAC1E,IAAV,GAAkB,YAAWgE,OAAQ,GAAEY,OAAQ,EAA/C;;AACA,qBAAKD,yBAAL,CAA+BD,SAA/B,EAA0C1K,aAA1C,EAAyDyB,EAAzD,EAA6DwI,KAAK,CAACtP,MAAnE,EACI8F,SADJ,EACe+C,gBADf,EACiC0E,SADjC,EAC4CtJ,KAD5C;AAEH;;AACDqL,cAAAA,KAAK,CAACC,UAAN,GAAmB,KAAnB;AACH;;AACD,cAAEF,OAAF;;AACA,gBAAIA,OAAO,KAAKD,aAAhB,EAA+B;AAC3B;AACH;AACJ;AACJ;;AACOY,QAAAA,yBAAyB,CAC7BvK,IAD6B,EAE7BJ,aAF6B,EAG7ByB,EAH6B,EAI7B9G,MAJ6B,EAK7B8F,SAL6B,EAM7B+C,gBAN6B,EAO7B0E,SAP6B,EAQ7BtJ,KAAkC,GAAG,IARR,EASzB;AACJ,gBAAMuC,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMkF,UAAU,GAAGnF,SAAS,CAACmF,UAA7B,CAFI,CAGJ;;AACA,gBAAM0E,YAAY,GAAG/K,aAAa,CAACwI,UAAd,GAA2B5H,OAAO,CAAC+F,OAAnC,GAA6C/F,OAAO,CAACC,KAA1E,CAJI,CAMJ;;AACA,cAAInG,qBAAqB,CAACC,MAAD,CAAzB,EAAmC;AAC/B,iBAAKiN,0BAAL,CAAgCxI,CAAhC,GAAoCzE,MAAM,CAACqQ,UAAP,CAAkB5L,CAAtD;AACA,iBAAKwI,0BAAL,CAAgC/C,CAAhC,GAAoClK,MAAM,CAACqQ,UAAP,CAAkBnG,CAAtD;AACA,iBAAK+C,0BAAL,CAAgC9C,CAAhC,GAAoCnK,MAAM,CAACqQ,UAAP,CAAkBlG,CAAtD;AACA,kBAAMkG,UAAU,GAAG9J,SAAS,CAAC+J,QAAV,CAAmB,KAAKrD,0BAAxB,CAAnB;AACA,iBAAKD,WAAL,CAAiBvI,CAAjB,GAAqB4L,UAAU,CAAC5L,CAAhC;AACA,iBAAKuI,WAAL,CAAiB9C,CAAjB,GAAqBmG,UAAU,CAACnG,CAAhC;AACA,iBAAK8C,WAAL,CAAiB7C,CAAjB,GAAqBkG,UAAU,CAAClG,CAAhC;AACA,iBAAK6C,WAAL,CAAiBzM,CAAjB,GAAqB8P,UAAU,CAAC9P,CAAhC;AACAkF,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CoK,YAA9C,EAA4D,KAAKpD,WAAjE;AACH,WAVD,MAUO;AACHvH,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC6G,IAAvC,EAA6CwD,YAA7C;AACH,WAnBG,CAqBJ;;;AACA,cAAIpQ,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aAApC,EAAmD;AAC/CkB,YAAAA,IAAI,CAACsG,eAAL,CACIlD,gBADJ,EAEI9C,MAAM,CAACC,KAFX,EAGIC,OAAO,CAAC+F,OAHZ,EAIIhM,MAAM,CAACuQ,UAJX,EAKIvQ,MAAM,CAACwQ,YALX,EAMIxQ,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aANpC;AAQH,WATD,MASO;AACHkB,YAAAA,IAAI,CAACsG,eAAL,CAAqBlD,gBAArB,EAAuC9C,MAAM,CAAC6G,IAA9C,EAAoD3G,OAAO,CAAC+F,OAA5D;AACH,WAjCG,CAmCJ;;;AACA,cAAI3G,aAAa,CAACiI,wBAAlB,EAA4C;AACxC7H,YAAAA,IAAI,CAACW,UAAL,CAAiB,YAAWU,EAAG,EAA/B,EAAkC,cAAlC;AACH,WAtCG,CAwCJ;AAEA;;;AACArB,UAAAA,IAAI,CAACa,QAAL,CAAcE,SAAS,CAACyF,IAAxB,EAA8B,aAA9B,EAA6C;AAA7C,WACKR,QADL,CACczL,MADd,EAEQ0L,UAAU,CAACjF,MAAX,GAAoBiF,UAAU,CAACQ,IAA/B,GAAsCR,UAAU,CAAC+E,gBAFzD,EAGQlD,SAAS,IAAImD,SAHrB,EAIQzM,KAAK,GAAGA,KAAH,GAAWyM,SAJxB;AAKH;;AACO7B,QAAAA,yBAAyB,CAC7BpN,GAD6B,EAE7B2D,UAF6B,EAG7BC,aAH6B,EAI7ByB,EAJ6B,EAK7B9G,MAL6B,EAM7BkB,KAN6B,EAO7BG,MAP6B,EAQ7BkM,SAR6B,EAS7BzH,SAT6B,EAU7B+C,gBAV6B,EAW7B8H,WAAoB,GAAG,KAXM,EAY7BpE,mBAAgC,GAAGtG,OAAO,CAAC+F,OAZd,EAaG;AAChC,gBAAMxF,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMkF,UAAU,GAAGnF,SAAS,CAACmF,UAA7B,CAFgC,CAGhC;AACA;AACA;AACA;;AACA,gBAAM2E,UAAU,GAAGrQ,MAAM,CAACqQ,UAA1B,CAPgC,CAOM;;AACtC,eAAKrD,WAAL,CAAiBvI,CAAjB,GAAqB4L,UAAU,CAAC5L,CAAhC;AACA,eAAKuI,WAAL,CAAiB9C,CAAjB,GAAqBmG,UAAU,CAACnG,CAAhC;AACA,eAAK8C,WAAL,CAAiB7C,CAAjB,GAAqBkG,UAAU,CAAClG,CAAhC;AACA,eAAK6C,WAAL,CAAiBzM,CAAjB,GAAqB8P,UAAU,CAAC9P,CAAhC,CAXgC,CAahC;;AACA,gBAAMiM,QAAQ,GAAGxM,MAAM,CAACwM,QAAxB,CAdgC,CAcE;;AAClC,eAAKO,SAAL,CAAe/L,IAAf,GAAsBG,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAAC/H,CAAT,GAAavD,KAAxB,CAAtB;AACA,eAAK6L,SAAL,CAAe9L,GAAf,GAAqBE,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAACtC,CAAT,GAAa7I,MAAxB,CAArB,CAhBgC,CAiBhC;AACA;;AACA,eAAK0L,SAAL,CAAe7L,KAAf,GAAuBC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAACtL,KAAT,GAAiBA,KAA5B,CAAT,EAA6C,CAA7C,CAAvB;AACA,eAAK6L,SAAL,CAAe1L,MAAf,GAAwBF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAACnL,MAAT,GAAkBA,MAA7B,CAAT,EAA+C,CAA/C,CAAxB,CApBgC,CAsBhC;;AACA,gBAAMwM,UAAU,GAAG,CAAC8C,WAAD,IAAgBtL,aAAa,CAACwI,UAAjD;AACAtI,UAAAA,MAAM,CAAC,CAACsI,UAAD,IAAexI,aAAa,CAAC0I,uBAA9B,CAAN,CAxBgC,CA0BhC;AACA;AACA;;AACA,gBAAMtI,IAAI,GAAGJ,aAAa,CAAC0I,uBAAd,GACP,KAAK8C,6BAAL,CAAmCpP,GAAnC,EAAwC2D,UAAxC,EAAoDC,aAApD,EACEyB,EADF,EACM9G,MADN,EACc6N,UADd,EAC0B3M,KAD1B,EACiCG,MADjC,EACyCkM,SADzC,EAEEzH,SAFF,EAEa+C,gBAFb,EAE+B0D,mBAF/B,CADO,GAIP,KAAKuE,iCAAL,CAAuCrP,GAAvC,EAA4C4D,aAA5C,EACEyB,EADF,EACM9G,MADN,EACckB,KADd,EACqBG,MADrB,EAC6BkM,SAD7B,EAEEzH,SAFF,EAEa+C,gBAFb,EAE+B0D,mBAF/B,CAJN,CA7BgC,CAqChC;;AACA,cAAIlH,aAAa,CAACmI,8BAAlB,EAAkD;AAC9C,iBAAKuD,qBAAL,CAA2B/Q,MAA3B,EAAmCuN,SAAnC,EAA8C9H,IAA9C;AACH,WAxC+B,CA0ChC;AACA;AACA;AACA;;;AAEA,gBAAMuL,UAAU,GAAGtF,UAAU,CAACP,KAAX,IACdnL,MAAM,CAACiR,gBAAP,GACKvF,UAAU,CAACwF,QADhB,GAEKxF,UAAU,CAACO,IAHF,CAAnB;AAKAxG,UAAAA,IAAI,CACCa,QADL,CACcE,SAAS,CAAC2E,KADxB,EAEKM,QAFL,CAEczL,MAFd,EAEsBgR,UAFtB,EAEkCzD,SAAS,IAAImD,SAF/C;AAIA,iBAAOjL,IAAP;AACH;;AACOoL,QAAAA,6BAA6B,CACjCpP,GADiC,EAEjC2D,UAFiC,EAGjCC,aAHiC,EAIjCyB,EAJiC,EAKjC9G,MALiC,EAMjC6N,UANiC,EAOjC3M,KAPiC,EAQjCG,MARiC,EASjCkM,SATiC,EAUjCzH,SAViC,EAWjC+C,gBAXiC,EAYjC0D,mBAZiC,EAaD;AAChChH,UAAAA,MAAM,CAACF,aAAa,CAAC0I,uBAAf,CAAN,CADgC,CAEhC;AACA;AACA;;AACA,cAAItI,IAAJ;;AACA,cAAIoI,UAAJ,EAAgB;AACZ,kBAAMsD,gBAAgB,GAAI,eAAcrK,EAAG,EAA3C;AACA,kBAAMsK,oBAAoB,GAAI,mBAAkBtK,EAAG,EAAnD;AACA,kBAAMuH,WAAW,GAAGhJ,aAAa,CAACqD,QAAd,CAAuBoF,IAAvB,CAA4BO,WAAhD;AAEA,kBAAMgD,MAAM,GAAG5P,GAAG,CAAC6P,wBAAJ,CAA6BpQ,KAA7B,EAAoCG,MAApC,EAA4CgN,WAA5C,EAAyD,CAAzD,EAA4D,SAA5D,CAAf;AACAgD,YAAAA,MAAM,CAAChG,IAAP,GAAc,iBAAd,CANY,CAQZ;;AACA,iBAAKkG,0BAAL,CAAgCF,MAAhC,EAAwChM,aAAxC,EAAuDyB,EAAvD,EAA2D9G,MAA3D,EACImR,gBADJ,EACsBC,oBADtB,EAC4CnL,OAAO,CAAC+F,OADpD,EAC6DuB,SAD7D;;AAGA8D,YAAAA,MAAM,CAACG,mBAAP,CAA2BL,gBAA3B,EAA6CrL,SAA7C;AAEAL,YAAAA,IAAI,GAAG4L,MAAP;AACH,WAfD,MAeO;AACH5L,YAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAP;AACAoE,YAAAA,IAAI,CAAC4F,IAAL,GAAY,aAAZ;;AAEA,iBAAKkG,0BAAL,CAAgC9L,IAAhC,EAAsCJ,aAAtC,EAAqDyB,EAArD,EAAyD9G,MAAzD,EACI8F,SADJ,EACe+C,gBADf,EACiC0D,mBADjC,EACsDgB,SADtD;AAEH;;AACDhI,UAAAA,MAAM,CAACE,IAAI,KAAKiL,SAAV,CAAN,CA5BgC,CA8BhC;;AACA,eAAK5D,eAAL,CAAqBT,cAArB,CACI5G,IADJ,EAEIzF,MAFJ,EAGIoF,UAAU,CAACoD,4BAHf;AAMA,iBAAO/C,IAAP;AACH;;AACOqL,QAAAA,iCAAiC,CACrCrP,GADqC,EAErC4D,aAFqC,EAGrCyB,EAHqC,EAIrC9G,MAJqC,EAKrCkB,KALqC,EAMrCG,MANqC,EAOrCkM,SAPqC,EAQrCzH,SARqC,EASrC+C,gBATqC,EAUrC0D,mBAVqC,EAWL;AAChChH,UAAAA,MAAM,CAAC,CAACF,aAAa,CAAC0I,uBAAhB,CAAN,CADgC,CAGhC;;AACA,cAAItI,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAX;AACAoE,UAAAA,IAAI,CAAC4F,IAAL,GAAY,aAAZ;AAEA,gBAAMoG,YAAY,GAAG,KAAK3E,eAAL,CAAqBD,2BAArB,KACf5G,OAAO,CAACC,KADO,GAEfqG,mBAFN;;AAIA,eAAKgF,0BAAL,CAAgC9L,IAAhC,EAAsCJ,aAAtC,EACIyB,EADJ,EACQ9G,MADR,EACgB8F,SADhB,EAC2B+C,gBAD3B,EAC6C4I,YAD7C,EAC2DlE,SAD3D,EAXgC,CAchC;;;AACA9H,UAAAA,IAAI,GAAG,KAAKqH,eAAL,CACFR,cADE,CACaxG,SADb,EACwB+C,gBADxB,EAC0C0D,mBAD1C,EAECzF,EAFD,EAEK5F,KAFL,EAEYG,MAFZ,EAEoBrB,MAFpB,EAE4B,KAAK+M,SAFjC,EAE4CtL,GAF5C,EAEiDgE,IAFjD,CAAP;AAIA,iBAAOA,IAAP;AACH;;AACO8L,QAAAA,0BAA0B,CAC9B9L,IAD8B,EAE9BJ,aAF8B,EAG9ByB,EAH8B,EAI9B9G,MAJ8B,EAK9B8F,SAL8B,EAM9B+C,gBAN8B,EAO9B0D,mBAP8B,EAQ9BgB,SAR8B,EAS9BtJ,KAAkC,GAAG,IATP,EAU1B;AACJ,gBAAMuC,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMkF,UAAU,GAAGnF,SAAS,CAACmF,UAA7B,CAFI,CAGJ;;AACAjG,UAAAA,IAAI,CAACkH,WAAL,CAAiB,KAAKI,SAAtB;AAEA,gBAAMqD,YAAY,GAAG/K,aAAa,CAACwI,UAAd,GAA2B5H,OAAO,CAAC+F,OAAnC,GAA6C/F,OAAO,CAACC,KAA1E,CANI,CAQJ;;AACA,cAAInG,qBAAqB,CAACC,MAAD,CAAzB,EAAmC;AAC/ByF,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CoK,YAA9C,EAA4D,KAAKpD,WAAjE;AACH,WAFD,MAEO;AACHvH,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC6G,IAAvC,EAA6CwD,YAA7C;AACH,WAbG,CAeJ;;;AACA,cAAIvI,KAAJ,EAAW;AACP,gBAAI/B,SAAS,KAAKT,aAAa,CAACS,SAA5B,IACA+C,gBAAgB,KAAKxD,aAAa,CAACwD,gBADvC,EACyD;AACrDjB,cAAAA,IAAI,CAAC,4DAAD,CAAJ;AACH;AACJ;;AAED,cAAI5H,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aAApC,EAAmD;AAC/CkB,YAAAA,IAAI,CAACsG,eAAL,CACIlD,gBADJ,EAEI9C,MAAM,CAACC,KAFX,EAGIuG,mBAHJ,EAIIvM,MAAM,CAACuQ,UAJX,EAKIvQ,MAAM,CAACwQ,YALX,EAMIxQ,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aANpC;AAQH,WATD,MASO;AACHkB,YAAAA,IAAI,CAACsG,eAAL,CAAqBlD,gBAArB,EAAuC9C,MAAM,CAAC6G,IAA9C,EAAoDL,mBAApD;AACH,WAlCG,CAoCJ;;;AACA,cAAIlH,aAAa,CAACiI,wBAAlB,EAA4C;AACxC7H,YAAAA,IAAI,CAACW,UAAL,CAAiB,YAAWU,EAAG,EAA/B,EAAkC,cAAlC;AACH,WAvCG,CAyCJ;AAEA;;;AACArB,UAAAA,IAAI,CAACa,QAAL,CAAcE,SAAS,CAACyF,IAAxB,EAA8B;AAA9B,WACKR,QADL,CACczL,MADd,EAEQ0L,UAAU,CAACjF,MAAX,GAAoBiF,UAAU,CAACQ,IAFvC,EAGQqB,SAAS,IAAImD,SAHrB,EAIQzM,KAAK,GAAGA,KAAH,GAAWyM,SAJxB;AAKH;;AACOK,QAAAA,qBAAqB,CACzB/Q,MADyB,EAEzBuN,SAFyB,EAGzB9H,IAHyB,EAI3B;AACE,gBAAMe,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMkF,UAAU,GAAGnF,SAAS,CAACmF,UAA7B;AACAjG,UAAAA,IAAI,CAACa,QAAL,CAAcE,SAAS,CAAC2E,KAAxB,EAA+B,eAA/B,EACKM,QADL,CAEQzL,MAFR,EAGQ0L,UAAU,CAACS,aAAX,GAA2BT,UAAU,CAACgG,aAAtC,GAAsDhG,UAAU,CAACP,KAHzE,EAIQoC,SAAS,IAAImD,SAJrB;AAMH;;AAzjB2E,O;;AAAnEjR,MAAAA,yB,CACFyN,W,GAAc,G;AADZzN,MAAAA,yB,CAEF0N,W,GAAc,G;;yCAkkBZzN,uB,GAAN,MAAMA,uBAAN,CAAuE;AAAA;AAkK1E;AAlK0E,eAmKzDiS,2BAnKyD,GAmK3B,IAAIxJ,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAnK2B;AAAA,eAoKzDyJ,YApKyD,GAoK1C,IAAIjK,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CApK0C;AAAA,eAqKzDkK,aArKyD,GAqKzC,IAAIlK,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CArKyC;AAAA,eAsKzDmK,YAtKyD,GAsK3B,EAtK2B;AAAA,eAuKzDC,aAvKyD,GAuK1B,EAvK0B;AAAA,eAwKzDC,cAxKyD,GAwKzB,EAxKyB;AAAA;;AAC1EhN,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDkI,QAAAA,YAAY,CACRpN,MADQ,EAERqN,eAFQ,EAGRhI,aAHQ,EAG+C;AACvDA,UAAAA,aAAa,CAAC4M,WAAd,GACM5M,aAAa,CAACqD,QAAd,CAAuBwJ,KAAvB,CAA6B9O,OAA7B,IACC,CAAC,CAACiC,aAAa,CAACqD,QAAd,CAAuBwJ,KAAvB,CAA6BC,QAFtC;;AAGA,cAAI9M,aAAa,CAAC4M,WAAlB,EAA+B;AAC3B,cAAE5M,aAAa,CAAC2D,eAAhB;AACH;AACJ;;AACDgF,QAAAA,YAAY,CACRvM,GADQ,EAER2D,UAFQ,EAGRC,aAHQ,EAIR4I,MAJQ,EAI6B;AACrC,cAAI5I,aAAa,CAAC4M,WAAlB,EAA+B;AAC3B,kBAAMnL,EAAE,GAAGmH,MAAM,CAACrF,cAAlB;AACA,gBAAIwJ,UAAU,GAAG/M,aAAa,CAACnE,KAA/B;AACA,gBAAImR,WAAW,GAAGhN,aAAa,CAAChE,MAAhC;;AACA,iBAAK,IAAIwK,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAKxG,aAAa,CAACqD,QAAd,CAAuBwJ,KAAvB,CAA6BI,UAA7B,GAA0C,CAAhE,EAAmE,EAAEzG,CAArE,EAAwE;AACpEuG,cAAAA,UAAU,GAAGjR,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW8Q,UAAU,GAAG,CAAxB,CAAT,EAAqC,CAArC,CAAb;AACAC,cAAAA,WAAW,GAAGlR,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW+Q,WAAW,GAAG,CAAzB,CAAT,EAAsC,CAAtC,CAAd;AACA5Q,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,WAAUiB,EAAG,IAAG+E,CAAE,EAAvC,EACIxG,aAAa,CAAC+D,cADlB,EACkCgJ,UADlC,EAC8CC,WAD9C;AAEH;AACJ;AACJ;;AAED5D,QAAAA,KAAK,CACDhN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD0O,OALC,EAMD6D,cANC,EAO8C;AAC/C,cAAI,CAAClN,aAAa,CAAC4M,WAAnB,EAAgC;AAC5B,mBAAOM,cAAP;AACH;;AAED,YAAElN,aAAa,CAAC2D,eAAhB;AACAzD,UAAAA,MAAM,CAACF,aAAa,CAAC2D,eAAd,IAAiC,CAAlC,CAAN;AACA,gBAAMlC,EAAE,GAAG9G,MAAM,CAACiO,MAAP,CAAcrF,cAAzB;AACArD,UAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACqD,QAAd,CAAuBwJ,KAAvB,CAA6BC,QAAhC,CAAN;AACA,iBAAO,KAAKK,+BAAL,CACH/Q,GADG,EACE2D,UADF,EAEHC,aAFG,EAGHA,aAAa,CAACqD,QAHX,EAIHrD,aAAa,CAACqD,QAAd,CAAuBwJ,KAAvB,CAA6BC,QAJ1B,EAKHrL,EALG,EAMHzB,aAAa,CAACnE,KANX,EAOHmE,aAAa,CAAChE,MAPX,EAQHqN,OAAO,CAAC5I,SARL,CAAP;AASH;;AAEO0M,QAAAA,+BAA+B,CACnC/Q,GADmC,EAEnC2D,UAFmC,EAGnCC,aAHmC,EAInCqD,QAJmC,EAKnC+J,aALmC,EAMnC3L,EANmC,EAOnC5F,KAPmC,EAQnCG,MARmC,EASnCqR,YATmC,EAUH;AAChC,gBAAMlM,SAAS,GAAGD,SAAS,CAACC,SAA5B,CADgC,CAEhC;AACA;AACA;AAEA;;AACA,gBAAM8L,UAAU,GAAG5J,QAAQ,CAACwJ,KAAT,CAAeI,UAAlC;AACA,gBAAMK,SAAS,GAAGL,UAAU,GAAG,CAA/B;AACA,eAAKR,YAAL,CAAkB5K,MAAlB,GAA2ByL,SAA3B;AACA,eAAKZ,aAAL,CAAmB7K,MAAnB,GAA4ByL,SAA5B;AACA,eAAKb,YAAL,CAAkB,CAAlB,IAAuB3Q,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWJ,KAAK,GAAG,CAAnB,CAAT,EAAgC,CAAhC,CAAvB;AACA,eAAK6Q,aAAL,CAAmB,CAAnB,IAAwB5Q,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWD,MAAM,GAAG,CAApB,CAAT,EAAiC,CAAjC,CAAxB;;AACA,eAAK,IAAIwK,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAK8G,SAAtB,EAAiC,EAAE9G,CAAnC,EAAsC;AAClC,iBAAKiG,YAAL,CAAkBjG,CAAlB,IAAuB1K,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW,KAAKwQ,YAAL,CAAkBjG,CAAC,GAAG,CAAtB,IAA2B,CAAtC,CAAT,EAAmD,CAAnD,CAAvB;AACA,iBAAKkG,aAAL,CAAmBlG,CAAnB,IAAwB1K,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW,KAAKyQ,aAAL,CAAmBlG,CAAC,GAAG,CAAvB,IAA4B,CAAvC,CAAT,EAAoD,CAApD,CAAxB;AACH,WAhB+B,CAkBhC;;;AACA,eAAKmG,cAAL,CAAoB9K,MAApB,GAA6ByL,SAA7B;;AACA,eAAK,IAAI9G,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAK8G,SAAtB,EAAiC,EAAE9G,CAAnC,EAAsC;AAClC,iBAAKmG,cAAL,CAAoBnG,CAApB,IAA0B,WAAU/E,EAAG,IAAG+E,CAAE,EAA5C;AACH,WAtB+B,CAwBhC;;;AACA,eAAK+F,YAAL,CAAkBnN,CAAlB,GAAsBW,UAAU,CAACvC,cAAX,GAA4B,CAA5B,GAAgC,CAAtD;AACA,eAAK+O,YAAL,CAAkBnN,CAAlB,GAAsB,CAAtB,CA1BgC,CA0BP;;AACzB,eAAKmN,YAAL,CAAkBzH,CAAlB,GAAsBzB,QAAQ,CAACwJ,KAAT,CAAeU,SAArC;AACA,eAAKhB,YAAL,CAAkBrR,CAAlB,GAAsBmI,QAAQ,CAACwJ,KAAT,CAAeW,eAAf,GAAiC,CAAjC,GAAqC,CAA3D,CA5BgC,CA8BhC;;AACA,gBAAMC,aAAa,GAAGrR,GAAG,CAACiE,aAAJ,CAAkB,KAAKoM,YAAL,CAAkB,CAAlB,CAAlB,EAAwC,KAAKC,aAAL,CAAmB,CAAnB,CAAxC,EAA+D,oBAA/D,CAAtB;AACAe,UAAAA,aAAa,CAACjN,eAAd,CACI,KAAKmM,cAAL,CAAoB,CAApB,CADJ,EAEIjM,MAAM,CAACC,KAFX,EAGIC,OAAO,CAACC,KAHZ,EAII,KAAKyL,2BAJT;AAMAmB,UAAAA,aAAa,CAAC1M,UAAd,CAAyBsM,YAAzB,EAAuC,cAAvC;AACAI,UAAAA,aAAa,CAACzM,OAAd,CAAsB,YAAtB,EAAoCjB,UAAU,CAACZ,QAA/C;AACAsO,UAAAA,aAAa,CAACzM,OAAd,CAAsB,aAAtB,EAAqC,KAAKuL,YAA1C;AACAkB,UAAAA,aAAa,CACRxM,QADL,CACcE,SAAS,CAACC,MADxB,EAEKC,iBAFL,CAEuB+L,aAFvB,EAEsC,CAFtC,EAzCgC,CA6ChC;;AACA,eAAK,IAAI5G,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAK8G,SAAtB,EAAiC,EAAE9G,CAAnC,EAAsC;AAClC,kBAAMkH,QAAQ,GAAGtR,GAAG,CAACiE,aAAJ,CAAkB,KAAKoM,YAAL,CAAkBjG,CAAlB,CAAlB,EAAwC,KAAKkG,aAAL,CAAmBlG,CAAnB,CAAxC,EAA+D,qBAA/D,CAAjB;AACAkH,YAAAA,QAAQ,CAAClN,eAAT,CAAyB,KAAKmM,cAAL,CAAoBnG,CAApB,CAAzB,EAAiD9F,MAAM,CAACC,KAAxD,EAA+DC,OAAO,CAACC,KAAvE,EAA8E,KAAKyL,2BAAnF;AACAoB,YAAAA,QAAQ,CAAC3M,UAAT,CAAoB,KAAK4L,cAAL,CAAoBnG,CAAC,GAAG,CAAxB,CAApB,EAAgD,cAAhD;AACA,iBAAKgG,aAAL,CAAmBpN,CAAnB,GAAuB,KAAKqN,YAAL,CAAkBjG,CAAC,GAAG,CAAtB,CAAvB;AACA,iBAAKgG,aAAL,CAAmB3H,CAAnB,GAAuB,KAAK6H,aAAL,CAAmBlG,CAAC,GAAG,CAAvB,CAAvB;AACAkH,YAAAA,QAAQ,CAAC1M,OAAT,CAAiB,YAAjB,EAA+BjB,UAAU,CAACZ,QAA1C;AACAuO,YAAAA,QAAQ,CAAC1M,OAAT,CAAiB,cAAjB,EAAiC,KAAKwL,aAAtC;AACAkB,YAAAA,QAAQ,CACHzM,QADL,CACcE,SAAS,CAACC,MADxB,EAEKC,iBAFL,CAEuB+L,aAFvB,EAEsC,CAFtC;AAGH,WAzD+B,CA2DhC;;;AACA,eAAK,IAAI5G,CAAC,GAAGyG,UAAb,EAAyBzG,CAAC,KAAK,CAA/B,GAAmC;AAC/B,kBAAMmH,MAAM,GAAGvR,GAAG,CAACiE,aAAJ,CAAkB,KAAKoM,YAAL,CAAkBjG,CAAlB,CAAlB,EAAwC,KAAKkG,aAAL,CAAmBlG,CAAnB,CAAxC,EAA+D,mBAA/D,CAAf;AACAmH,YAAAA,MAAM,CAACnN,eAAP,CAAuB,KAAKmM,cAAL,CAAoBnG,CAApB,CAAvB,EAA+C9F,MAAM,CAACC,KAAtD,EAA6DC,OAAO,CAACC,KAArE,EAA4E,KAAKyL,2BAAjF;AACAqB,YAAAA,MAAM,CAAC5M,UAAP,CAAkB,KAAK4L,cAAL,CAAoBnG,CAAC,GAAG,CAAxB,CAAlB,EAA8C,cAA9C;AACA,iBAAKgG,aAAL,CAAmBpN,CAAnB,GAAuB,KAAKqN,YAAL,CAAkBjG,CAAC,GAAG,CAAtB,CAAvB;AACA,iBAAKgG,aAAL,CAAmB3H,CAAnB,GAAuB,KAAK6H,aAAL,CAAmBlG,CAAC,GAAG,CAAvB,CAAvB;AACAmH,YAAAA,MAAM,CAAC3M,OAAP,CAAe,YAAf,EAA6BjB,UAAU,CAACZ,QAAxC;AACAwO,YAAAA,MAAM,CAAC3M,OAAP,CAAe,cAAf,EAA+B,KAAKwL,aAApC;AACAmB,YAAAA,MAAM,CACD1M,QADL,CACcE,SAAS,CAACC,MADxB,EAEKC,iBAFL,CAEuB+L,aAFvB,EAEsC,CAFtC;AAGH,WAvE+B,CAyEhC;;;AACA,gBAAMQ,WAAW,GAAGxR,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,kBAAjC,CAApB;AACA4R,UAAAA,WAAW,CAACpN,eAAZ,CAA4B6M,YAA5B,EAA0C3M,MAAM,CAAC6G,IAAjD,EAAuD3G,OAAO,CAACC,KAA/D;AACA+M,UAAAA,WAAW,CAAC7M,UAAZ,CAAuB,KAAK4L,cAAL,CAAoB,CAApB,CAAvB,EAA+C,cAA/C;AACAiB,UAAAA,WAAW,CAAC5M,OAAZ,CAAoB,YAApB,EAAkCjB,UAAU,CAACZ,QAA7C;AACAyO,UAAAA,WAAW,CAAC5M,OAAZ,CAAoB,aAApB,EAAmC,KAAKuL,YAAxC;AACAqB,UAAAA,WAAW,CACN3M,QADL,CACcE,SAAS,CAAC2E,KADxB,EAEKzE,iBAFL,CAEuB+L,aAFvB,EAEsC,CAFtC;;AAIA,cAAIpN,aAAa,CAAC2D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO7D,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCqN,YAAjC,CAA1B;AACH,WAFD,MAEO;AACH,mBAAOO,WAAP;AACH;AACJ;;AAjKyE,O;;+CAgLjEtT,6B,GAAN,MAAMA,6BAAN,CAA6E;AAAA;AAAA,eAsH/DuT,oBAtH+D,GAsHxC,IAAIzL,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAtHwC;AAAA;;AAChFzC,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDkI,QAAAA,YAAY,CACRpN,MADQ,EAERoF,UAFQ,EAGRC,aAHQ,EAGqD;AAC7D,gBAAMqD,QAAQ,GAAGrD,aAAa,CAACqD,QAA/B;AAEArD,UAAAA,aAAa,CAAC8N,kBAAd,GACMzK,QAAQ,CAAC0K,YAAT,CAAsBhQ,OAAtB,IACC,CAAC,CAACsF,QAAQ,CAAC0K,YAAT,CAAsBjB,QADzB,IAEC,CAAC,CAACzJ,QAAQ,CAAC0K,YAAT,CAAsBC,eAH/B;AAKAhO,UAAAA,aAAa,CAACiO,iBAAd,GACMjO,aAAa,CAAC8D,SAAd,CAAwB;AAAxB,aACC9D,aAAa,CAAC8N,kBAFrB,CAR6D,CAUpB;;AAEzC,cAAI9N,aAAa,CAACiO,iBAAlB,EAAqC;AACjC,cAAEjO,aAAa,CAAC2D,eAAhB;AACH;AACJ;;AACDgF,QAAAA,YAAY,CACRvM,GADQ,EAER2D,UAFQ,EAGRC,aAHQ,EAGqD;AAC7D,cAAIA,aAAa,CAAC8N,kBAAlB,EAAsC;AAClC5N,YAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACqD,QAAd,CAAuB0K,YAAvB,CAAoCjB,QAAvC,CAAN;AACA9M,YAAAA,aAAa,CAACqD,QAAd,CAAuB0K,YAAvB,CAAoCjB,QAApC,CAA6CoB,WAA7C,CACI,iBADJ,EAEIlO,aAAa,CAACqD,QAAd,CAAuB0K,YAAvB,CAAoCC,eAFxC;AAGH;AACJ;;AACD5E,QAAAA,KAAK,CACDhN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD0O,OALC,EAMD6D,cANC,EAO8C;AAC/C,cAAI,CAAClN,aAAa,CAACiO,iBAAnB,EAAsC;AAClC,mBAAOf,cAAP;AACH;;AAED,YAAElN,aAAa,CAAC2D,eAAhB;AACAzD,UAAAA,MAAM,CAACF,aAAa,CAAC2D,eAAd,IAAiC,CAAlC,CAAN;;AACA,cAAI3D,aAAa,CAAC2D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO,KAAKwK,sBAAL,CAA4B/R,GAA5B,EAAiC2D,UAAjC,EAA6CC,aAA7C,EACHA,aAAa,CAACnE,KADX,EACkBmE,aAAa,CAAChE,MADhC,EAEHqN,OAAO,CAAC5I,SAFL,EAEgBT,aAAa,CAACS,SAF9B,CAAP;AAGH,WAJD,MAIO;AACH,kBAAMgB,EAAE,GAAGzB,aAAa,CAACuD,cAAzB;AACA,kBAAM6K,cAAc,GAAGpO,aAAa,CAAC4D,kBAAd,GAChB,gBADgB,GAEhB,UAFP;AAIA,kBAAMyK,YAAY,GAAG/M,uBAAuB,CAAC+H,OAAO,CAAC5I,SAAT,EAAoB2N,cAApB,EAAoC3M,EAApC,CAA5C;AACA,kBAAM4L,YAAY,GAAGhE,OAAO,CAAC5I,SAA7B;AACA4I,YAAAA,OAAO,CAAC5I,SAAR,GAAoB4N,YAApB;AAEA,mBAAO,KAAKF,sBAAL,CAA4B/R,GAA5B,EAAiC2D,UAAjC,EAA6CC,aAA7C,EACHA,aAAa,CAACnE,KADX,EACkBmE,aAAa,CAAChE,MADhC,EAEHqR,YAFG,EAEWgB,YAFX,CAAP;AAGH;AACJ;;AACOF,QAAAA,sBAAsB,CAC1B/R,GAD0B,EAE1B2D,UAF0B,EAG1BC,aAH0B,EAI1BnE,KAJ0B,EAK1BG,MAL0B,EAM1BqR,YAN0B,EAO1B5M,SAP0B,EAQM;AAChC,cAAIL,IAAJ;AACA,gBAAMiD,QAAQ,GAAGrD,aAAa,CAACqD,QAA/B;;AACA,cAAIrD,aAAa,CAAC8N,kBAAlB,EAAsC;AAClC5N,YAAAA,MAAM,CAAC,CAAC,CAACmD,QAAQ,CAAC0K,YAAT,CAAsBjB,QAAzB,CAAN;AACA5M,YAAAA,MAAM,CAAC,CAAC,CAACmD,QAAQ,CAAC0K,YAAT,CAAsBC,eAAzB,CAAN;AAEA,kBAAMM,MAAM,GAAGjL,QAAQ,CAAC0K,YAAT,CAAsBC,eAArC;AACA,iBAAKH,oBAAL,CAA0BzO,CAA1B,GAA8BkP,MAAM,CAACzS,KAArC;AACA,iBAAKgS,oBAAL,CAA0BhJ,CAA1B,GAA8ByJ,MAAM,CAACtS,MAArC;AAEA,kBAAMuS,WAAW,GAAGD,MAAM,CAACzS,KAAP,KAAiByS,MAAM,CAACtS,MAA5C;;AACA,gBAAIuS,WAAJ,EAAiB;AACbnO,cAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,sBAAjC,CAAP;AACH,aAFD,MAEO;AACHoE,cAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,sBAAjC,CAAP;AACH;;AACDoE,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6DC,2BAA7D;AACAV,YAAAA,IAAI,CAACW,UAAL,CAAgBsM,YAAhB,EAA8B,eAA9B;AACAjN,YAAAA,IAAI,CAACY,OAAL,CAAa,YAAb,EAA2BjB,UAAU,CAACZ,QAAtC;AACAiB,YAAAA,IAAI,CAACoO,OAAL,CAAa,gBAAb,EAA+B,KAAKX,oBAApC;AACAzN,YAAAA,IAAI,CAACqO,QAAL,CAAc,YAAd,EAA4BpL,QAAQ,CAAC0K,YAAT,CAAsBW,UAAlD;AACAtO,YAAAA,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBgC,QAAQ,CAAC0K,YAAT,CAAsBjB,QAD7C,EACuDyB,WAAW,GAAG,CAAH,GAAO,CADzE;AAEH,WArBD,MAqBO;AACHnO,YAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,iBAAjC,CAAP;AACAoE,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6DC,2BAA7D;AACAV,YAAAA,IAAI,CAACW,UAAL,CAAgBsM,YAAhB,EAA8B,cAA9B;AACAjN,YAAAA,IAAI,CAACY,OAAL,CAAa,YAAb,EAA2BjB,UAAU,CAACZ,QAAtC;;AACA,gBAAIkE,QAAQ,CAACsL,WAAT,CAAqB7B,QAAzB,EAAmC;AAC/B1M,cAAAA,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBgC,QAAQ,CAACsL,WAAT,CAAqB7B,QAD5C,EACsD,CADtD;AAEH,aAHD,MAGO;AACH5M,cAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACG,sBAAjB,CAAN;AACAC,cAAAA,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBrB,aAAa,CAACG,sBADrC,EAC6D,CAD7D;AAEH;AACJ;;AACD,iBAAOC,IAAP;AACH;;AArH+E,O;;wCA6HvE7F,sB,GAAN,MAAMA,sBAAN,CAAsE;AAAA;AA6FzE;AA7FyE,eA8FxDqU,WA9FwD,GA8F1C,IAAItM,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CA9F0C;AAAA;;AACzE3C,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDkI,QAAAA,YAAY,CACRpN,MADQ,EAERoF,UAFQ,EAGRC,aAHQ,EAG8C;AACtDA,UAAAA,aAAa,CAAC6O,UAAd,GACM7O,aAAa,CAACqD,QAAd,CAAuByL,IAAvB,CAA4B/Q,OAA5B,IACC,CAAC,CAACiC,aAAa,CAACqD,QAAd,CAAuByL,IAAvB,CAA4BhC,QAFrC;;AAGA,cAAI9M,aAAa,CAAC6O,UAAlB,EAA8B;AAC1B,cAAE7O,aAAa,CAAC2D,eAAhB;AACH;AACJ;;AACDyF,QAAAA,KAAK,CACDhN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD0O,OALC,EAMD6D,cANC,EAO8C;AAC/C,cAAI,CAAClN,aAAa,CAAC6O,UAAnB,EAA+B;AAC3B,mBAAO3B,cAAP;AACH;;AACD,YAAElN,aAAa,CAAC2D,eAAhB;AACAzD,UAAAA,MAAM,CAACF,aAAa,CAAC2D,eAAd,IAAiC,CAAlC,CAAN;AAEA,gBAAMlC,EAAE,GAAGzB,aAAa,CAACuD,cAAzB;AACA,gBAAM6K,cAAc,GAAGpO,aAAa,CAAC4D,kBAAd,GAChB,gBADgB,GAEhB,UAFP;AAGA,gBAAMyK,YAAY,GAAG/M,uBAAuB,CAAC+H,OAAO,CAAC5I,SAAT,EAAoB2N,cAApB,EAAoC3M,EAApC,CAA5C;AAEAvB,UAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACqD,QAAd,CAAuByL,IAAvB,CAA4BhC,QAA/B,CAAN;;AACA,cAAI9M,aAAa,CAAC2D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,gBAAI3D,aAAa,CAAC4D,kBAAlB,EAAsC;AAClC,mBAAKmL,YAAL,CAAkB3S,GAAlB,EAAuB2D,UAAvB,EACIC,aAAa,CAACqD,QAAd,CAAuByL,IAAvB,CAA4BhC,QADhC,EAEI9M,aAAa,CAACnE,KAFlB,EAGImE,aAAa,CAAChE,MAHlB,EAIIqN,OAAO,CAAC5I,SAJZ,EAKI4N,YALJ;;AAMA,qBAAOvO,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCqO,YAAjC,CAA1B;AACH,aARD,MAQO;AACHnO,cAAAA,MAAM,CAACF,aAAa,CAACnE,KAAd,KAAwBmE,aAAa,CAACM,WAAvC,CAAN;AACAJ,cAAAA,MAAM,CAACF,aAAa,CAAChE,MAAd,KAAyBgE,aAAa,CAACO,YAAxC,CAAN;AACA,qBAAO,KAAKwO,YAAL,CAAkB3S,GAAlB,EAAuB2D,UAAvB,EACHC,aAAa,CAACqD,QAAd,CAAuByL,IAAvB,CAA4BhC,QADzB,EAEH9M,aAAa,CAACnE,KAFX,EAGHmE,aAAa,CAAChE,MAHX,EAIHqN,OAAO,CAAC5I,SAJL,EAKHT,aAAa,CAACS,SALX,CAAP;AAMH;AACJ,WAnBD,MAmBO;AACH,kBAAMuO,cAAc,GAAG3F,OAAO,CAAC5I,SAA/B;AACA4I,YAAAA,OAAO,CAAC5I,SAAR,GAAoB4N,YAApB;;AACA,kBAAMY,QAAQ,GAAG,KAAKF,YAAL,CAAkB3S,GAAlB,EAAuB2D,UAAvB,EACbC,aAAa,CAACqD,QAAd,CAAuByL,IAAvB,CAA4BhC,QADf,EAEb9M,aAAa,CAACnE,KAFD,EAGbmE,aAAa,CAAChE,MAHD,EAIbgT,cAJa,EAKbX,YALa,CAAjB;;AAMA,mBAAOY,QAAP;AACH;AACJ;;AACOF,QAAAA,YAAY,CAChB3S,GADgB,EAEhB2D,UAFgB,EAGhBmP,YAHgB,EAIhBrT,KAJgB,EAKhBG,MALgB,EAMhBqS,YANgB,EAOhB5N,SAPgB,EAQgB;AAChC,eAAKmO,WAAL,CAAiBxP,CAAjB,GAAqBvD,KAArB;AACA,eAAK+S,WAAL,CAAiB/J,CAAjB,GAAqB7I,MAArB;AACA,eAAK4S,WAAL,CAAiB9J,CAAjB,GAAqB,IAAIjJ,KAAzB;AACA,eAAK+S,WAAL,CAAiB1T,CAAjB,GAAqB,IAAIc,MAAzB;AAEA,gBAAMoE,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAb;AACAoE,UAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6DC,2BAA7D;AACAV,UAAAA,IAAI,CAACW,UAAL,CAAgBsN,YAAhB,EAA8B,eAA9B;AACAjO,UAAAA,IAAI,CAACY,OAAL,CAAa,YAAb,EAA2BjB,UAAU,CAACZ,QAAtC;AACAiB,UAAAA,IAAI,CAACY,OAAL,CAAa,SAAb,EAAwB,KAAK4N,WAA7B;AACAxO,UAAAA,IAAI,CAACa,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuB6N,YADvB,EACqC,CADrC;AAEA,iBAAO9O,IAAP;AACH;;AA5FwE,O;;uCAqGhE5F,qB,GAAN,MAAMA,qBAAN,CAAqE;AAAA;AAkGxE;AAlGwE,eAmGvD2U,UAnGuD,GAmG1C,IAAI7M,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAnG0C;AAAA,eAoGvD8M,WApGuD,GAoGzC,IAAI9M,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CApGyC;AAAA;;AACxE3C,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDkI,QAAAA,YAAY,CACRpN,MADQ,EAERoF,UAFQ,EAGRC,aAHQ,EAG6C;AACrD;AACAA,UAAAA,aAAa,CAACqP,SAAd,GAA0BrP,aAAa,CAACqD,QAAd,CAAuBiM,GAAvB,CAA2BvR,OAA3B,IACnB,CAAC,CAACiC,aAAa,CAACqD,QAAd,CAAuBiM,GAAvB,CAA2BxC,QADV,IAEnB9M,aAAa,CAAC4D,kBAFK,IAGnB5D,aAAa,CAAC6D,YAAd,GAA6B,GAHpC;;AAKA,cAAI7D,aAAa,CAACqP,SAAlB,EAA6B;AACzB,cAAErP,aAAa,CAAC2D,eAAhB;AACH;AACJ;;AACDyF,QAAAA,KAAK,CACDhN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD0O,OALC,EAMD6D,cANC,EAO8C;AAC/C,cAAI,CAAClN,aAAa,CAACqP,SAAnB,EAA8B;AAC1B,mBAAOnC,cAAP;AACH;;AACD,YAAElN,aAAa,CAAC2D,eAAhB;AAEA,gBAAMqL,cAAc,GAAG3F,OAAO,CAAC5I,SAA/B;AACA,gBAAM8O,eAAe,GACfvP,aAAa,CAAC2D,eAAd,KAAkC,CAAlC,GACI3D,aAAa,CAACS,SADlB,GAEIa,uBAAuB,CAAC+H,OAAO,CAAC5I,SAAT,EAAoB,SAApB,EAA+BT,aAAa,CAACuD,cAA7C,CAHjC;AAIA8F,UAAAA,OAAO,CAAC5I,SAAR,GAAoB8O,eAApB;AAEArP,UAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACqD,QAAd,CAAuBiM,GAAvB,CAA2BxC,QAA9B,CAAN;AACA,iBAAO,KAAK0C,WAAL,CAAiBpT,GAAjB,EAAsB2D,UAAtB,EAAkCC,aAAlC,EACHA,aAAa,CAACqD,QADX,EAEHrD,aAAa,CAACqD,QAAd,CAAuBiM,GAAvB,CAA2BxC,QAFxB,EAGH9M,aAAa,CAACuD,cAHX,EAIHvD,aAAa,CAACnE,KAJX,EAKHmE,aAAa,CAAChE,MALX,EAMHgT,cANG,EAOHhP,aAAa,CAACM,WAPX,EAQHN,aAAa,CAACO,YARX,EASHgP,eATG,CAAP;AAUH;;AACOC,QAAAA,WAAW,CACfpT,GADe,EAEf2D,UAFe,EAGfC,aAHe,EAIfqD,QAJe,EAKfoM,WALe,EAMfhO,EANe,EAOf5F,KAPe,EAQfG,MARe,EASfgT,cATe,EAUf1O,WAVe,EAWfC,YAXe,EAYfgP,eAZe,EAaiB;AAChC,eAAKH,WAAL,CAAiBhQ,CAAjB,GAAqBvD,KAArB;AACA,eAAKuT,WAAL,CAAiBvK,CAAjB,GAAqB7I,MAArB;AACA,eAAKoT,WAAL,CAAiBtK,CAAjB,GAAqBxE,WAArB;AACA,eAAK8O,WAAL,CAAiBlU,CAAjB,GAAqBqF,YAArB;AACA,eAAK4O,UAAL,CAAgB/P,CAAhB,GAAoB2C,KAAK,CAAC,MAAMsB,QAAQ,CAACiM,GAAT,CAAaI,SAApB,EAA+B,IAA/B,EAAqC,IAArC,CAAzB;AAEA,gBAAMC,aAAa,GAAG,SAAtB;AAEA,gBAAMC,YAAY,GAAGtO,uBAAuB,CAACiO,eAAD,EAAkBI,aAAlB,EAAiClO,EAAjC,CAA5C;AAEA,gBAAMoO,QAAQ,GAAGzT,GAAG,CAACiE,aAAJ,CAAkBC,WAAlB,EAA+BC,YAA/B,EAA6C,aAA7C,CAAjB;AACAsP,UAAAA,QAAQ,CAACrP,eAAT,CAAyBoP,YAAzB,EAAuClP,MAAM,CAACC,KAA9C,EAAqDC,OAAO,CAACC,KAA7D,EAAoEC,2BAApE;AACA+O,UAAAA,QAAQ,CAAC9O,UAAT,CAAoBiO,cAApB,EAAoC,iBAApC;AACAa,UAAAA,QAAQ,CAAC7O,OAAT,CAAiB,YAAjB,EAA+BjB,UAAU,CAACZ,QAA1C;AACA0Q,UAAAA,QAAQ,CAAC7O,OAAT,CAAiB,YAAjB,EAA+B,KAAKoO,WAApC;AACAS,UAAAA,QAAQ,CACH5O,QADL,CACcC,SAAS,CAACC,SAAV,CAAoBC,MADlC,EAEKC,iBAFL,CAEuBoO,WAFvB,EAEoC,CAFpC;AAIA,gBAAMK,QAAQ,GAAG1T,GAAG,CAACiE,aAAJ,CAAkBC,WAAlB,EAA+BC,YAA/B,EAA6C,aAA7C,CAAjB;AACAuP,UAAAA,QAAQ,CAACtP,eAAT,CAAyB+O,eAAzB,EAA0C7O,MAAM,CAACC,KAAjD,EAAwDC,OAAO,CAACC,KAAhE,EAAuEC,2BAAvE;AACAgP,UAAAA,QAAQ,CAAC/O,UAAT,CAAoB6O,YAApB,EAAkC,iBAAlC;AACAE,UAAAA,QAAQ,CAAC9O,OAAT,CAAiB,YAAjB,EAA+BjB,UAAU,CAACZ,QAA1C;AACA2Q,UAAAA,QAAQ,CAAC9O,OAAT,CAAiB,YAAjB,EAA+B,KAAKoO,WAApC;AACAU,UAAAA,QAAQ,CAAC9O,OAAT,CAAiB,WAAjB,EAA8B,KAAKmO,UAAnC;AACAW,UAAAA,QAAQ,CACH7O,QADL,CACcC,SAAS,CAACC,SAAV,CAAoBC,MADlC,EAEKC,iBAFL,CAEuBoO,WAFvB,EAEoC,CAFpC;AAIA,iBAAOK,QAAP;AACH;;AAjGuE,O;;sCAuG/DrV,oB,GAAN,MAAMA,oBAAN,CAAoE;AACvEkF,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,IAAP;AACH;;AACDuJ,QAAAA,KAAK,CACDhN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD0O,OALC,EAMD6D,cANC,EAO8C;AAC/ChN,UAAAA,MAAM,CAAC,CAAC,CAACgN,cAAH,CAAN;AAEA,cAAI6C,KAAK,GAAG7O,SAAS,CAACmF,UAAV,CAAqB2J,EAAjC;;AACA,cAAIhQ,aAAa,CAAC0D,cAAlB,EAAkC;AAC9BqM,YAAAA,KAAK,IAAI7O,SAAS,CAACmF,UAAV,CAAqB4J,QAA9B;AACA/C,YAAAA,cAAc,CAACgD,cAAf,GAAgC,IAAhC;AACH;;AACDhD,UAAAA,cAAc,CACTjM,QADL,CACcC,SAAS,CAACC,SAAV,CAAoB2E,KADlC,EACyC,SADzC,EACoD,SADpD,EAEKM,QAFL,CAEczL,MAFd,EAEsBoV,KAFtB;AAIA,iBAAO7C,cAAP;AACH;;AA3BsE,O;;AA8B3E,UAAIhM,SAAJ,EAAe;AAAA,SAEL;AAAEC,UAAAA,SAAF;AAAakF,UAAAA;AAAb,SAFK,GAEuBnF,SAFvB;;AAIX,cAAMiP,sBAAN,CAAkE;AAAA;AAAA,iBAC7CC,cAD6C,GACJtO,QAAQ,CAACuO,QAAT,CAAkBC,IAAlB,CAAuBC,aADnB;AAAA,iBAE7CC,YAF6C,GAE9B,IAAIpW,yBAAJ,EAF8B;AAAA,iBAG7CqW,UAH6C,GAGhC,IAAIpW,uBAAJ,EAHgC;AAAA,iBAI7CqW,gBAJ6C,GAI1B,IAAIpW,6BAAJ,EAJ0B;AAAA,iBAK7CqW,SAL6C,GAKjC,IAAIpW,sBAAJ,EALiC;AAAA,iBAM7CqW,QAN6C,GAMlC,IAAIpW,qBAAJ,EANkC;AAAA,iBAO7CqW,OAP6C,GAOnC,IAAIpW,oBAAJ,EAPmC;AAQ9D;AAR8D,iBAS7CkN,WAT6C,GAS/B,IAAI7E,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAT+B;AAAA,iBAU7C4E,SAV6C,GAUjC,IAAI1E,QAAJ,EAViC;AAAA,iBAW7C8N,QAX6C,GAWlC,IAAI7W,eAAJ,EAXkC;AAAA,iBAY7C8W,cAZ6C,GAY5B,IAAI7W,aAAJ,EAZ4B;AAa9D;AAb8D,iBAc7C8W,uBAd6C,GAcnB,IAAI9O,QAAJ,EAdmB;AAgB9D;AAhB8D,iBAiBtD+O,YAjBsD,GAiBvC,KAjBuC;AAiBhC;AAjBgC,iBAkBtDC,aAlBsD,GAkBL,EAlBK;AAAA;;AAoBtDC,UAAAA,qBAAqB,CACzBxW,MADyB,EAEzBqF,aAFyB,EAEK;AAC9B,kBAAMoR,YAAqB,GACrBzW,MAAM,CAAC0N,WAAP,KAAuBpF,WAAW,CAACqF,UAAnC,IACC3N,MAAM,CAAC0N,WAAP,KAAuBpF,WAAW,CAACoO,OAF1C;;AAIA,gBAAID,YAAJ,EAAkB;AACd,oBAAME,cAAc,GAAGpQ,SAAS,CAACqQ,yBAAV,EAAvB;;AACA,kBAAID,cAAJ,EAAoB;AAChBtR,gBAAAA,aAAa,CAACqD,QAAd,GAAyBiO,cAAzB;AACH,eAFD,MAEO;AACHtR,gBAAAA,aAAa,CAACqD,QAAd,GAAyBD,eAAzB;AACH;AACJ,aAPD,MAOO;AACH,kBAAIzI,MAAM,CAAC6W,gBAAX,EAA6B;AACzBxR,gBAAAA,aAAa,CAACqD,QAAd,GAAyB1I,MAAM,CAAC6W,gBAAhC;AACH,eAFD,MAEO;AACHxR,gBAAAA,aAAa,CAACqD,QAAd,GAAyBD,eAAzB;AACH;AACJ;AACJ;;AAEOqO,UAAAA,sBAAsB,CAACzR,aAAD,EAAqC;AAC/D,kBAAMT,YAAY,GAAG,KAAK2R,aAA1B;AACA3R,YAAAA,YAAY,CAACsC,MAAb,GAAsB,CAAtB;AAEA,kBAAMwB,QAAQ,GAAGrD,aAAa,CAACqD,QAA/B;;AACA,gBAAIA,QAAQ,CAACqO,OAAb,EAAsB;AAClB,mBAAK,MAAMtR,IAAX,IAAmBiD,QAAQ,CAACqO,OAA5B,EAAqC;AACjCnS,gBAAAA,YAAY,CAAC0F,IAAb,CAAkB7E,IAAlB;AACH;;AACDF,cAAAA,MAAM,CAACX,YAAY,CAACsC,MAAb,KAAwBwB,QAAQ,CAACqO,OAAT,CAAiB7P,MAA1C,CAAN;AACH;;AAEDtC,YAAAA,YAAY,CAAC0F,IAAb,CAAkB,KAAKuL,YAAvB;;AAEA,gBAAInN,QAAQ,CAACwJ,KAAT,CAAe9O,OAAnB,EAA4B;AACxBwB,cAAAA,YAAY,CAAC0F,IAAb,CAAkB,KAAKwL,UAAvB;AACH;;AAEDlR,YAAAA,YAAY,CAAC0F,IAAb,CAAkB,KAAKyL,gBAAvB;;AAEA,gBAAIrN,QAAQ,CAACyL,IAAT,CAAc/Q,OAAlB,EAA2B;AACvBwB,cAAAA,YAAY,CAAC0F,IAAb,CAAkB,KAAK0L,SAAvB;AACH;;AAED,gBAAItN,QAAQ,CAACiM,GAAT,CAAavR,OAAjB,EAA0B;AACtBwB,cAAAA,YAAY,CAAC0F,IAAb,CAAkB,KAAK2L,QAAvB;AACH;;AACDrR,YAAAA,YAAY,CAAC0F,IAAb,CAAkB,KAAK4L,OAAvB;AACH;;AAEOc,UAAAA,0BAA0B,CAC9BhX,MAD8B,EAE9BqN,eAF8B,EAG9BhI,aAH8B,EAIhC;AACE,kBAAM4I,MAAM,GAAGjO,MAAM,CAACiO,MAAtB;AACA,kBAAMtF,gBAAyB,GAAG3I,MAAM,CAAC0N,WAAP,KAAuBpF,WAAW,CAAC2O,IAAnC,IAA2C,CAAC,CAAChJ,MAAM,CAACiJ,SAAtF,CAFF,CAIE;;AACA7R,YAAAA,aAAa,CAACsD,gBAAd,GAAiCA,gBAAjC;AACAtD,YAAAA,aAAa,CAACuD,cAAd,GAA+BqF,MAAM,CAACrF,cAAtC,CANF,CAQE;;AACAvD,YAAAA,aAAa,CAACS,SAAd,GAA0BmI,MAAM,CAACnI,SAAjC;AACAT,YAAAA,aAAa,CAACwD,gBAAd,GAAiCoF,MAAM,CAACpF,gBAAxC,CAVF,CAYE;;AACAxD,YAAAA,aAAa,CAACyD,kBAAd,GAAmC,CAAC9I,MAAM,CAACmX,UAAP,GAAqB7P,MAAM,CAAC8P,IAAP,CAAYC,OAAlC,MAAgD,CAAnF;AACAhS,YAAAA,aAAa,CAAC0D,cAAd,GAA+BlB,KAAK,IAAIc,gBAAxC;AACAtD,YAAAA,aAAa,CAAC2D,eAAd,GAAgC,CAAhC,CAfF,CAiBE;;AACA3D,YAAAA,aAAa,CAAC6D,YAAd,GAA6B7D,aAAa,CAACqD,QAAd,CAAuBQ,YAApD;AACA7D,YAAAA,aAAa,CAAC4D,kBAAd,GAAmC5D,aAAa,CAACqD,QAAd,CAAuBO,kBAAvB,IAC5B5D,aAAa,CAAC6D,YAAd,KAA+B,GADtC;AAGA7D,YAAAA,aAAa,CAACM,WAAd,GAA4BxE,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW2M,MAAM,CAAC/M,KAAlB,CAAT,EAAmC,CAAnC,CAA5B;AACAmE,YAAAA,aAAa,CAACO,YAAd,GAA6BzE,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW2M,MAAM,CAAC5M,MAAlB,CAAT,EAAoC,CAApC,CAA7B;AAEAgE,YAAAA,aAAa,CAACnE,KAAd,GAAsBmE,aAAa,CAAC4D,kBAAd,GAChB9H,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW+D,aAAa,CAACM,WAAd,GAA4BN,aAAa,CAAC6D,YAArD,CAAT,EAA6E,CAA7E,CADgB,GAEhB7D,aAAa,CAACM,WAFpB;AAGAN,YAAAA,aAAa,CAAChE,MAAd,GAAuBgE,aAAa,CAAC4D,kBAAd,GACjB9H,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW+D,aAAa,CAACO,YAAd,GAA6BP,aAAa,CAAC6D,YAAtD,CAAT,EAA8E,CAA9E,CADiB,GAEjB7D,aAAa,CAACO,YAFpB,CA5BF,CAgCE;;AACAP,YAAAA,aAAa,CAAC8D,SAAd,GAA0B9D,aAAa,CAACyD,kBAAd,IACnBuE,eAAe,CAACxK,cADvB;AAEAwC,YAAAA,aAAa,CAAC+D,cAAd,GAA+B/D,aAAa,CAAC8D,SAAd,GACzB9G,GAAG,CAACmB,MAAJ,CAAW8T,OADc,GACJjV,GAAG,CAACmB,MAAJ,CAAWE,KADtC,CAnCF,CAsCE;;AACA2B,YAAAA,aAAa,CAACG,sBAAd,GAAuC,KAAK6Q,uBAA5C,CAvCF,CAyCE;;AACAhR,YAAAA,aAAa,CAACgE,qBAAd,GAAsC,KAAtC;AACH;;AAEOkO,UAAAA,mBAAmB,CACvBvX,MADuB,EAEvBqN,eAFuB,EAGvBhI,aAHuB,EAInB;AACJ,iBAAKmR,qBAAL,CAA2BxW,MAA3B,EAAmCqF,aAAnC;;AAEA,iBAAKyR,sBAAL,CAA4BzR,aAA5B;;AAEAV,YAAAA,qCAAqC,CAAC,KAAK4R,aAAN,CAArC;;AAEA,iBAAKS,0BAAL,CAAgChX,MAAhC,EAAwCqN,eAAxC,EAAyDhI,aAAzD;;AAEA,iBAAK,MAAMmS,OAAX,IAAsB,KAAKjB,aAA3B,EAA0C;AACtC,kBAAIiB,OAAO,CAACpK,YAAZ,EAA0B;AACtBoK,gBAAAA,OAAO,CAACpK,YAAR,CAAqBpN,MAArB,EAA6BqN,eAA7B,EAA8ChI,aAA9C;AACH;AACJ;AACJ,WA5I6D,CA8I9D;AACA;AACA;;;AACA2I,UAAAA,YAAY,CACRvM,GADQ,EAERwM,MAFQ,EAGRjO,MAHQ,EAIR2F,WAJQ,EAKRC,YALQ,EAMJ;AACJpE,YAAAA,oBAAoB,CAACC,GAAD,EAAM,KAAK0U,QAAX,CAApB;;AAEA,iBAAKoB,mBAAL,CAAyBvX,MAAzB,EAAiC,KAAKmW,QAAtC,EAAgD,KAAKC,cAArD,EAHI,CAKJ;;;AACA,kBAAMtP,EAAE,GAAGmH,MAAM,CAACrF,cAAlB;AAEAnH,YAAAA,GAAG,CAACqO,eAAJ,CAAoB,KAAKsG,cAAL,CAAoBtQ,SAAxC,EACItC,MAAM,CAACE,KADX,EACkBiC,WADlB,EAC+BC,YAD/B,EAC6CqI,MAD7C,EAEI,KAAKmI,cAAL,CAAoBvN,gBAFxB;AAIA,kBAAM3H,KAAK,GAAG,KAAKkV,cAAL,CAAoBlV,KAAlC;AACA,kBAAMG,MAAM,GAAG,KAAK+U,cAAL,CAAoB/U,MAAnC;;AAEA,gBAAI,KAAK+U,cAAL,CAAoBnN,kBAAxB,EAA4C;AACxCxH,cAAAA,GAAG,CAACsK,eAAJ,CAAqB,oBAAmBjF,EAAG,EAA3C,EAA8CtD,MAAM,CAACe,aAArD,EAAoErD,KAApE,EAA2EG,MAA3E;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBiB,EAAG,EAA1C,EAA6C,KAAKsP,cAAL,CAAoBhN,cAAjE,EAAiFlI,KAAjF,EAAwFG,MAAxF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBiB,EAAG,EAA1C,EAA6C,KAAKsP,cAAL,CAAoBhN,cAAjE,EAAiFlI,KAAjF,EAAwFG,MAAxF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBiB,EAAG,EAA1C,EAA6CtD,MAAM,CAACE,KAApD,EAA2DxC,KAA3D,EAAkEG,MAAlE;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBiB,EAAG,EAA1C,EAA6CtD,MAAM,CAACE,KAApD,EAA2DxC,KAA3D,EAAkEG,MAAlE;AACH,aAND,MAMO;AACHI,cAAAA,GAAG,CAACsK,eAAJ,CAAqB,cAAajF,EAAG,EAArC,EAAwCtD,MAAM,CAACe,aAA/C,EAA8DrD,KAA9D,EAAqEG,MAArE;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYiB,EAAG,EAApC,EAAuC,KAAKsP,cAAL,CAAoBhN,cAA3D,EAA2ElI,KAA3E,EAAkFG,MAAlF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYiB,EAAG,EAApC,EAAuC,KAAKsP,cAAL,CAAoBhN,cAA3D,EAA2ElI,KAA3E,EAAkFG,MAAlF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYiB,EAAG,EAApC,EAAuCtD,MAAM,CAACE,KAA9C,EAAqDxC,KAArD,EAA4DG,MAA5D;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYiB,EAAG,EAApC,EAAuCtD,MAAM,CAACE,KAA9C,EAAqDxC,KAArD,EAA4DG,MAA5D;AACH;;AACDI,YAAAA,GAAG,CAACoE,eAAJ,CAAqB,YAAWiB,EAAG,EAAnC,EAAsCtD,MAAM,CAACE,KAA7C,EAAoDiC,WAApD,EAAiEC,YAAjE;AACAnE,YAAAA,GAAG,CAACoE,eAAJ,CAAqB,YAAWiB,EAAG,EAAnC,EAAsCtD,MAAM,CAACE,KAA7C,EAAoDiC,WAApD,EAAiEC,YAAjE;;AAEA,iBAAK,MAAM4R,OAAX,IAAsB,KAAKjB,aAA3B,EAA0C;AACtC,kBAAIiB,OAAO,CAACxJ,YAAZ,EAA0B;AACtBwJ,gBAAAA,OAAO,CAACxJ,YAAR,CAAqBvM,GAArB,EAA0B,KAAK0U,QAA/B,EAAyC,KAAKC,cAA9C,EAA8DnI,MAA9D,EAAsEjO,MAAtE,EAA8E2F,WAA9E,EAA2FC,YAA3F;AACH;AACJ;AACJ;;AACD6I,UAAAA,KAAK,CAACgJ,OAAD,EAAmChW,GAAnC,EAAuE;AACxE;AACA,gBAAI,KAAKiW,cAAL,CAAoBjW,GAApB,CAAJ,EAA8B;AAC1B;AACH,aAJuE,CAKxE;AACA;;;AACA,iBAAK,MAAMzB,MAAX,IAAqByX,OAArB,EAA8B;AAC1B;AACA,kBAAI,CAACzX,MAAM,CAACiE,KAAR,IAAiB,CAACjE,MAAM,CAACiO,MAA7B,EAAqC;AACjC;AACH,eAJyB,CAK1B;;;AACA,mBAAKsJ,mBAAL,CAAyBvX,MAAzB,EAAiC,KAAKmW,QAAtC,EAAgD,KAAKC,cAArD,EAN0B,CAO1B;AACA;;;AAEA,mBAAKX,cAAL,CAAoBkC,IAApB,CAAyBnQ,iBAAiB,CAACoQ,mBAA3C,EAAgE5X,MAAhE,EAV0B,CAY1B;;;AACA,kBAAI,KAAKoW,cAAL,CAAoBtN,kBAAxB,EAA4C;AACxC,qBAAK+O,qBAAL,CAA2BpW,GAA3B,EAAgCzB,MAAhC,EAAwCA,MAAM,CAACiE,KAA/C,EAAsD,KAAKsS,aAA3D;AACH,eAFD,MAEO;AACH,qBAAKuB,oBAAL,CAA0BrW,GAA1B,EAA+BzB,MAA/B;AACH;;AAED,mBAAKyV,cAAL,CAAoBkC,IAApB,CAAyBnQ,iBAAiB,CAACuQ,iBAA3C,EAA8D/X,MAA9D;AACH;AACJ,WAxN6D,CAyN9D;AACA;AACA;;;AACQ8X,UAAAA,oBAAoB,CACxBrW,GADwB,EAExBzB,MAFwB,EAGpB;AACJ,kBAAMkB,KAAK,GAAGC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWtB,MAAM,CAACiO,MAAP,CAAc/M,KAAzB,CAAT,EAA0C,CAA1C,CAAd;AACA,kBAAMG,MAAM,GAAGF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWtB,MAAM,CAACiO,MAAP,CAAc5M,MAAzB,CAAT,EAA2C,CAA3C,CAAf;AACA,kBAAMyE,SAAS,GAAG,KAAKsQ,cAAL,CAAoBtQ,SAAtC;AACA,kBAAM+C,gBAAgB,GAAG,KAAKuN,cAAL,CAAoBvN,gBAA7C;AAEA,kBAAM2D,QAAQ,GAAGxM,MAAM,CAACwM,QAAxB,CANI,CAM+B;;AACnC,iBAAKO,SAAL,CAAe/L,IAAf,GAAsBG,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAAC/H,CAAT,GAAavD,KAAxB,CAAtB;AACA,iBAAK6L,SAAL,CAAe9L,GAAf,GAAqBE,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAACtC,CAAT,GAAa7I,MAAxB,CAArB,CARI,CASJ;AACA;;AACA,iBAAK0L,SAAL,CAAe7L,KAAf,GAAuBC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAACtL,KAAT,GAAiBA,KAA5B,CAAT,EAA6C,CAA7C,CAAvB;AACA,iBAAK6L,SAAL,CAAe1L,MAAf,GAAwBF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACyP,KAAL,CAAWpE,QAAQ,CAACnL,MAAT,GAAkBA,MAA7B,CAAT,EAA+C,CAA/C,CAAxB;AAEA,kBAAMgP,UAAU,GAAGrQ,MAAM,CAACqQ,UAA1B,CAdI,CAcmC;;AACvC,iBAAKrD,WAAL,CAAiBvI,CAAjB,GAAqB4L,UAAU,CAAC5L,CAAhC;AACA,iBAAKuI,WAAL,CAAiB9C,CAAjB,GAAqBmG,UAAU,CAACnG,CAAhC;AACA,iBAAK8C,WAAL,CAAiB7C,CAAjB,GAAqBkG,UAAU,CAAClG,CAAhC;AACA,iBAAK6C,WAAL,CAAiBzM,CAAjB,GAAqB8P,UAAU,CAAC9P,CAAhC;AAEA,kBAAMkF,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAb,CApBI,CAsBJ;;AACA,gBAAItB,qBAAqB,CAACC,MAAD,CAAzB,EAAmC;AAC/ByF,cAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6D,KAAK8G,WAAlE;AACH,aAFD,MAEO;AACHvH,cAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC6G,IAAvC,EAA6C3G,OAAO,CAACC,KAArD;AACH,aA3BG,CA6BJ;;;AACA,gBAAIlG,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aAApC,EAAmD;AAC/CkB,cAAAA,IAAI,CAACsG,eAAL,CACIlD,gBADJ,EAEI9C,MAAM,CAACC,KAFX,EAGIC,OAAO,CAAC+F,OAHZ,EAIIhM,MAAM,CAACuQ,UAJX,EAKIvQ,MAAM,CAACwQ,YALX,EAMIxQ,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aANpC;AAQH,aATD,MASO;AACHkB,cAAAA,IAAI,CAACsG,eAAL,CAAqBlD,gBAArB,EAAuC9C,MAAM,CAAC6G,IAA9C,EAAoD3G,OAAO,CAAC+F,OAA5D;AACH;;AAEDvG,YAAAA,IAAI,CAACkH,WAAL,CAAiB,KAAKI,SAAtB,EA3CI,CA6CJ;;AACAtH,YAAAA,IAAI,CAACa,QAAL,CAAcE,SAAS,CAACC,MAAxB,EACKgF,QADL,CACczL,MADd,EACsB0L,UAAU,CAACjF,MADjC,EA9CI,CAiDJ;;AACA,gBAAI2O,KAAK,GAAG1J,UAAU,CAACP,KAAX,GAAmBO,UAAU,CAAC2J,EAA1C;;AACA,gBAAI,KAAKe,cAAL,CAAoBrN,cAAxB,EAAwC;AACpCqM,cAAAA,KAAK,IAAI1J,UAAU,CAAC4J,QAApB;AACA7P,cAAAA,IAAI,CAAC8P,cAAL,GAAsB,IAAtB;AACH;;AACD9P,YAAAA,IAAI,CAACa,QAAL,CAAcE,SAAS,CAAC2E,KAAxB,EACKM,QADL,CACczL,MADd,EACsBoV,KADtB;AAEH;;AAEOyC,UAAAA,qBAAqB,CACzBpW,GADyB,EAEzBzB,MAFyB,EAGzBiE,KAHyB,EAIzBW,YAJyB,EAKrB;AACJK,YAAAA,qCAAqC,CAACL,YAAD,CAArC;AAEA,kBAAM8J,OAAwB,GAAG;AAC7B5I,cAAAA,SAAS,EAAE,EADkB;AAE7B+C,cAAAA,gBAAgB,EAAE;AAFW,aAAjC;AAKA,gBAAIyL,QAAsD,GAAG5D,SAA7D;;AAEA,iBAAK,MAAM8G,OAAX,IAAsB5S,YAAtB,EAAoC;AAChC,kBAAI4S,OAAO,CAAC/I,KAAZ,EAAmB;AACf6F,gBAAAA,QAAQ,GAAGkD,OAAO,CAAC/I,KAAR,CAAchN,GAAd,EAAmB,KAAK0U,QAAxB,EAAkC,KAAKC,cAAvC,EACPpW,MADO,EACC0O,OADD,EACU4F,QADV,CAAX;AAEH;AACJ;;AAED/O,YAAAA,MAAM,CAAC,KAAK6Q,cAAL,CAAoBpN,eAApB,KAAwC,CAAzC,CAAN;AACH;;AAEO0O,UAAAA,cAAc,CAACjW,GAAD,EAAuC;AACzD,gBAAI,KAAK6U,YAAT,EAAuB;AACnB,qBAAO,CAAP;AACH;;AAED9U,YAAAA,oBAAoB,CAACC,GAAD,EAAM,KAAK0U,QAAX,CAApB,CALyD,CAOzD;;AACA,iBAAKE,uBAAL,CAA6B2B,KAA7B,GAAsC,wCAAtC;;AACA,iBAAK3B,uBAAL,CAA6B4B,UAA7B,CAAwC;AAAEC,cAAAA,UAAU,EAAE;AAAd,aAAxC;;AAEA,gBAAI,KAAK7B,uBAAL,CAA6B8B,WAAjC,EAA8C;AAC1C,mBAAK7B,YAAL,GAAoB,IAApB;AACH;;AAED,mBAAO,KAAKA,YAAL,GAAoB,CAApB,GAAwB,CAA/B;AACH;;AAnU6D;;AAsUlE/P,QAAAA,SAAS,CAAC6R,iBAAV,CAA4B,SAA5B,EAAuC,IAAI5C,sBAAJ,EAAvC;AAEH,O,CAAC", "sourcesContent": ["/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com/\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\nimport {\n    assert, cclegacy, clamp, geometry, gfx, Layers, Material, pipeline,\n    PipelineEventProcessor, PipelineEventType, ReflectionProbeManager, renderer,\n    rendering, sys, Vec2, Vec3, Vec4, warn,\n} from 'cc';\n\nimport { DEBUG, EDITOR } from 'cc/env';\n\nimport {\n    makePipelineSettings,\n    PipelineSettings,\n} from './builtin-pipeline-types';\n\nconst { AABB, Sphere, intersect } = geometry;\nconst { ClearFlagBit, Color, Format, FormatFeatureBit, LoadOp, StoreOp, TextureType, Viewport } = gfx;\nconst { scene } = renderer;\nconst { CameraUsage, CSMLevel, LightType } = scene;\n\nfunction forwardNeedClearColor(camera: renderer.scene.Camera): boolean {\n    return !!(camera.clearFlag & (ClearFlagBit.COLOR | (ClearFlagBit.STENCIL << 1)));\n}\n\nfunction getCsmMainLightViewport(\n    light: renderer.scene.DirectionalLight,\n    w: number,\n    h: number,\n    level: number,\n    vp: gfx.Viewport,\n    screenSpaceSignY: number,\n): void {\n    if (light.shadowFixedArea || light.csmLevel === CSMLevel.LEVEL_1) {\n        vp.left = 0;\n        vp.top = 0;\n        vp.width = Math.trunc(w);\n        vp.height = Math.trunc(h);\n    } else {\n        vp.left = Math.trunc(level % 2 * 0.5 * w);\n        if (screenSpaceSignY > 0) {\n            vp.top = Math.trunc((1 - Math.floor(level / 2)) * 0.5 * h);\n        } else {\n            vp.top = Math.trunc(Math.floor(level / 2) * 0.5 * h);\n        }\n        vp.width = Math.trunc(0.5 * w);\n        vp.height = Math.trunc(0.5 * h);\n    }\n    vp.left = Math.max(0, vp.left);\n    vp.top = Math.max(0, vp.top);\n    vp.width = Math.max(1, vp.width);\n    vp.height = Math.max(1, vp.height);\n}\n\nexport class PipelineConfigs {\n    isWeb = false;\n    isWebGL1 = false;\n    isWebGPU = false;\n    isMobile = false;\n    isHDR = false;\n    useFloatOutput = false;\n    toneMappingType = 0; // 0: ACES, 1: None\n    shadowEnabled = false;\n    shadowMapFormat = Format.R32F;\n    shadowMapSize = new Vec2(1, 1);\n    usePlanarShadow = false;\n    screenSpaceSignY = 1;\n    supportDepthSample = false;\n    mobileMaxSpotLightShadowMaps = 1;\n\n    platform = new Vec4(0, 0, 0, 0);\n}\n\nfunction setupPipelineConfigs(\n    ppl: rendering.BasicPipeline,\n    configs: PipelineConfigs,\n): void {\n    const sampleFeature = FormatFeatureBit.SAMPLED_TEXTURE | FormatFeatureBit.LINEAR_FILTER;\n    const device = ppl.device;\n    // Platform\n    configs.isWeb = !sys.isNative;\n    configs.isWebGL1 = device.gfxAPI === gfx.API.WEBGL;\n    configs.isWebGPU = device.gfxAPI === gfx.API.WEBGPU;\n    configs.isMobile = sys.isMobile;\n\n    // Rendering\n    configs.isHDR = ppl.pipelineSceneData.isHDR; // Has tone mapping\n    configs.useFloatOutput = ppl.getMacroBool('CC_USE_FLOAT_OUTPUT');\n    configs.toneMappingType = ppl.pipelineSceneData.postSettings.toneMappingType;\n    // Shadow\n    const shadowInfo = ppl.pipelineSceneData.shadows;\n    configs.shadowEnabled = shadowInfo.enabled;\n    configs.shadowMapFormat = pipeline.supportsR32FloatTexture(ppl.device) ? Format.R32F : Format.RGBA8;\n    configs.shadowMapSize.set(shadowInfo.size);\n    configs.usePlanarShadow = shadowInfo.enabled && shadowInfo.type === renderer.scene.ShadowType.Planar;\n    // Device\n    configs.screenSpaceSignY = ppl.device.capabilities.screenSpaceSignY;\n    configs.supportDepthSample = (ppl.device.getFormatFeatures(Format.DEPTH_STENCIL) & sampleFeature) === sampleFeature;\n    // Constants\n    const screenSpaceSignY = device.capabilities.screenSpaceSignY;\n    configs.platform.x = configs.isMobile ? 1.0 : 0.0;\n    configs.platform.w = (screenSpaceSignY * 0.5 + 0.5) << 1 | (device.capabilities.clipSpaceSignY * 0.5 + 0.5);\n}\n\nexport interface PipelineSettings2 extends PipelineSettings {\n    _passes?: rendering.PipelinePassBuilder[];\n}\n\nconst defaultSettings = makePipelineSettings();\n\nexport class CameraConfigs {\n    settings: PipelineSettings = defaultSettings;\n    // Window\n    isMainGameWindow = false;\n    renderWindowId = 0;\n    // Camera\n    colorName = '';\n    depthStencilName = '';\n    // Pipeline\n    enableFullPipeline = false;\n    enableProfiler = false;\n    remainingPasses = 0;\n    // Shading Scale\n    enableShadingScale = false;\n    shadingScale = 1.0;\n    nativeWidth = 1;\n    nativeHeight = 1;\n    width = 1; // Scaled width\n    height = 1; // Scaled height\n    // Radiance\n    enableHDR = false;\n    radianceFormat = gfx.Format.RGBA8;\n    // Tone Mapping\n    copyAndTonemapMaterial: Material | null = null;\n    // Depth\n    /** @en mutable */\n    enableStoreSceneDepth = false;\n}\n\nconst sClearColorTransparentBlack = new Color(0, 0, 0, 0);\n\nfunction sortPipelinePassBuildersByConfigOrder(passBuilders: rendering.PipelinePassBuilder[]): void {\n    passBuilders.sort((a, b) => {\n        return a.getConfigOrder() - b.getConfigOrder();\n    });\n}\n\nfunction sortPipelinePassBuildersByRenderOrder(passBuilders: rendering.PipelinePassBuilder[]): void {\n    passBuilders.sort((a, b) => {\n        return a.getRenderOrder() - b.getRenderOrder();\n    });\n}\n\nfunction addCopyToScreenPass(\n    ppl: rendering.BasicPipeline,\n    pplConfigs: Readonly<PipelineConfigs>,\n    cameraConfigs: CameraConfigs,\n    input: string,\n): rendering.BasicRenderPassBuilder {\n    assert(!!cameraConfigs.copyAndTonemapMaterial);\n    const pass = ppl.addRenderPass(\n        cameraConfigs.nativeWidth,\n        cameraConfigs.nativeHeight,\n        'cc-tone-mapping');\n    pass.addRenderTarget(\n        cameraConfigs.colorName,\n        LoadOp.CLEAR, StoreOp.STORE,\n        sClearColorTransparentBlack);\n    pass.addTexture(input, 'inputTexture');\n    pass.setVec4('g_platform', pplConfigs.platform);\n    pass.addQueue(rendering.QueueHint.OPAQUE)\n        .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 1);\n    return pass;\n}\n\nexport function getPingPongRenderTarget(prevName: string, prefix: string, id: number): string {\n    if (prevName.startsWith(prefix)) {\n        return `${prefix}${1 - Number(prevName.charAt(prefix.length))}_${id}`;\n    } else {\n        return `${prefix}0_${id}`;\n    }\n}\n\nexport interface PipelineContext {\n    colorName: string;\n    depthStencilName: string;\n}\n\nclass ForwardLighting {\n    // Active lights\n    private readonly lights: renderer.scene.Light[] = [];\n    // Active spot lights with shadows (Mutually exclusive with `lights`)\n    private readonly shadowEnabledSpotLights: renderer.scene.SpotLight[] = [];\n\n    // Internal cached resources\n    private readonly _sphere = Sphere.create(0, 0, 0, 1);\n    private readonly _boundingBox = new AABB();\n    private readonly _rangedDirLightBoundingBox = new AABB(0.0, 0.0, 0.0, 0.5, 0.5, 0.5);\n\n    // ----------------------------------------------------------------\n    // Interface\n    // ----------------------------------------------------------------\n    public cullLights(scene: renderer.RenderScene, frustum: geometry.Frustum, cameraPos?: Vec3): void {\n        // TODO(zhouzhenglong): Make light culling native\n        this.lights.length = 0;\n        this.shadowEnabledSpotLights.length = 0;\n        // spot lights\n        for (const light of scene.spotLights) {\n            if (light.baked) {\n                continue;\n            }\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\n                if (light.shadowEnabled) {\n                    this.shadowEnabledSpotLights.push(light);\n                } else {\n                    this.lights.push(light);\n                }\n            }\n        }\n        // sphere lights\n        for (const light of scene.sphereLights) {\n            if (light.baked) {\n                continue;\n            }\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\n                this.lights.push(light);\n            }\n        }\n        // point lights\n        for (const light of scene.pointLights) {\n            if (light.baked) {\n                continue;\n            }\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\n                this.lights.push(light);\n            }\n        }\n        // ranged dir lights\n        for (const light of scene.rangedDirLights) {\n            AABB.transform(this._boundingBox, this._rangedDirLightBoundingBox, light.node!.getWorldMatrix());\n            if (intersect.aabbFrustum(this._boundingBox, frustum)) {\n                this.lights.push(light);\n            }\n        }\n\n        if (cameraPos) {\n            this.shadowEnabledSpotLights.sort(\n                (lhs, rhs) => Vec3.squaredDistance(cameraPos, lhs.position) - Vec3.squaredDistance(cameraPos, rhs.position),\n            );\n        }\n    }\n    private _addLightQueues(camera: renderer.scene.Camera, pass: rendering.BasicRenderPassBuilder): void {\n        for (const light of this.lights) {\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\n            switch (light.type) {\n                case LightType.SPHERE:\n                    queue.name = 'sphere-light';\n                    break;\n                case LightType.SPOT:\n                    queue.name = 'spot-light';\n                    break;\n                case LightType.POINT:\n                    queue.name = 'point-light';\n                    break;\n                case LightType.RANGED_DIRECTIONAL:\n                    queue.name = 'ranged-directional-light';\n                    break;\n                default:\n                    queue.name = 'unknown-light';\n            }\n            queue.addScene(\n                camera,\n                rendering.SceneFlags.BLEND,\n                light,\n            );\n        }\n    }\n    public addSpotlightShadowPasses(\n        ppl: rendering.BasicPipeline,\n        camera: renderer.scene.Camera,\n        maxNumShadowMaps: number,\n    ): void {\n        let i = 0;\n        for (const light of this.shadowEnabledSpotLights) {\n            const shadowMapSize = ppl.pipelineSceneData.shadows.size;\n            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');\n            shadowPass.name = `SpotLightShadowPass${i}`;\n            shadowPass.addRenderTarget(`SpotShadowMap${i}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\n            shadowPass.addDepthStencil(`SpotShadowDepth${i}`, LoadOp.CLEAR, StoreOp.DISCARD);\n            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')\n                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)\n                .useLightFrustum(light);\n            ++i;\n            if (i >= maxNumShadowMaps) {\n                break;\n            }\n        }\n    }\n    public addLightQueues(pass: rendering.BasicRenderPassBuilder,\n        camera: renderer.scene.Camera, maxNumShadowMaps: number): void {\n        this._addLightQueues(camera, pass);\n        let i = 0;\n        for (const light of this.shadowEnabledSpotLights) {\n            // Add spot-light pass\n            // Save last RenderPass to the `pass` variable\n            // TODO(zhouzhenglong): Fix per queue addTexture\n            pass.addTexture(`SpotShadowMap${i}`, 'cc_spotShadowMap');\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\n            queue.addScene(camera, rendering.SceneFlags.BLEND, light);\n            ++i;\n            if (i >= maxNumShadowMaps) {\n                break;\n            }\n        }\n    }\n\n    // Notice: ForwardLighting cannot handle a lot of lights.\n    // If there are too many lights, the performance will be very poor.\n    // If many lights are needed, please implement a forward+ or deferred rendering pipeline.\n    public addLightPasses(\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp,\n        id: number, // window id\n        width: number,\n        height: number,\n        camera: renderer.scene.Camera,\n        viewport: gfx.Viewport,\n        ppl: rendering.BasicPipeline,\n        pass: rendering.BasicRenderPassBuilder,\n    ): rendering.BasicRenderPassBuilder {\n        this._addLightQueues(camera, pass);\n\n        let count = 0;\n        const shadowMapSize = ppl.pipelineSceneData.shadows.size;\n        for (const light of this.shadowEnabledSpotLights) {\n            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');\n            shadowPass.name = 'SpotlightShadowPass';\n            // Reuse csm shadow map\n            shadowPass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\n            shadowPass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);\n            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')\n                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)\n                .useLightFrustum(light);\n\n            // Add spot-light pass\n            // Save last RenderPass to the `pass` variable\n            ++count;\n            const storeOp = count === this.shadowEnabledSpotLights.length\n                ? depthStencilStoreOp\n                : StoreOp.STORE;\n\n            pass = ppl.addRenderPass(width, height, 'default');\n            pass.name = 'SpotlightWithShadowMap';\n            pass.setViewport(viewport);\n            pass.addRenderTarget(colorName, LoadOp.LOAD);\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, storeOp);\n            pass.addTexture(`ShadowMap${id}`, 'cc_spotShadowMap');\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\n            queue.addScene(\n                camera,\n                rendering.SceneFlags.BLEND,\n                light,\n            );\n        }\n        return pass;\n    }\n\n    public isMultipleLightPassesNeeded(): boolean {\n        return this.shadowEnabledSpotLights.length > 0;\n    }\n}\n\nexport interface ForwardPassConfigs {\n    enableMainLightShadowMap: boolean;\n    enableMainLightPlanarShadowMap: boolean;\n    enablePlanarReflectionProbe: boolean;\n    enableMSAA: boolean;\n    enableSingleForwardPass: boolean;\n}\n\nexport class BuiltinForwardPassBuilder implements rendering.PipelinePassBuilder {\n    static ConfigOrder = 100;\n    static RenderOrder = 100;\n    getConfigOrder(): number {\n        return BuiltinForwardPassBuilder.ConfigOrder;\n    }\n    getRenderOrder(): number {\n        return BuiltinForwardPassBuilder.RenderOrder;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pipelineConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ForwardPassConfigs): void {\n        // Shadow\n        cameraConfigs.enableMainLightShadowMap = pipelineConfigs.shadowEnabled\n            && !pipelineConfigs.usePlanarShadow\n            && !!camera.scene\n            && !!camera.scene.mainLight\n            && camera.scene.mainLight.shadowEnabled;\n\n        cameraConfigs.enableMainLightPlanarShadowMap = pipelineConfigs.shadowEnabled\n            && pipelineConfigs.usePlanarShadow\n            && !!camera.scene\n            && !!camera.scene.mainLight\n            && camera.scene.mainLight.shadowEnabled;\n\n        // Reflection Probe\n        cameraConfigs.enablePlanarReflectionProbe = cameraConfigs.isMainGameWindow\n            || camera.cameraUsage === CameraUsage.SCENE_VIEW\n            || camera.cameraUsage === CameraUsage.GAME_VIEW;\n\n        // MSAA\n        cameraConfigs.enableMSAA = cameraConfigs.settings.msaa.enabled\n            && !cameraConfigs.enableStoreSceneDepth // Cannot store MS depth, resolve depth is also not cross-platform\n            && !pipelineConfigs.isWeb // TODO(zhouzhenglong): remove this constraint\n            && !pipelineConfigs.isWebGL1;\n\n        // Forward rendering (Depend on MSAA and TBR)\n        cameraConfigs.enableSingleForwardPass\n            = pipelineConfigs.isMobile || cameraConfigs.enableMSAA;\n\n        ++cameraConfigs.remainingPasses;\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        window: renderer.RenderWindow,\n        camera: renderer.scene.Camera,\n        nativeWidth: number,\n        nativeHeight: number): void {\n        const ResourceFlags = rendering.ResourceFlags;\n        const ResourceResidency = rendering.ResourceResidency;\n        const id = window.renderWindowId;\n        const settings = cameraConfigs.settings;\n\n        const width = cameraConfigs.enableShadingScale\n            ? Math.max(Math.floor(nativeWidth * cameraConfigs.shadingScale), 1)\n            : nativeWidth;\n        const height = cameraConfigs.enableShadingScale\n            ? Math.max(Math.floor(nativeHeight * cameraConfigs.shadingScale), 1)\n            : nativeHeight;\n\n        // MsaaRadiance\n        if (cameraConfigs.enableMSAA) {\n            // Notice: We never store multisample results.\n            // These samples are always resolved and discarded at the end of the render pass.\n            // So the ResourceResidency should be MEMORYLESS.\n            if (cameraConfigs.enableHDR) {\n                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, cameraConfigs.radianceFormat, width, height, 1, 1, 1,\n                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);\n            } else {\n                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, Format.RGBA8, width, height, 1, 1, 1,\n                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);\n            }\n            ppl.addTexture(`MsaaDepthStencil${id}`, TextureType.TEX2D, Format.DEPTH_STENCIL, width, height, 1, 1, 1,\n                settings.msaa.sampleCount, ResourceFlags.DEPTH_STENCIL_ATTACHMENT, ResourceResidency.MEMORYLESS);\n        }\n\n        // Mainlight ShadowMap\n        ppl.addRenderTarget(\n            `ShadowMap${id}`,\n            pplConfigs.shadowMapFormat,\n            pplConfigs.shadowMapSize.x,\n            pplConfigs.shadowMapSize.y,\n        );\n        ppl.addDepthStencil(\n            `ShadowDepth${id}`,\n            Format.DEPTH_STENCIL,\n            pplConfigs.shadowMapSize.x,\n            pplConfigs.shadowMapSize.y,\n        );\n\n        // Spot-light shadow maps\n        if (cameraConfigs.enableSingleForwardPass) {\n            const count = pplConfigs.mobileMaxSpotLightShadowMaps;\n            for (let i = 0; i !== count; ++i) {\n                ppl.addRenderTarget(\n                    `SpotShadowMap${i}`,\n                    pplConfigs.shadowMapFormat,\n                    pplConfigs.shadowMapSize.x,\n                    pplConfigs.shadowMapSize.y,\n                );\n                ppl.addDepthStencil(\n                    `SpotShadowDepth${i}`,\n                    Format.DEPTH_STENCIL,\n                    pplConfigs.shadowMapSize.x,\n                    pplConfigs.shadowMapSize.y,\n                );\n            }\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ForwardPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext): rendering.BasicRenderPassBuilder | undefined {\n        const id = camera.window.renderWindowId;\n\n        const scene = camera.scene!;\n        const mainLight = scene.mainLight;\n\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n\n        // Forward Lighting (Light Culling)\n        this.forwardLighting.cullLights(scene, camera.frustum);\n\n        // Main Directional light CSM Shadow Map\n        if (cameraConfigs.enableMainLightShadowMap) {\n            assert(!!mainLight);\n            this._addCascadedShadowMapPass(ppl, pplConfigs, id, mainLight, camera);\n        }\n\n        // Spot light shadow maps (Mobile or MSAA)\n        if (cameraConfigs.enableSingleForwardPass) {\n            // Currently, only support 1 spot light with shadow map on mobile platform.\n            // TODO(zhouzhenglong): Relex this limitation.\n            this.forwardLighting.addSpotlightShadowPasses(\n                ppl, camera, pplConfigs.mobileMaxSpotLightShadowMaps);\n        }\n\n        this._tryAddReflectionProbePasses(ppl, cameraConfigs, id, mainLight, camera.scene);\n\n        if (cameraConfigs.remainingPasses > 0 || cameraConfigs.enableShadingScale) {\n            context.colorName = cameraConfigs.enableShadingScale\n                ? `ScaledRadiance0_${id}`\n                : `Radiance0_${id}`;\n            context.depthStencilName = cameraConfigs.enableShadingScale\n                ? `ScaledSceneDepth_${id}`\n                : `SceneDepth_${id}`;\n        } else {\n            context.colorName = cameraConfigs.colorName;\n            context.depthStencilName = cameraConfigs.depthStencilName;\n        }\n\n        const pass = this._addForwardRadiancePasses(\n            ppl, pplConfigs, cameraConfigs, id, camera,\n            cameraConfigs.width, cameraConfigs.height, mainLight,\n            context.colorName, context.depthStencilName,\n            !cameraConfigs.enableMSAA,\n            cameraConfigs.enableStoreSceneDepth ? StoreOp.STORE : StoreOp.DISCARD);\n\n        if (!cameraConfigs.enableStoreSceneDepth) {\n            context.depthStencilName = '';\n        }\n\n        if (cameraConfigs.remainingPasses === 0 && cameraConfigs.enableShadingScale) {\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, context.colorName);\n        } else {\n            return pass;\n        }\n    }\n    private _addCascadedShadowMapPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        id: number,\n        light: renderer.scene.DirectionalLight,\n        camera: renderer.scene.Camera,\n    ): void {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // ----------------------------------------------------------------\n        // Dynamic states\n        // ----------------------------------------------------------------\n        const shadowSize = ppl.pipelineSceneData.shadows.size;\n        const width = shadowSize.x;\n        const height = shadowSize.y;\n\n        const viewport = this._viewport;\n        viewport.left = viewport.top = 0;\n        viewport.width = width;\n        viewport.height = height;\n\n        // ----------------------------------------------------------------\n        // CSM Shadow Map\n        // ----------------------------------------------------------------\n        const pass = ppl.addRenderPass(width, height, 'default');\n        pass.name = 'CascadedShadowMap';\n        pass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\n        pass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);\n        const csmLevel = ppl.pipelineSceneData.csmSupported ? light.csmLevel : 1;\n\n        // Add shadow map viewports\n        for (let level = 0; level !== csmLevel; ++level) {\n            getCsmMainLightViewport(light, width, height, level, this._viewport, pplConfigs.screenSpaceSignY);\n            const queue = pass.addQueue(QueueHint.NONE, 'shadow-caster');\n            if (!pplConfigs.isWebGPU) { // Temporary workaround for WebGPU\n                queue.setViewport(this._viewport);\n            }\n            queue\n                .addScene(camera, SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.SHADOW_CASTER)\n                .useLightFrustum(light, level);\n        }\n    }\n    private _tryAddReflectionProbePasses(\n        ppl: rendering.BasicPipeline,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        scene: renderer.RenderScene | null,\n    ): void {\n        const reflectionProbeManager = cclegacy.internal.reflectionProbeManager as ReflectionProbeManager | undefined;\n        if (!reflectionProbeManager) {\n            return;\n        }\n        const ResourceResidency = rendering.ResourceResidency;\n        const probes = reflectionProbeManager.getProbes();\n        const maxProbeCount = 4;\n        let probeID = 0;\n        for (const probe of probes) {\n            if (!probe.needRender) {\n                continue;\n            }\n            const area = probe.renderArea();\n            const width = Math.max(Math.floor(area.x), 1);\n            const height = Math.max(Math.floor(area.y), 1);\n\n            if (probe.probeType === renderer.scene.ProbeType.PLANAR) {\n                if (!cameraConfigs.enablePlanarReflectionProbe) {\n                    continue;\n                }\n                const window: renderer.RenderWindow = probe.realtimePlanarTexture!.window!;\n                const colorName = `PlanarProbeRT${probeID}`;\n                const depthStencilName = `PlanarProbeDS${probeID}`;\n                // ProbeResource\n                ppl.addRenderWindow(colorName,\n                    cameraConfigs.radianceFormat, width, height, window);\n                ppl.addDepthStencil(depthStencilName,\n                    gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);\n\n                // Rendering\n                const probePass = ppl.addRenderPass(width, height, 'default');\n                probePass.name = `PlanarReflectionProbe${probeID}`;\n                this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,\n                    colorName, depthStencilName, mainLight, scene);\n            } else if (EDITOR) {\n                for (let faceIdx = 0; faceIdx < probe.bakedCubeTextures.length; faceIdx++) {\n                    probe.updateCameraDir(faceIdx);\n                    const window: renderer.RenderWindow = probe.bakedCubeTextures[faceIdx].window!;\n                    const colorName = `CubeProbeRT${probeID}${faceIdx}`;\n                    const depthStencilName = `CubeProbeDS${probeID}${faceIdx}`;\n                    // ProbeResource\n                    ppl.addRenderWindow(colorName,\n                        cameraConfigs.radianceFormat, width, height, window);\n                    ppl.addDepthStencil(depthStencilName,\n                        gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);\n\n                    // Rendering\n                    const probePass = ppl.addRenderPass(width, height, 'default');\n                    probePass.name = `CubeProbe${probeID}${faceIdx}`;\n                    this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,\n                        colorName, depthStencilName, mainLight, scene);\n                }\n                probe.needRender = false;\n            }\n            ++probeID;\n            if (probeID === maxProbeCount) {\n                break;\n            }\n        }\n    }\n    private _buildReflectionProbePass(\n        pass: rendering.BasicRenderPassBuilder,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        colorName: string,\n        depthStencilName: string,\n        mainLight: renderer.scene.DirectionalLight | null,\n        scene: renderer.RenderScene | null = null,\n    ): void {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // set viewport\n        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;\n\n        // bind output render target\n        if (forwardNeedClearColor(camera)) {\n            this._reflectionProbeClearColor.x = camera.clearColor.x;\n            this._reflectionProbeClearColor.y = camera.clearColor.y;\n            this._reflectionProbeClearColor.z = camera.clearColor.z;\n            const clearColor = rendering.packRGBE(this._reflectionProbeClearColor);\n            this._clearColor.x = clearColor.x;\n            this._clearColor.y = clearColor.y;\n            this._clearColor.z = clearColor.z;\n            this._clearColor.w = clearColor.w;\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);\n        } else {\n            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);\n        }\n\n        // bind depth stencil buffer\n        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\n            pass.addDepthStencil(\n                depthStencilName,\n                LoadOp.CLEAR,\n                StoreOp.DISCARD,\n                camera.clearDepth,\n                camera.clearStencil,\n                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\n            );\n        } else {\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);\n        }\n\n        // Set shadow map if enabled\n        if (cameraConfigs.enableMainLightShadowMap) {\n            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');\n        }\n\n        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue\n\n        // add opaque and mask queue\n        pass.addQueue(QueueHint.NONE, 'reflect-map') // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE\n            .addScene(camera,\n                SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.REFLECTION_PROBE,\n                mainLight || undefined,\n                scene ? scene : undefined);\n    }\n    private _addForwardRadiancePasses(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        width: number,\n        height: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        colorName: string,\n        depthStencilName: string,\n        disableMSAA: boolean = false,\n        depthStencilStoreOp: gfx.StoreOp = StoreOp.DISCARD,\n    ): rendering.BasicRenderPassBuilder {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // ----------------------------------------------------------------\n        // Dynamic states\n        // ----------------------------------------------------------------\n        // Prepare camera clear color\n        const clearColor = camera.clearColor; // Reduce C++/TS interop\n        this._clearColor.x = clearColor.x;\n        this._clearColor.y = clearColor.y;\n        this._clearColor.z = clearColor.z;\n        this._clearColor.w = clearColor.w;\n\n        // Prepare camera viewport\n        const viewport = camera.viewport; // Reduce C++/TS interop\n        this._viewport.left = Math.round(viewport.x * width);\n        this._viewport.top = Math.round(viewport.y * height);\n        // Here we must use camera.viewport.width instead of camera.viewport.z, which\n        // is undefined on native platform. The same as camera.viewport.height.\n        this._viewport.width = Math.max(Math.round(viewport.width * width), 1);\n        this._viewport.height = Math.max(Math.round(viewport.height * height), 1);\n\n        // MSAA\n        const enableMSAA = !disableMSAA && cameraConfigs.enableMSAA;\n        assert(!enableMSAA || cameraConfigs.enableSingleForwardPass);\n\n        // ----------------------------------------------------------------\n        // Forward Lighting (Main Directional Light)\n        // ----------------------------------------------------------------\n        const pass = cameraConfigs.enableSingleForwardPass\n            ? this._addForwardSingleRadiancePass(ppl, pplConfigs, cameraConfigs,\n                id, camera, enableMSAA, width, height, mainLight,\n                colorName, depthStencilName, depthStencilStoreOp)\n            : this._addForwardMultipleRadiancePasses(ppl, cameraConfigs,\n                id, camera, width, height, mainLight,\n                colorName, depthStencilName, depthStencilStoreOp);\n\n        // Planar Shadow\n        if (cameraConfigs.enableMainLightPlanarShadowMap) {\n            this._addPlanarShadowQueue(camera, mainLight, pass);\n        }\n\n        // ----------------------------------------------------------------\n        // Forward Lighting (Blend)\n        // ----------------------------------------------------------------\n        // Add transparent queue\n\n        const sceneFlags = SceneFlags.BLEND |\n            (camera.geometryRenderer\n                ? SceneFlags.GEOMETRY\n                : SceneFlags.NONE);\n\n        pass\n            .addQueue(QueueHint.BLEND)\n            .addScene(camera, sceneFlags, mainLight || undefined);\n\n        return pass;\n    }\n    private _addForwardSingleRadiancePass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        enableMSAA: boolean,\n        width: number,\n        height: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp\n    ): rendering.BasicRenderPassBuilder {\n        assert(cameraConfigs.enableSingleForwardPass);\n        // ----------------------------------------------------------------\n        // Forward Lighting (Main Directional Light)\n        // ----------------------------------------------------------------\n        let pass: rendering.BasicRenderPassBuilder;\n        if (enableMSAA) {\n            const msaaRadianceName = `MsaaRadiance${id}`;\n            const msaaDepthStencilName = `MsaaDepthStencil${id}`;\n            const sampleCount = cameraConfigs.settings.msaa.sampleCount;\n\n            const msPass = ppl.addMultisampleRenderPass(width, height, sampleCount, 0, 'default');\n            msPass.name = 'MsaaForwardPass';\n\n            // MSAA always discards depth stencil\n            this._buildForwardMainLightPass(msPass, cameraConfigs, id, camera,\n                msaaRadianceName, msaaDepthStencilName, StoreOp.DISCARD, mainLight);\n\n            msPass.resolveRenderTarget(msaaRadianceName, colorName);\n\n            pass = msPass;\n        } else {\n            pass = ppl.addRenderPass(width, height, 'default');\n            pass.name = 'ForwardPass';\n\n            this._buildForwardMainLightPass(pass, cameraConfigs, id, camera,\n                colorName, depthStencilName, depthStencilStoreOp, mainLight);\n        }\n        assert(pass !== undefined);\n\n        // Forward Lighting (Additive Lights)\n        this.forwardLighting.addLightQueues(\n            pass,\n            camera,\n            pplConfigs.mobileMaxSpotLightShadowMaps,\n        );\n\n        return pass;\n    }\n    private _addForwardMultipleRadiancePasses(\n        ppl: rendering.BasicPipeline,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        width: number,\n        height: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp\n    ): rendering.BasicRenderPassBuilder {\n        assert(!cameraConfigs.enableSingleForwardPass);\n\n        // Forward Lighting (Main Directional Light)\n        let pass = ppl.addRenderPass(width, height, 'default');\n        pass.name = 'ForwardPass';\n\n        const firstStoreOp = this.forwardLighting.isMultipleLightPassesNeeded()\n            ? StoreOp.STORE\n            : depthStencilStoreOp;\n\n        this._buildForwardMainLightPass(pass, cameraConfigs,\n            id, camera, colorName, depthStencilName, firstStoreOp, mainLight);\n\n        // Forward Lighting (Additive Lights)\n        pass = this.forwardLighting\n            .addLightPasses(colorName, depthStencilName, depthStencilStoreOp,\n                id, width, height, camera, this._viewport, ppl, pass);\n\n        return pass;\n    }\n    private _buildForwardMainLightPass(\n        pass: rendering.BasicRenderPassBuilder,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp,\n        mainLight: renderer.scene.DirectionalLight | null,\n        scene: renderer.RenderScene | null = null,\n    ): void {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // set viewport\n        pass.setViewport(this._viewport);\n\n        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;\n\n        // bind output render target\n        if (forwardNeedClearColor(camera)) {\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);\n        } else {\n            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);\n        }\n\n        // bind depth stencil buffer\n        if (DEBUG) {\n            if (colorName === cameraConfigs.colorName &&\n                depthStencilName !== cameraConfigs.depthStencilName) {\n                warn('Default framebuffer cannot use custom depth stencil buffer');\n            }\n        }\n\n        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\n            pass.addDepthStencil(\n                depthStencilName,\n                LoadOp.CLEAR,\n                depthStencilStoreOp,\n                camera.clearDepth,\n                camera.clearStencil,\n                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\n            );\n        } else {\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, depthStencilStoreOp);\n        }\n\n        // Set shadow map if enabled\n        if (cameraConfigs.enableMainLightShadowMap) {\n            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');\n        }\n\n        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue\n\n        // add opaque and mask queue\n        pass.addQueue(QueueHint.NONE) // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE\n            .addScene(camera,\n                SceneFlags.OPAQUE | SceneFlags.MASK,\n                mainLight || undefined,\n                scene ? scene : undefined);\n    }\n    private _addPlanarShadowQueue(\n        camera: renderer.scene.Camera,\n        mainLight: renderer.scene.DirectionalLight | null,\n        pass: rendering.BasicRenderPassBuilder,\n    ) {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        pass.addQueue(QueueHint.BLEND, 'planar-shadow')\n            .addScene(\n                camera,\n                SceneFlags.SHADOW_CASTER | SceneFlags.PLANAR_SHADOW | SceneFlags.BLEND,\n                mainLight || undefined,\n            );\n    }\n    private readonly forwardLighting = new ForwardLighting();\n    private readonly _viewport = new Viewport();\n    private readonly _clearColor = new Color(0, 0, 0, 1);\n    private readonly _reflectionProbeClearColor = new Vec3(0, 0, 0);\n}\n\nexport interface BloomPassConfigs {\n    enableBloom: boolean;\n}\n\nexport class BuiltinBloomPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 200;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pipelineConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & BloomPassConfigs): void {\n        cameraConfigs.enableBloom\n            = cameraConfigs.settings.bloom.enabled\n            && !!cameraConfigs.settings.bloom.material;\n        if (cameraConfigs.enableBloom) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & BloomPassConfigs,\n        window: renderer.RenderWindow): void {\n        if (cameraConfigs.enableBloom) {\n            const id = window.renderWindowId;\n            let bloomWidth = cameraConfigs.width;\n            let bloomHeight = cameraConfigs.height;\n            for (let i = 0; i !== cameraConfigs.settings.bloom.iterations + 1; ++i) {\n                bloomWidth = Math.max(Math.floor(bloomWidth / 2), 1);\n                bloomHeight = Math.max(Math.floor(bloomHeight / 2), 1);\n                ppl.addRenderTarget(`BloomTex${id}_${i}`,\n                    cameraConfigs.radianceFormat, bloomWidth, bloomHeight);\n            }\n        }\n    }\n\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & BloomPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableBloom) {\n            return prevRenderPass;\n        }\n\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n        const id = camera.window.renderWindowId;\n        assert(!!cameraConfigs.settings.bloom.material);\n        return this._addKawaseDualFilterBloomPasses(\n            ppl, pplConfigs,\n            cameraConfigs,\n            cameraConfigs.settings,\n            cameraConfigs.settings.bloom.material,\n            id,\n            cameraConfigs.width,\n            cameraConfigs.height,\n            context.colorName);\n    }\n\n    private _addKawaseDualFilterBloomPasses(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & Readonly<BloomPassConfigs>,\n        settings: PipelineSettings,\n        bloomMaterial: Material,\n        id: number,\n        width: number,\n        height: number,\n        radianceName: string,\n    ): rendering.BasicRenderPassBuilder {\n        const QueueHint = rendering.QueueHint;\n        // Based on Kawase Dual Filter Blur. Saves bandwidth on mobile devices.\n        // eslint-disable-next-line max-len\n        // https://community.arm.com/cfs-file/__key/communityserver-blogs-components-weblogfiles/00-00-00-20-66/siggraph2015_2D00_mmg_2D00_marius_2D00_slides.pdf\n\n        // Size: [prefilter(1/2), downsample(1/4), downsample(1/8), downsample(1/16), ...]\n        const iterations = settings.bloom.iterations;\n        const sizeCount = iterations + 1;\n        this._bloomWidths.length = sizeCount;\n        this._bloomHeights.length = sizeCount;\n        this._bloomWidths[0] = Math.max(Math.floor(width / 2), 1);\n        this._bloomHeights[0] = Math.max(Math.floor(height / 2), 1);\n        for (let i = 1; i !== sizeCount; ++i) {\n            this._bloomWidths[i] = Math.max(Math.floor(this._bloomWidths[i - 1] / 2), 1);\n            this._bloomHeights[i] = Math.max(Math.floor(this._bloomHeights[i - 1] / 2), 1);\n        }\n\n        // Bloom texture names\n        this._bloomTexNames.length = sizeCount;\n        for (let i = 0; i !== sizeCount; ++i) {\n            this._bloomTexNames[i] = `BloomTex${id}_${i}`;\n        }\n\n        // Setup bloom parameters\n        this._bloomParams.x = pplConfigs.useFloatOutput ? 1 : 0;\n        this._bloomParams.x = 0; // unused\n        this._bloomParams.z = settings.bloom.threshold;\n        this._bloomParams.w = settings.bloom.enableAlphaMask ? 1 : 0;\n\n        // Prefilter pass\n        const prefilterPass = ppl.addRenderPass(this._bloomWidths[0], this._bloomHeights[0], 'cc-bloom-prefilter');\n        prefilterPass.addRenderTarget(\n            this._bloomTexNames[0],\n            LoadOp.CLEAR,\n            StoreOp.STORE,\n            this._clearColorTransparentBlack,\n        );\n        prefilterPass.addTexture(radianceName, 'inputTexture');\n        prefilterPass.setVec4('g_platform', pplConfigs.platform);\n        prefilterPass.setVec4('bloomParams', this._bloomParams);\n        prefilterPass\n            .addQueue(QueueHint.OPAQUE)\n            .addFullscreenQuad(bloomMaterial, 0);\n\n        // Downsample passes\n        for (let i = 1; i !== sizeCount; ++i) {\n            const downPass = ppl.addRenderPass(this._bloomWidths[i], this._bloomHeights[i], 'cc-bloom-downsample');\n            downPass.addRenderTarget(this._bloomTexNames[i], LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\n            downPass.addTexture(this._bloomTexNames[i - 1], 'bloomTexture');\n            this._bloomTexSize.x = this._bloomWidths[i - 1];\n            this._bloomTexSize.y = this._bloomHeights[i - 1];\n            downPass.setVec4('g_platform', pplConfigs.platform);\n            downPass.setVec4('bloomTexSize', this._bloomTexSize);\n            downPass\n                .addQueue(QueueHint.OPAQUE)\n                .addFullscreenQuad(bloomMaterial, 1);\n        }\n\n        // Upsample passes\n        for (let i = iterations; i-- > 0;) {\n            const upPass = ppl.addRenderPass(this._bloomWidths[i], this._bloomHeights[i], 'cc-bloom-upsample');\n            upPass.addRenderTarget(this._bloomTexNames[i], LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\n            upPass.addTexture(this._bloomTexNames[i + 1], 'bloomTexture');\n            this._bloomTexSize.x = this._bloomWidths[i + 1];\n            this._bloomTexSize.y = this._bloomHeights[i + 1];\n            upPass.setVec4('g_platform', pplConfigs.platform);\n            upPass.setVec4('bloomTexSize', this._bloomTexSize);\n            upPass\n                .addQueue(QueueHint.OPAQUE)\n                .addFullscreenQuad(bloomMaterial, 2);\n        }\n\n        // Combine pass\n        const combinePass = ppl.addRenderPass(width, height, 'cc-bloom-combine');\n        combinePass.addRenderTarget(radianceName, LoadOp.LOAD, StoreOp.STORE);\n        combinePass.addTexture(this._bloomTexNames[0], 'bloomTexture');\n        combinePass.setVec4('g_platform', pplConfigs.platform);\n        combinePass.setVec4('bloomParams', this._bloomParams);\n        combinePass\n            .addQueue(QueueHint.BLEND)\n            .addFullscreenQuad(bloomMaterial, 3);\n\n        if (cameraConfigs.remainingPasses === 0) {\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, radianceName);\n        } else {\n            return combinePass;\n        }\n    }\n    // Bloom\n    private readonly _clearColorTransparentBlack = new Color(0, 0, 0, 0);\n    private readonly _bloomParams = new Vec4(0, 0, 0, 0);\n    private readonly _bloomTexSize = new Vec4(0, 0, 0, 0);\n    private readonly _bloomWidths: Array<number> = [];\n    private readonly _bloomHeights: Array<number> = [];\n    private readonly _bloomTexNames: Array<string> = [];\n}\n\nexport interface ToneMappingPassConfigs {\n    enableToneMapping: boolean;\n    enableColorGrading: boolean;\n}\n\nexport class BuiltinToneMappingPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 300;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {\n        const settings = cameraConfigs.settings;\n\n        cameraConfigs.enableColorGrading\n            = settings.colorGrading.enabled\n            && !!settings.colorGrading.material\n            && !!settings.colorGrading.colorGradingMap;\n\n        cameraConfigs.enableToneMapping\n            = cameraConfigs.enableHDR // From Half to RGBA8\n            || cameraConfigs.enableColorGrading; // Color grading\n\n        if (cameraConfigs.enableToneMapping) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {\n        if (cameraConfigs.enableColorGrading) {\n            assert(!!cameraConfigs.settings.colorGrading.material);\n            cameraConfigs.settings.colorGrading.material.setProperty(\n                'colorGradingMap',\n                cameraConfigs.settings.colorGrading.colorGradingMap);\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableToneMapping) {\n            return prevRenderPass;\n        }\n\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n        if (cameraConfigs.remainingPasses === 0) {\n            return this._addCopyAndTonemapPass(ppl, pplConfigs, cameraConfigs,\n                cameraConfigs.width, cameraConfigs.height,\n                context.colorName, cameraConfigs.colorName);\n        } else {\n            const id = cameraConfigs.renderWindowId;\n            const ldrColorPrefix = cameraConfigs.enableShadingScale\n                ? `ScaledLdrColor`\n                : `LdrColor`;\n\n            const ldrColorName = getPingPongRenderTarget(context.colorName, ldrColorPrefix, id);\n            const radianceName = context.colorName;\n            context.colorName = ldrColorName;\n\n            return this._addCopyAndTonemapPass(ppl, pplConfigs, cameraConfigs,\n                cameraConfigs.width, cameraConfigs.height,\n                radianceName, ldrColorName);\n        }\n    }\n    private _addCopyAndTonemapPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,\n        width: number,\n        height: number,\n        radianceName: string,\n        colorName: string,\n    ): rendering.BasicRenderPassBuilder {\n        let pass: rendering.BasicRenderPassBuilder;\n        const settings = cameraConfigs.settings;\n        if (cameraConfigs.enableColorGrading) {\n            assert(!!settings.colorGrading.material);\n            assert(!!settings.colorGrading.colorGradingMap);\n\n            const lutTex = settings.colorGrading.colorGradingMap;\n            this._colorGradingTexSize.x = lutTex.width;\n            this._colorGradingTexSize.y = lutTex.height;\n\n            const isSquareMap = lutTex.width === lutTex.height;\n            if (isSquareMap) {\n                pass = ppl.addRenderPass(width, height, 'cc-color-grading-8x8');\n            } else {\n                pass = ppl.addRenderPass(width, height, 'cc-color-grading-nx1');\n            }\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n            pass.addTexture(radianceName, 'sceneColorMap');\n            pass.setVec4('g_platform', pplConfigs.platform);\n            pass.setVec2('lutTextureSize', this._colorGradingTexSize);\n            pass.setFloat('contribute', settings.colorGrading.contribute);\n            pass.addQueue(rendering.QueueHint.OPAQUE)\n                .addFullscreenQuad(settings.colorGrading.material, isSquareMap ? 1 : 0);\n        } else {\n            pass = ppl.addRenderPass(width, height, 'cc-tone-mapping');\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n            pass.addTexture(radianceName, 'inputTexture');\n            pass.setVec4('g_platform', pplConfigs.platform);\n            if (settings.toneMapping.material) {\n                pass.addQueue(rendering.QueueHint.OPAQUE)\n                    .addFullscreenQuad(settings.toneMapping.material, 0);\n            } else {\n                assert(!!cameraConfigs.copyAndTonemapMaterial);\n                pass.addQueue(rendering.QueueHint.OPAQUE)\n                    .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 0);\n            }\n        }\n        return pass;\n    }\n    private readonly _colorGradingTexSize = new Vec2(0, 0);\n}\n\nexport interface FXAAPassConfigs {\n    enableFXAA: boolean;\n}\n\nexport class BuiltinFXAAPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 400;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FXAAPassConfigs): void {\n        cameraConfigs.enableFXAA\n            = cameraConfigs.settings.fxaa.enabled\n            && !!cameraConfigs.settings.fxaa.material;\n        if (cameraConfigs.enableFXAA) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FXAAPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableFXAA) {\n            return prevRenderPass;\n        }\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n\n        const id = cameraConfigs.renderWindowId;\n        const ldrColorPrefix = cameraConfigs.enableShadingScale\n            ? `ScaledLdrColor`\n            : `LdrColor`;\n        const ldrColorName = getPingPongRenderTarget(context.colorName, ldrColorPrefix, id);\n\n        assert(!!cameraConfigs.settings.fxaa.material);\n        if (cameraConfigs.remainingPasses === 0) {\n            if (cameraConfigs.enableShadingScale) {\n                this._addFxaaPass(ppl, pplConfigs,\n                    cameraConfigs.settings.fxaa.material,\n                    cameraConfigs.width,\n                    cameraConfigs.height,\n                    context.colorName,\n                    ldrColorName);\n                return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, ldrColorName);\n            } else {\n                assert(cameraConfigs.width === cameraConfigs.nativeWidth);\n                assert(cameraConfigs.height === cameraConfigs.nativeHeight);\n                return this._addFxaaPass(ppl, pplConfigs,\n                    cameraConfigs.settings.fxaa.material,\n                    cameraConfigs.width,\n                    cameraConfigs.height,\n                    context.colorName,\n                    cameraConfigs.colorName);\n            }\n        } else {\n            const inputColorName = context.colorName;\n            context.colorName = ldrColorName;\n            const lastPass = this._addFxaaPass(ppl, pplConfigs,\n                cameraConfigs.settings.fxaa.material,\n                cameraConfigs.width,\n                cameraConfigs.height,\n                inputColorName,\n                ldrColorName);\n            return lastPass;\n        }\n    }\n    private _addFxaaPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        fxaaMaterial: Material,\n        width: number,\n        height: number,\n        ldrColorName: string,\n        colorName: string,\n    ): rendering.BasicRenderPassBuilder {\n        this._fxaaParams.x = width;\n        this._fxaaParams.y = height;\n        this._fxaaParams.z = 1 / width;\n        this._fxaaParams.w = 1 / height;\n\n        const pass = ppl.addRenderPass(width, height, 'cc-fxaa');\n        pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n        pass.addTexture(ldrColorName, 'sceneColorMap');\n        pass.setVec4('g_platform', pplConfigs.platform);\n        pass.setVec4('texSize', this._fxaaParams);\n        pass.addQueue(rendering.QueueHint.OPAQUE)\n            .addFullscreenQuad(fxaaMaterial, 0);\n        return pass;\n    }\n    // FXAA\n    private readonly _fxaaParams = new Vec4(0, 0, 0, 0);\n}\n\nexport interface FSRPassConfigs {\n    enableFSR: boolean;\n}\n\nexport class BuiltinFsrPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 500;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs): void {\n        // FSR (Depend on Shading scale)\n        cameraConfigs.enableFSR = cameraConfigs.settings.fsr.enabled\n            && !!cameraConfigs.settings.fsr.material\n            && cameraConfigs.enableShadingScale\n            && cameraConfigs.shadingScale < 1.0;\n\n        if (cameraConfigs.enableFSR) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableFSR) {\n            return prevRenderPass;\n        }\n        --cameraConfigs.remainingPasses;\n\n        const inputColorName = context.colorName;\n        const outputColorName\n            = cameraConfigs.remainingPasses === 0\n                ? cameraConfigs.colorName\n                : getPingPongRenderTarget(context.colorName, 'UiColor', cameraConfigs.renderWindowId);\n        context.colorName = outputColorName;\n\n        assert(!!cameraConfigs.settings.fsr.material);\n        return this._addFsrPass(ppl, pplConfigs, cameraConfigs,\n            cameraConfigs.settings,\n            cameraConfigs.settings.fsr.material,\n            cameraConfigs.renderWindowId,\n            cameraConfigs.width,\n            cameraConfigs.height,\n            inputColorName,\n            cameraConfigs.nativeWidth,\n            cameraConfigs.nativeHeight,\n            outputColorName);\n    }\n    private _addFsrPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\n        settings: PipelineSettings,\n        fsrMaterial: Material,\n        id: number,\n        width: number,\n        height: number,\n        inputColorName: string,\n        nativeWidth: number,\n        nativeHeight: number,\n        outputColorName: string,\n    ): rendering.BasicRenderPassBuilder {\n        this._fsrTexSize.x = width;\n        this._fsrTexSize.y = height;\n        this._fsrTexSize.z = nativeWidth;\n        this._fsrTexSize.w = nativeHeight;\n        this._fsrParams.x = clamp(1.0 - settings.fsr.sharpness, 0.02, 0.98);\n\n        const uiColorPrefix = 'UiColor';\n\n        const fsrColorName = getPingPongRenderTarget(outputColorName, uiColorPrefix, id);\n\n        const easuPass = ppl.addRenderPass(nativeWidth, nativeHeight, 'cc-fsr-easu');\n        easuPass.addRenderTarget(fsrColorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n        easuPass.addTexture(inputColorName, 'outputResultMap');\n        easuPass.setVec4('g_platform', pplConfigs.platform);\n        easuPass.setVec4('fsrTexSize', this._fsrTexSize);\n        easuPass\n            .addQueue(rendering.QueueHint.OPAQUE)\n            .addFullscreenQuad(fsrMaterial, 0);\n\n        const rcasPass = ppl.addRenderPass(nativeWidth, nativeHeight, 'cc-fsr-rcas');\n        rcasPass.addRenderTarget(outputColorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n        rcasPass.addTexture(fsrColorName, 'outputResultMap');\n        rcasPass.setVec4('g_platform', pplConfigs.platform);\n        rcasPass.setVec4('fsrTexSize', this._fsrTexSize);\n        rcasPass.setVec4('fsrParams', this._fsrParams);\n        rcasPass\n            .addQueue(rendering.QueueHint.OPAQUE)\n            .addFullscreenQuad(fsrMaterial, 1);\n\n        return rcasPass;\n    }\n    // FSR\n    private readonly _fsrParams = new Vec4(0, 0, 0, 0);\n    private readonly _fsrTexSize = new Vec4(0, 0, 0, 0);\n}\n\nexport class BuiltinUiPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 1000;\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        assert(!!prevRenderPass);\n\n        let flags = rendering.SceneFlags.UI;\n        if (cameraConfigs.enableProfiler) {\n            flags |= rendering.SceneFlags.PROFILER;\n            prevRenderPass.showStatistics = true;\n        }\n        prevRenderPass\n            .addQueue(rendering.QueueHint.BLEND, 'default', 'default')\n            .addScene(camera, flags);\n\n        return prevRenderPass;\n    }\n}\n\nif (rendering) {\n\n    const { QueueHint, SceneFlags } = rendering;\n\n    class BuiltinPipelineBuilder implements rendering.PipelineBuilder {\n        private readonly _pipelineEvent: PipelineEventProcessor = cclegacy.director.root.pipelineEvent as PipelineEventProcessor;\n        private readonly _forwardPass = new BuiltinForwardPassBuilder();\n        private readonly _bloomPass = new BuiltinBloomPassBuilder();\n        private readonly _toneMappingPass = new BuiltinToneMappingPassBuilder();\n        private readonly _fxaaPass = new BuiltinFXAAPassBuilder();\n        private readonly _fsrPass = new BuiltinFsrPassBuilder();\n        private readonly _uiPass = new BuiltinUiPassBuilder();\n        // Internal cached resources\n        private readonly _clearColor = new Color(0, 0, 0, 1);\n        private readonly _viewport = new Viewport();\n        private readonly _configs = new PipelineConfigs();\n        private readonly _cameraConfigs = new CameraConfigs();\n        // Materials\n        private readonly _copyAndTonemapMaterial = new Material();\n\n        // Internal States\n        private _initialized = false; // TODO(zhouzhenglong): Make default effect asset loading earlier and remove this flag\n        private _passBuilders: rendering.PipelinePassBuilder[] = [];\n\n        private _setupPipelinePreview(\n            camera: renderer.scene.Camera,\n            cameraConfigs: CameraConfigs) {\n            const isEditorView: boolean\n                = camera.cameraUsage === CameraUsage.SCENE_VIEW\n                || camera.cameraUsage === CameraUsage.PREVIEW;\n\n            if (isEditorView) {\n                const editorSettings = rendering.getEditorPipelineSettings() as PipelineSettings | null;\n                if (editorSettings) {\n                    cameraConfigs.settings = editorSettings;\n                } else {\n                    cameraConfigs.settings = defaultSettings;\n                }\n            } else {\n                if (camera.pipelineSettings) {\n                    cameraConfigs.settings = camera.pipelineSettings as PipelineSettings;\n                } else {\n                    cameraConfigs.settings = defaultSettings;\n                }\n            }\n        }\n\n        private _preparePipelinePasses(cameraConfigs: CameraConfigs): void {\n            const passBuilders = this._passBuilders;\n            passBuilders.length = 0;\n\n            const settings = cameraConfigs.settings as PipelineSettings2;\n            if (settings._passes) {\n                for (const pass of settings._passes) {\n                    passBuilders.push(pass);\n                }\n                assert(passBuilders.length === settings._passes.length);\n            }\n\n            passBuilders.push(this._forwardPass);\n\n            if (settings.bloom.enabled) {\n                passBuilders.push(this._bloomPass);\n            }\n\n            passBuilders.push(this._toneMappingPass);\n\n            if (settings.fxaa.enabled) {\n                passBuilders.push(this._fxaaPass);\n            }\n\n            if (settings.fsr.enabled) {\n                passBuilders.push(this._fsrPass);\n            }\n            passBuilders.push(this._uiPass);\n        }\n\n        private _setupBuiltinCameraConfigs(\n            camera: renderer.scene.Camera,\n            pipelineConfigs: PipelineConfigs,\n            cameraConfigs: CameraConfigs\n        ) {\n            const window = camera.window;\n            const isMainGameWindow: boolean = camera.cameraUsage === CameraUsage.GAME && !!window.swapchain;\n\n            // Window\n            cameraConfigs.isMainGameWindow = isMainGameWindow;\n            cameraConfigs.renderWindowId = window.renderWindowId;\n\n            // Camera\n            cameraConfigs.colorName = window.colorName;\n            cameraConfigs.depthStencilName = window.depthStencilName;\n\n            // Pipeline\n            cameraConfigs.enableFullPipeline = (camera.visibility & (Layers.Enum.DEFAULT)) !== 0;\n            cameraConfigs.enableProfiler = DEBUG && isMainGameWindow;\n            cameraConfigs.remainingPasses = 0;\n\n            // Shading scale\n            cameraConfigs.shadingScale = cameraConfigs.settings.shadingScale;\n            cameraConfigs.enableShadingScale = cameraConfigs.settings.enableShadingScale\n                && cameraConfigs.shadingScale !== 1.0;\n\n            cameraConfigs.nativeWidth = Math.max(Math.floor(window.width), 1);\n            cameraConfigs.nativeHeight = Math.max(Math.floor(window.height), 1);\n\n            cameraConfigs.width = cameraConfigs.enableShadingScale\n                ? Math.max(Math.floor(cameraConfigs.nativeWidth * cameraConfigs.shadingScale), 1)\n                : cameraConfigs.nativeWidth;\n            cameraConfigs.height = cameraConfigs.enableShadingScale\n                ? Math.max(Math.floor(cameraConfigs.nativeHeight * cameraConfigs.shadingScale), 1)\n                : cameraConfigs.nativeHeight;\n\n            // Radiance\n            cameraConfigs.enableHDR = cameraConfigs.enableFullPipeline\n                && pipelineConfigs.useFloatOutput;\n            cameraConfigs.radianceFormat = cameraConfigs.enableHDR\n                ? gfx.Format.RGBA16F : gfx.Format.RGBA8;\n\n            // Tone Mapping\n            cameraConfigs.copyAndTonemapMaterial = this._copyAndTonemapMaterial;\n\n            // Depth\n            cameraConfigs.enableStoreSceneDepth = false;\n        }\n\n        private _setupCameraConfigs(\n            camera: renderer.scene.Camera,\n            pipelineConfigs: PipelineConfigs,\n            cameraConfigs: CameraConfigs\n        ): void {\n            this._setupPipelinePreview(camera, cameraConfigs);\n\n            this._preparePipelinePasses(cameraConfigs);\n\n            sortPipelinePassBuildersByConfigOrder(this._passBuilders);\n\n            this._setupBuiltinCameraConfigs(camera, pipelineConfigs, cameraConfigs);\n\n            for (const builder of this._passBuilders) {\n                if (builder.configCamera) {\n                    builder.configCamera(camera, pipelineConfigs, cameraConfigs);\n                }\n            }\n        }\n\n        // ----------------------------------------------------------------\n        // Interface\n        // ----------------------------------------------------------------\n        windowResize(\n            ppl: rendering.BasicPipeline,\n            window: renderer.RenderWindow,\n            camera: renderer.scene.Camera,\n            nativeWidth: number,\n            nativeHeight: number,\n        ): void {\n            setupPipelineConfigs(ppl, this._configs);\n\n            this._setupCameraConfigs(camera, this._configs, this._cameraConfigs);\n\n            // Render Window (UI)\n            const id = window.renderWindowId;\n\n            ppl.addRenderWindow(this._cameraConfigs.colorName,\n                Format.RGBA8, nativeWidth, nativeHeight, window,\n                this._cameraConfigs.depthStencilName);\n\n            const width = this._cameraConfigs.width;\n            const height = this._cameraConfigs.height;\n\n            if (this._cameraConfigs.enableShadingScale) {\n                ppl.addDepthStencil(`ScaledSceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);\n                ppl.addRenderTarget(`ScaledRadiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`ScaledRadiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`ScaledLdrColor0_${id}`, Format.RGBA8, width, height);\n                ppl.addRenderTarget(`ScaledLdrColor1_${id}`, Format.RGBA8, width, height);\n            } else {\n                ppl.addDepthStencil(`SceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);\n                ppl.addRenderTarget(`Radiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`Radiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`LdrColor0_${id}`, Format.RGBA8, width, height);\n                ppl.addRenderTarget(`LdrColor1_${id}`, Format.RGBA8, width, height);\n            }\n            ppl.addRenderTarget(`UiColor0_${id}`, Format.RGBA8, nativeWidth, nativeHeight);\n            ppl.addRenderTarget(`UiColor1_${id}`, Format.RGBA8, nativeWidth, nativeHeight);\n\n            for (const builder of this._passBuilders) {\n                if (builder.windowResize) {\n                    builder.windowResize(ppl, this._configs, this._cameraConfigs, window, camera, nativeWidth, nativeHeight);\n                }\n            }\n        }\n        setup(cameras: renderer.scene.Camera[], ppl: rendering.BasicPipeline): void {\n            // TODO(zhouzhenglong): Make default effect asset loading earlier and remove _initMaterials\n            if (this._initMaterials(ppl)) {\n                return;\n            }\n            // Render cameras\n            // log(`==================== One Frame ====================`);\n            for (const camera of cameras) {\n                // Skip invalid camera\n                if (!camera.scene || !camera.window) {\n                    continue;\n                }\n                // Setup camera configs\n                this._setupCameraConfigs(camera, this._configs, this._cameraConfigs);\n                // log(`Setup camera: ${camera.node!.name}, window: ${camera.window.renderWindowId}, isFull: ${this._cameraConfigs.enableFullPipeline}, `\n                //     + `size: ${camera.window.width}x${camera.window.height}`);\n\n                this._pipelineEvent.emit(PipelineEventType.RENDER_CAMERA_BEGIN, camera);\n\n                // Build pipeline\n                if (this._cameraConfigs.enableFullPipeline) {\n                    this._buildForwardPipeline(ppl, camera, camera.scene, this._passBuilders);\n                } else {\n                    this._buildSimplePipeline(ppl, camera);\n                }\n\n                this._pipelineEvent.emit(PipelineEventType.RENDER_CAMERA_END, camera);\n            }\n        }\n        // ----------------------------------------------------------------\n        // Pipelines\n        // ----------------------------------------------------------------\n        private _buildSimplePipeline(\n            ppl: rendering.BasicPipeline,\n            camera: renderer.scene.Camera,\n        ): void {\n            const width = Math.max(Math.floor(camera.window.width), 1);\n            const height = Math.max(Math.floor(camera.window.height), 1);\n            const colorName = this._cameraConfigs.colorName;\n            const depthStencilName = this._cameraConfigs.depthStencilName;\n\n            const viewport = camera.viewport;  // Reduce C++/TS interop\n            this._viewport.left = Math.round(viewport.x * width);\n            this._viewport.top = Math.round(viewport.y * height);\n            // Here we must use camera.viewport.width instead of camera.viewport.z, which\n            // is undefined on native platform. The same as camera.viewport.height.\n            this._viewport.width = Math.max(Math.round(viewport.width * width), 1);\n            this._viewport.height = Math.max(Math.round(viewport.height * height), 1);\n\n            const clearColor = camera.clearColor;  // Reduce C++/TS interop\n            this._clearColor.x = clearColor.x;\n            this._clearColor.y = clearColor.y;\n            this._clearColor.z = clearColor.z;\n            this._clearColor.w = clearColor.w;\n\n            const pass = ppl.addRenderPass(width, height, 'default');\n\n            // bind output render target\n            if (forwardNeedClearColor(camera)) {\n                pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, this._clearColor);\n            } else {\n                pass.addRenderTarget(colorName, LoadOp.LOAD, StoreOp.STORE);\n            }\n\n            // bind depth stencil buffer\n            if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\n                pass.addDepthStencil(\n                    depthStencilName,\n                    LoadOp.CLEAR,\n                    StoreOp.DISCARD,\n                    camera.clearDepth,\n                    camera.clearStencil,\n                    camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\n                );\n            } else {\n                pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);\n            }\n\n            pass.setViewport(this._viewport);\n\n            // The opaque queue is used for Reflection probe preview\n            pass.addQueue(QueueHint.OPAQUE)\n                .addScene(camera, SceneFlags.OPAQUE);\n\n            // The blend queue is used for UI and Gizmos\n            let flags = SceneFlags.BLEND | SceneFlags.UI;\n            if (this._cameraConfigs.enableProfiler) {\n                flags |= SceneFlags.PROFILER;\n                pass.showStatistics = true;\n            }\n            pass.addQueue(QueueHint.BLEND)\n                .addScene(camera, flags);\n        }\n\n        private _buildForwardPipeline(\n            ppl: rendering.BasicPipeline,\n            camera: renderer.scene.Camera,\n            scene: renderer.RenderScene,\n            passBuilders: rendering.PipelinePassBuilder[],\n        ): void {\n            sortPipelinePassBuildersByRenderOrder(passBuilders);\n\n            const context: PipelineContext = {\n                colorName: '',\n                depthStencilName: '',\n            };\n\n            let lastPass: rendering.BasicRenderPassBuilder | undefined = undefined;\n\n            for (const builder of passBuilders) {\n                if (builder.setup) {\n                    lastPass = builder.setup(ppl, this._configs, this._cameraConfigs,\n                        camera, context, lastPass);\n                }\n            }\n\n            assert(this._cameraConfigs.remainingPasses === 0);\n        }\n\n        private _initMaterials(ppl: rendering.BasicPipeline): number {\n            if (this._initialized) {\n                return 0;\n            }\n\n            setupPipelineConfigs(ppl, this._configs);\n\n            // When add new effect asset, please add its uuid to the dependentAssets in cc.config.json.\n            this._copyAndTonemapMaterial._uuid = `builtin-pipeline-tone-mapping-material`;\n            this._copyAndTonemapMaterial.initialize({ effectName: 'pipeline/post-process/tone-mapping' });\n\n            if (this._copyAndTonemapMaterial.effectAsset) {\n                this._initialized = true;\n            }\n\n            return this._initialized ? 0 : 1;\n        }\n    }\n\n    rendering.setCustomPipeline('Builtin', new BuiltinPipelineBuilder());\n\n} // if (rendering)\n"]}