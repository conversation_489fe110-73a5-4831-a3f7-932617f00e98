{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransitionTest.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Label", "director", "SceneTransition", "ccclass", "property", "SceneTransitionTest", "testScenes", "currentSceneIndex", "start", "bindEvents", "updateStatusLabel", "findCurrentSceneIndex", "testButton", "node", "on", "EventType", "CLICK", "onTestButtonClick", "currentSceneName", "getScene", "name", "index", "indexOf", "length", "nextScene", "console", "log", "loadScene", "statusLabel", "currentScene", "string", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;;AACtCC,MAAAA,e,iBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;AAE9B;AACA;AACA;AACA;;qCAEaQ,mB,WADZF,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACJ,KAAD,C,2BALb,MACaK,mBADb,SACyCP,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAO/C;AAP+C,eAQvCQ,UARuC,GAQhB,CAAC,UAAD,EAAa,aAAb,EAA4B,WAA5B,CARgB;AAAA,eASvCC,iBATuC,GASX,CATW;AAAA;;AAW/CC,QAAAA,KAAK,GAAG;AACJ,eAAKC,UAAL;AACA,eAAKC,iBAAL;AACA,eAAKC,qBAAL;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,UAAU,GAAS;AACvB,cAAI,KAAKG,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,IAAhB,CAAqBC,EAArB,CAAwBf,MAAM,CAACgB,SAAP,CAAiBC,KAAzC,EAAgD,KAAKC,iBAArD,EAAwE,IAAxE;AACH;AACJ;AAED;AACJ;AACA;;;AACYN,QAAAA,qBAAqB,GAAS;AAAA;;AAClC,gBAAMO,gBAAgB,yBAAGjB,QAAQ,CAACkB,QAAT,EAAH,qBAAG,mBAAqBC,IAA9C;AACA,gBAAMC,KAAK,GAAG,KAAKf,UAAL,CAAgBgB,OAAhB,CAAwBJ,gBAAgB,IAAI,EAA5C,CAAd;;AACA,cAAIG,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAKd,iBAAL,GAAyBc,KAAzB;AACH;;AACD,eAAKX,iBAAL;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,iBAAiB,GAAS;AAC9B;AACA,eAAKV,iBAAL,GAAyB,CAAC,KAAKA,iBAAL,GAAyB,CAA1B,IAA+B,KAAKD,UAAL,CAAgBiB,MAAxE;AACA,gBAAMC,SAAS,GAAG,KAAKlB,UAAL,CAAgB,KAAKC,iBAArB,CAAlB;AAEAkB,UAAAA,OAAO,CAACC,GAAR,CAAa,0CAAyCF,SAAU,EAAhE,EAL8B,CAO9B;;AACA;AAAA;AAAA,kDAAgBG,SAAhB,CAA0BH,SAA1B;AACH;AAED;AACJ;AACA;;;AACYd,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,KAAKkB,WAAT,EAAsB;AAAA;;AAClB,kBAAMC,YAAY,GAAG,wBAAA5B,QAAQ,CAACkB,QAAT,2CAAqBC,IAArB,KAA6B,SAAlD;AACA,kBAAMI,SAAS,GAAG,KAAKlB,UAAL,CAAgB,CAAC,KAAKC,iBAAL,GAAyB,CAA1B,IAA+B,KAAKD,UAAL,CAAgBiB,MAA/D,CAAlB;AACA,iBAAKK,WAAL,CAAiBE,MAAjB,GAA2B,SAAQD,YAAa,YAAWL,SAAU,cAArE;AACH;AACJ;;AAEDO,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKnB,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,IAAhB,CAAqBmB,GAArB,CAAyBjC,MAAM,CAACgB,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,iBAAtD,EAAyE,IAAzE;AACH;AACJ;;AApE8C,O;;;;;iBAE1B,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, But<PERSON>, Label, director } from 'cc';\nimport { SceneTransition } from './SceneTransition';\nconst { ccclass, property } = _decorator;\n\n/**\n * 场景过渡测试脚本\n * 用于测试场景过渡效果是否正常工作\n */\n@ccclass('SceneTransitionTest')\nexport class SceneTransitionTest extends Component {\n    @property(Button)\n    testButton: Button = null!;\n\n    @property(Label)\n    statusLabel: Label = null!;\n\n    // 测试场景列表\n    private testScenes: string[] = ['mainmenu', 'LevelSelect', 'gamescene'];\n    private currentSceneIndex: number = 0;\n\n    start() {\n        this.bindEvents();\n        this.updateStatusLabel();\n        this.findCurrentSceneIndex();\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindEvents(): void {\n        if (this.testButton) {\n            this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);\n        }\n    }\n\n    /**\n     * 查找当前场景在测试列表中的索引\n     */\n    private findCurrentSceneIndex(): void {\n        const currentSceneName = director.getScene()?.name;\n        const index = this.testScenes.indexOf(currentSceneName || '');\n        if (index !== -1) {\n            this.currentSceneIndex = index;\n        }\n        this.updateStatusLabel();\n    }\n\n    /**\n     * 测试按钮点击事件\n     */\n    private onTestButtonClick(): void {\n        // 循环切换到下一个场景\n        this.currentSceneIndex = (this.currentSceneIndex + 1) % this.testScenes.length;\n        const nextScene = this.testScenes[this.currentSceneIndex];\n        \n        console.log(`Testing SceneTransition: Loading scene ${nextScene}`);\n        \n        // 使用SceneTransition切换场景\n        SceneTransition.loadScene(nextScene);\n    }\n\n    /**\n     * 更新状态标签\n     */\n    private updateStatusLabel(): void {\n        if (this.statusLabel) {\n            const currentScene = director.getScene()?.name || 'Unknown';\n            const nextScene = this.testScenes[(this.currentSceneIndex + 1) % this.testScenes.length];\n            this.statusLabel.string = `当前场景: ${currentScene}\\n下一个场景: ${nextScene}\\n点击按钮测试场景切换`;\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.testButton) {\n            this.testButton.node.off(Button.EventType.CLICK, this.onTestButtonClick, this);\n        }\n    }\n}\n"]}