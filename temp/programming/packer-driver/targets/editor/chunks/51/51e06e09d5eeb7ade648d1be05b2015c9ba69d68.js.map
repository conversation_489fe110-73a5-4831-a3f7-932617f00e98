{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA", "sourcesContent": ["import {InterstitialType} from './interstitial_type';\n\n/**\n * Interface for Interstitial AdBreak API Callback\n */\nexport interface InterstitialCallback {\n  type: InterstitialType;\n  name: string;\n  beforeAd: () => void;\n  afterAd: () => void;\n  adBreakDone: (placementInfo: unknown) => void;\n}"]}