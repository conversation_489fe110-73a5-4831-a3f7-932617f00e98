{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts"], "names": ["_decorator", "Component", "Label", "<PERSON><PERSON>", "ccclass", "property", "PurchasePanel", "type", "tooltip", "currentPrice", "onConfirmCallback", "onLoad", "closeButton", "node", "on", "EventType", "CLICK", "onCloseButtonClick", "confirmButton", "onConfirmButtonClick", "show", "price", "info", "onConfirm", "priceLabel", "string", "infoLabel", "active", "hide"], "mappings": ";;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;;;;qFAD7C;;;AAGA;;;OACM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;+BAGjBM,a,WADZF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,KAAR;AAAeM,QAAAA,OAAO,EAAE;AAAxB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL,KAAR;AAAeM,QAAAA,OAAO,EAAE;AAAxB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEJ,MAAR;AAAgBK,QAAAA,OAAO,EAAE;AAAzB,OAAD,C,UAGRH,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEJ,MAAR;AAAgBK,QAAAA,OAAO,EAAE;AAAzB,OAAD,C,2BAXb,MACaF,aADb,SACmCL,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAazC;AACA;AAdyC,eAgBjCQ,YAhBiC,GAgBV,CAhBU;AAAA,eAiBjCC,iBAjBiC,GAiBY,IAjBZ;AAAA;;AAmBzCC,QAAAA,MAAM,GAAG;AACL;AACA,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBX,MAAM,CAACY,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACH;;AAED,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBL,IAAnB,CAAwBC,EAAxB,CAA2BX,MAAM,CAACY,SAAP,CAAiBC,KAA5C,EAAmD,KAAKG,oBAAxD,EAA8E,IAA9E;AACH;AACJ,SA5BwC,CA8BzC;;;AACOC,QAAAA,IAAI,CAACC,KAAD,EAAgBC,IAAhB,EAA8BC,SAA9B,EAAkE;AACzE,eAAKd,YAAL,GAAoBY,KAApB;AAEA,eAAKX,iBAAL,GAAyBa,SAAzB;;AAEA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAA0B,IAAGJ,KAAM,EAAnC;AACH;;AACD,cAAG,KAAKK,SAAR,EACA;AACI,iBAAKA,SAAL,CAAeD,MAAf,GAAyB,GAAEH,IAAK,EAAhC;AACH,WAXwE,CAazE;;;AACA,cAAI,KAAKT,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUc,MAAV,GAAmB,IAAnB;AACH;AACJ,SAhDwC,CAkDzC;;;AACOC,QAAAA,IAAI,GAAG;AACV;AACA,cAAI,KAAKf,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUc,MAAV,GAAmB,KAAnB;AACH;;AAED,eAAKjB,iBAAL,GAAyB,IAAzB;AACH,SA1DwC,CA4DzC;;;AACQO,QAAAA,kBAAkB,GAAG;AACzB,eAAKW,IAAL;AACH,SA/DwC,CAiEzC;;;AACQT,QAAAA,oBAAoB,GAAG;AAC3B,cAAI,KAAKT,iBAAT,EAA4B;AACxB,iBAAKA,iBAAL,CAAuB,KAAKD,YAA5B;AACH;;AACD,eAAKmB,IAAL;AACH,SAvEwC,CAyEzC;AACA;AACA;AACA;;;AA5EyC,O;;;;;iBAEb,I;;;;;;;iBAGD,I;;;;;;;iBAGG,I;;;;;;;iBAGE,I", "sourcesContent": ["// 添加必要的import\nimport { _decorator, Component, Node, Label, Button, Color, Sprite, Graphics } from 'cc';\n\n// 在PurchasePanel类中添加必要的组件引用\nconst { ccclass, property } = _decorator;\n\n@ccclass('PurchasePanel')\nexport class PurchasePanel extends Component {\n    @property({ type: Label, tooltip: '价格显示文本' })\n    private priceLabel: Label = null!;\n\n    @property({ type: Label, tooltip: '车辆介绍文本' })\n    private infoLabel: Label = null!;\n\n    @property({ type: Button, tooltip: '关闭按钮' })\n    private closeButton: Button = null!;\n\n    @property({ type: Button, tooltip: '购买确认按钮' })\n    private confirmButton: Button = null!;\n\n    // @property({ type: Node, tooltip: '面板节点' })\n    // private panelNode: Node = null!;\n\n    private currentPrice: number = 0;\n    private onConfirmCallback: (price: number) => void = null;\n\n    onLoad() {\n        // 初始化按钮事件\n        if (this.closeButton) {\n            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClick, this);\n        }\n        \n        if (this.confirmButton) {\n            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmButtonClick, this);\n        }\n    }\n\n    // 显示面板\n    public show(price: number, info: string, onConfirm: (price: number) => void) {\n        this.currentPrice = price;\n        \n        this.onConfirmCallback = onConfirm;\n        \n        if (this.priceLabel) {\n            this.priceLabel.string = `$${price}`;\n        }\n        if(this.infoLabel)\n        {\n            this.infoLabel.string = `${info}`;\n        }\n        \n        // 使用拖拽关联的节点控制显示\n        if (this.node) {\n            this.node.active = true;\n        }\n    }\n\n    // 隐藏面板\n    public hide() {\n        // 使用拖拽关联的节点控制隐藏\n        if (this.node) {\n            this.node.active = false;\n        }\n        \n        this.onConfirmCallback = null;\n    }\n\n    // 关闭按钮点击事件\n    private onCloseButtonClick() {\n        this.hide();\n    }\n\n    // 确认按钮点击事件\n    private onConfirmButtonClick() {\n        if (this.onConfirmCallback) {\n            this.onConfirmCallback(this.currentPrice);\n        }\n        this.hide();\n    }\n\n    // // 添加静态方法用于查找节点\n    // static find(path: string): Node {\n    //     return (cc as any).find(path);\n    // }\n}"]}