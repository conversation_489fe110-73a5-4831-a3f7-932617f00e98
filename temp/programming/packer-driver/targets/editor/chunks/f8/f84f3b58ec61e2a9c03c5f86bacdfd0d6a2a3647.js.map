{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"], "names": ["makeMSAA", "enabled", "sampleCount", "SampleCount", "X4", "fillRequiredMSAA", "value", "undefined", "makeHBAO", "radiusScale", "angleBiasDegree", "blurSharpness", "aoSaturation", "need<PERSON><PERSON><PERSON>", "fillRequiredHBAO", "makeBloom", "material", "enableAlphaMask", "iterations", "threshold", "intensity", "fillRequiredBloom", "makeColorGrading", "contribute", "colorGradingMap", "fillRequiredColorGrading", "makeFSR", "sharpness", "fillRequiredFSR", "makeFXAA", "fillRequiredFXAA", "makeToneMapping", "fillRequiredToneMapping", "makePipelineSettings", "msaa", "enableShadingScale", "shadingScale", "bloom", "toneMapping", "colorGrading", "fsr", "fxaa", "fillRequiredPipelineSettings", "gfx"], "mappings": ";;;;;AAwCO,WAASA,QAAT,GAA0B;AAC7B,WAAO;AACHC,MAAAA,OAAO,EAAE,KADN;AAEHC,MAAAA,WAAW,EAAEC,WAAW,CAACC;AAFtB,KAAP;AAIH;;AAEM,WAASC,gBAAT,CAA0BC,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACJ,WAAN,KAAsBK,SAA1B,EAAqC;AACjCD,MAAAA,KAAK,CAACJ,WAAN,GAAoBC,WAAW,CAACC,EAAhC;AACH;AACJ;;AAYM,WAASI,QAAT,GAA0B;AAC7B,WAAO;AACHP,MAAAA,OAAO,EAAE,KADN;AAEHQ,MAAAA,WAAW,EAAE,CAFV;AAGHC,MAAAA,eAAe,EAAE,EAHd;AAIHC,MAAAA,aAAa,EAAE,CAJZ;AAKHC,MAAAA,YAAY,EAAE,CALX;AAMHC,MAAAA,QAAQ,EAAE;AANP,KAAP;AAQH;;AAEM,WAASC,gBAAT,CAA0BR,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACG,WAAN,KAAsBF,SAA1B,EAAqC;AACjCD,MAAAA,KAAK,CAACG,WAAN,GAAoB,CAApB;AACH;;AACD,QAAIH,KAAK,CAACI,eAAN,KAA0BH,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACI,eAAN,GAAwB,EAAxB;AACH;;AACD,QAAIJ,KAAK,CAACK,aAAN,KAAwBJ,SAA5B,EAAuC;AACnCD,MAAAA,KAAK,CAACK,aAAN,GAAsB,CAAtB;AACH;;AACD,QAAIL,KAAK,CAACM,YAAN,KAAuBL,SAA3B,EAAsC;AAClCD,MAAAA,KAAK,CAACM,YAAN,GAAqB,CAArB;AACH;;AACD,QAAIN,KAAK,CAACO,QAAN,KAAmBN,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACO,QAAN,GAAiB,KAAjB;AACH;AACJ;;AAYM,WAASE,SAAT,GAA4B;AAC/B,WAAO;AACHd,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE,IAFP;AAGHC,MAAAA,eAAe,EAAE,KAHd;AAIHC,MAAAA,UAAU,EAAE,CAJT;AAKHC,MAAAA,SAAS,EAAE,GALR;AAMHC,MAAAA,SAAS,EAAE;AANR,KAAP;AAQH;;AAEM,WAASC,iBAAT,CAA2Bf,KAA3B,EAA+C;AAClD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIV,KAAK,CAACW,eAAN,KAA0BV,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACW,eAAN,GAAwB,KAAxB;AACH;;AACD,QAAIX,KAAK,CAACY,UAAN,KAAqBX,SAAzB,EAAoC;AAChCD,MAAAA,KAAK,CAACY,UAAN,GAAmB,CAAnB;AACH;;AACD,QAAIZ,KAAK,CAACa,SAAN,KAAoBZ,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACa,SAAN,GAAkB,GAAlB;AACH;;AACD,QAAIb,KAAK,CAACc,SAAN,KAAoBb,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACc,SAAN,GAAkB,GAAlB;AACH;AACJ;;AAUM,WAASE,gBAAT,GAA0C;AAC7C,WAAO;AACHrB,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE,IAFP;AAGHO,MAAAA,UAAU,EAAE,CAHT;AAIHC,MAAAA,eAAe,EAAE;AAJd,KAAP;AAMH;;AAEM,WAASC,wBAAT,CAAkCnB,KAAlC,EAA6D;AAChE,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIV,KAAK,CAACiB,UAAN,KAAqBhB,SAAzB,EAAoC;AAChCD,MAAAA,KAAK,CAACiB,UAAN,GAAmB,CAAnB;AACH;;AACD,QAAIjB,KAAK,CAACkB,eAAN,KAA0BjB,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACkB,eAAN,GAAwB,IAAxB;AACH;AACJ;;AASM,WAASE,OAAT,GAAwB;AAC3B,WAAO;AACHzB,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE,IAFP;AAGHW,MAAAA,SAAS,EAAE;AAHR,KAAP;AAKH;;AAEM,WAASC,eAAT,CAAyBtB,KAAzB,EAA2C;AAC9C,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIV,KAAK,CAACqB,SAAN,KAAoBpB,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACqB,SAAN,GAAkB,GAAlB;AACH;AACJ;;AAQM,WAASE,QAAT,GAA0B;AAC7B,WAAO;AACH5B,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,QAAQ,EAAE;AAFP,KAAP;AAIH;;AAEM,WAASc,gBAAT,CAA0BxB,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;AACJ;;AAOM,WAASe,eAAT,GAAwC;AAC3C,WAAO;AACHf,MAAAA,QAAQ,EAAE;AADP,KAAP;AAGH;;AAEM,WAASgB,uBAAT,CAAiC1B,KAAjC,EAA2D;AAC9D,QAAIA,KAAK,CAACU,QAAN,KAAmBT,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACU,QAAN,GAAiB,IAAjB;AACH;AACJ;;AAcM,WAASiB,oBAAT,GAAkD;AACrD,WAAO;AACHC,MAAAA,IAAI,EAAElC,QAAQ,EADX;AAEHmC,MAAAA,kBAAkB,EAAE,KAFjB;AAGHC,MAAAA,YAAY,EAAE,GAHX;AAIHC,MAAAA,KAAK,EAAEtB,SAAS,EAJb;AAKHuB,MAAAA,WAAW,EAAEP,eAAe,EALzB;AAMHQ,MAAAA,YAAY,EAAEjB,gBAAgB,EAN3B;AAOHkB,MAAAA,GAAG,EAAEd,OAAO,EAPT;AAQHe,MAAAA,IAAI,EAAEZ,QAAQ;AARX,KAAP;AAUH;;AAEM,WAASa,4BAAT,CAAsCpC,KAAtC,EAAqE;AACxE,QAAI,CAACA,KAAK,CAAC4B,IAAX,EAAiB;AACZ5B,MAAAA,KAAK,CAAC4B,IAAP,GAAuBlC,QAAQ,EAA/B;AACH,KAFD,MAEO;AACHK,MAAAA,gBAAgB,CAACC,KAAK,CAAC4B,IAAP,CAAhB;AACH;;AACD,QAAI5B,KAAK,CAAC6B,kBAAN,KAA6B5B,SAAjC,EAA4C;AACxCD,MAAAA,KAAK,CAAC6B,kBAAN,GAA2B,KAA3B;AACH;;AACD,QAAI7B,KAAK,CAAC8B,YAAN,KAAuB7B,SAA3B,EAAsC;AAClCD,MAAAA,KAAK,CAAC8B,YAAN,GAAqB,GAArB;AACH;;AACD,QAAI,CAAC9B,KAAK,CAAC+B,KAAX,EAAkB;AACb/B,MAAAA,KAAK,CAAC+B,KAAP,GAAyBtB,SAAS,EAAlC;AACH,KAFD,MAEO;AACHM,MAAAA,iBAAiB,CAACf,KAAK,CAAC+B,KAAP,CAAjB;AACH;;AACD,QAAI,CAAC/B,KAAK,CAACgC,WAAX,EAAwB;AACnBhC,MAAAA,KAAK,CAACgC,WAAP,GAAqCP,eAAe,EAApD;AACH,KAFD,MAEO;AACHC,MAAAA,uBAAuB,CAAC1B,KAAK,CAACgC,WAAP,CAAvB;AACH;;AACD,QAAI,CAAChC,KAAK,CAACiC,YAAX,EAAyB;AACpBjC,MAAAA,KAAK,CAACiC,YAAP,GAAuCjB,gBAAgB,EAAvD;AACH,KAFD,MAEO;AACHG,MAAAA,wBAAwB,CAACnB,KAAK,CAACiC,YAAP,CAAxB;AACH;;AACD,QAAI,CAACjC,KAAK,CAACkC,GAAX,EAAgB;AACXlC,MAAAA,KAAK,CAACkC,GAAP,GAAqBd,OAAO,EAA5B;AACH,KAFD,MAEO;AACHE,MAAAA,eAAe,CAACtB,KAAK,CAACkC,GAAP,CAAf;AACH;;AACD,QAAI,CAAClC,KAAK,CAACmC,IAAX,EAAiB;AACZnC,MAAAA,KAAK,CAACmC,IAAP,GAAuBZ,QAAQ,EAA/B;AACH,KAFD,MAEO;AACHC,MAAAA,gBAAgB,CAACxB,KAAK,CAACmC,IAAP,CAAhB;AACH;AACJ;;;cApQezC,Q;sBAOAK,gB;cAmBAG,Q;sBAWAM,gB;eA+BAC,S;uBAWAM,iB;sBA6BAC,gB;8BASAG,wB;aAsBAC,O;qBAQAE,e;cAkBAC,Q;sBAOAC,gB;qBAcAC,e;6BAMAC,uB;0BAkBAC,oB;kCAaAS;;;;;;;;AAzOcC,MAAAA,G,OAAAA,G;;;;;;AA9B9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACA;;;;;OAGM;AAAExC,QAAAA;AAAF,O,GAAkBwC,G", "sourcesContent": ["/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\n/**\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\n * The following section is auto-generated.\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\n */\n/* eslint-disable max-len */\nimport { Material, Texture2D, gfx } from 'cc';\n\nconst { SampleCount } = gfx;\n\nexport interface MSAA {\n    enabled: boolean; /* false */\n    sampleCount: gfx.SampleCount; /* SampleCount.X4 */\n    [name: string]: unknown;\n}\n\nexport function makeMSAA(): MSAA {\n    return {\n        enabled: false,\n        sampleCount: SampleCount.X4,\n    };\n}\n\nexport function fillRequiredMSAA(value: MSAA): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.sampleCount === undefined) {\n        value.sampleCount = SampleCount.X4;\n    }\n}\n\nexport interface HBAO {\n    enabled: boolean; /* false */\n    radiusScale: number; /* 1 */\n    angleBiasDegree: number; /* 10 */\n    blurSharpness: number; /* 3 */\n    aoSaturation: number; /* 1 */\n    needBlur: boolean; /* false */\n    [name: string]: unknown;\n}\n\nexport function makeHBAO(): HBAO {\n    return {\n        enabled: false,\n        radiusScale: 1,\n        angleBiasDegree: 10,\n        blurSharpness: 3,\n        aoSaturation: 1,\n        needBlur: false,\n    };\n}\n\nexport function fillRequiredHBAO(value: HBAO): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.radiusScale === undefined) {\n        value.radiusScale = 1;\n    }\n    if (value.angleBiasDegree === undefined) {\n        value.angleBiasDegree = 10;\n    }\n    if (value.blurSharpness === undefined) {\n        value.blurSharpness = 3;\n    }\n    if (value.aoSaturation === undefined) {\n        value.aoSaturation = 1;\n    }\n    if (value.needBlur === undefined) {\n        value.needBlur = false;\n    }\n}\n\nexport interface Bloom {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    enableAlphaMask: boolean; /* false */\n    iterations: number; /* 3 */\n    threshold: number; /* 0.8 */\n    intensity: number; /* 2.3 */\n    [name: string]: unknown;\n}\n\nexport function makeBloom(): Bloom {\n    return {\n        enabled: false,\n        material: null,\n        enableAlphaMask: false,\n        iterations: 3,\n        threshold: 0.8,\n        intensity: 2.3,\n    };\n}\n\nexport function fillRequiredBloom(value: Bloom): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n    if (value.enableAlphaMask === undefined) {\n        value.enableAlphaMask = false;\n    }\n    if (value.iterations === undefined) {\n        value.iterations = 3;\n    }\n    if (value.threshold === undefined) {\n        value.threshold = 0.8;\n    }\n    if (value.intensity === undefined) {\n        value.intensity = 2.3;\n    }\n}\n\nexport interface ColorGrading {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    contribute: number; /* 1 */\n    /* refcount */ colorGradingMap: Texture2D | null;\n    [name: string]: unknown;\n}\n\nexport function makeColorGrading(): ColorGrading {\n    return {\n        enabled: false,\n        material: null,\n        contribute: 1,\n        colorGradingMap: null,\n    };\n}\n\nexport function fillRequiredColorGrading(value: ColorGrading): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n    if (value.contribute === undefined) {\n        value.contribute = 1;\n    }\n    if (value.colorGradingMap === undefined) {\n        value.colorGradingMap = null;\n    }\n}\n\nexport interface FSR {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    sharpness: number; /* 0.8 */\n    [name: string]: unknown;\n}\n\nexport function makeFSR(): FSR {\n    return {\n        enabled: false,\n        material: null,\n        sharpness: 0.8,\n    };\n}\n\nexport function fillRequiredFSR(value: FSR): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n    if (value.sharpness === undefined) {\n        value.sharpness = 0.8;\n    }\n}\n\nexport interface FXAA {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    [name: string]: unknown;\n}\n\nexport function makeFXAA(): FXAA {\n    return {\n        enabled: false,\n        material: null,\n    };\n}\n\nexport function fillRequiredFXAA(value: FXAA): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n}\n\nexport interface ToneMapping {\n    /* refcount */ material: Material | null;\n    [name: string]: unknown;\n}\n\nexport function makeToneMapping(): ToneMapping {\n    return {\n        material: null,\n    };\n}\n\nexport function fillRequiredToneMapping(value: ToneMapping): void {\n    if (value.material === undefined) {\n        value.material = null;\n    }\n}\n\nexport interface PipelineSettings {\n    readonly msaa: MSAA;\n    enableShadingScale: boolean; /* false */\n    shadingScale: number; /* 0.5 */\n    readonly bloom: Bloom;\n    readonly toneMapping: ToneMapping;\n    readonly colorGrading: ColorGrading;\n    readonly fsr: FSR;\n    readonly fxaa: FXAA;\n    [name: string]: unknown;\n}\n\nexport function makePipelineSettings(): PipelineSettings {\n    return {\n        msaa: makeMSAA(),\n        enableShadingScale: false,\n        shadingScale: 0.5,\n        bloom: makeBloom(),\n        toneMapping: makeToneMapping(),\n        colorGrading: makeColorGrading(),\n        fsr: makeFSR(),\n        fxaa: makeFXAA(),\n    };\n}\n\nexport function fillRequiredPipelineSettings(value: PipelineSettings): void {\n    if (!value.msaa) {\n        (value.msaa as MSAA) = makeMSAA();\n    } else {\n        fillRequiredMSAA(value.msaa);\n    }\n    if (value.enableShadingScale === undefined) {\n        value.enableShadingScale = false;\n    }\n    if (value.shadingScale === undefined) {\n        value.shadingScale = 0.5;\n    }\n    if (!value.bloom) {\n        (value.bloom as Bloom) = makeBloom();\n    } else {\n        fillRequiredBloom(value.bloom);\n    }\n    if (!value.toneMapping) {\n        (value.toneMapping as ToneMapping) = makeToneMapping();\n    } else {\n        fillRequiredToneMapping(value.toneMapping);\n    }\n    if (!value.colorGrading) {\n        (value.colorGrading as ColorGrading) = makeColorGrading();\n    } else {\n        fillRequiredColorGrading(value.colorGrading);\n    }\n    if (!value.fsr) {\n        (value.fsr as FSR) = makeFSR();\n    } else {\n        fillRequiredFSR(value.fsr);\n    }\n    if (!value.fxaa) {\n        (value.fxaa as FXAA) = makeFXAA();\n    } else {\n        fillRequiredFXAA(value.fxaa);\n    }\n}\n"]}