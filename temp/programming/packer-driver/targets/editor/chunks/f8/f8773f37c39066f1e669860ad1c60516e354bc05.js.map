{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts"], "names": ["_decorator", "Component", "director", "UIOpacity", "tween", "ccclass", "SceneFader", "uiOpacity", "loadScene", "scene<PERSON><PERSON>", "instance", "onLoad", "node", "destroy", "addPersistRootNode", "getComponent", "console", "error", "fadeIn", "duration", "opacity", "to", "start", "fadeOut", "onComplete", "call", "onDestroy"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;;;;;;;;OACrD;AAAEC,QAAAA;AAAF,O,GAAcL,U;;4BAGPM,U,WADZD,OAAO,CAAC,YAAD,C,2BAAR,MACaC,UADb,SACgCL,SADhC,CAC0C;AAAA;AAAA;AAAA,eAE9BM,SAF8B,GAEA,IAFA;AAAA;;AAItC;AACJ;AACA;AACoB,eAATC,SAAS,CAACC,SAAD,EAAoB;AAChC,cAAIH,UAAU,CAACI,QAAf,EAAyB;AACrBJ,YAAAA,UAAU,CAACI,QAAX,CAAoBF,SAApB,CAA8BC,SAA9B;AACH,WAFD,MAEO;AACH;AACAP,YAAAA,QAAQ,CAACM,SAAT,CAAmBC,SAAnB;AACH;AACJ;;AAEDE,QAAAA,MAAM,GAAG;AACL;AACA,cAAIL,UAAU,CAACI,QAAf,EAAyB;AACrB,iBAAKE,IAAL,CAAUC,OAAV;AACA;AACH;;AAEDP,UAAAA,UAAU,CAACI,QAAX,GAAsB,IAAtB,CAPK,CASL;;AACAR,UAAAA,QAAQ,CAACY,kBAAT,CAA4B,KAAKF,IAAjC,EAVK,CAYL;;AACA,eAAKL,SAAL,GAAiB,KAAKQ,YAAL,CAAkBZ,SAAlB,CAAjB;;AACA,cAAI,CAAC,KAAKI,SAAV,EAAqB;AACjBS,YAAAA,OAAO,CAACC,KAAR,CAAc,kDAAd;AACA;AACH;;AAGD,eAAKC,MAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,MAAM,CAACC,QAAgB,GAAG,GAApB,EAAyB;AAC3B,cAAI,CAAC,KAAKZ,SAAV,EAAqB;AAErB,eAAKA,SAAL,CAAea,OAAf,GAAyB,GAAzB;AACAhB,UAAAA,KAAK,CAAC,KAAKG,SAAN,CAAL,CACKc,EADL,CACQF,QADR,EACkB;AAAEC,YAAAA,OAAO,EAAE;AAAX,WADlB,EAEKE,KAFL;AAGH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,CAACC,UAAD,EAA0BL,QAAgB,GAAG,GAA7C,EAAkD;AACrD,cAAI,CAAC,KAAKZ,SAAV,EAAqB;AACjB,gBAAIiB,UAAJ,EAAgBA,UAAU;AAC1B;AACH;;AAED,eAAKjB,SAAL,CAAea,OAAf,GAAyB,CAAzB;AACAhB,UAAAA,KAAK,CAAC,KAAKG,SAAN,CAAL,CACKc,EADL,CACQF,QADR,EACkB;AAAEC,YAAAA,OAAO,EAAE;AAAX,WADlB,EAEKK,IAFL,CAEU,MAAM;AACR,gBAAID,UAAJ,EAAgBA,UAAU;AAC7B,WAJL,EAKKF,KALL;AAMH;AAED;AACJ;AACA;;;AACId,QAAAA,SAAS,CAACC,SAAD,EAAoB;AACzB,eAAKc,OAAL,CAAa,MAAM;AACfrB,YAAAA,QAAQ,CAACM,SAAT,CAAmBC,SAAnB,EAA8B,MAAM;AAChC,mBAAKS,MAAL;AACH,aAFD;AAGH,WAJD;AAKH;;AAEDQ,QAAAA,SAAS,GAAG;AACR,cAAIpB,UAAU,CAACI,QAAX,KAAwB,IAA5B,EAAkC;AAC9BJ,YAAAA,UAAU,CAACI,QAAX,GAAsB,IAAtB;AACH;AACJ;;AApFqC,O,UACvBA,Q,GAA8B,I", "sourcesContent": ["import { _decorator, Component, Node, director, UIOpacity, tween, view, UITransform, color, Graphics, Canvas } from 'cc';\nconst { ccclass } = _decorator;\n\n@ccclass('SceneFader')\nexport class SceneFader extends Component {\n    private static instance: SceneFader | null = null;\n    private uiOpacity: UIOpacity | null = null;\n\n    /**\n     * 静态方法：使用渐变效果加载场景\n     */\n    static loadScene(sceneName: string) {\n        if (SceneFader.instance) {\n            SceneFader.instance.loadScene(sceneName);\n        } else {\n            // 如果没有实例，直接加载场景\n            director.loadScene(sceneName);\n        }\n    }\n\n    onLoad() {\n        // 确保只有一个实例\n        if (SceneFader.instance) {\n            this.node.destroy();\n            return;\n        }\n\n        SceneFader.instance = this;\n\n        // 设置为常驻节点，不会在场景切换时被销毁\n        director.addPersistRootNode(this.node);\n\n        // 从当前节点获取 UIOpacity 组件\n        this.uiOpacity = this.getComponent(UIOpacity);\n        if (!this.uiOpacity) {\n            console.error('UIOpacity component not found on SceneFader node');\n            return;\n        }\n\n\n        this.fadeIn();\n    }\n\n    /**\n     * 渐入效果（从黑屏到透明）\n     */\n    fadeIn(duration: number = 0.5) {\n        if (!this.uiOpacity) return;\n        \n        this.uiOpacity.opacity = 255;\n        tween(this.uiOpacity)\n            .to(duration, { opacity: 0 })\n            .start();\n    }\n\n    /**\n     * 渐出效果（从透明到黑屏）\n     */\n    fadeOut(onComplete?: () => void, duration: number = 0.5) {\n        if (!this.uiOpacity) {\n            if (onComplete) onComplete();\n            return;\n        }\n        \n        this.uiOpacity.opacity = 0;\n        tween(this.uiOpacity)\n            .to(duration, { opacity: 255 })\n            .call(() => {\n                if (onComplete) onComplete();\n            })\n            .start();\n    }\n\n    /**\n     * 带渐变效果的场景加载\n     */\n    loadScene(sceneName: string) {\n        this.fadeOut(() => {\n            director.loadScene(sceneName, () => {\n                this.fadeIn();\n            });\n        });\n    }\n\n    onDestroy() {\n        if (SceneFader.instance === this) {\n            SceneFader.instance = null;\n        }\n    }\n}\n"]}