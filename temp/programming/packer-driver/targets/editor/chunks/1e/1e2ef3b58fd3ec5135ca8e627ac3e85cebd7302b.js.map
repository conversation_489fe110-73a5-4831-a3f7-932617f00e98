{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/playerCar.ts"], "names": ["_decorator", "Component", "input", "Input", "KeyCode", "Vec2", "Vec3", "RigidBody2D", "ERigidBody2DType", "Contact2DType", "BoxCollider2D", "Sprite", "SpriteFrame", "tween", "AIPlayer", "GameManager", "SoundManager", "ccclass", "property", "player", "_rigidBody", "_direction", "_accel", "_angle", "_targetAngle", "_lastValidPosition", "_currentHealth", "_isDestroyed", "_originalSprite", "_destroyAnimationSpeed", "onLoad", "maxHealth", "onEnable", "on", "EventType", "KEY_DOWN", "onKeyDown", "KEY_UP", "onKeyUp", "onDisable", "off", "onDestroy", "collider", "getComponent", "BEGIN_CONTACT", "onBeginContact", "start", "node", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "type", "Dynamic", "allowSleep", "gravityScale", "linearDamping", "angularDamping", "fixedRotation", "worldPosition", "x", "y", "initAngle", "setRotationFromEuler", "sprite", "spriteFrame", "log", "event", "keyCode", "ARROW_UP", "ARROW_DOWN", "ARROW_LEFT", "instance", "playSoundEffect", "ARROW_RIGHT", "update", "deltaTime", "currentVelocity", "linearVelocity", "currentSpeed", "length", "currentPos", "turnAmount", "turnSpeed", "angleDiff", "Math", "abs", "rad", "PI", "force", "cos", "acceleration", "sin", "applyForce", "forward", "dot", "brakeForce", "clone", "multiplyScalar", "brakeDeceleration", "reverseForce", "frictionForce", "friction", "maxSpeed", "normalizedVelocity", "normalize", "distanceToLastPos", "distance", "setWorldPosition", "z", "ZERO", "init", "angle", "getRigidBody", "selfCollider", "otherCollider", "contact", "name", "otherNode", "aiPlayer", "mySpeed", "aiRigidBody", "aiSpeed", "damageFactor", "aiDamage", "round", "playerDamage", "takeDamage", "recoilForce", "boundaryDamage", "damage", "max", "gameManager", "getInstance", "syncPlayerHealth", "destroyVehicle", "destroyedSprite", "disableInput", "startDestroyAnimation", "scheduleRemoveNode", "scheduleOnce", "removeVehicleNode", "<PERSON><PERSON><PERSON><PERSON>", "removeFromParent", "to", "scale", "getCurrentHealth", "getMaxHealth", "isDestroyed", "restoreVehicle", "unschedule", "setScale"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAsBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,a,OAAAA,a;AAA8CC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AAEhMC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;wBAMjBmB,M,WADZF,OAAO,CAAC,QAAD,C,UAkBHC,QAAQ,CAACN,WAAD,C,2BAlBb,MACaO,MADb,SAC4BlB,SAD5B,CACsC;AAAA;AAAA;;AAAA;;AAEX;AAFW;;AAIP;AAJO;;AAMD;AANC;;AAQT;AARS;;AAUV;AAVU;;AAYX;AAZW;;AAeT;AAfS;;AAkBI;AAlBJ;;AAqBP;AArBO,eAuBxBmB,UAvBwB,GAuBE,IAvBF;AAAA,eAwB1BC,UAxB0B,GAwBL,CAxBK;AAwBF;AAxBE,eAyB1BC,MAzB0B,GAyBT,CAzBS;AAyBN;AAzBM,eA0B1BC,MA1B0B,GA0BT,CAAC,EA1BQ;AA0BJ;AA1BI,eA2B1BC,YA3B0B,GA2BH,CAAC,EA3BE;AA2BE;AA3BF,eA4B1BC,kBA5B0B,GA4BC,IAAIpB,IAAJ,EA5BD;AA4Ba;AAE/C;AA9BkC,eA+B1BqB,cA/B0B,GA+BD,GA/BC;AA+BI;AA/BJ,eAgC1BC,YAhC0B,GAgCF,KAhCE;AAgCK;AAhCL,eAiC1BC,eAjC0B,GAiCK,IAjCL;AAiCY;AAjCZ,eAkC1BC,sBAlC0B,GAkCO,IAlCP;AAAA;;AAkCa;AAE/CC,QAAAA,MAAM,GAAG;AACL;AACA,eAAKV,UAAL,GAAkB,IAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,CAAd;AACA,eAAKC,MAAL,GAAc,CAAC,EAAf;AACA,eAAKC,YAAL,GAAoB,CAAC,EAArB;AACA,eAAKC,kBAAL,GAA0B,IAAIpB,IAAJ,EAA1B,CAPK,CASL;;AACA,eAAKqB,cAAL,GAAsB,KAAKK,SAA3B;AACA,eAAKJ,YAAL,GAAoB,KAApB;AACH;;AAEDK,QAAAA,QAAQ,GAAG;AACP9B,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBC,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACAlC,UAAAA,KAAK,CAAC+B,EAAN,CAAS9B,KAAK,CAAC+B,SAAN,CAAgBG,MAAzB,EAAiC,KAAKC,OAAtC,EAA+C,IAA/C;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACRrC,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAAC+B,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACAlC,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAAC+B,SAAN,CAAgBG,MAA1B,EAAkC,KAAKC,OAAvC,EAAgD,IAAhD;AACH;;AAEDG,QAAAA,SAAS,GAAG;AAER;AACAvC,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAAC+B,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACAlC,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAAC+B,SAAN,CAAgBG,MAA1B,EAAkC,KAAKC,OAAvC,EAAgD,IAAhD,EAJQ,CAMR;;AACA,eAAKlB,UAAL,GAAkB,IAAlB,CAPQ,CAQR;;AACA,gBAAMsB,QAAQ,GAAG,KAAKC,YAAL,CAAkBjC,aAAlB,CAAjB;;AACA,cAAIgC,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACF,GAAT,CAAa/B,aAAa,CAACmC,aAA3B,EAA0C,KAAKC,cAA/C,EAA+D,IAA/D;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAK1B,UAAL,GAAkB,KAAKuB,YAAL,CAAkBpC,WAAlB,CAAlB;;AACA,cAAI,CAAC,KAAKa,UAAN,IAAoB,CAAC,KAAK2B,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUC,OAAjD,EAA0D;AACtDC,YAAAA,OAAO,CAACC,KAAR,CAAc,sDAAd;AACA;AACH,WANG,CAQJ;;;AACA,eAAK9B,UAAL,CAAgB+B,IAAhB,GAAuB3C,gBAAgB,CAAC4C,OAAxC;AACA,eAAKhC,UAAL,CAAgBiC,UAAhB,GAA6B,KAA7B,CAVI,CAUgC;;AACpC,eAAKjC,UAAL,CAAgBkC,YAAhB,GAA+B,CAA/B,CAXI,CAW8B;;AAClC,eAAKlC,UAAL,CAAgBmC,aAAhB,GAAgC,GAAhC,CAZI,CAYiC;;AACrC,eAAKnC,UAAL,CAAgBoC,cAAhB,GAAiC,GAAjC,CAbI,CAakC;;AACtC,eAAKpC,UAAL,CAAgBqC,aAAhB,GAAgC,IAAhC,CAdI,CAckC;AAEtC;;AACA,eAAKhC,kBAAL,GAA0B,IAAIpB,IAAJ,CAAS,KAAK0C,IAAL,CAAUW,aAAV,CAAwBC,CAAjC,EAAoC,KAAKZ,IAAL,CAAUW,aAAV,CAAwBE,CAA5D,CAA1B,CAjBI,CAmBJ;;AACA,eAAKrC,MAAL,GAAc,KAAKsC,SAAnB;AACA,eAAKrC,YAAL,GAAoB,KAAKqC,SAAzB;AACA,eAAKd,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKD,SAA1C,EAtBI,CAwBJ;;AACA,gBAAME,MAAM,GAAG,KAAKpB,YAAL,CAAkBhC,MAAlB,CAAf;;AACA,cAAIoD,MAAM,IAAIA,MAAM,CAACC,WAArB,EAAkC;AAC9B,iBAAKpC,eAAL,GAAuBmC,MAAM,CAACC,WAA9B;AACH,WA5BG,CA8BJ;;;AACA,gBAAMtB,QAAQ,GAAG,KAAKC,YAAL,CAAkBjC,aAAlB,CAAjB;;AACA,cAAIgC,QAAJ,EAAc;AACVO,YAAAA,OAAO,CAACgB,GAAR,CAAY,8CAAZ;AACAvB,YAAAA,QAAQ,CAACT,EAAT,CAAYxB,aAAa,CAACmC,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH,WAHD,MAGO;AACHI,YAAAA,OAAO,CAACC,KAAR,CAAc,mCAAd;AACH;AACJ;;AAEDd,QAAAA,SAAS,CAAC8B,KAAD,EAAuB;AAC5B,kBAAQA,KAAK,CAACC,OAAd;AACI,iBAAK/D,OAAO,CAACgE,QAAb;AACI,mBAAK9C,MAAL,GAAc,CAAd;AACA;;AACJ,iBAAKlB,OAAO,CAACiE,UAAb;AACI,mBAAK/C,MAAL,GAAc,CAAC,CAAf;AACA;;AACJ,iBAAKlB,OAAO,CAACkE,UAAb;AACI;AAAA;AAAA,gDAAaC,QAAb,CAAsBC,eAAtB,CAAsC,UAAtC;AACA,mBAAKnD,UAAL,GAAkB,CAAC,CAAnB;AACA;;AACJ,iBAAKjB,OAAO,CAACqE,WAAb;AACI;AAAA;AAAA,gDAAaF,QAAb,CAAsBC,eAAtB,CAAsC,UAAtC;AACA,mBAAKnD,UAAL,GAAkB,CAAlB;AACA;AAdR;AAgBH;;AAEDiB,QAAAA,OAAO,CAAC4B,KAAD,EAAuB;AAC1B,kBAAQA,KAAK,CAACC,OAAd;AACI,iBAAK/D,OAAO,CAACgE,QAAb;AACI,kBAAI,KAAK9C,MAAL,KAAgB,CAApB,EAAuB,KAAKA,MAAL,GAAc,CAAd;AACvB;;AACJ,iBAAKlB,OAAO,CAACiE,UAAb;AACI,kBAAI,KAAK/C,MAAL,KAAgB,CAAC,CAArB,EAAwB,KAAKA,MAAL,GAAc,CAAd;AACxB;;AACJ,iBAAKlB,OAAO,CAACkE,UAAb;AACI,kBAAI,KAAKjD,UAAL,KAAoB,CAAC,CAAzB,EAA4B,KAAKA,UAAL,GAAkB,CAAlB;AAC5B;;AACJ,iBAAKjB,OAAO,CAACqE,WAAb;AACI,kBAAI,KAAKpD,UAAL,KAAoB,CAAxB,EAA2B,KAAKA,UAAL,GAAkB,CAAlB;AAC3B;AAZR;AAcH;;AAEDqD,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC,KAAKvD,UAAN,IAAoB,CAAC,KAAK2B,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUC,OAAjD,EAA0D,OADpC,CAGtB;;AACA,cAAI,KAAKrB,YAAT,EAAuB;AACnB;AACA;AACH,WAPqB,CAStB;;;AACA,gBAAMiD,eAAe,GAAG,KAAKxD,UAAL,CAAgByD,cAAxC;AACA,gBAAMC,YAAY,GAAGF,eAAe,CAACG,MAAhB,EAArB;AACA,gBAAMC,UAAU,GAAG,IAAI3E,IAAJ,CAAS,KAAK0C,IAAL,CAAUW,aAAV,CAAwBC,CAAjC,EAAoC,KAAKZ,IAAL,CAAUW,aAAV,CAAwBE,CAA5D,CAAnB,CAZsB,CActB;;AACA,cAAI,KAAKvC,UAAL,KAAoB,CAAxB,EAA2B;AACvB,kBAAM4D,UAAU,GAAG,KAAKC,SAAL,GAAiBP,SAAjB,GAA6B,KAAKtD,UAArD;AACA,iBAAKG,YAAL,IAAqByD,UAArB;AACH,WAlBqB,CAoBtB;;;AACA,gBAAME,SAAS,GAAG,KAAK3D,YAAL,GAAoB,KAAKD,MAA3C;;AACA,cAAI6D,IAAI,CAACC,GAAL,CAASF,SAAT,IAAsB,GAA1B,EAA+B;AAC3B,iBAAK5D,MAAL,IAAe4D,SAAS,GAAG,GAA3B,CAD2B,CACK;AACnC,WAFD,MAEO;AACH,iBAAK5D,MAAL,GAAc,KAAKC,YAAnB;AACH,WA1BqB,CA4BtB;;;AACA,eAAKuB,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKvC,MAA1C,EA7BsB,CA+BtB;;AACA,cAAI,KAAKD,MAAL,KAAgB,CAApB,EAAuB;AACnB;AACA,kBAAMgE,GAAG,GAAG,CAAC,KAAK/D,MAAL,GAAc,EAAf,IAAqB6D,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,kBAAMC,KAAK,GAAG,IAAInF,IAAJ,CACV+E,IAAI,CAACK,GAAL,CAASH,GAAT,IAAgB,KAAKI,YADX,EAEVN,IAAI,CAACO,GAAL,CAASL,GAAT,IAAgB,KAAKI,YAFX,CAAd;;AAIA,iBAAKtE,UAAL,CAAgBwE,UAAhB,CAA2BJ,KAA3B,EAAkCR,UAAlC,EAA8C,IAA9C;AACH,WARD,CASA;AATA,eAUK,IAAI,KAAK1D,MAAL,KAAgB,CAAC,CAArB,EAAwB;AACzB;AACA,kBAAMgE,GAAG,GAAG,CAAC,KAAK/D,MAAL,GAAc,EAAf,IAAqB6D,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,kBAAMM,OAAO,GAAG,IAAIxF,IAAJ,CAAS+E,IAAI,CAACK,GAAL,CAASH,GAAT,CAAT,EAAwBF,IAAI,CAACO,GAAL,CAASL,GAAT,CAAxB,CAAhB;AACA,kBAAMQ,GAAG,GAAGlB,eAAe,CAACkB,GAAhB,CAAoBD,OAApB,CAAZ;;AAEA,gBAAIC,GAAG,GAAG,CAAV,EAAa;AACT;AACA,oBAAMC,UAAU,GAAGF,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKC,iBAArC,CAAnB;;AACA,mBAAK9E,UAAL,CAAgBwE,UAAhB,CAA2BG,UAA3B,EAAuCf,UAAvC,EAAmD,IAAnD;AACH,aAJD,MAIO;AACH;AACA,oBAAMmB,YAAY,GAAGN,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKP,YAAN,GAAqB,GAApD,CAArB;;AACA,mBAAKtE,UAAL,CAAgBwE,UAAhB,CAA2BO,YAA3B,EAAyCnB,UAAzC,EAAqD,IAArD;AACH;AACJ,WAfI,CAgBL;AAhBK,eAiBA;AACD;AACA,gBAAIF,YAAY,GAAG,CAAnB,EAAsB;AAClB,oBAAMsB,aAAa,GAAGxB,eAAe,CAACoB,KAAhB,GAAwBC,cAAxB,CAAuC,CAAC,KAAKI,QAAN,GAAiB,CAAxD,CAAtB,CADkB,CACgE;;AAClF,mBAAKjF,UAAL,CAAgBwE,UAAhB,CAA2BQ,aAA3B,EAA0CpB,UAA1C,EAAsD,IAAtD;AACH;AACJ,WAjEqB,CAmEtB;;;AACA,cAAIF,YAAY,GAAG,KAAKwB,QAAxB,EAAkC;AAC9B,kBAAMC,kBAAkB,GAAG3B,eAAe,CAACoB,KAAhB,GAAwBQ,SAAxB,EAA3B;AACA,iBAAKpF,UAAL,CAAgByD,cAAhB,GAAiC0B,kBAAkB,CAACN,cAAnB,CAAkC,KAAKK,QAAvC,CAAjC;AACH,WAvEqB,CAyEtB;;;AACA,cAAIxB,YAAY,GAAG,GAAnB,EAAwB;AACpB;AACA,kBAAM2B,iBAAiB,GAAGpG,IAAI,CAACqG,QAAL,CAAc1B,UAAd,EAA0B,KAAKvD,kBAA/B,CAA1B;;AACA,gBAAIgF,iBAAiB,GAAG,EAAxB,EAA4B;AAAE;AAC1B,mBAAK1D,IAAL,CAAU4D,gBAAV,CAA2B,KAAKlF,kBAAL,CAAwBkC,CAAnD,EAAsD,KAAKlC,kBAAL,CAAwBmC,CAA9E,EAAiF,KAAKb,IAAL,CAAUW,aAAV,CAAwBkD,CAAzG;AACA,mBAAKxF,UAAL,CAAgByD,cAAhB,GAAiCxE,IAAI,CAACwG,IAAtC;AACH;AACJ,WAPD,MAOO;AACH;AACA,iBAAKpF,kBAAL,GAA0BuD,UAAU,CAACgB,KAAX,EAA1B;AACH,WApFqB,CAsFtB;;;AACA,cAAIZ,IAAI,CAACC,GAAL,CAAS,KAAK9D,MAAd,IAAwB,GAA5B,EAAiC;AAC7B,iBAAKA,MAAL,GAAc,KAAKA,MAAL,GAAc,GAA5B;AACA,iBAAKC,YAAL,GAAoB,KAAKA,YAAL,GAAoB,GAAxC;AACH;AACJ;;AAEMsF,QAAAA,IAAI,CAACC,KAAD,EAAgB;AACvB,eAAKlD,SAAL,GAAiBkD,KAAjB;AACA,eAAKxF,MAAL,GAAcwF,KAAd;AACA,eAAKvF,YAAL,GAAoBuF,KAApB;AACA,eAAKhE,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCiD,KAArC;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,YAAY,GAAgB;AAC/B,iBAAO,KAAK5F,UAAZ;AACH;AAED;AACJ;AACA;;;AACIyB,QAAAA,cAAc,CAACoE,YAAD,EAA8BC,aAA9B,EAA4DC,OAA5D,EAA+F;AACzG;AAAA;AAAA,4CAAa5C,QAAb,CAAsBC,eAAtB,CAAsC,cAAtC;AACAvB,UAAAA,OAAO,CAACgB,GAAR,CAAY,gBAAZ,EAA8BiD,aAAa,CAACnE,IAAd,CAAmBqE,IAAjD,EAFyG,CAGzG;;AACA,gBAAMC,SAAS,GAAGH,aAAa,CAACnE,IAAhC;AACA,gBAAMuE,QAAQ,GAAGD,SAAS,CAAC1E,YAAV;AAAA;AAAA,mCAAjB;;AACA,cAAI2E,QAAJ,EAAc;AACVrE,YAAAA,OAAO,CAACgB,GAAR,CAAY,YAAZ,EAA0BoD,SAAS,CAACD,IAApC,EADU,CAEV;;AACA,kBAAMG,OAAO,GAAG,KAAKnG,UAAL,CAAgByD,cAAhB,CAA+BE,MAA/B,EAAhB;;AACA,kBAAMyC,WAAW,GAAGF,QAAQ,CAACvE,IAAT,CAAcJ,YAAd,CAA2BpC,WAA3B,CAApB;AACA,kBAAMkH,OAAO,GAAGD,WAAW,GAAGA,WAAW,CAAC3C,cAAZ,CAA2BE,MAA3B,EAAH,GAAyC,CAApE,CALU,CAMV;;AACA,kBAAM2C,YAAY,GAAG,GAArB,CAPU,CAOgB;;AAC1B,kBAAMC,QAAQ,GAAGvC,IAAI,CAACwC,KAAL,CAAWL,OAAO,GAAGG,YAArB,CAAjB;AACA,kBAAMG,YAAY,GAAGzC,IAAI,CAACwC,KAAL,CAAWH,OAAO,GAAGC,YAArB,CAArB,CATU,CAUV;;AACAJ,YAAAA,QAAQ,CAACQ,UAAT,CAAoBH,QAApB,EAXU,CAaV;;AACA,kBAAMI,WAAW,GAAG,IAAI1H,IAAJ,CAAS,KAAKe,UAAL,CAAgByD,cAAhB,CAA+BlB,CAAxC,EAA2C,KAAKvC,UAAL,CAAgByD,cAAhB,CAA+BjB,CAA1E,CAApB;AACAmE,YAAAA,WAAW,CAACvB,SAAZ,GAfU,CAee;;AACzBuB,YAAAA,WAAW,CAAC9B,cAAZ,CAA2B,CAACsB,OAAD,GAAW,IAAtC,EAhBU,CAgBmC;;AAC7C,iBAAKnG,UAAL,CAAgByD,cAAhB,GAAiCkD,WAAjC;AAEA,iBAAKD,UAAL,CAAgBD,YAAhB;AACH,WApBD,CAsBA;AAtBA,eAuBK;AACD,kBAAMN,OAAO,GAAG,KAAKnG,UAAL,CAAgByD,cAAhB,CAA+BE,MAA/B,EAAhB;;AACA,kBAAM2C,YAAY,GAAG,GAArB,CAFC,CAEyB;;AAC1B,kBAAMM,cAAc,GAAG5C,IAAI,CAACwC,KAAL,CAAWL,OAAO,GAAGG,YAArB,CAAvB;AACAzE,YAAAA,OAAO,CAACgB,GAAR,CAAa,mBAAkBsD,OAAQ,SAAQS,cAAe,EAA9D,EAJC,CAMD;;AACA,kBAAMD,WAAW,GAAG,IAAI1H,IAAJ,CAAS,KAAKe,UAAL,CAAgByD,cAAhB,CAA+BlB,CAAxC,EAA2C,KAAKvC,UAAL,CAAgByD,cAAhB,CAA+BjB,CAA1E,CAApB;AACAmE,YAAAA,WAAW,CAACvB,SAAZ,GARC,CAQwB;;AACzBuB,YAAAA,WAAW,CAAC9B,cAAZ,CAA2B,CAACsB,OAAD,GAAW,IAAtC,EATC,CAS4C;;AAC7C,iBAAKnG,UAAL,CAAgByD,cAAhB,GAAiCkD,WAAjC;AAEA,iBAAKD,UAAL,CAAgBE,cAAhB;AACH;AACJ,SAhTiC,CAkTlC;;AAEA;AACJ;AACA;;;AACWF,QAAAA,UAAU,CAACG,MAAD,EAAiB;AAC9B,cAAI,KAAKtG,YAAT,EAAuB;AAEvB,eAAKD,cAAL,GAAsB0D,IAAI,CAAC8C,GAAL,CAAS,CAAT,EAAY,KAAKxG,cAAL,GAAsBuG,MAAlC,CAAtB;AACAhF,UAAAA,OAAO,CAACgB,GAAR,CAAa,WAAUgE,MAAO,YAAW,KAAKvG,cAAe,EAA7D,EAJ8B,CAM9B;;AACA,gBAAMyG,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACE,gBAAZ;AACH,WAV6B,CAY9B;;;AACA,cAAI,KAAK3G,cAAL,IAAuB,CAA3B,EAA8B;AAC1B,iBAAK4G,cAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYA,QAAAA,cAAc,GAAG;AACrB,cAAI,KAAK3G,YAAT,EAAuB;AACvB;AAAA;AAAA,4CAAa4C,QAAb,CAAsBC,eAAtB,CAAsC,gBAAtC;AACA,eAAK7C,YAAL,GAAoB,IAApB;AACAsB,UAAAA,OAAO,CAACgB,GAAR,CAAY,UAAZ,EAJqB,CAMrB;;AACA,cAAI,KAAKsE,eAAT,EAA0B;AACtB,kBAAMxE,MAAM,GAAG,KAAKpB,YAAL,CAAkBhC,MAAlB,CAAf;;AACA,gBAAIoD,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,WAAP,GAAqB,KAAKuE,eAA1B;AACH;AACJ,WAZoB,CAcrB;;;AACA,eAAKC,YAAL,GAfqB,CAiBrB;;AACA,eAAKC,qBAAL,GAlBqB,CAoBrB;;AACA,gBAAMN,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB,CACb;AACA;AACH,WAzBoB,CA2BrB;AACA;;AACH;AAED;AACJ;AACA;;;AACYK,QAAAA,YAAY,GAAG;AACnBtI,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAAC+B,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACAlC,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAAC+B,SAAN,CAAgBG,MAA1B,EAAkC,KAAKC,OAAvC,EAAgD,IAAhD,EAFmB,CAInB;;AACA,eAAKjB,UAAL,GAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,CAAd;AACH;AAED;AACJ;AACA;;;AACYoH,QAAAA,kBAAkB,GAAG;AACzB,cAAI,KAAK3F,IAAL,IAAa,KAAKA,IAAL,CAAUC,OAA3B,EAAoC;AAChC,iBAAK2F,YAAL,CAAkB,MAAM;AACpB,mBAAKC,iBAAL;AACH,aAFD,EAEG,KAAKC,WAFR;AAGH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK7F,IAAL,IAAa,KAAKA,IAAL,CAAUC,OAA3B,EAAoC;AAChCC,YAAAA,OAAO,CAACgB,GAAR,CAAY,UAAZ;AACA,iBAAKlB,IAAL,CAAU+F,gBAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACYL,QAAAA,qBAAqB,GAAG;AAC5B;AACA,cAAI,KAAK1F,IAAT,EAAe;AACX;AACAlC,YAAAA,KAAK,CAAC,KAAKkC,IAAN,CAAL,CACKgG,EADL,CACQ,GADR,EACa;AACLC,cAAAA,KAAK,EAAE,IAAI1I,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CADF;AACsB;AAC3ByG,cAAAA,KAAK,EAAE,KAAKhE,IAAL,CAAUgE,KAAV,GAAkB,GAFpB,CAEwB;;AAFxB,aADb,EAKKjE,KALL;AAMH;AACJ,SA1ZiC,CA8ZlC;;AAEA;AACJ;AACA;;;AACWmG,QAAAA,gBAAgB,GAAW;AAC9B,iBAAO,KAAKvH,cAAZ;AACH;AAED;AACJ;AACA;;;AACWwH,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAKnH,SAAZ;AACH;AAED;AACJ;AACA;;;AACWoH,QAAAA,WAAW,GAAY;AAC1B,iBAAO,KAAKxH,YAAZ;AACH;AAED;AACJ;AACA;;;AACWyH,QAAAA,cAAc,GAAG;AACpB;AACA,eAAKC,UAAL,CAAgB,KAAKT,iBAArB;AAEA,eAAKjH,YAAL,GAAoB,KAApB;AACA,eAAKD,cAAL,GAAsB,KAAKK,SAA3B,CALoB,CAOpB;;AACA,cAAI,KAAKH,eAAT,EAA0B;AACtB,kBAAMmC,MAAM,GAAG,KAAKpB,YAAL,CAAkBhC,MAAlB,CAAf;;AACA,gBAAIoD,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,WAAP,GAAqB,KAAKpC,eAA1B;AACH;AACJ,WAbmB,CAepB;;;AACA,eAAKI,QAAL,GAhBoB,CAkBpB;;AACA,cAAI,KAAKe,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUuG,QAAV,CAAmB,CAAnB,EAAsB,CAAtB;AACA,iBAAKvG,IAAL,CAAUgE,KAAV,GAAkB,KAAKlD,SAAvB;AACH,WAtBmB,CAwBpB;;;AACA,cAAI,KAAKzC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgByD,cAAhB,GAAiCxE,IAAI,CAACwG,IAAtC;AACH;;AAED5D,UAAAA,OAAO,CAACgB,GAAR,CAAY,SAAZ;AACH;;AAtdiC,O,2EACjC/C,Q;;;;;iBACkB,E;;uFAClBA,Q;;;;;iBACsB,E;;4FACtBA,Q;;;;;iBAC2B,G;;oFAC3BA,Q;;;;;iBACmB,G;;mFACnBA,Q;;;;;iBACkB,G;;oFAClBA,Q;;;;;iBACmB,C;;oFAEnBA,Q;;;;;iBACmB,G;;;;;;;iBAGW,I;;sFAE9BA,Q;;;;;iBACqB,G", "sourcesContent": ["import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode, Vec2, Vec3, RigidBody2D, ERigidBody2DType, Contact2DType, Collider2D, IPhysics2DContact, BoxCollider2D, Sprite, SpriteFrame, tween } from 'cc';\nconst { ccclass, property } = _decorator;\nimport { AIPlayer } from './AIPlayer';\nimport { GameManager } from './GameManager';\nimport { SoundManager } from './SoundManager';\n\n@ccclass('player')\nexport class player extends Component {\n    @property\n    maxSpeed: number = 50; // 最大速度（像素/秒）\n    @property\n    acceleration: number = 50; // 加速度（像素/秒²）\n    @property\n    brakeDeceleration: number = 200; // 刹车减速度\n    @property\n    turnSpeed: number = 200; // 转向速度（度/秒）\n    @property\n    friction: number = 1.5; // 摩擦力系数\n    @property\n    initAngle: number = 0; // 初始角度（度），可由外部设置\n\n    @property\n    maxHealth: number = 200; // 最大生命值\n\n    @property(SpriteFrame)\n    destroyedSprite: SpriteFrame = null!; // 摧毁状态的精灵图\n\n    @property\n    removeDelay: number = 3.0; // 摧毁后移除节点的延迟时间（秒）\n\n    protected _rigidBody: RigidBody2D = null!;\n    private _direction: number = 0; // -1:左, 0:不转, 1:右\n    private _accel: number = 0; // -1:刹车, 0:无, 1:加速\n    private _angle: number = -90; // 车辆朝向角度（度）\n    private _targetAngle: number = -90; // 目标角度\n    private _lastValidPosition: Vec2 = new Vec2(); // 上次有效位置\n\n    // 生命值和摧毁相关\n    private _currentHealth: number = 100; // 当前生命值\n    private _isDestroyed: boolean = false; // 是否已摧毁\n    private _originalSprite: SpriteFrame = null!; // 原始精灵图\n    private _destroyAnimationSpeed: number = 0.95; // 摧毁动画速度衰减系数\n\n    onLoad() {\n        // 确保在组件加载时初始化\n        this._rigidBody = null!;\n        this._direction = 0;\n        this._accel = 0;\n        this._angle = -90;\n        this._targetAngle = -90;\n        this._lastValidPosition = new Vec2();\n\n        // 初始化生命值和摧毁状态\n        this._currentHealth = this.maxHealth;\n        this._isDestroyed = false;\n    }\n\n    onEnable() {\n        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);\n    }\n\n    onDisable() {\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);\n    }\n\n    onDestroy() {\n        \n        // 确保在组件销毁时清理所有事件监听\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);\n        \n        // 清理刚体引用\n        this._rigidBody = null!;\n        // 注销碰撞回调\n        const collider = this.getComponent(BoxCollider2D);\n        if (collider) {\n            collider.off(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n    }\n\n    start() {\n        // 获取刚体组件\n        this._rigidBody = this.getComponent(RigidBody2D)!;\n        if (!this._rigidBody || !this.node || !this.node.isValid) {\n            console.error('player requires RigidBody2D component and valid node');\n            return;\n        }\n\n        // 设置刚体属性\n        this._rigidBody.type = ERigidBody2DType.Dynamic;\n        this._rigidBody.allowSleep = false; // 不允许休眠\n        this._rigidBody.gravityScale = 0; // 无重力\n        this._rigidBody.linearDamping = 0.3; // 降低线性阻尼\n        this._rigidBody.angularDamping = 0.9; // 增加角阻尼防止过度旋转\n        this._rigidBody.fixedRotation = true; // 固定旋转，手动控制\n\n        // 记录初始位置\n        this._lastValidPosition = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n\n        // 设置初始角度\n        this._angle = this.initAngle;\n        this._targetAngle = this.initAngle;\n        this.node.setRotationFromEuler(0, 0, this.initAngle);\n\n        // 保存原始精灵图\n        const sprite = this.getComponent(Sprite);\n        if (sprite && sprite.spriteFrame) {\n            this._originalSprite = sprite.spriteFrame;\n        }\n\n        // 注册碰撞回调\n        const collider = this.getComponent(BoxCollider2D);\n        if (collider) {\n            console.log('BoxCollider2D component found and registered');\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        } else {\n            console.error('BoxCollider2D component not found');\n        }\n    }\n\n    onKeyDown(event: EventKeyboard) {\n        switch (event.keyCode) {\n            case KeyCode.ARROW_UP:\n                this._accel = 1;\n                break;\n            case KeyCode.ARROW_DOWN:\n                this._accel = -1;\n                break;\n            case KeyCode.ARROW_LEFT:\n                SoundManager.instance.playSoundEffect('carDrift');\n                this._direction = -1;\n                break;\n            case KeyCode.ARROW_RIGHT:\n                SoundManager.instance.playSoundEffect('carDrift');\n                this._direction = 1;\n                break;\n        }\n    }\n\n    onKeyUp(event: EventKeyboard) {\n        switch (event.keyCode) {\n            case KeyCode.ARROW_UP:\n                if (this._accel === 1) this._accel = 0;\n                break;\n            case KeyCode.ARROW_DOWN:\n                if (this._accel === -1) this._accel = 0;\n                break;\n            case KeyCode.ARROW_LEFT:\n                if (this._direction === -1) this._direction = 0;\n                break;\n            case KeyCode.ARROW_RIGHT:\n                if (this._direction === 1) this._direction = 0;\n                break;\n        }\n    }\n\n    update(deltaTime: number) {\n        if (!this._rigidBody || !this.node || !this.node.isValid) return;\n\n        // 如果车辆已摧毁，执行摧毁动画逻辑\n        if (this._isDestroyed) {\n            // this.updateDestroyAnimation();\n            return;\n        }\n\n        // 获取当前速度和位置\n        const currentVelocity = this._rigidBody.linearVelocity;\n        const currentSpeed = currentVelocity.length();\n        const currentPos = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n\n        // 更新目标角度（转向）\n        if (this._direction !== 0) {\n            const turnAmount = this.turnSpeed * deltaTime * this._direction;\n            this._targetAngle -= turnAmount;\n        }\n\n        // 平滑角度插值，防止突然转向\n        const angleDiff = this._targetAngle - this._angle;\n        if (Math.abs(angleDiff) > 0.1) {\n            this._angle += angleDiff * 0.1; // 平滑插值\n        } else {\n            this._angle = this._targetAngle;\n        }\n\n        // 设置节点旋转\n        this.node.setRotationFromEuler(0, 0, this._angle);\n\n        // 前进\n        if (this._accel === 1) {\n            // 正常加速\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const force = new Vec2(\n                Math.cos(rad) * this.acceleration,\n                Math.sin(rad) * this.acceleration\n            );\n            this._rigidBody.applyForce(force, currentPos, true);\n        }\n        // 刹车\n        else if (this._accel === -1) {\n            // 如果当前速度方向与车辆朝向一致，施加反向力（刹车）\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const forward = new Vec2(Math.cos(rad), Math.sin(rad));\n            const dot = currentVelocity.dot(forward);\n            \n            if (dot > 0) {\n                // 施加强力反向力（刹车）\n                const brakeForce = forward.clone().multiplyScalar(-this.brakeDeceleration);\n                this._rigidBody.applyForce(brakeForce, currentPos, true);\n            } else {\n                // 允许倒车\n                const reverseForce = forward.clone().multiplyScalar(-this.acceleration * 0.5);\n                this._rigidBody.applyForce(reverseForce, currentPos, true);\n            }\n        }\n        // 松开加速/刹车键\n        else {\n            // 增大摩擦力，让车辆更快停下来\n            if (currentSpeed > 1) {\n                const frictionForce = currentVelocity.clone().multiplyScalar(-this.friction * 2); // 2倍摩擦\n                this._rigidBody.applyForce(frictionForce, currentPos, true);\n            }\n        }\n\n        // 限制最大速度\n        if (currentSpeed > this.maxSpeed) {\n            const normalizedVelocity = currentVelocity.clone().normalize();\n            this._rigidBody.linearVelocity = normalizedVelocity.multiplyScalar(this.maxSpeed);\n        }\n\n        // 防止车辆卡住或异常位置\n        if (currentSpeed < 0.1) {\n            // 如果速度很小，重置到上次有效位置附近\n            const distanceToLastPos = Vec2.distance(currentPos, this._lastValidPosition);\n            if (distanceToLastPos > 50) { // 如果偏离太远\n                this.node.setWorldPosition(this._lastValidPosition.x, this._lastValidPosition.y, this.node.worldPosition.z);\n                this._rigidBody.linearVelocity = Vec2.ZERO;\n            }\n        } else {\n            // 更新有效位置\n            this._lastValidPosition = currentPos.clone();\n        }\n\n        // 防止车辆旋转过度\n        if (Math.abs(this._angle) > 360) {\n            this._angle = this._angle % 360;\n            this._targetAngle = this._targetAngle % 360;\n        }\n    }\n\n    public init(angle: number) {\n        this.initAngle = angle;\n        this._angle = angle;\n        this._targetAngle = angle;\n        this.node.setRotationFromEuler(0, 0, angle);\n    }\n\n    /**\n     * 获取玩家车辆的刚体组件\n     */\n    public getRigidBody(): RigidBody2D {\n        return this._rigidBody;\n    }\n\n    /**\n     * 玩家车辆与AI车辆碰撞时，按双方速度造成伤害\n     */\n    onBeginContact(selfCollider: BoxCollider2D, otherCollider: BoxCollider2D, contact: IPhysics2DContact | null) {\n        SoundManager.instance.playSoundEffect('carCollision');\n        console.log('玩家车辆发生碰撞，碰撞对象:', otherCollider.node.name);\n        // 判断对方是否为AI车辆\n        const otherNode = otherCollider.node;\n        const aiPlayer = otherNode.getComponent(AIPlayer);\n        if (aiPlayer) {\n            console.log('碰撞对象是AI车辆:', otherNode.name);\n            // 获取双方速度\n            const mySpeed = this._rigidBody.linearVelocity.length();\n            const aiRigidBody = aiPlayer.node.getComponent(RigidBody2D);\n            const aiSpeed = aiRigidBody ? aiRigidBody.linearVelocity.length() : 0;\n            // 伤害计算：对方受到我速度*系数的伤害，我受到对方速度*系数的伤害\n            const damageFactor = 0.5; // 可调节\n            const aiDamage = Math.round(mySpeed * damageFactor);\n            const playerDamage = Math.round(aiSpeed * damageFactor);\n            // 造成伤害\n            aiPlayer.takeDamage(aiDamage);\n            \n            // 施加反作用力\n            const recoilForce = new Vec2(this._rigidBody.linearVelocity.x, this._rigidBody.linearVelocity.y);\n            recoilForce.normalize(); // 归一化方向\n            recoilForce.multiplyScalar(-mySpeed * 0.05); // 根据速度大小施加反作用力\n            this._rigidBody.linearVelocity = recoilForce;\n            \n            this.takeDamage(playerDamage);\n        }\n        \n        // 检测与地图边界的碰撞\n        else {\n            const mySpeed = this._rigidBody.linearVelocity.length();\n            const damageFactor = 0.3; // 地图边界碰撞的伤害系数\n            const boundaryDamage = Math.round(mySpeed * damageFactor);\n            console.log(`玩家车辆与地图边界碰撞，速度: ${mySpeed}, 伤害: ${boundaryDamage}`);\n            \n            // 施加反作用力\n            const recoilForce = new Vec2(this._rigidBody.linearVelocity.x, this._rigidBody.linearVelocity.y);\n            recoilForce.normalize(); // 归一化方向\n            recoilForce.multiplyScalar(-mySpeed * 0.05); // 根据速度大小施加反作用力\n            this._rigidBody.linearVelocity = recoilForce;\n            \n            this.takeDamage(boundaryDamage);\n        }\n    }\n\n    // ==================== 生命值和摧毁系统 ====================\n\n    /**\n     * 受到伤害\n     */\n    public takeDamage(damage: number) {\n        if (this._isDestroyed) return;\n\n        this._currentHealth = Math.max(0, this._currentHealth - damage);\n        console.log(`玩家受到伤害: ${damage}, 剩余生命值: ${this._currentHealth}`);\n\n        // 同步GameManager中的玩家血量显示\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.syncPlayerHealth();\n        }\n\n        // 检查是否死亡\n        if (this._currentHealth <= 0) {\n            this.destroyVehicle();\n        }\n    }\n\n    /**\n     * 摧毁车辆\n     */\n    private destroyVehicle() {\n        if (this._isDestroyed) return;\n        SoundManager.instance.playSoundEffect('carDestruction');\n        this._isDestroyed = true;\n        console.log('玩家车辆被摧毁！');\n\n        // 切换到摧毁状态的精灵图\n        if (this.destroyedSprite) {\n            const sprite = this.getComponent(Sprite);\n            if (sprite) {\n                sprite.spriteFrame = this.destroyedSprite;\n            }\n        }\n\n        // 禁用输入控制\n        this.disableInput();\n\n        // 开始摧毁动画\n        this.startDestroyAnimation();\n\n        // 玩家摧毁时可以触发游戏结束\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            // 可以在这里调用游戏结束逻辑\n            // gameManager.gameOver(false); // false表示玩家失败\n        }\n\n        // 延迟移除节点（可选，通常玩家车辆不移除）\n        // this.scheduleRemoveNode();\n    }\n\n    /**\n     * 禁用输入控制\n     */\n    private disableInput() {\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);\n\n        // 重置控制状态\n        this._direction = 0;\n        this._accel = 0;\n    }\n\n    /**\n     * 安排移除节点（可选功能，通常玩家车辆不使用）\n     */\n    private scheduleRemoveNode() {\n        if (this.node && this.node.isValid) {\n            this.scheduleOnce(() => {\n                this.removeVehicleNode();\n            }, this.removeDelay);\n        }\n    }\n\n    /**\n     * 移除车辆节点（可选功能，通常玩家车辆不使用）\n     */\n    private removeVehicleNode() {\n        if (this.node && this.node.isValid) {\n            console.log('移除玩家车辆节点');\n            this.node.removeFromParent();\n        }\n    }\n\n    /**\n     * 开始摧毁动画\n     */\n    private startDestroyAnimation() {\n        // 使用缓动动画让车辆逐渐停止并可能添加一些视觉效果\n        if (this.node) {\n            // 可以添加旋转、缩放等效果\n            tween(this.node)\n                .to(2.0, {\n                    scale: new Vec3(1, 1, 1),  // 稍微缩小\n                    angle: this.node.angle + 180 // 旋转180度\n                })\n                .start();\n        }\n    }\n\n\n\n    // ==================== 公共方法 ====================\n\n    /**\n     * 获取当前生命值\n     */\n    public getCurrentHealth(): number {\n        return this._currentHealth;\n    }\n\n    /**\n     * 获取最大生命值\n     */\n    public getMaxHealth(): number {\n        return this.maxHealth;\n    }\n\n    /**\n     * 是否已摧毁\n     */\n    public isDestroyed(): boolean {\n        return this._isDestroyed;\n    }\n\n    /**\n     * 恢复车辆（用于重新开始游戏）\n     */\n    public restoreVehicle() {\n        // 取消移除节点的计划\n        this.unschedule(this.removeVehicleNode);\n\n        this._isDestroyed = false;\n        this._currentHealth = this.maxHealth;\n\n        // 恢复原始精灵图\n        if (this._originalSprite) {\n            const sprite = this.getComponent(Sprite);\n            if (sprite) {\n                sprite.spriteFrame = this._originalSprite;\n            }\n        }\n\n        // 重新启用输入\n        this.onEnable();\n\n        // 恢复节点状态\n        if (this.node) {\n            this.node.setScale(1, 1);\n            this.node.angle = this.initAngle;\n        }\n\n        // 重置速度\n        if (this._rigidBody) {\n            this._rigidBody.linearVelocity = Vec2.ZERO;\n        }\n\n        console.log('玩家车辆已恢复');\n    }\n}\n\n"]}