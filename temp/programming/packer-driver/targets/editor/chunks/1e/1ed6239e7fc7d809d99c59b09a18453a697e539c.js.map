{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/ConfigManager.ts"], "names": ["_decorator", "resources", "JsonAsset", "ccclass", "ConfigManager", "carConfig", "playerDataConfig", "isLoaded", "getInstance", "instance", "initialize", "Promise", "all", "loadCarConfig", "loadPlayerDataConfig", "console", "log", "error", "resolve", "reject", "load", "err", "jsonAsset", "json", "getCarProperty", "carId", "warn", "cars", "getAllCarProperties", "getDefaultPlayerData", "player<PERSON><PERSON>", "isConfigLoaded", "reloadConfigs", "getCarOverallRating", "property", "Math", "round", "speed", "steering", "durability", "getConfigVersions", "version"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;;;;;;;;OAC1B;AAAEC,QAAAA;AAAF,O,GAAcH,U;AAEpB;AACA;AACA;;AASA;AACA;AACA;;AAOA;AACA;AACA;;AAuBA;AACA;AACA;AACA;+BAEaI,a,WADZD,OAAO,CAAC,eAAD,C,2BAAR,MACaC,aADb,CAC2B;AAAA;AAAA,eAEfC,SAFe,GAEe,IAFf;AAAA,eAGfC,gBAHe,GAG6B,IAH7B;AAAA,eAIfC,QAJe,GAIK,KAJL;AAAA;;AAMvB;AACJ;AACA;AAC6B,eAAXC,WAAW,GAAkB;AACvC,cAAI,CAACJ,aAAa,CAACK,QAAnB,EAA6B;AACzBL,YAAAA,aAAa,CAACK,QAAd,GAAyB,IAAIL,aAAJ,EAAzB;AACH;;AACD,iBAAOA,aAAa,CAACK,QAArB;AACH;AAED;AACJ;AACA;;;AAC2B,cAAVC,UAAU,GAAkB;AACrC,cAAI,KAAKH,QAAT,EAAmB;AACf;AACH;;AAED,cAAI;AACA,kBAAMI,OAAO,CAACC,GAAR,CAAY,CACd,KAAKC,aAAL,EADc,EAEd,KAAKC,oBAAL,EAFc,CAAZ,CAAN;AAIA,iBAAKP,QAAL,GAAgB,IAAhB;AACAQ,YAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ;AACH,WAPD,CAOE,OAAOC,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,qCAAd,EAAqDA,KAArD;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;AACJ;AACA;;;AACYJ,QAAAA,aAAa,GAAkB;AACnC,iBAAO,IAAIF,OAAJ,CAAY,CAACO,OAAD,EAAUC,MAAV,KAAqB;AACpClB,YAAAA,SAAS,CAACmB,IAAV,CAAe,uBAAf,EAAwClB,SAAxC,EAAmD,CAACmB,GAAD,EAAMC,SAAN,KAAoB;AACnE,kBAAID,GAAJ,EAAS;AACLN,gBAAAA,OAAO,CAACE,KAAR,CAAc,uCAAd,EAAuDI,GAAvD;AACAF,gBAAAA,MAAM,CAACE,GAAD,CAAN;AACA;AACH;;AAED,mBAAKhB,SAAL,GAAiBiB,SAAS,CAACC,IAA3B;AACAR,cAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6C,KAAKX,SAAlD;AACAa,cAAAA,OAAO;AACV,aAVD;AAWH,WAZM,CAAP;AAaH;AAED;AACJ;AACA;;;AACYJ,QAAAA,oBAAoB,GAAkB;AAC1C,iBAAO,IAAIH,OAAJ,CAAY,CAACO,OAAD,EAAUC,MAAV,KAAqB;AACpClB,YAAAA,SAAS,CAACmB,IAAV,CAAe,4BAAf,EAA6ClB,SAA7C,EAAwD,CAACmB,GAAD,EAAMC,SAAN,KAAoB;AACxE,kBAAID,GAAJ,EAAS;AACLN,gBAAAA,OAAO,CAACE,KAAR,CAAc,oCAAd,EAAoDI,GAApD;AACAF,gBAAAA,MAAM,CAACE,GAAD,CAAN;AACA;AACH;;AAED,mBAAKf,gBAAL,GAAwBgB,SAAS,CAACC,IAAlC;AACAR,cAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0C,KAAKV,gBAA/C;AACAY,cAAAA,OAAO;AACV,aAVD;AAWH,WAZM,CAAP;AAaH;AAED;AACJ;AACA;;;AACWM,QAAAA,cAAc,CAACC,KAAD,EAAoC;AACrD,cAAI,CAAC,KAAKpB,SAAV,EAAqB;AACjBU,YAAAA,OAAO,CAACW,IAAR,CAAa,2BAAb;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKrB,SAAL,CAAesB,IAAf,CAAoBF,KAApB,KAA8B,IAArC;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,mBAAmB,GAAqC;AAC3D,cAAI,CAAC,KAAKvB,SAAV,EAAqB;AACjBU,YAAAA,OAAO,CAACW,IAAR,CAAa,2BAAb;AACA,mBAAO,EAAP;AACH;;AACD,iBAAO,KAAKrB,SAAL,CAAesB,IAAtB;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,oBAAoB,GAA0C;AACjE,cAAI,CAAC,KAAKvB,gBAAV,EAA4B;AACxBS,YAAAA,OAAO,CAACW,IAAR,CAAa,mCAAb;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKpB,gBAAL,CAAsBwB,UAA7B;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,cAAc,GAAY;AAC7B,iBAAO,KAAKxB,QAAZ;AACH;AAED;AACJ;AACA;;;AAC8B,cAAbyB,aAAa,GAAkB;AACxC,eAAKzB,QAAL,GAAgB,KAAhB;AACA,eAAKF,SAAL,GAAiB,IAAjB;AACA,eAAKC,gBAAL,GAAwB,IAAxB;AACA,gBAAM,KAAKI,UAAL,EAAN;AACH;AAED;AACJ;AACA;;;AACWuB,QAAAA,mBAAmB,CAACR,KAAD,EAAwB;AAC9C,gBAAMS,QAAQ,GAAG,KAAKV,cAAL,CAAoBC,KAApB,CAAjB;AACA,cAAI,CAACS,QAAL,EAAe,OAAO,CAAP;AAEf,iBAAOC,IAAI,CAACC,KAAL,CAAW,CAACF,QAAQ,CAACG,KAAT,GAAiBH,QAAQ,CAACI,QAA1B,GAAqCJ,QAAQ,CAACK,UAA/C,IAA6D,CAAxE,CAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,iBAAiB,GAAoD;AAAA;;AACxE,iBAAO;AACHnC,YAAAA,SAAS,EAAE,yBAAKA,SAAL,qCAAgBoC,OAAhB,KAA2B,SADnC;AAEHnC,YAAAA,gBAAgB,EAAE,+BAAKA,gBAAL,2CAAuBmC,OAAvB,KAAkC;AAFjD,WAAP;AAIH;;AA/IsB,O,UACRhC,Q,GAAiC,I", "sourcesContent": ["import { _decorator, resources, JsonAsset } from 'cc';\nconst { ccclass } = _decorator;\n\n/**\n * 车辆属性接口\n */\nexport interface CarProperty {\n    name: string;\n    speed: number;\n    steering: number;\n    durability: number;\n    description: string;\n}\n\n/**\n * 车辆配置接口\n */\nexport interface CarConfig {\n    cars: { [carId: string]: CarProperty };\n    version: string;\n    lastModified: string;\n}\n\n/**\n * 玩家数据接口\n */\nexport interface PlayerDataConfig {\n    playerData: {\n        coins: number;\n        unlockedCars: string[];\n        unlockedLevels: string[];\n        levelProgress: { [levelId: string]: any };\n        settings: {\n            soundEnabled: boolean;\n            musicEnabled: boolean;\n            vibrationEnabled: boolean;\n        };\n        statistics: {\n            totalPlayTime: number;\n            totalGamesPlayed: number;\n            totalWins: number;\n            totalCoinsEarned: number;\n        };\n    };\n    version: string;\n    lastModified: string;\n}\n\n/**\n * 配置管理器\n * 负责加载和管理所有JSON配置文件\n */\n@ccclass('ConfigManager')\nexport class ConfigManager {\n    private static instance: ConfigManager | null = null;\n    private carConfig: CarConfig | null = null;\n    private playerDataConfig: PlayerDataConfig | null = null;\n    private isLoaded: boolean = false;\n\n    /**\n     * 获取单例实例\n     */\n    public static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * 初始化配置管理器，加载所有配置文件\n     */\n    public async initialize(): Promise<void> {\n        if (this.isLoaded) {\n            return;\n        }\n\n        try {\n            await Promise.all([\n                this.loadCarConfig(),\n                this.loadPlayerDataConfig()\n            ]);\n            this.isLoaded = true;\n            console.log('ConfigManager initialized successfully');\n        } catch (error) {\n            console.error('Failed to initialize ConfigManager:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 加载车辆配置\n     */\n    private loadCarConfig(): Promise<void> {\n        return new Promise((resolve, reject) => {\n            resources.load('config/car_properties', JsonAsset, (err, jsonAsset) => {\n                if (err) {\n                    console.error('Failed to load car properties config:', err);\n                    reject(err);\n                    return;\n                }\n\n                this.carConfig = jsonAsset.json as CarConfig;\n                console.log('Car properties config loaded:', this.carConfig);\n                resolve();\n            });\n        });\n    }\n\n    /**\n     * 加载玩家数据配置\n     */\n    private loadPlayerDataConfig(): Promise<void> {\n        return new Promise((resolve, reject) => {\n            resources.load('config/player_data_default', JsonAsset, (err, jsonAsset) => {\n                if (err) {\n                    console.error('Failed to load player data config:', err);\n                    reject(err);\n                    return;\n                }\n\n                this.playerDataConfig = jsonAsset.json as PlayerDataConfig;\n                console.log('Player data config loaded:', this.playerDataConfig);\n                resolve();\n            });\n        });\n    }\n\n    /**\n     * 获取车辆属性\n     */\n    public getCarProperty(carId: string): CarProperty | null {\n        if (!this.carConfig) {\n            console.warn('Car config not loaded yet');\n            return null;\n        }\n        return this.carConfig.cars[carId] || null;\n    }\n\n    /**\n     * 获取所有车辆配置\n     */\n    public getAllCarProperties(): { [carId: string]: CarProperty } {\n        if (!this.carConfig) {\n            console.warn('Car config not loaded yet');\n            return {};\n        }\n        return this.carConfig.cars;\n    }\n\n    /**\n     * 获取默认玩家数据\n     */\n    public getDefaultPlayerData(): PlayerDataConfig['playerData'] | null {\n        if (!this.playerDataConfig) {\n            console.warn('Player data config not loaded yet');\n            return null;\n        }\n        return this.playerDataConfig.playerData;\n    }\n\n    /**\n     * 检查配置是否已加载\n     */\n    public isConfigLoaded(): boolean {\n        return this.isLoaded;\n    }\n\n    /**\n     * 重新加载配置（用于热更新）\n     */\n    public async reloadConfigs(): Promise<void> {\n        this.isLoaded = false;\n        this.carConfig = null;\n        this.playerDataConfig = null;\n        await this.initialize();\n    }\n\n    /**\n     * 获取车辆综合评分\n     */\n    public getCarOverallRating(carId: string): number {\n        const property = this.getCarProperty(carId);\n        if (!property) return 0;\n        \n        return Math.round((property.speed + property.steering + property.durability) / 3);\n    }\n\n    /**\n     * 获取配置版本信息\n     */\n    public getConfigVersions(): { carConfig: string; playerDataConfig: string } {\n        return {\n            carConfig: this.carConfig?.version || 'unknown',\n            playerDataConfig: this.playerDataConfig?.version || 'unknown'\n        };\n    }\n}\n"]}