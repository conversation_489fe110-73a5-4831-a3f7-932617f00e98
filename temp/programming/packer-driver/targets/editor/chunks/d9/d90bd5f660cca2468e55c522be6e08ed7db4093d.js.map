{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts"], "names": ["_decorator", "Component", "Vec2", "RigidBody2D", "Contact2DType", "Collider2D", "Enum", "instantiate", "Prefab", "tween", "Vec3", "Animation", "director", "Director", "player", "AIPlayer", "SoundManager", "GameManager", "ccclass", "property", "WeaponType", "BulletType", "Bullet", "type", "tooltip", "_shooterId", "_rigidBody", "_direction", "_velocity", "_timer", "_isExploding", "_pending<PERSON><PERSON><PERSON>", "onLoad", "getComponent", "console", "error", "collider", "on", "BEGIN_CONTACT", "onBeginContact", "start", "update", "deltaTime", "lifeTime", "bulletType", "ROCKET", "handleTimeoutExplosion", "destroyBullet", "set", "x", "speed", "y", "linearVelocity", "init", "direction", "shooterId", "normalize", "angle", "Math", "atan2", "PI", "node", "setRotationFromEuler", "selfCollider", "otherCollider", "contact", "otherNode", "otherVehicleId", "getVehicleId", "DART", "otherBullet", "playerComponent", "aiPlayerComponent", "handleVehicleHit", "handleObstacleHit", "vehicleNode", "log", "NORMAL", "handleNormalBulletHit", "handleBulletExplosion", "handleDartHit", "handleDartExplosion", "handleRocketHit", "takeDamage", "damage", "stopMovement", "createBulletExplosion", "instance", "playSoundEffect", "createDartExplosion", "hitVehicleNode", "createRocketExplosion", "dealExplosionDamage", "clearPaintInRange", "explosionPrefab", "explosion", "<PERSON><PERSON><PERSON><PERSON>", "setPosition", "ZERO", "animationComponent", "play", "scheduleOnce", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "to", "scale", "delay", "call", "allVehicles", "getAllVehiclesInRange", "for<PERSON>ach", "vehicle", "distance", "worldPosition", "explosionRadius", "damageRatio", "actualDamage", "gameManager", "getInstance", "warn", "paintManager", "getPaintManager", "explosionCenter", "removedCount", "vehicles", "playerNodes", "scene", "getComponentsInChildren", "p", "push", "aiNodes", "ai", "playerComp", "aiComp", "playHitSound", "once", "EVENT_AFTER_PHYSICS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,a,OAAAA,a;AAAkCC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,Q,OAAAA,Q;;AACrJC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U,GAE9B;;4BACYoB,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;cAMZ;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;cAMZ;;;AACAf,MAAAA,IAAI,CAACc,UAAD,CAAJ;AACAd,MAAAA,IAAI,CAACe,UAAD,CAAJ;;wBAGaC,M,WADZJ,OAAO,CAAC,QAAD,C,UAQHC,QAAQ,CAAC;AACNI,QAAAA,IAAI,EAAEF,UADA;AAENG,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRL,QAAQ,CAAC;AACNK,QAAAA,OAAO,EAAE;AADH,OAAD,C,UAKRL,QAAQ,CAAC;AACNI,QAAAA,IAAI,EAAEf,MADA;AAENgB,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRL,QAAQ,CAAC;AACNK,QAAAA,OAAO,EAAE;AADH,OAAD,C,2BAzBb,MACaF,MADb,SAC4BrB,SAD5B,CACsC;AAAA;AAAA;;AAAA;;AAEd;AAFc;;AAKd;AALc;;AAAA;;AAAA;;AAAA;;AAAA,eA6B1BwB,UA7B0B,GA6BL,EA7BK;AA6BD;AA7BC,eA8B1BC,UA9B0B,GA8BA,IA9BA;AAAA,eA+B1BC,UA/B0B,GA+BP,IAAIzB,IAAJ,CAAS,CAAT,EAAY,CAAZ,CA/BO;AA+BS;AA/BT,eAgC1B0B,SAhC0B,GAgCR,IAAI1B,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAhCQ;AAAA,eAiC1B2B,MAjC0B,GAiCT,CAjCS;AAAA,eAkC1BC,YAlC0B,GAkCF,KAlCE;AAkCK;AAlCL,eAmC1BC,eAnC0B,GAmCC,KAnCD;AAAA;;AAmCQ;AAE1CC,QAAAA,MAAM,GAAG;AACL;AACA;AACA,eAAKN,UAAL,GAAkB,KAAKO,YAAL,CAAkB9B,WAAlB,CAAlB;;AACA,cAAI,CAAC,KAAKuB,UAAV,EAAsB;AAClBQ,YAAAA,OAAO,CAACC,KAAR,CAAc,0BAAd,EADkB,CAEtB;AACC,WAPI,CAUL;;;AACA,gBAAMC,QAAQ,GAAG,KAAKH,YAAL,CAAkB5B,UAAlB,CAAjB;;AACA,cAAI+B,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACC,EAAT,CAAYjC,aAAa,CAACkC,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG,CACJ;AACA;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB;AACA,cAAI,KAAKZ,YAAT,EAAuB;AACnB;AACH;;AAED,eAAKD,MAAL,IAAea,SAAf,CANsB,CAQtB;;AACA,cAAI,KAAKb,MAAL,IAAe,KAAKc,QAAxB,EAAkC;AAC9B;AACA,gBAAI,KAAKC,UAAL,KAAoBvB,UAAU,CAACwB,MAAnC,EAA2C;AACvC,mBAAKC,sBAAL;AACA;AACH;;AACD,iBAAKC,aAAL;AACA;AACH,WAjBqB,CAmBtB;;;AACA,cAAI,KAAKrB,UAAT,EAAqB;AACjB,iBAAKE,SAAL,CAAeoB,GAAf,CAAmB,KAAKrB,UAAL,CAAgBsB,CAAhB,GAAoB,KAAKC,KAA5C,EAAmD,KAAKvB,UAAL,CAAgBwB,CAAhB,GAAoB,KAAKD,KAA5E;;AACA,iBAAKxB,UAAL,CAAgB0B,cAAhB,GAAiC,KAAKxB,SAAtC;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWyB,QAAAA,IAAI,CAACC,SAAD,EAAkBC,SAAlB,EAAqC;AAC5C,eAAK5B,UAAL,GAAkB2B,SAAS,CAACE,SAAV,EAAlB;AACA,eAAK/B,UAAL,GAAkB8B,SAAlB,CAF4C,CAIhD;AACA;AACA;;AACI,gBAAME,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWL,SAAS,CAACH,CAArB,EAAwBG,SAAS,CAACL,CAAlC,IAAuC,GAAvC,GAA6CS,IAAI,CAACE,EAAhE;AACA,eAAKC,IAAL,CAAUC,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCL,KAAK,GAAG,EAA7C,EAR4C,CAShD;AACK;AAEL;AACJ;AACA;;;AACYlB,QAAAA,cAAc,CAACwB,YAAD,EAA2BC,aAA3B,EAAsDC,OAAtD,EAAkF;AACpG,gBAAMC,SAAS,GAAGF,aAAa,CAACH,IAAhC,CADoG,CAGpG;;AACA,gBAAMM,cAAc,GAAG,KAAKC,YAAL,CAAkBF,SAAlB,CAAvB;;AACA,cAAIC,cAAc,KAAK,KAAK1C,UAA5B,EAAwC;AACpC;AACH,WAPmG,CASpG;;;AACA,cAAI,KAAKmB,UAAL,KAAoBvB,UAAU,CAACgD,IAAnC,EAAyC;AACrC,kBAAMC,WAAW,GAAGJ,SAAS,CAACjC,YAAV,CAAuBX,MAAvB,CAApB;;AACA,gBAAIgD,WAAW,IAAIA,WAAW,CAAC1B,UAAZ,KAA2BvB,UAAU,CAACgD,IAAzD,EAA+D;AAC3D;AACH;AACJ,WAfmG,CAiBpG;;;AACA,gBAAME,eAAe,GAAGL,SAAS,CAACjC,YAAV;AAAA;AAAA,+BAAxB;AACA,gBAAMuC,iBAAiB,GAAGN,SAAS,CAACjC,YAAV;AAAA;AAAA,mCAA1B;;AAEA,cAAIsC,eAAe,IAAIC,iBAAvB,EAA0C;AACtC,iBAAKC,gBAAL,CAAsBP,SAAtB,EAAiCK,eAAjC,EAAkDC,iBAAlD;AACH,WAFD,MAEO;AACH;AACA,iBAAKE,iBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,CAACE,WAAD,EAAmBJ,eAAnB,EAAmDC,iBAAnD,EAAuF;AAC3GtC,UAAAA,OAAO,CAAC0C,GAAR,CAAY,QAAZ,EAAqB,KAAKhC,UAA1B;;AACA,kBAAQ,KAAKA,UAAb;AACI,iBAAKvB,UAAU,CAACwD,MAAhB;AACI,mBAAKC,qBAAL,CAA2BP,eAA3B,EAA4CC,iBAA5C,EADJ,CAEI;;AACA,mBAAKO,qBAAL;AACA;;AAEJ,iBAAK1D,UAAU,CAACgD,IAAhB;AACI,mBAAKW,aAAL,CAAmBT,eAAnB,EAAoCC,iBAApC,EADJ,CAEI;;AACA,mBAAKS,mBAAL;AACA;;AAEJ,iBAAK5D,UAAU,CAACwB,MAAhB;AACI,mBAAKqC,eAAL,CAAqBP,WAArB,EADJ,CAEI;;AACA;AAhBR,WAF2G,CAqB3G;AACA;AAEA;AACA;;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,qBAAqB,CAACP,eAAD,EAAiCC,iBAAjC,EAAqE;AAC9F,cAAID,eAAJ,EAAqB;AACjBA,YAAAA,eAAe,CAACY,UAAhB,CAA2B,KAAKC,MAAhC;AACH,WAFD,MAEO,IAAIZ,iBAAJ,EAAuB;AAC1BA,YAAAA,iBAAiB,CAACW,UAAlB,CAA6B,KAAKC,MAAlC;AACH;AACJ;AAED;AACJ;AACA;;;AACYL,QAAAA,qBAAqB,GAAG;AAC5B;AACA,eAAKjD,YAAL,GAAoB,IAApB,CAF4B,CAI5B;;AACA,eAAKuD,YAAL,GAL4B,CAO5B;;AACA,eAAKC,qBAAL,GAR4B,CAU5B;;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,eAAtB,CAAsC,WAAtC;AACH;;AAEOR,QAAAA,aAAa,CAACT,eAAD,EAAiCC,iBAAjC,EAAqE;AACtF,cAAID,eAAJ,EAAqB;AACjBA,YAAAA,eAAe,CAACY,UAAhB,CAA2B,KAAKC,MAAhC;AACH,WAFD,MAEO,IAAIZ,iBAAJ,EAAuB;AAC1BA,YAAAA,iBAAiB,CAACW,UAAlB,CAA6B,KAAKC,MAAlC;AACH;AACJ;;AAEOH,QAAAA,mBAAmB,GAAG;AAC1B;AACA,eAAKnD,YAAL,GAAoB,IAApB,CAF0B,CAI1B;;AACA,eAAKuD,YAAL,GAL0B,CAO1B;;AACA,eAAKI,mBAAL,GAR0B,CAU1B;;AACA;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,eAAtB,CAAsC,SAAtC;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,eAAe,CAACQ,cAAD,EAAsB;AACzC;AACA,eAAK5D,YAAL,GAAoB,IAApB,CAFyC,CAIzC;;AACA,eAAKuD,YAAL,GALyC,CAOzC;;AACA,eAAKM,qBAAL,GARyC,CAUzC;;AACA,eAAKC,mBAAL,GAXyC,CAazC;;AACA,eAAKC,iBAAL,GAdyC,CAgBzC;;AACA;AAAA;AAAA,4CAAaN,QAAb,CAAsBC,eAAtB,CAAsC,WAAtC;AACH;AAED;AACJ;AACA;;;AACY1C,QAAAA,sBAAsB,GAAG;AAC7B;AACA,eAAKhB,YAAL,GAAoB,IAApB,CAF6B,CAI7B;;AACA,eAAKuD,YAAL,GAL6B,CAO7B;;AACA,eAAKM,qBAAL,GAR6B,CAU7B;;AACA,eAAKE,iBAAL,GAX6B,CAa7B;;AACA,eAAKD,mBAAL,GAd6B,CAgB7B;;AACA;AAAA;AAAA,4CAAaL,QAAb,CAAsBC,eAAtB,CAAsC,WAAtC;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,qBAAqB,GAAG;AAC5B,cAAI,KAAKQ,eAAT,EAA0B;AACtB,kBAAMC,SAAS,GAAGxF,WAAW,CAAC,KAAKuF,eAAN,CAA7B,CADsB,CAEtB;;AACA,iBAAKjC,IAAL,CAAUmC,QAAV,CAAmBD,SAAnB,EAHsB,CAItB;;AACAA,YAAAA,SAAS,CAACE,WAAV,CAAsBvF,IAAI,CAACwF,IAA3B,EALsB,CAOtB;;AACA,kBAAMC,kBAAkB,GAAGJ,SAAS,CAAC9D,YAAV,CAAuBtB,SAAvB,CAA3B;;AACA,gBAAIwF,kBAAJ,EAAwB;AACpB;AACAA,cAAAA,kBAAkB,CAACC,IAAnB,CAAwB,iBAAxB;AACA,mBAAKC,YAAL,CAAkB,MAAM;AACpB,oBAAIN,SAAS,IAAIA,SAAS,CAACO,OAA3B,EAAoC;AAChCP,kBAAAA,SAAS,CAACQ,OAAV;AACH;;AACD,qBAAKxD,aAAL;AACH,eALD,EAKG,GALH;AAMH,aATD,MASO;AACH;AACAtC,cAAAA,KAAK,CAACsF,SAAD,CAAL,CACKS,EADL,CACQ,GADR,EACa;AAAEC,gBAAAA,KAAK,EAAE,IAAI/F,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,CAAnB;AAAT,eADb,EAEKgG,KAFL,CAEW,GAFX,EAGKC,IAHL,CAGU,MAAM;AACR,oBAAIZ,SAAS,IAAIA,SAAS,CAACO,OAA3B,EAAoC;AAChCP,kBAAAA,SAAS,CAACQ,OAAV;AACH;;AACD,qBAAKxD,aAAL;AACH,eARL,EASKP,KATL;AAUH;AACJ,WA/BD,MA+BO;AACH;AACA,iBAAKO,aAAL;AACH;AACJ;;AAEO0C,QAAAA,mBAAmB,GAAG;AAC1B,cAAI,KAAKK,eAAT,EAA0B;AACtB,kBAAMC,SAAS,GAAGxF,WAAW,CAAC,KAAKuF,eAAN,CAA7B,CADsB,CAEtB;;AACA,iBAAKjC,IAAL,CAAUmC,QAAV,CAAmBD,SAAnB,EAHsB,CAItB;;AACAA,YAAAA,SAAS,CAACE,WAAV,CAAsBvF,IAAI,CAACwF,IAA3B,EALsB,CAOtB;;AACA,kBAAMC,kBAAkB,GAAGJ,SAAS,CAAC9D,YAAV,CAAuBtB,SAAvB,CAA3B;;AACA,gBAAIwF,kBAAJ,EAAwB;AACpB;AACAA,cAAAA,kBAAkB,CAACC,IAAnB,CAAwB,eAAxB;AACA,mBAAKC,YAAL,CAAkB,MAAM;AACpB,oBAAIN,SAAS,IAAIA,SAAS,CAACO,OAA3B,EAAoC;AAChCP,kBAAAA,SAAS,CAACQ,OAAV;AACH;;AACD,qBAAKxD,aAAL;AACH,eALD,EAKG,GALH;AAMH,aATD,MASO;AACH;AACAtC,cAAAA,KAAK,CAACsF,SAAD,CAAL,CACKS,EADL,CACQ,GADR,EACa;AAAEC,gBAAAA,KAAK,EAAE,IAAI/F,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,CAAnB;AAAT,eADb,EAEKgG,KAFL,CAEW,GAFX,EAGKC,IAHL,CAGU,MAAM;AACR,oBAAIZ,SAAS,IAAIA,SAAS,CAACO,OAA3B,EAAoC;AAChCP,kBAAAA,SAAS,CAACQ,OAAV;AACH;;AACD,qBAAKxD,aAAL;AACH,eARL,EASKP,KATL;AAUH;AACJ,WA/BD,MA+BO;AACH;AACA,iBAAKO,aAAL;AACH;AACJ;AAGD;AACJ;AACA;;;AACY4C,QAAAA,qBAAqB,GAAG;AAC5B,cAAI,KAAKG,eAAT,EAA0B;AACtB,kBAAMC,SAAS,GAAGxF,WAAW,CAAC,KAAKuF,eAAN,CAA7B,CADsB,CAEtB;;AACA,iBAAKjC,IAAL,CAAUmC,QAAV,CAAmBD,SAAnB,EAHsB,CAItB;;AACAA,YAAAA,SAAS,CAACE,WAAV,CAAsBvF,IAAI,CAACwF,IAA3B,EALsB,CAOtB;;AACA,kBAAMC,kBAAkB,GAAGJ,SAAS,CAAC9D,YAAV,CAAuBtB,SAAvB,CAA3B;;AACA,gBAAIwF,kBAAJ,EAAwB;AACpB;AACAA,cAAAA,kBAAkB,CAACC,IAAnB,CAAwB,WAAxB;AACA,mBAAKC,YAAL,CAAkB,MAAM;AACpB,oBAAIN,SAAS,IAAIA,SAAS,CAACO,OAA3B,EAAoC;AAChCP,kBAAAA,SAAS,CAACQ,OAAV;AACH;;AACD,qBAAKxD,aAAL;AACH,eALD,EAKG,GALH;AAMH,aATD,MASO;AACH;AACAtC,cAAAA,KAAK,CAACsF,SAAD,CAAL,CACKS,EADL,CACQ,GADR,EACa;AAAEC,gBAAAA,KAAK,EAAE,IAAI/F,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,eADb,EAEKgG,KAFL,CAEW,GAFX,EAGKC,IAHL,CAGU,MAAM;AACR,oBAAIZ,SAAS,IAAIA,SAAS,CAACO,OAA3B,EAAoC;AAChCP,kBAAAA,SAAS,CAACQ,OAAV;AACH;;AACD,qBAAKxD,aAAL;AACH,eARL,EASKP,KATL;AAUH;AACJ,WA/BD,MA+BO;AACH;AACA,iBAAKO,aAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACY6C,QAAAA,mBAAmB,GAAG;AAC1B;AACA,gBAAMgB,WAAW,GAAG,KAAKC,qBAAL,EAApB;AAEAD,UAAAA,WAAW,CAACE,OAAZ,CAAoBC,OAAO,IAAI;AAC3B,kBAAMC,QAAQ,GAAG9G,IAAI,CAAC8G,QAAL,CACb,IAAI9G,IAAJ,CAAS,KAAK2D,IAAL,CAAUoD,aAAV,CAAwBhE,CAAjC,EAAoC,KAAKY,IAAL,CAAUoD,aAAV,CAAwB9D,CAA5D,CADa,EAEb,IAAIjD,IAAJ,CAAS6G,OAAO,CAAClD,IAAR,CAAaoD,aAAb,CAA2BhE,CAApC,EAAuC8D,OAAO,CAAClD,IAAR,CAAaoD,aAAb,CAA2B9D,CAAlE,CAFa,CAAjB;;AAKA,gBAAI6D,QAAQ,IAAI,KAAKE,eAArB,EAAsC;AAClC;AACA,oBAAMC,WAAW,GAAG,IAAKH,QAAQ,GAAG,KAAKE,eAAzC;AACA,oBAAME,YAAY,GAAG,KAAKhC,MAAL,GAAc+B,WAAnC;AAEAJ,cAAAA,OAAO,CAAC5B,UAAR,CAAmBiC,YAAnB;AACH;AACJ,WAbD;AAcH;AAED;AACJ;AACA;;;AACYvB,QAAAA,iBAAiB,GAAG;AACxB;AACA,gBAAMwB,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAI,CAACD,WAAL,EAAkB;AACdnF,YAAAA,OAAO,CAACqF,IAAR,CAAa,2BAAb;AACA;AACH,WANuB,CAQxB;;;AACA,gBAAMC,YAAY,GAAGH,WAAW,CAACI,eAAZ,EAArB;;AACA,cAAI,CAACD,YAAL,EAAmB;AACftF,YAAAA,OAAO,CAACqF,IAAR,CAAa,4BAAb;AACA;AACH,WAbuB,CAexB;;;AACA,gBAAMG,eAAe,GAAG,IAAIxH,IAAJ,CAAS,KAAK2D,IAAL,CAAUoD,aAAV,CAAwBhE,CAAjC,EAAoC,KAAKY,IAAL,CAAUoD,aAAV,CAAwB9D,CAA5D,CAAxB,CAhBwB,CAkBxB;;AACA,gBAAMwE,YAAY,GAAGH,YAAY,CAAC3B,iBAAb,CAA+B6B,eAA/B,EAAgD,KAAKR,eAArD,CAArB;AAEAhF,UAAAA,OAAO,CAAC0C,GAAR,CAAa,YAAW+C,YAAa,MAArC;AACH;AAED;AACJ;AACA;;;AACYd,QAAAA,qBAAqB,GAAU;AAAA;;AACnC,gBAAMe,QAAe,GAAG,EAAxB,CADmC,CAGnC;;AACA,gBAAMC,WAAW,GAAG,0BAAKhE,IAAL,CAAUiE,KAAV,sCAAiBC,uBAAjB;AAAA;AAAA,oCAAoD,EAAxE;AACAF,UAAAA,WAAW,CAACf,OAAZ,CAAoBkB,CAAC,IAAI;AACrB,gBAAI,KAAK5D,YAAL,CAAkB4D,CAAC,CAACnE,IAApB,MAA8B,KAAKpC,UAAvC,EAAmD;AAC/CmG,cAAAA,QAAQ,CAACK,IAAT,CAAcD,CAAd;AACH;AACJ,WAJD,EALmC,CAWnC;;AACA,gBAAME,OAAO,GAAG,2BAAKrE,IAAL,CAAUiE,KAAV,uCAAiBC,uBAAjB;AAAA;AAAA,wCAAsD,EAAtE;AACAG,UAAAA,OAAO,CAACpB,OAAR,CAAgBqB,EAAE,IAAI;AAClB,gBAAI,KAAK/D,YAAL,CAAkB+D,EAAE,CAACtE,IAArB,MAA+B,KAAKpC,UAAxC,EAAoD;AAChDmG,cAAAA,QAAQ,CAACK,IAAT,CAAcE,EAAd;AACH;AACJ,WAJD;AAMA,iBAAOP,QAAP;AACH;AAED;AACJ;AACA;;;AACYlD,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK9B,UAAL,KAAoBvB,UAAU,CAACwB,MAAnC,EAA2C;AACvC;AACA,iBAAKqC,eAAL,CAAqB,IAArB;AACA;AACH;;AACD,cAAI,KAAKtC,UAAL,KAAoBvB,UAAU,CAACwD,MAAnC,EAA2C;AACvC;AACA,iBAAKE,qBAAL;AACC;AACJ;;AACD,cAAI,KAAKnC,UAAL,KAAoBvB,UAAU,CAACgD,IAAnC,EAAyC;AACrC;AACA,iBAAKY,mBAAL;AACC;AACJ,WAfuB,CAmBxB;AACA;AACA;;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,YAAY,GAAG;AACnB,cAAI,KAAK3D,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB0B,cAAhB,GAAiClD,IAAI,CAACgG,IAAtC,CADiB,CAEjB;AACA;AACH;AACJ;AAID;AACJ;AACA;;;AACY9B,QAAAA,YAAY,CAACO,WAAD,EAA2B;AAC3C,gBAAMyD,UAAU,GAAGzD,WAAW,CAAC1C,YAAZ;AAAA;AAAA,+BAAnB;AACA,gBAAMoG,MAAM,GAAG1D,WAAW,CAAC1C,YAAZ;AAAA;AAAA,mCAAf;;AAEA,cAAImG,UAAJ,EAAgB;AACZ,mBAAO,QAAP;AACH,WAFD,MAEO,IAAIC,MAAJ,EAAY;AACf,mBAAOA,MAAM,CAACjE,YAAP,GAAsBiE,MAAM,CAACjE,YAAP,EAAtB,GAA8C,YAArD;AACH;;AAED,iBAAO,SAAP;AACH;AAED;AACJ;AACA;;;AACYkE,QAAAA,YAAY,GAAG;AACnB,kBAAQ,KAAK1F,UAAb;AACI,iBAAKvB,UAAU,CAACwD,MAAhB;AACI;AAAA;AAAA,gDAAaU,QAAb,CAAsBC,eAAtB,CAAsC,WAAtC;AACA;;AACJ,iBAAKnE,UAAU,CAACgD,IAAhB;AACI;AAAA;AAAA,gDAAakB,QAAb,CAAsBC,eAAtB,CAAsC,SAAtC;AACA;;AACJ,iBAAKnE,UAAU,CAACwB,MAAhB;AACI;AAAA;AAAA,gDAAa0C,QAAb,CAAsBC,eAAtB,CAAsC,WAAtC;AACA;AATR;AAWH;AAED;AACJ;AACA;;;AACYzC,QAAAA,aAAa,GAAG;AACpB,cAAI,CAAC,KAAKc,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUyC,OAA7B,EAAsC;AAClC;AACH;;AACD,cAAI,KAAKvE,eAAT,EAA0B;AACtB;AACH;;AACD,eAAKA,eAAL,GAAuB,IAAvB,CAPoB,CAQpB;;AACAnB,UAAAA,QAAQ,CAAC2H,IAAT,CAAc1H,QAAQ,CAAC2H,mBAAvB,EAA4C,MAAM;AAC9C,gBAAI,KAAK3E,IAAL,IAAa,KAAKA,IAAL,CAAUyC,OAA3B,EAAoC;AAChC,mBAAKzC,IAAL,CAAU0C,OAAV;AACH;AACJ,WAJD;AAKH;;AAniBiC,O,wEACjCpF,Q;;;;;iBACe,E;;iFAEfA,Q;;;;;iBACgB,C;;;;;;;iBAMQE,UAAU,CAACwD,M;;;;;;;iBAKjB,G;;;;;;;iBAMO,I;;;;;;;iBAKA,G", "sourcesContent": ["import { _decorator, Component, Vec2, RigidBody2D, Contact2DType, IPhysics2DContact, Collider2D, Enum, instantiate, Prefab, tween, Vec3, Animation, director, Director } from 'cc';\nimport { player } from './player';\nimport { AIPlayer } from './AIPlayer';\nimport { SoundManager } from './SoundManager';\nimport { GameManager } from './GameManager';\n\nconst { ccclass, property } = _decorator;\n\n// 定义武器类型枚举\nexport enum WeaponType {\n    NORMAL = 0,  // 普通子弹\n    DART = 1,  // 飞镖\n    ROCKET = 2   // 火箭弹\n}\n\n// 定义子弹类型枚举\nexport enum BulletType {\n    NORMAL = 0,  // 普通子弹\n    DART = 1,  // 飞镖\n    ROCKET = 2   // 火箭弹\n}\n\n// 将枚举注册到Cocos Creator中\nEnum(WeaponType);\nEnum(BulletType);\n\n@ccclass('Bullet')\nexport class Bullet extends Component {\n    @property\n    speed: number = 50; // 子弹速度\n\n    @property\n    damage: number = 5; // 伤害值\n\n    @property({\n        type: BulletType,\n        tooltip: \"子弹类型\"\n    })\n    bulletType: BulletType = BulletType.NORMAL;\n\n    @property({\n        tooltip: \"子弹存活时间（秒）\"\n    })\n    lifeTime: number = 3.0;\n\n    @property({\n        type: Prefab,\n        tooltip: \"爆炸效果预制体（火箭弹专用）\"\n    })\n    explosionPrefab: Prefab = null!;\n\n    @property({\n        tooltip: \"爆炸范围（火箭弹专用）\"\n    })\n    explosionRadius: number = 300;\n\n    private _shooterId: string = ''; // 发射者ID\n    private _rigidBody: RigidBody2D = null!;\n    private _direction: Vec2 = new Vec2(0, 1); // 默认向上\n    private _velocity: Vec2 = new Vec2(0, 0);\n    private _timer: number = 0;\n    private _isExploding: boolean = false; // 是否正在爆炸\n    private _pendingDestroy: boolean = false; // 延迟销毁标记，避免在物理回调中立刻销毁\n\n    onLoad() {\n        // if(this.bulletType!= BulletType.FLAME)\n        // {\n        this._rigidBody = this.getComponent(RigidBody2D);\n        if (!this._rigidBody) {\n            console.error('Bullet: RigidBody2D组件未找到');\n        // }\n        }\n       \n\n        // 注册碰撞回调\n        const collider = this.getComponent(Collider2D);\n        if (collider) {\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n    }\n\n    start() {\n        // // 根据子弹类型调整属性\n        // this.adjustBulletProperties();\n    }\n\n    update(deltaTime: number) {\n        // 如果正在爆炸，则不再更新位置\n        if (this._isExploding) {\n            return;\n        }\n\n        this._timer += deltaTime;\n\n        // 检查是否超过存活时间\n        if (this._timer >= this.lifeTime) {\n            // 火箭弹超时爆炸\n            if (this.bulletType === BulletType.ROCKET) {\n                this.handleTimeoutExplosion();\n                return;\n            }\n            this.destroyBullet();\n            return;\n        }\n\n        // 更新位置\n        if (this._rigidBody) {\n            this._velocity.set(this._direction.x * this.speed, this._direction.y * this.speed);\n            this._rigidBody.linearVelocity = this._velocity;\n        }\n    }\n\n    /**\n     * 初始化子弹\n     * @param direction 子弹方向\n     * @param shooterId 发射者ID\n     */\n    public init(direction: Vec2, shooterId: string) {\n        this._direction = direction.normalize();\n        this._shooterId = shooterId;\n\n    //     if(this.bulletType!= BulletType.FLAME)\n    //     {\n    // // 设置初始旋转角度\n        const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;\n        this.node.setRotationFromEuler(0, 0, angle - 90);\n    // }\n        }    \n\n    /**\n     * 碰撞回调\n     */\n    private onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {\n        const otherNode = otherCollider.node;\n        \n        // 不与发射者碰撞\n        const otherVehicleId = this.getVehicleId(otherNode);\n        if (otherVehicleId === this._shooterId) {\n            return;\n        }\n\n        // 飞镖子弹之间不互相碰撞\n        if (this.bulletType === BulletType.DART) {\n            const otherBullet = otherNode.getComponent(Bullet);\n            if (otherBullet && otherBullet.bulletType === BulletType.DART) {\n                return;\n            }\n        }\n\n        // 检查是否碰撞到车辆\n        const playerComponent = otherNode.getComponent(player);\n        const aiPlayerComponent = otherNode.getComponent(AIPlayer);\n\n        if (playerComponent || aiPlayerComponent) {\n            this.handleVehicleHit(otherNode, playerComponent, aiPlayerComponent);\n        } else {\n            // 碰撞到其他物体（如墙壁）\n            this.handleObstacleHit();\n        }\n    }\n\n    /**\n     * 处理车辆碰撞\n     */\n    private handleVehicleHit(vehicleNode: any, playerComponent: player | null, aiPlayerComponent: AIPlayer | null) {\n        console.log('子弹撞击类型',this.bulletType);\n        switch (this.bulletType) {\n            case BulletType.NORMAL:\n                this.handleNormalBulletHit(playerComponent, aiPlayerComponent);\n                // 普通子弹碰撞后也产生爆炸效果\n                this.handleBulletExplosion();\n                return;\n\n            case BulletType.DART:\n                this.handleDartHit(playerComponent, aiPlayerComponent);\n                // 飞镖子弹碰撞后产生爆炸效果\n                this.handleDartExplosion();\n                return;\n\n            case BulletType.ROCKET:\n                this.handleRocketHit(vehicleNode);\n                // 火箭弹由 handleRocketHit 方法负责销毁，这里直接返回\n                return;\n        }\n\n        // // 播放音效\n        // this.playHitSound();\n        \n        // // 销毁子弹（仅适用于普通子弹和火焰子弹）\n        // this.destroyBullet();\n    }\n\n    /**\n     * 处理普通子弹碰撞\n     */\n    private handleNormalBulletHit(playerComponent: player | null, aiPlayerComponent: AIPlayer | null) {\n        if (playerComponent) {\n            playerComponent.takeDamage(this.damage);\n        } else if (aiPlayerComponent) {\n            aiPlayerComponent.takeDamage(this.damage);\n        }\n    }\n\n    /**\n     * 处理子弹爆炸效果（普通子弹使用）\n     */\n    private handleBulletExplosion() {\n        // 设置爆炸标志\n        this._isExploding = true;\n        \n        // 停止移动\n        this.stopMovement();\n        \n        // 创建爆炸效果\n        this.createBulletExplosion();\n        \n        // 播放音效\n        SoundManager.instance.playSoundEffect('bulletHit');\n    }\n\n    private handleDartHit(playerComponent: player | null, aiPlayerComponent: AIPlayer | null) {\n        if (playerComponent) {\n            playerComponent.takeDamage(this.damage);\n        } else if (aiPlayerComponent) {\n            aiPlayerComponent.takeDamage(this.damage);\n        }\n    }\n\n    private handleDartExplosion() {\n        // 设置爆炸标志\n        this._isExploding = true;\n        \n        // 停止移动\n        this.stopMovement();\n        \n        // 创建爆炸效果\n        this.createDartExplosion();\n        \n        // 播放音效\n        SoundManager.instance.playSoundEffect('DartHit');\n    }\n\n    /**\n     * 处理火箭弹碰撞\n     */\n    private handleRocketHit(hitVehicleNode: any) {\n        // 设置爆炸标志\n        this._isExploding = true;\n        \n        // 停止移动\n        this.stopMovement();\n        \n        // 创建爆炸效果\n        this.createRocketExplosion();\n\n        // 范围伤害\n        this.dealExplosionDamage();\n        \n        // 清除爆炸范围内的颜料\n        this.clearPaintInRange();\n        \n        // 播放音效\n        SoundManager.instance.playSoundEffect('explosion');\n    }\n\n    /**\n     * 处理火箭弹超时爆炸\n     */\n    private handleTimeoutExplosion() {\n        // 设置爆炸标志\n        this._isExploding = true;\n        \n        // 停止移动\n        this.stopMovement();\n        \n        // 创建爆炸效果\n        this.createRocketExplosion();\n\n        // 清除爆炸范围内的颜料\n        this.clearPaintInRange();\n\n        // 范围伤害\n        this.dealExplosionDamage();\n        \n        // 播放音效\n        SoundManager.instance.playSoundEffect('explosion');\n    }\n\n    /**\n     * 创建普通子弹爆炸效果\n     */\n    private createBulletExplosion() {\n        if (this.explosionPrefab) {\n            const explosion = instantiate(this.explosionPrefab);\n            // 将爆炸效果添加为子弹节点的子节点\n            this.node.addChild(explosion);\n            // 重置位置，使其与子弹节点重合\n            explosion.setPosition(Vec3.ZERO);\n\n            // 获取动画组件并播放动画\n            const animationComponent = explosion.getComponent(Animation);\n            if (animationComponent) {\n                // 播放动画并在0.3秒后销毁\n                animationComponent.play('bulletexplosion');\n                this.scheduleOnce(() => {\n                    if (explosion && explosion.isValid) {\n                        explosion.destroy();\n                    }\n                    this.destroyBullet();\n                }, 0.3);\n            } else {\n                // 如果没有动画组件，使用tween动画\n                tween(explosion)\n                    .to(0.3, { scale: new Vec3(1.5, 1.5, 1) })\n                    .delay(0.3)\n                    .call(() => {\n                        if (explosion && explosion.isValid) {\n                            explosion.destroy();\n                        }\n                        this.destroyBullet();\n                    })\n                    .start();\n            }\n        } else {\n            // 如果没有爆炸预制体，直接销毁子弹\n            this.destroyBullet();\n        }\n    }\n\n    private createDartExplosion() {\n        if (this.explosionPrefab) {\n            const explosion = instantiate(this.explosionPrefab);\n            // 将爆炸效果添加为子弹节点的子节点\n            this.node.addChild(explosion);\n            // 重置位置，使其与子弹节点重合\n            explosion.setPosition(Vec3.ZERO);\n\n            // 获取动画组件并播放动画\n            const animationComponent = explosion.getComponent(Animation);\n            if (animationComponent) {\n                // 播放动画并在0.3秒后销毁\n                animationComponent.play('dartexplosion');\n                this.scheduleOnce(() => {\n                    if (explosion && explosion.isValid) {\n                        explosion.destroy();\n                    }\n                    this.destroyBullet();\n                }, 0.3);\n            } else {\n                // 如果没有动画组件，使用tween动画\n                tween(explosion)\n                    .to(0.3, { scale: new Vec3(1.5, 1.5, 1) })\n                    .delay(0.3)\n                    .call(() => {\n                        if (explosion && explosion.isValid) {\n                            explosion.destroy();\n                        }\n                        this.destroyBullet();\n                    })\n                    .start();\n            }\n        } else {\n            // 如果没有爆炸预制体，直接销毁子弹\n            this.destroyBullet();\n        }\n    }\n\n\n    /**\n     * 创建火箭弹爆炸效果\n     */\n    private createRocketExplosion() {\n        if (this.explosionPrefab) {\n            const explosion = instantiate(this.explosionPrefab);\n            // 将爆炸效果添加为子弹节点的子节点\n            this.node.addChild(explosion);\n            // 重置位置，使其与子弹节点重合\n            explosion.setPosition(Vec3.ZERO);\n\n            // 获取动画组件并播放动画\n            const animationComponent = explosion.getComponent(Animation);\n            if (animationComponent) {\n                // 播放动画并在0.5秒后销毁\n                animationComponent.play('explosion');\n                this.scheduleOnce(() => {\n                    if (explosion && explosion.isValid) {\n                        explosion.destroy();\n                    }\n                    this.destroyBullet();\n                }, 0.5);\n            } else {\n                // 如果没有动画组件，使用tween动画\n                tween(explosion)\n                    .to(0.5, { scale: new Vec3(2, 2, 1) })\n                    .delay(0.5)\n                    .call(() => {\n                        if (explosion && explosion.isValid) {\n                            explosion.destroy();\n                        }\n                        this.destroyBullet();\n                    })\n                    .start();\n            }\n        } else {\n            // 如果没有爆炸预制体，直接销毁子弹\n            this.destroyBullet();\n        }\n    }\n\n    /**\n     * 处理爆炸范围伤害\n     */\n    private dealExplosionDamage() {\n        // 获取场景中所有车辆\n        const allVehicles = this.getAllVehiclesInRange();\n        \n        allVehicles.forEach(vehicle => {\n            const distance = Vec2.distance(\n                new Vec2(this.node.worldPosition.x, this.node.worldPosition.y),\n                new Vec2(vehicle.node.worldPosition.x, vehicle.node.worldPosition.y)\n            );\n\n            if (distance <= this.explosionRadius) {\n                // 根据距离计算伤害衰减\n                const damageRatio = 1 - (distance / this.explosionRadius);\n                const actualDamage = this.damage * damageRatio;\n                \n                vehicle.takeDamage(actualDamage);\n            }\n        });\n    }\n\n    /**\n     * 清除爆炸范围内的颜料\n     */\n    private clearPaintInRange() {\n        // 获取GameManager单例\n        const gameManager = GameManager.getInstance();\n        if (!gameManager) {\n            console.warn('Bullet: 无法获取GameManager单例');\n            return;\n        }\n\n        // 通过GameManager获取PaintManager实例\n        const paintManager = gameManager.getPaintManager();\n        if (!paintManager) {\n            console.warn('Bullet: 无法获取PaintManager实例');\n            return;\n        }\n\n        // 获取爆炸中心位置\n        const explosionCenter = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n        \n        // 使用PaintManager的公共方法清除范围内的颜料\n        const removedCount = paintManager.clearPaintInRange(explosionCenter, this.explosionRadius);\n        \n        console.log(`火箭弹爆炸清除了 ${removedCount} 个颜料`);\n    }\n\n    /**\n     * 获取范围内的所有车辆\n     */\n    private getAllVehiclesInRange(): any[] {\n        const vehicles: any[] = [];\n        \n        // 查找所有玩家车辆\n        const playerNodes = this.node.scene?.getComponentsInChildren(player) || [];\n        playerNodes.forEach(p => {\n            if (this.getVehicleId(p.node) !== this._shooterId) {\n                vehicles.push(p);\n            }\n        });\n\n        // 查找所有AI车辆\n        const aiNodes = this.node.scene?.getComponentsInChildren(AIPlayer) || [];\n        aiNodes.forEach(ai => {\n            if (this.getVehicleId(ai.node) !== this._shooterId) {\n                vehicles.push(ai);\n            }\n        });\n\n        return vehicles;\n    }\n\n    /**\n     * 处理障碍物碰撞\n     */\n    private handleObstacleHit() {\n        if (this.bulletType === BulletType.ROCKET) {\n            // 火箭弹碰撞障碍物也会爆炸\n            this.handleRocketHit(null);\n            return;\n        }\n        if (this.bulletType === BulletType.NORMAL) {\n            // 普通子弹碰撞障碍物则销毁\n            this.handleBulletExplosion();\n             return;\n        }\n        if (this.bulletType === BulletType.DART) {\n            // 镖弹碰撞障碍物则销毁\n            this.handleDartExplosion();\n             return;\n        }\n        \n\n        \n        // 播放音效并销毁普通子弹\n        // this.playHitSound();\n        // this.destroyBullet();\n    }\n\n    /**\n     * 停止移动\n     */\n    private stopMovement() {\n        if (this._rigidBody) {\n            this._rigidBody.linearVelocity = Vec2.ZERO;\n            // 同时清空_velocity，防止其他地方误用\n            // this._velocity.set(0, 0);\n        }\n    }\n\n\n\n    /**\n     * 获取车辆ID\n     */\n    private getVehicleId(vehicleNode: any): string {\n        const playerComp = vehicleNode.getComponent(player);\n        const aiComp = vehicleNode.getComponent(AIPlayer);\n        \n        if (playerComp) {\n            return 'player';\n        } else if (aiComp) {\n            return aiComp.getVehicleId ? aiComp.getVehicleId() : 'ai_unknown';\n        }\n        \n        return 'unknown';\n    }\n\n    /**\n     * 播放碰撞音效\n     */\n    private playHitSound() {\n        switch (this.bulletType) {\n            case BulletType.NORMAL:\n                SoundManager.instance.playSoundEffect('bulletHit');\n                break;\n            case BulletType.DART:\n                SoundManager.instance.playSoundEffect('dartHit');\n                break;\n            case BulletType.ROCKET:\n                SoundManager.instance.playSoundEffect('explosion');\n                break;\n        }\n    }\n\n    /**\n     * 销毁子弹\n     */\n    private destroyBullet() {\n        if (!this.node || !this.node.isValid) {\n            return;\n        }\n        if (this._pendingDestroy) {\n            return;\n        }\n        this._pendingDestroy = true;\n        // 不能在物理接触回调中直接销毁刚体/节点，延迟到本帧物理步之后\n        director.once(Director.EVENT_AFTER_PHYSICS, () => {\n            if (this.node && this.node.isValid) {\n                this.node.destroy();\n            }\n        });\n    }\n}\n"]}