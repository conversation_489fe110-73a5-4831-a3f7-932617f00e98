{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Label", "KeyCode", "input", "Input", "player", "GameManager", "ccclass", "property", "VehicleDestructionTest", "<PERSON><PERSON><PERSON><PERSON>", "aiVehicles", "onLoad", "findVehicles", "start", "bindButtonEvents", "bindKeyboardEvents", "gameManager", "getInstance", "scene", "node", "playerNodes", "getComponentsInChildren", "length", "getAIPlayers", "damagePlayerButton", "on", "EventType", "CLICK", "onDamagePlayerClick", "damageAIButton", "onDamageAIClick", "restoreAllButton", "onRestoreAllClick", "destroyPlayerButton", "onDestroyPlayerClick", "destroyAIButton", "onDestroyAIClick", "KEY_DOWN", "onKeyDown", "event", "keyCode", "DIGIT_1", "damagePlayer", "DIGIT_2", "damageRandomAI", "DIGIT_3", "destroyPlayer", "DIGIT_4", "destroyRandomAI", "DIGIT_5", "destroyRandomAIWithRemoval", "DIGIT_6", "testHealthSync", "KEY_R", "restoreAllVehicles", "damage", "isDestroyed", "takeDamage", "console", "log", "aliveAIs", "filter", "ai", "randomAI", "Math", "floor", "random", "getCurrentHealth", "getHealth", "getPlayerHP", "getPlayerMaxHP", "getMaxHealth", "syncPlayerHealth", "restoreVehicle", "for<PERSON>ach", "update", "updateUI", "player<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "health", "maxHealth", "status", "string", "aiHealthLabel", "aliveCount", "totalCount", "statusLabel", "player<PERSON><PERSON><PERSON><PERSON>", "allAIDestroyed", "every", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAC5DC,MAAAA,M,iBAAAA,M;;AAEAC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;;wCAEaW,sB,WADZF,OAAO,CAAC,wBAAD,C,UAEHC,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,2BAvBb,MACaS,sBADb,SAC4CV,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAEhB;AAFgB;;AAKpB;AALoB;;AAQtB;AARsB;;AAWd;AAXc;;AAclB;AAdkB;;AAiBhB;AAjBgB;;AAoBb;AApBa;;AAuBjB;AAvBiB,eAyB1CW,aAzB0C,GAyBlB,IAzBkB;AAAA,eA0B1CC,UA1B0C,GA0BjB,EA1BiB;AAAA;;AA4BlDC,QAAAA,MAAM,GAAG;AACL,eAAKC,YAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKC,gBAAL;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYH,QAAAA,YAAY,GAAG;AACnB;AACA,gBAAMI,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACb;AACA,kBAAME,KAAK,GAAG,KAAKC,IAAL,CAAUD,KAAxB;;AACA,gBAAIA,KAAJ,EAAW;AACP,oBAAME,WAAW,GAAGF,KAAK,CAACG,uBAAN;AAAA;AAAA,mCAApB;;AACA,kBAAID,WAAW,CAACE,MAAZ,GAAqB,CAAzB,EAA4B;AACxB,qBAAKb,aAAL,GAAqBW,WAAW,CAAC,CAAD,CAAhC;AACH,eAJM,CAMP;;;AACA,mBAAKV,UAAL,GAAkBM,WAAW,CAACO,YAAZ,EAAlB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYT,QAAAA,gBAAgB,GAAG;AACvB,cAAI,KAAKU,kBAAT,EAA6B;AACzB,iBAAKA,kBAAL,CAAwBL,IAAxB,CAA6BM,EAA7B,CAAgC1B,MAAM,CAAC2B,SAAP,CAAiBC,KAAjD,EAAwD,KAAKC,mBAA7D,EAAkF,IAAlF;AACH;;AAED,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBV,IAApB,CAAyBM,EAAzB,CAA4B1B,MAAM,CAAC2B,SAAP,CAAiBC,KAA7C,EAAoD,KAAKG,eAAzD,EAA0E,IAA1E;AACH;;AAED,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBZ,IAAtB,CAA2BM,EAA3B,CAA8B1B,MAAM,CAAC2B,SAAP,CAAiBC,KAA/C,EAAsD,KAAKK,iBAA3D,EAA8E,IAA9E;AACH;;AAED,cAAI,KAAKC,mBAAT,EAA8B;AAC1B,iBAAKA,mBAAL,CAAyBd,IAAzB,CAA8BM,EAA9B,CAAiC1B,MAAM,CAAC2B,SAAP,CAAiBC,KAAlD,EAAyD,KAAKO,oBAA9D,EAAoF,IAApF;AACH;;AAED,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBhB,IAArB,CAA0BM,EAA1B,CAA6B1B,MAAM,CAAC2B,SAAP,CAAiBC,KAA9C,EAAqD,KAAKS,gBAA1D,EAA4E,IAA5E;AACH;AACJ;AAED;AACJ;AACA;;;AACYrB,QAAAA,kBAAkB,GAAG;AACzBb,UAAAA,KAAK,CAACuB,EAAN,CAAStB,KAAK,CAACuB,SAAN,CAAgBW,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,SAAS,CAACC,KAAD,EAAuB;AACpC,kBAAQA,KAAK,CAACC,OAAd;AACI,iBAAKvC,OAAO,CAACwC,OAAb;AAAsB;AAClB,mBAAKC,YAAL,CAAkB,EAAlB;AACA;;AACJ,iBAAKzC,OAAO,CAAC0C,OAAb;AAAsB;AAClB,mBAAKC,cAAL,CAAoB,EAApB;AACA;;AACJ,iBAAK3C,OAAO,CAAC4C,OAAb;AAAsB;AAClB,mBAAKC,aAAL;AACA;;AACJ,iBAAK7C,OAAO,CAAC8C,OAAb;AAAsB;AAClB,mBAAKC,eAAL;AACA;;AACJ,iBAAK/C,OAAO,CAACgD,OAAb;AAAsB;AAClB,mBAAKC,0BAAL;AACA;;AACJ,iBAAKjD,OAAO,CAACkD,OAAb;AAAsB;AAClB,mBAAKC,cAAL;AACA;;AACJ,iBAAKnD,OAAO,CAACoD,KAAb;AAAoB;AAChB,mBAAKC,kBAAL;AACA;AArBR;AAuBH;AAED;AACJ;AACA;;;AACY1B,QAAAA,mBAAmB,GAAG;AAC1B,eAAKc,YAAL,CAAkB,EAAlB;AACH;AAED;AACJ;AACA;;;AACYZ,QAAAA,eAAe,GAAG;AACtB,eAAKc,cAAL,CAAoB,EAApB;AACH;AAED;AACJ;AACA;;;AACYZ,QAAAA,iBAAiB,GAAG;AACxB,eAAKsB,kBAAL;AACH;AAED;AACJ;AACA;;;AACYpB,QAAAA,oBAAoB,GAAG;AAC3B,eAAKY,aAAL;AACH;AAED;AACJ;AACA;;;AACYV,QAAAA,gBAAgB,GAAG;AACvB,eAAKY,eAAL;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,YAAY,CAACa,MAAD,EAAiB;AACjC,cAAI,KAAK9C,aAAL,IAAsB,CAAC,KAAKA,aAAL,CAAmB+C,WAAnB,EAA3B,EAA6D;AACzD,iBAAK/C,aAAL,CAAmBgD,UAAnB,CAA8BF,MAA9B;AACAG,YAAAA,OAAO,CAACC,GAAR,CAAa,QAAOJ,MAAO,KAA3B;AACH;AACJ;AAED;AACJ;AACA;;;AACYX,QAAAA,cAAc,CAACW,MAAD,EAAiB;AACnC,gBAAMK,QAAQ,GAAG,KAAKlD,UAAL,CAAgBmD,MAAhB,CAAuBC,EAAE,IAAI,CAACA,EAAE,CAACN,WAAH,EAA9B,CAAjB;;AACA,cAAII,QAAQ,CAACtC,MAAT,GAAkB,CAAtB,EAAyB;AACrB,kBAAMyC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBN,QAAQ,CAACtC,MAApC,CAAD,CAAzB;AACAyC,YAAAA,QAAQ,CAACN,UAAT,CAAoBF,MAApB;AACAG,YAAAA,OAAO,CAACC,GAAR,CAAa,UAASJ,MAAO,KAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACYT,QAAAA,aAAa,GAAG;AACpB,cAAI,KAAKrC,aAAL,IAAsB,CAAC,KAAKA,aAAL,CAAmB+C,WAAnB,EAA3B,EAA6D;AACzD,iBAAK/C,aAAL,CAAmBgD,UAAnB,CAA8B,KAAKhD,aAAL,CAAmB0D,gBAAnB,EAA9B;AACAT,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYX,QAAAA,eAAe,GAAG;AACtB,gBAAMY,QAAQ,GAAG,KAAKlD,UAAL,CAAgBmD,MAAhB,CAAuBC,EAAE,IAAI,CAACA,EAAE,CAACN,WAAH,EAA9B,CAAjB;;AACA,cAAII,QAAQ,CAACtC,MAAT,GAAkB,CAAtB,EAAyB;AACrB,kBAAMyC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBN,QAAQ,CAACtC,MAApC,CAAD,CAAzB;AACAyC,YAAAA,QAAQ,CAACN,UAAT,CAAoBM,QAAQ,CAACK,SAAT,EAApB;AACAV,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYT,QAAAA,0BAA0B,GAAG;AACjC,gBAAMU,QAAQ,GAAG,KAAKlD,UAAL,CAAgBmD,MAAhB,CAAuBC,EAAE,IAAI,CAACA,EAAE,CAACN,WAAH,EAA9B,CAAjB;;AACA,cAAII,QAAQ,CAACtC,MAAT,GAAkB,CAAtB,EAAyB;AACrB,kBAAMyC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBN,QAAQ,CAACtC,MAApC,CAAD,CAAzB;AACAyC,YAAAA,QAAQ,CAACN,UAAT,CAAoBM,QAAQ,CAACK,SAAT,EAApB;AACAV,YAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYP,QAAAA,cAAc,GAAG;AACrB,gBAAMpC,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAW,IAAI,KAAKP,aAAxB,EAAuC;AACnCiD,YAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACAD,YAAAA,OAAO,CAACC,GAAR,CAAa,sBAAqB3C,WAAW,CAACqD,WAAZ,EAA0B,IAAGrD,WAAW,CAACsD,cAAZ,EAA6B,EAA5F;AACAZ,YAAAA,OAAO,CAACC,GAAR,CAAa,iBAAgB,KAAKlD,aAAL,CAAmB0D,gBAAnB,EAAsC,IAAG,KAAK1D,aAAL,CAAmB8D,YAAnB,EAAkC,EAAxG,EAHmC,CAKnC;;AACAvD,YAAAA,WAAW,CAACwD,gBAAZ;AACAd,YAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACAD,YAAAA,OAAO,CAACC,GAAR,CAAa,sBAAqB3C,WAAW,CAACqD,WAAZ,EAA0B,IAAGrD,WAAW,CAACsD,cAAZ,EAA6B,EAA5F;AACH;AACJ;AAED;AACJ;AACA;;;AACYhB,QAAAA,kBAAkB,GAAG;AACzB;AACA,cAAI,KAAK7C,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBgE,cAAnB;AACH,WAJwB,CAMzB;;;AACA,eAAK/D,UAAL,CAAgBgE,OAAhB,CAAwBZ,EAAE,IAAI;AAC1BA,YAAAA,EAAE,CAACW,cAAH;AACH,WAFD;AAIAf,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH;;AAEDgB,QAAAA,MAAM,GAAG;AACL,eAAKC,QAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,QAAQ,GAAG;AACf;AACA,cAAI,KAAKC,iBAAL,IAA0B,KAAKpE,aAAnC,EAAkD;AAC9C,kBAAMqE,MAAM,GAAG,KAAKrE,aAAL,CAAmB0D,gBAAnB,EAAf;AACA,kBAAMY,SAAS,GAAG,KAAKtE,aAAL,CAAmB8D,YAAnB,EAAlB;AACA,kBAAMS,MAAM,GAAG,KAAKvE,aAAL,CAAmB+C,WAAnB,KAAmC,QAAnC,GAA8C,EAA7D;AACA,iBAAKqB,iBAAL,CAAuBI,MAAvB,GAAiC,SAAQH,MAAO,IAAGC,SAAU,GAAEC,MAAO,EAAtE;AACH,WAPc,CASf;;;AACA,cAAI,KAAKE,aAAT,EAAwB;AACpB,kBAAMC,UAAU,GAAG,KAAKzE,UAAL,CAAgBmD,MAAhB,CAAuBC,EAAE,IAAI,CAACA,EAAE,CAACN,WAAH,EAA9B,EAAgDlC,MAAnE;AACA,kBAAM8D,UAAU,GAAG,KAAK1E,UAAL,CAAgBY,MAAnC;AACA,iBAAK4D,aAAL,CAAmBD,MAAnB,GAA6B,SAAQE,UAAW,IAAGC,UAAW,KAA9D;AACH,WAdc,CAgBf;;;AACA,cAAI,KAAKC,WAAT,EAAsB;AAClB,kBAAMC,eAAe,GAAG,KAAK7E,aAAL,GAAqB,KAAKA,aAAL,CAAmB+C,WAAnB,EAArB,GAAwD,KAAhF;AACA,kBAAM+B,cAAc,GAAG,KAAK7E,UAAL,CAAgBY,MAAhB,GAAyB,CAAzB,IAA8B,KAAKZ,UAAL,CAAgB8E,KAAhB,CAAsB1B,EAAE,IAAIA,EAAE,CAACN,WAAH,EAA5B,CAArD;AAEA,gBAAIwB,MAAM,GAAG,OAAb;;AACA,gBAAIM,eAAJ,EAAqB;AACjBN,cAAAA,MAAM,GAAG,OAAT;AACH,aAFD,MAEO,IAAIO,cAAJ,EAAoB;AACvBP,cAAAA,MAAM,GAAG,SAAT;AACH;;AAED,iBAAKK,WAAL,CAAiBJ,MAAjB,GAA2B,OAAMD,MAAO,EAAxC;AACH;AACJ;;AAEDS,QAAAA,SAAS,GAAG;AACR;AACAvF,UAAAA,KAAK,CAACwF,GAAN,CAAUvF,KAAK,CAACuB,SAAN,CAAgBW,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;;AAEA,cAAI,KAAKd,kBAAT,EAA6B;AACzB,iBAAKA,kBAAL,CAAwBL,IAAxB,CAA6BuE,GAA7B,CAAiC3F,MAAM,CAAC2B,SAAP,CAAiBC,KAAlD,EAAyD,KAAKC,mBAA9D,EAAmF,IAAnF;AACH;;AACD,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBV,IAApB,CAAyBuE,GAAzB,CAA6B3F,MAAM,CAAC2B,SAAP,CAAiBC,KAA9C,EAAqD,KAAKG,eAA1D,EAA2E,IAA3E;AACH;;AACD,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBZ,IAAtB,CAA2BuE,GAA3B,CAA+B3F,MAAM,CAAC2B,SAAP,CAAiBC,KAAhD,EAAuD,KAAKK,iBAA5D,EAA+E,IAA/E;AACH;;AACD,cAAI,KAAKC,mBAAT,EAA8B;AAC1B,iBAAKA,mBAAL,CAAyBd,IAAzB,CAA8BuE,GAA9B,CAAkC3F,MAAM,CAAC2B,SAAP,CAAiBC,KAAnD,EAA0D,KAAKO,oBAA/D,EAAqF,IAArF;AACH;;AACD,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBhB,IAArB,CAA0BuE,GAA1B,CAA8B3F,MAAM,CAAC2B,SAAP,CAAiBC,KAA/C,EAAsD,KAAKS,gBAA3D,EAA6E,IAA7E;AACH;AACJ;;AA9SiD,O;;;;;iBAEvB,I;;;;;;;iBAGJ,I;;;;;;;iBAGF,I;;;;;;;iBAGQ,I;;;;;;;iBAGJ,I;;;;;;;iBAGE,I;;;;;;;iBAGG,I;;;;;;;iBAGJ,I", "sourcesContent": ["import { _decorator, Component, Node, Button, Label, KeyCode, input, Input, EventKeyboard } from 'cc';\nimport { player } from './player';\nimport { AIPlayer } from './AIPlayer';\nimport { GameManager } from './GameManager';\nconst { ccclass, property } = _decorator;\n\n/**\n * 车辆摧毁系统测试组件\n * 用于测试玩家车辆和AI车辆的摧毁功能\n */\n@ccclass('VehicleDestructionTest')\nexport class VehicleDestructionTest extends Component {\n    @property(Label)\n    playerHealthLabel: Label = null!; // 玩家血量显示\n\n    @property(Label)\n    aiHealthLabel: Label = null!; // AI血量显示\n\n    @property(Label)\n    statusLabel: Label = null!; // 状态显示\n\n    @property(Button)\n    damagePlayerButton: Button = null!; // 伤害玩家按钮\n\n    @property(Button)\n    damageAIButton: Button = null!; // 伤害AI按钮\n\n    @property(Button)\n    restoreAllButton: Button = null!; // 恢复所有车辆按钮\n\n    @property(Button)\n    destroyPlayerButton: Button = null!; // 直接摧毁玩家按钮\n\n    @property(Button)\n    destroyAIButton: Button = null!; // 直接摧毁AI按钮\n\n    private playerVehicle: player = null!;\n    private aiVehicles: AIPlayer[] = [];\n\n    onLoad() {\n        this.findVehicles();\n    }\n\n    start() {\n        this.bindButtonEvents();\n        this.bindKeyboardEvents();\n    }\n\n    /**\n     * 查找场景中的车辆\n     */\n    private findVehicles() {\n        // 查找玩家车辆\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            // 通过GameManager获取玩家车辆\n            const scene = this.node.scene;\n            if (scene) {\n                const playerNodes = scene.getComponentsInChildren(player);\n                if (playerNodes.length > 0) {\n                    this.playerVehicle = playerNodes[0];\n                }\n\n                // 获取AI车辆\n                this.aiVehicles = gameManager.getAIPlayers();\n            }\n        }\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        if (this.damagePlayerButton) {\n            this.damagePlayerButton.node.on(Button.EventType.CLICK, this.onDamagePlayerClick, this);\n        }\n\n        if (this.damageAIButton) {\n            this.damageAIButton.node.on(Button.EventType.CLICK, this.onDamageAIClick, this);\n        }\n\n        if (this.restoreAllButton) {\n            this.restoreAllButton.node.on(Button.EventType.CLICK, this.onRestoreAllClick, this);\n        }\n\n        if (this.destroyPlayerButton) {\n            this.destroyPlayerButton.node.on(Button.EventType.CLICK, this.onDestroyPlayerClick, this);\n        }\n\n        if (this.destroyAIButton) {\n            this.destroyAIButton.node.on(Button.EventType.CLICK, this.onDestroyAIClick, this);\n        }\n    }\n\n    /**\n     * 绑定键盘事件\n     */\n    private bindKeyboardEvents() {\n        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n    }\n\n    /**\n     * 键盘按下事件\n     */\n    private onKeyDown(event: EventKeyboard) {\n        switch (event.keyCode) {\n            case KeyCode.DIGIT_1: // 1键伤害玩家\n                this.damagePlayer(20);\n                break;\n            case KeyCode.DIGIT_2: // 2键伤害AI\n                this.damageRandomAI(20);\n                break;\n            case KeyCode.DIGIT_3: // 3键摧毁玩家\n                this.destroyPlayer();\n                break;\n            case KeyCode.DIGIT_4: // 4键摧毁AI\n                this.destroyRandomAI();\n                break;\n            case KeyCode.DIGIT_5: // 5键摧毁AI并测试移除\n                this.destroyRandomAIWithRemoval();\n                break;\n            case KeyCode.DIGIT_6: // 6键测试血量同步\n                this.testHealthSync();\n                break;\n            case KeyCode.KEY_R: // R键恢复所有\n                this.restoreAllVehicles();\n                break;\n        }\n    }\n\n    /**\n     * 伤害玩家按钮点击\n     */\n    private onDamagePlayerClick() {\n        this.damagePlayer(20);\n    }\n\n    /**\n     * 伤害AI按钮点击\n     */\n    private onDamageAIClick() {\n        this.damageRandomAI(20);\n    }\n\n    /**\n     * 恢复所有车辆按钮点击\n     */\n    private onRestoreAllClick() {\n        this.restoreAllVehicles();\n    }\n\n    /**\n     * 摧毁玩家按钮点击\n     */\n    private onDestroyPlayerClick() {\n        this.destroyPlayer();\n    }\n\n    /**\n     * 摧毁AI按钮点击\n     */\n    private onDestroyAIClick() {\n        this.destroyRandomAI();\n    }\n\n    /**\n     * 对玩家造成伤害\n     */\n    private damagePlayer(damage: number) {\n        if (this.playerVehicle && !this.playerVehicle.isDestroyed()) {\n            this.playerVehicle.takeDamage(damage);\n            console.log(`对玩家造成${damage}点伤害`);\n        }\n    }\n\n    /**\n     * 对随机AI造成伤害\n     */\n    private damageRandomAI(damage: number) {\n        const aliveAIs = this.aiVehicles.filter(ai => !ai.isDestroyed());\n        if (aliveAIs.length > 0) {\n            const randomAI = aliveAIs[Math.floor(Math.random() * aliveAIs.length)];\n            randomAI.takeDamage(damage);\n            console.log(`对AI车辆造成${damage}点伤害`);\n        }\n    }\n\n    /**\n     * 直接摧毁玩家\n     */\n    private destroyPlayer() {\n        if (this.playerVehicle && !this.playerVehicle.isDestroyed()) {\n            this.playerVehicle.takeDamage(this.playerVehicle.getCurrentHealth());\n            console.log('直接摧毁玩家车辆');\n        }\n    }\n\n    /**\n     * 直接摧毁随机AI\n     */\n    private destroyRandomAI() {\n        const aliveAIs = this.aiVehicles.filter(ai => !ai.isDestroyed());\n        if (aliveAIs.length > 0) {\n            const randomAI = aliveAIs[Math.floor(Math.random() * aliveAIs.length)];\n            randomAI.takeDamage(randomAI.getHealth());\n            console.log('直接摧毁AI车辆');\n        }\n    }\n\n    /**\n     * 摧毁AI并测试3秒后移除功能\n     */\n    private destroyRandomAIWithRemoval() {\n        const aliveAIs = this.aiVehicles.filter(ai => !ai.isDestroyed());\n        if (aliveAIs.length > 0) {\n            const randomAI = aliveAIs[Math.floor(Math.random() * aliveAIs.length)];\n            randomAI.takeDamage(randomAI.getHealth());\n            console.log('摧毁AI车辆，3秒后将自动移除节点');\n        }\n    }\n\n    /**\n     * 测试血量同步功能\n     */\n    private testHealthSync() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager && this.playerVehicle) {\n            console.log('=== 血量同步测试 ===');\n            console.log(`GameManager中的玩家血量: ${gameManager.getPlayerHP()}/${gameManager.getPlayerMaxHP()}`);\n            console.log(`Player组件中的血量: ${this.playerVehicle.getCurrentHealth()}/${this.playerVehicle.getMaxHealth()}`);\n\n            // 测试同步\n            gameManager.syncPlayerHealth();\n            console.log('执行同步后:');\n            console.log(`GameManager中的玩家血量: ${gameManager.getPlayerHP()}/${gameManager.getPlayerMaxHP()}`);\n        }\n    }\n\n    /**\n     * 恢复所有车辆\n     */\n    private restoreAllVehicles() {\n        // 恢复玩家车辆\n        if (this.playerVehicle) {\n            this.playerVehicle.restoreVehicle();\n        }\n\n        // 恢复所有AI车辆\n        this.aiVehicles.forEach(ai => {\n            ai.restoreVehicle();\n        });\n\n        console.log('所有车辆已恢复');\n    }\n\n    update() {\n        this.updateUI();\n    }\n\n    /**\n     * 更新UI显示\n     */\n    private updateUI() {\n        // 更新玩家血量显示\n        if (this.playerHealthLabel && this.playerVehicle) {\n            const health = this.playerVehicle.getCurrentHealth();\n            const maxHealth = this.playerVehicle.getMaxHealth();\n            const status = this.playerVehicle.isDestroyed() ? ' [已摧毁]' : '';\n            this.playerHealthLabel.string = `玩家血量: ${health}/${maxHealth}${status}`;\n        }\n\n        // 更新AI血量显示\n        if (this.aiHealthLabel) {\n            const aliveCount = this.aiVehicles.filter(ai => !ai.isDestroyed()).length;\n            const totalCount = this.aiVehicles.length;\n            this.aiHealthLabel.string = `AI车辆: ${aliveCount}/${totalCount} 存活`;\n        }\n\n        // 更新状态显示\n        if (this.statusLabel) {\n            const playerDestroyed = this.playerVehicle ? this.playerVehicle.isDestroyed() : false;\n            const allAIDestroyed = this.aiVehicles.length > 0 && this.aiVehicles.every(ai => ai.isDestroyed());\n            \n            let status = '游戏进行中';\n            if (playerDestroyed) {\n                status = '玩家已摧毁';\n            } else if (allAIDestroyed) {\n                status = '所有AI已摧毁';\n            }\n            \n            this.statusLabel.string = `状态: ${status}`;\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        \n        if (this.damagePlayerButton) {\n            this.damagePlayerButton.node.off(Button.EventType.CLICK, this.onDamagePlayerClick, this);\n        }\n        if (this.damageAIButton) {\n            this.damageAIButton.node.off(Button.EventType.CLICK, this.onDamageAIClick, this);\n        }\n        if (this.restoreAllButton) {\n            this.restoreAllButton.node.off(Button.EventType.CLICK, this.onRestoreAllClick, this);\n        }\n        if (this.destroyPlayerButton) {\n            this.destroyPlayerButton.node.off(Button.EventType.CLICK, this.onDestroyPlayerClick, this);\n        }\n        if (this.destroyAIButton) {\n            this.destroyAIButton.node.off(Button.EventType.CLICK, this.onDestroyAIClick, this);\n        }\n    }\n}\n"]}