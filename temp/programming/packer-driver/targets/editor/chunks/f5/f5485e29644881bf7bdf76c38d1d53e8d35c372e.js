System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts at runtime.
      throw new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts: Unterminated string constant. (63:17)

  61 |     // 车辆价格配置
  62 |     private carInfos: CarinfoConfig = {
> 63 |         'car-1': "操控性超强小车，武器配备为子弹发射器，击中对手你可造成伤害This super maneuverable car is equipped with a bullet launcher. When you hit your opponent, you can cause damage.',      
     |                  ^
  64 |         'car-2': '经典跑车,具有坚固的车身,武器配备为火箭炮，爆炸后会清除附近的颜料',    
  65 |         'car-3': '现代化的超级跑车，速度与转向均衡，配备武器为机炮，击中后可造成伤害',   
  66 |         'car-4': '甩尾加速犹如闪电，武器配备为火箭炮，爆炸后会清除附近的颜料',   `);
    }
  };
});
//# sourceMappingURL=f5485e29644881bf7bdf76c38d1d53e8d35c372e.js.map