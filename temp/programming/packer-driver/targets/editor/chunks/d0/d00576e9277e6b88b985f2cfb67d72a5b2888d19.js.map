{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts"], "names": ["_decorator", "Component", "Node", "instantiate", "Vec2", "Vec3", "Layers", "UITransform", "ccclass", "property", "PaintManager", "paintMap", "Map", "paintContainer", "getInstance", "_instance", "onLoad", "console", "log", "node", "destroy", "layer", "Enum", "UI_2D", "<PERSON><PERSON><PERSON><PERSON>", "onDestroy", "add<PERSON><PERSON><PERSON>", "paintPrefab", "worldPosition", "ownerId", "warn", "paintContainerTransform", "getComponent", "uiTransform", "addComponent", "setContentSize", "localPosition", "convertToNodeSpaceAR", "position2D", "x", "y", "isNearOwnPaint", "checkAndRemoveOverlappingPaint", "paintNode", "setPosition", "paintId", "generatePaintId", "paintData", "position", "timestamp", "Date", "now", "set", "newPosition", "newOwnerId", "toRemove", "for<PERSON>ach", "distance", "coverageRadius", "push", "<PERSON><PERSON><PERSON><PERSON>", "length", "get", "<PERSON><PERSON><PERSON><PERSON>", "delete", "removePaintById", "values", "toFixed", "clearAll<PERSON><PERSON><PERSON>", "clear", "getPaintCountByOwner", "count", "getTotalPaintCount", "size", "getPaintRatioByOwner", "totalCount", "ownerCount", "getAllPaintRatios", "ratios", "ownerCounts", "getSortedPaintRatios", "result", "ratio", "sort", "a", "b", "clearPaintInRange", "center", "radius", "worldPos", "localCenter", "localCenter2D"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;;;;;;;;OACzE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;AAE9B;AACA;AACA;;AAQA;AACA;AACA;AACA;8BAEaU,Y,WADZF,OAAO,CAAC,cAAD,C,sCAAR,MACaE,YADb,SACkCT,SADlC,CAC4C;AAAA;AAAA;AAGxC;AAHwC,eAIhCU,QAJgC,GAIG,IAAIC,GAAJ,EAJH;;AAMxC;AANwC;;AAUxC;AAVwC,eAWhCC,cAXgC,GAWT,IAXS;AAAA;;AAaf,eAAXC,WAAW,GAAiB;AACtC,iBAAOJ,YAAY,CAACK,SAApB;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAIN,YAAY,CAACK,SAAjB,EAA4B;AACxBE,YAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACA,iBAAKC,IAAL,CAAUC,OAAV;AACA;AACH;;AACDV,UAAAA,YAAY,CAACK,SAAb,GAAyB,IAAzB,CANK,CAQL;;AACA,eAAKF,cAAL,GAAsB,IAAIX,IAAJ,CAAS,gBAAT,CAAtB,CATK,CAWL;;AACA,eAAKW,cAAL,CAAoBQ,KAApB,GAA4Bf,MAAM,CAACgB,IAAP,CAAYC,KAAxC,CAZK,CAcL;;AACA,eAAKJ,IAAL,CAAUK,QAAV,CAAmB,KAAKX,cAAxB;AAEAI,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH;;AAEDO,QAAAA,SAAS,GAAG;AACR,cAAIf,YAAY,CAACK,SAAb,KAA2B,IAA/B,EAAqC;AACjCL,YAAAA,YAAY,CAACK,SAAb,GAAyB,IAAzB;AACAE,YAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWQ,QAAAA,QAAQ,CAACC,WAAD,EAAsBC,aAAtB,EAA2CC,OAA3C,EAAkE;AAC7E,cAAI,CAACF,WAAD,IAAgB,CAAC,KAAKd,cAA1B,EAA0C;AACtCI,YAAAA,OAAO,CAACa,IAAR,CAAa,0BAAb;AACA;AACH,WAJ4E,CAM7E;AACA;;;AACA,gBAAMC,uBAAuB,GAAG,KAAKlB,cAAL,CAAoBmB,YAApB,CAAiCzB,WAAjC,CAAhC;;AACA,cAAI,CAACwB,uBAAL,EAA8B;AAC1B;AACA,kBAAME,WAAW,GAAG,KAAKpB,cAAL,CAAoBqB,YAApB,CAAiC3B,WAAjC,CAApB;AACA0B,YAAAA,WAAW,CAACE,cAAZ,CAA2B,IAA3B,EAAiC,GAAjC,EAH0B,CAGa;AAC1C,WAb4E,CAe7E;;;AACA,gBAAMC,aAAa,GAAG,KAAKvB,cAAL,CAAoBmB,YAApB,CAAiCzB,WAAjC,EAA+C8B,oBAA/C,CAAoET,aAApE,CAAtB;AACA,gBAAMU,UAAU,GAAG,IAAIlC,IAAJ,CAASgC,aAAa,CAACG,CAAvB,EAA0BH,aAAa,CAACI,CAAxC,CAAnB,CAjB6E,CAmB7E;;AACA,cAAI,KAAKC,cAAL,CAAoBH,UAApB,EAAgCT,OAAhC,CAAJ,EAA8C;AAC1C;AACA;AACH,WAvB4E,CAyB7E;;;AACA,eAAKa,8BAAL,CAAoCJ,UAApC,EAAgDT,OAAhD,EA1B6E,CA4B7E;;AACA,gBAAMc,SAAS,GAAGxC,WAAW,CAACwB,WAAD,CAA7B,CA7B6E,CA+B7E;;AACAgB,UAAAA,SAAS,CAACC,WAAV,CAAsBR,aAAtB,EAhC6E,CAkC7E;;AACAO,UAAAA,SAAS,CAACtB,KAAV,GAAkBf,MAAM,CAACgB,IAAP,CAAYC,KAA9B;AAEA,eAAKV,cAAL,CAAoBW,QAApB,CAA6BmB,SAA7B,EArC6E,CAuC7E;;AACA,gBAAME,OAAO,GAAG,KAAKC,eAAL,CAAqBR,UAArB,EAAiCT,OAAjC,CAAhB,CAxC6E,CA0C7E;;AACA,gBAAMkB,SAAoB,GAAG;AACzB5B,YAAAA,IAAI,EAAEwB,SADmB;AAEzBK,YAAAA,QAAQ,EAAEV,UAFe;AAGzBT,YAAAA,OAAO,EAAEA,OAHgB;AAIzBoB,YAAAA,SAAS,EAAEC,IAAI,CAACC,GAAL;AAJc,WAA7B;AAOA,eAAKxC,QAAL,CAAcyC,GAAd,CAAkBP,OAAlB,EAA2BE,SAA3B,EAlD6E,CAoD7E;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYL,QAAAA,8BAA8B,CAACW,WAAD,EAAoBC,UAApB,EAA8C;AAChF,gBAAMC,QAAkB,GAAG,EAA3B;AAEA,eAAK5C,QAAL,CAAc6C,OAAd,CAAsB,CAACT,SAAD,EAAYF,OAAZ,KAAwB;AAC1C,kBAAMY,QAAQ,GAAGrD,IAAI,CAACqD,QAAL,CAAcV,SAAS,CAACC,QAAxB,EAAkCK,WAAlC,CAAjB,CAD0C,CAG1C;;AACA,gBAAII,QAAQ,GAAG,KAAKC,cAAhB,IAAkCX,SAAS,CAAClB,OAAV,KAAsByB,UAA5D,EAAwE;AACpEC,cAAAA,QAAQ,CAACI,IAAT,CAAcd,OAAd;AAEH;AACJ,WARD,EAHgF,CAahF;;AACAU,UAAAA,QAAQ,CAACC,OAAT,CAAiBX,OAAO,IAAI;AACxB,iBAAKe,WAAL,CAAiBf,OAAjB;AACH,WAFD;;AAIA,cAAIU,QAAQ,CAACM,MAAT,GAAkB,CAAtB,EAAyB,CACrB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYD,QAAAA,WAAW,CAACf,OAAD,EAAwB;AACvC,gBAAME,SAAS,GAAG,KAAKpC,QAAL,CAAcmD,GAAd,CAAkBjB,OAAlB,CAAlB;;AACA,cAAIE,SAAJ,EAAe;AACX;AACA,gBAAIA,SAAS,CAAC5B,IAAV,IAAkB4B,SAAS,CAAC5B,IAAV,CAAe4C,OAArC,EAA8C;AAC1ChB,cAAAA,SAAS,CAAC5B,IAAV,CAAeC,OAAf;AACH,aAJU,CAMX;;;AACA,iBAAKT,QAAL,CAAcqD,MAAd,CAAqBnB,OAArB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWoB,QAAAA,eAAe,CAACpB,OAAD,EAAwB;AAC1C,eAAKe,WAAL,CAAiBf,OAAjB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACYJ,QAAAA,cAAc,CAACO,QAAD,EAAiBnB,OAAjB,EAA2C;AAC7D,eAAK,MAAMkB,SAAX,IAAwB,KAAKpC,QAAL,CAAcuD,MAAd,EAAxB,EAAgD;AAC5C;AACA,gBAAInB,SAAS,CAAClB,OAAV,KAAsBA,OAA1B,EAAmC;AAC/B,oBAAM4B,QAAQ,GAAGrD,IAAI,CAACqD,QAAL,CAAcV,SAAS,CAACC,QAAxB,EAAkCA,QAAlC,CAAjB,CAD+B,CAG/B;;AACA,kBAAIS,QAAQ,GAAG,KAAKC,cAApB,EAAoC;AAChC,uBAAO,IAAP;AACH;AACJ;AACJ;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACYZ,QAAAA,eAAe,CAACE,QAAD,EAAiBnB,OAAjB,EAA0C;AAC7D,gBAAMoB,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;AACA,iBAAQ,SAAQtB,OAAQ,IAAGmB,QAAQ,CAACT,CAAT,CAAW4B,OAAX,CAAmB,CAAnB,CAAsB,IAAGnB,QAAQ,CAACR,CAAT,CAAW2B,OAAX,CAAmB,CAAnB,CAAsB,IAAGlB,SAAU,EAAvF;AACH;AAED;AACJ;AACA;;;AACWmB,QAAAA,aAAa,GAAS;AACzB,eAAKzD,QAAL,CAAc6C,OAAd,CAAuBT,SAAD,IAAe;AACjC,gBAAIA,SAAS,CAAC5B,IAAV,IAAkB4B,SAAS,CAAC5B,IAAV,CAAe4C,OAArC,EAA8C;AAC1ChB,cAAAA,SAAS,CAAC5B,IAAV,CAAeC,OAAf;AACH;AACJ,WAJD;AAMA,eAAKT,QAAL,CAAc0D,KAAd;AACApD,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWoD,QAAAA,oBAAoB,CAACzC,OAAD,EAA0B;AACjD,cAAI0C,KAAK,GAAG,CAAZ;AACA,eAAK5D,QAAL,CAAc6C,OAAd,CAAuBT,SAAD,IAAe;AACjC,gBAAIA,SAAS,CAAClB,OAAV,KAAsBA,OAA1B,EAAmC;AAC/B0C,cAAAA,KAAK;AACR;AACJ,WAJD;AAKA,iBAAOA,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,kBAAkB,GAAW;AAChC,iBAAO,KAAK7D,QAAL,CAAc8D,IAArB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,oBAAoB,CAAC7C,OAAD,EAA0B;AACjD,gBAAM8C,UAAU,GAAG,KAAKH,kBAAL,EAAnB;;AACA,cAAIG,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAO,CAAP;AACH;;AAED,gBAAMC,UAAU,GAAG,KAAKN,oBAAL,CAA0BzC,OAA1B,CAAnB;AACA,iBAAO+C,UAAU,GAAGD,UAApB;AACH;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,iBAAiB,GAAkC;AACtD,gBAAMC,MAAqC,GAAG,EAA9C;AACA,gBAAMH,UAAU,GAAG,KAAKH,kBAAL,EAAnB;;AAEA,cAAIG,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAOG,MAAP;AACH,WANqD,CAQtD;;;AACA,gBAAMC,WAA0C,GAAG,EAAnD;AACA,eAAKpE,QAAL,CAAc6C,OAAd,CAAuBT,SAAD,IAAe;AACjC,kBAAMlB,OAAO,GAAGkB,SAAS,CAAClB,OAA1B;AACAkD,YAAAA,WAAW,CAAClD,OAAD,CAAX,GAAuB,CAACkD,WAAW,CAAClD,OAAD,CAAX,IAAwB,CAAzB,IAA8B,CAArD;AACH,WAHD,EAVsD,CAetD;;AACA,eAAK,MAAMA,OAAX,IAAsBkD,WAAtB,EAAmC;AAC/BD,YAAAA,MAAM,CAACjD,OAAD,CAAN,GAAkBkD,WAAW,CAAClD,OAAD,CAAX,GAAuB8C,UAAzC;AACH;;AAED,iBAAOG,MAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,oBAAoB,GAA6D;AACpF,gBAAMF,MAAM,GAAG,KAAKD,iBAAL,EAAf;AACA,gBAAMI,MAAgE,GAAG,EAAzE;;AAEA,eAAK,MAAMpD,OAAX,IAAsBiD,MAAtB,EAA8B;AAC1BG,YAAAA,MAAM,CAACtB,IAAP,CAAY;AACR9B,cAAAA,OAAO,EAAEA,OADD;AAERqD,cAAAA,KAAK,EAAEJ,MAAM,CAACjD,OAAD,CAFL;AAGR0C,cAAAA,KAAK,EAAE,KAAKD,oBAAL,CAA0BzC,OAA1B;AAHC,aAAZ;AAKH,WAVmF,CAYpF;;;AACAoD,UAAAA,MAAM,CAACE,IAAP,CAAY,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACH,KAAF,GAAUE,CAAC,CAACF,KAAlC;AAEA,iBAAOD,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWK,QAAAA,iBAAiB,CAACC,MAAD,EAAeC,MAAf,EAAuC;AAC3D,cAAI,CAAC,KAAK3E,cAAV,EAA0B;AACtBI,YAAAA,OAAO,CAACa,IAAR,CAAa,wBAAb;AACA,mBAAO,CAAP;AACH;;AAED,gBAAMC,uBAAuB,GAAG,KAAKlB,cAAL,CAAoBmB,YAApB,CAAiCzB,WAAjC,CAAhC;;AACA,cAAI,CAACwB,uBAAL,EAA8B;AAC1Bd,YAAAA,OAAO,CAACa,IAAR,CAAa,mCAAb;AACA,mBAAO,CAAP;AACH,WAV0D,CAY3D;;;AACA,gBAAM2D,QAAQ,GAAG,IAAIpF,IAAJ,CAASkF,MAAM,CAAChD,CAAhB,EAAmBgD,MAAM,CAAC/C,CAA1B,EAA6B,CAA7B,CAAjB;AACA,gBAAMkD,WAAW,GAAG3D,uBAAuB,CAACM,oBAAxB,CAA6CoD,QAA7C,CAApB;AACA,gBAAME,aAAa,GAAG,IAAIvF,IAAJ,CAASsF,WAAW,CAACnD,CAArB,EAAwBmD,WAAW,CAAClD,CAApC,CAAtB;AAEA,gBAAMe,QAAkB,GAAG,EAA3B;AAEA,eAAK5C,QAAL,CAAc6C,OAAd,CAAsB,CAACT,SAAD,EAAYF,OAAZ,KAAwB;AAC1C,kBAAMY,QAAQ,GAAGrD,IAAI,CAACqD,QAAL,CAAcV,SAAS,CAACC,QAAxB,EAAkC2C,aAAlC,CAAjB,CAD0C,CAG1C;;AACA,gBAAIlC,QAAQ,IAAI+B,MAAhB,EAAwB;AACpBjC,cAAAA,QAAQ,CAACI,IAAT,CAAcd,OAAd;AACH;AACJ,WAPD,EAnB2D,CA4B3D;;AACAU,UAAAA,QAAQ,CAACC,OAAT,CAAiBX,OAAO,IAAI;AACxB,iBAAKe,WAAL,CAAiBf,OAAjB,EADwB,CACG;AAC9B,WAFD;AAIA5B,UAAAA,OAAO,CAACC,GAAR,CAAa,OAAMqC,QAAQ,CAACM,MAAO,UAAnC;AACA,iBAAON,QAAQ,CAACM,MAAhB;AACH;;AA5UuC,O,UACzB9C,S,GAA0B,I,2FAMxCN,Q;;;;;iBACwB,E", "sourcesContent": ["import { _decorator, Component, Node, Prefab, instantiate, Vec2, Vec3, Layers, UITransform, Canvas, view } from 'cc';\nconst { ccclass, property } = _decorator;\n\n/**\n * 颜料数据接口\n */\ninterface PaintData {\n    node: Node;           // 颜料节点\n    position: Vec2;       // 位置\n    ownerId: string;      // 拥有者ID（车辆ID）\n    timestamp: number;    // 创建时间戳\n}\n\n/**\n * 颜料管理器\n * 负责管理游戏中所有车辆喷洒的颜料\n */\n@ccclass('PaintManager')\nexport class PaintManager extends Component {\n    private static _instance: PaintManager = null!;\n    \n    // 存储所有颜料的数据\n    private paintMap: Map<string, PaintData> = new Map();\n    \n    // 颜料覆盖检测的距离阈值（像素）\n    @property\n    coverageRadius: number = 30;\n    \n    // 颜料容器节点\n    private paintContainer: Node = null!;\n\n    public static getInstance(): PaintManager {\n        return PaintManager._instance;\n    }\n\n    onLoad() {\n        if (PaintManager._instance) {\n            console.log(\"销毁原有PaintManager单例\");\n            this.node.destroy();\n            return;\n        }\n        PaintManager._instance = this;\n\n        // 创建颜料容器节点，保持在PaintManager节点下\n        this.paintContainer = new Node('PaintContainer');\n\n        // 设置容器节点的层级为UI_2D，确保颜料固定在游戏世界坐标中\n        this.paintContainer.layer = Layers.Enum.UI_2D;\n\n        // 将颜料容器添加到PaintManager节点下，保持项目层次结构\n        this.node.addChild(this.paintContainer);\n\n        console.log('PaintManager初始化完成');\n    }\n\n    onDestroy() {\n        if (PaintManager._instance === this) {\n            PaintManager._instance = null!;\n            console.log(\"PaintManager实例已销毁\");\n        }\n    }\n\n    /**\n     * 添加颜料\n     * @param paintPrefab 颜料预制体\n     * @param worldPosition 世界坐标位置\n     * @param ownerId 拥有者ID\n     */\n    public addPaint(paintPrefab: Prefab, worldPosition: Vec3, ownerId: string): void {\n        if (!paintPrefab || !this.paintContainer) {\n            console.warn('PaintManager: 颜料预制体或容器为空');\n            return;\n        }\n\n        // 将世界坐标转换为paintContainer的本地坐标\n        // 这样可以保持节点层次结构，同时确保坐标正确\n        const paintContainerTransform = this.paintContainer.getComponent(UITransform);\n        if (!paintContainerTransform) {\n            // 如果paintContainer没有UITransform，添加一个\n            const uiTransform = this.paintContainer.addComponent(UITransform);\n            uiTransform.setContentSize(1280, 720); // 设置为设计分辨率大小\n        }\n\n        // 转换世界坐标到paintContainer的本地坐标\n        const localPosition = this.paintContainer.getComponent(UITransform)!.convertToNodeSpaceAR(worldPosition);\n        const position2D = new Vec2(localPosition.x, localPosition.y);\n\n        // 检查是否在同一拥有者的颜料附近，如果是则不喷洒\n        if (this.isNearOwnPaint(position2D, ownerId)) {\n            // console.log(`跳过颜料喷洒: 拥有者=${ownerId}, 位置附近已有自己的颜料`);\n            return;\n        }\n\n        // 检查是否需要覆盖其他拥有者的颜料\n        this.checkAndRemoveOverlappingPaint(position2D, ownerId);\n\n        // 创建新颜料节点\n        const paintNode = instantiate(paintPrefab);\n\n        // 设置颜料节点的本地位置（相对于paintContainer）\n        paintNode.setPosition(localPosition);\n\n        // 设置颜料节点的层级为UI_2D，确保它固定在游戏世界坐标中\n        paintNode.layer = Layers.Enum.UI_2D;\n\n        this.paintContainer.addChild(paintNode);\n\n        // 生成唯一ID\n        const paintId = this.generatePaintId(position2D, ownerId);\n\n        // 存储颜料数据（使用本地坐标）\n        const paintData: PaintData = {\n            node: paintNode,\n            position: position2D,\n            ownerId: ownerId,\n            timestamp: Date.now()\n        };\n\n        this.paintMap.set(paintId, paintData);\n\n        // console.log(`添加颜料: 拥有者=${ownerId}, 位置=(${position2D.x.toFixed(1)}, ${position2D.y.toFixed(1)})`);\n    }\n\n    /**\n     * 检查并移除重叠的颜料\n     * @param newPosition 新颜料位置\n     * @param newOwnerId 新颜料拥有者ID\n     */\n    private checkAndRemoveOverlappingPaint(newPosition: Vec2, newOwnerId: string): void {\n        const toRemove: string[] = [];\n        \n        this.paintMap.forEach((paintData, paintId) => {\n            const distance = Vec2.distance(paintData.position, newPosition);\n            \n            // 如果距离小于覆盖半径，且不是同一个拥有者，则移除旧颜料\n            if (distance < this.coverageRadius && paintData.ownerId !== newOwnerId) {\n                toRemove.push(paintId);\n                \n            }\n        });\n        \n        // 移除重叠的颜料\n        toRemove.forEach(paintId => {\n            this.removePaint(paintId);\n        });\n        \n        if (toRemove.length > 0) {\n            // console.log(`移除了 ${toRemove.length} 个重叠的颜料`);\n        }\n    }\n\n    /**\n     * 移除指定的颜料\n     * @param paintId 颜料ID\n     */\n    private removePaint(paintId: string): void {\n        const paintData = this.paintMap.get(paintId);\n        if (paintData) {\n            // 销毁节点\n            if (paintData.node && paintData.node.isValid) {\n                paintData.node.destroy();\n            }\n            \n            // 从映射中移除\n            this.paintMap.delete(paintId);\n        }\n    }\n\n    /**\n     * 公开方法：移除指定的颜料（供外部调用）\n     * @param paintId 颜料ID\n     */\n    public removePaintById(paintId: string): void {\n        this.removePaint(paintId);\n    }\n\n    /**\n     * 检查指定位置是否在同一拥有者的颜料附近\n     * @param position 要检查的位置\n     * @param ownerId 拥有者ID\n     * @returns 如果附近有同一拥有者的颜料则返回true\n     */\n    private isNearOwnPaint(position: Vec2, ownerId: string): boolean {\n        for (const paintData of this.paintMap.values()) {\n            // 只检查同一拥有者的颜料\n            if (paintData.ownerId === ownerId) {\n                const distance = Vec2.distance(paintData.position, position);\n\n                // 如果距离小于覆盖半径，说明附近已有自己的颜料\n                if (distance < this.coverageRadius) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n\n    /**\n     * 生成颜料唯一ID\n     * @param position 位置\n     * @param ownerId 拥有者ID\n     * @returns 唯一ID\n     */\n    private generatePaintId(position: Vec2, ownerId: string): string {\n        const timestamp = Date.now();\n        return `paint_${ownerId}_${position.x.toFixed(0)}_${position.y.toFixed(0)}_${timestamp}`;\n    }\n\n    /**\n     * 清除所有颜料\n     */\n    public clearAllPaint(): void {\n        this.paintMap.forEach((paintData) => {\n            if (paintData.node && paintData.node.isValid) {\n                paintData.node.destroy();\n            }\n        });\n        \n        this.paintMap.clear();\n        console.log('清除了所有颜料');\n    }\n\n    /**\n     * 获取指定拥有者的颜料数量\n     * @param ownerId 拥有者ID\n     * @returns 颜料数量\n     */\n    public getPaintCountByOwner(ownerId: string): number {\n        let count = 0;\n        this.paintMap.forEach((paintData) => {\n            if (paintData.ownerId === ownerId) {\n                count++;\n            }\n        });\n        return count;\n    }\n\n    /**\n     * 获取总颜料数量\n     * @returns 总数量\n     */\n    public getTotalPaintCount(): number {\n        return this.paintMap.size;\n    }\n\n    /**\n     * 获取指定拥有者的颜料占比\n     * @param ownerId 拥有者ID\n     * @returns 占比（0-1之间的小数）\n     */\n    public getPaintRatioByOwner(ownerId: string): number {\n        const totalCount = this.getTotalPaintCount();\n        if (totalCount === 0) {\n            return 0;\n        }\n\n        const ownerCount = this.getPaintCountByOwner(ownerId);\n        return ownerCount / totalCount;\n    }\n\n    /**\n     * 获取所有车辆的颜料占比\n     * @returns 包含每个车辆ID和其占比的对象\n     */\n    public getAllPaintRatios(): { [ownerId: string]: number } {\n        const ratios: { [ownerId: string]: number } = {};\n        const totalCount = this.getTotalPaintCount();\n\n        if (totalCount === 0) {\n            return ratios;\n        }\n\n        // 统计每个拥有者的颜料数量\n        const ownerCounts: { [ownerId: string]: number } = {};\n        this.paintMap.forEach((paintData) => {\n            const ownerId = paintData.ownerId;\n            ownerCounts[ownerId] = (ownerCounts[ownerId] || 0) + 1;\n        });\n\n        // 计算占比\n        for (const ownerId in ownerCounts) {\n            ratios[ownerId] = ownerCounts[ownerId] / totalCount;\n        }\n\n        return ratios;\n    }\n\n    /**\n     * 获取排序后的颜料占比（从高到低）\n     * @returns 按占比排序的数组，每个元素包含ownerId和ratio\n     */\n    public getSortedPaintRatios(): Array<{ ownerId: string, ratio: number, count: number }> {\n        const ratios = this.getAllPaintRatios();\n        const result: Array<{ ownerId: string, ratio: number, count: number }> = [];\n\n        for (const ownerId in ratios) {\n            result.push({\n                ownerId: ownerId,\n                ratio: ratios[ownerId],\n                count: this.getPaintCountByOwner(ownerId)\n            });\n        }\n\n        // 按占比从高到低排序\n        result.sort((a, b) => b.ratio - a.ratio);\n\n        return result;\n    }\n\n    /**\n     * 清除指定范围内的颜料\n     * @param center 爆炸中心位置（世界坐标）\n     * @param radius 爆炸半径\n     * @returns 被清除的颜料数量\n     */\n    public clearPaintInRange(center: Vec2, radius: number): number {\n        if (!this.paintContainer) {\n            console.warn('PaintManager: 颜料容器未初始化');\n            return 0;\n        }\n\n        const paintContainerTransform = this.paintContainer.getComponent(UITransform);\n        if (!paintContainerTransform) {\n            console.warn('PaintManager: 颜料容器缺少UITransform组件');\n            return 0;\n        }\n\n        // 将世界坐标转换为paintContainer本地坐标\n        const worldPos = new Vec3(center.x, center.y, 0);\n        const localCenter = paintContainerTransform.convertToNodeSpaceAR(worldPos);\n        const localCenter2D = new Vec2(localCenter.x, localCenter.y);\n\n        const toRemove: string[] = [];\n\n        this.paintMap.forEach((paintData, paintId) => {\n            const distance = Vec2.distance(paintData.position, localCenter2D);\n\n            // 如果颜料在爆炸范围内，则标记为需要移除\n            if (distance <= radius) {\n                toRemove.push(paintId);\n            }\n        });\n\n        // 移除范围内的颜料\n        toRemove.forEach(paintId => {\n            this.removePaint(paintId); // 使用现有的私有方法\n        });\n\n        console.log(`清除了 ${toRemove.length} 个范围内的颜料`);\n        return toRemove.length;\n    }\n}\n"]}