{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts"], "names": ["PrerollAdEvent", "InterstitialAdEvent", "RewardedVideoAdEvent"], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;gCACYA,c,0BAAAA,c;AAAAA,QAAAA,c;eAAAA,c;;AAIZ;AACA;AACA;;;qCACYC,mB,0BAAAA,mB;AAAAA,QAAAA,mB;AAAAA,QAAAA,mB;AAAAA,QAAAA,mB;eAAAA,mB;;AAMZ;AACA;AACA;;;sCACYC,oB,0BAAAA,oB;AAAAA,QAAAA,oB;AAAAA,QAAAA,oB;AAAAA,QAAAA,oB;AAAAA,QAAAA,oB;AAAAA,QAAAA,oB;AAAAA,QAAAA,oB;eAAAA,oB", "sourcesContent": ["/**\n * Ad Events for Interstitial Callback\n */\nexport enum PrerollAdEvent {\n  AD_BREAK_DONE = 'H5GA_EVENT_PREROLL_AD_BREAK_DONE',\n}\n\n/**\n * Ad Events for Interstitial Callback\n */\nexport enum InterstitialAdEvent {\n  BEFORE_AD = 'H5GA_EVENT_INTERSTITIAL_BEFORE_AD',\n  AFTER_AD = 'H5GA_EVENT_INTERSTITIAL_AFTER_AD',\n  AD_BREAK_DONE = 'H5GA_EVENT_INTERSTITIAL_AD_BREAK_DONE',\n}\n\n/**\n * Ad Events for Rewarded Video Callback\n */\nexport enum RewardedVideoAdEvent {\n  BEFORE_AD = 'H5GA_EVENT_REWARDED_VIDEO_BEFORE_AD',\n  AFTER_AD = 'H5GA_EVENT_REWARDED_VIDEO_AFTER_AD',\n  AD_BREAK_DONE = 'H5GA_EVENT_REWARDED_VIDEO_AD_BREAK_DONE',\n  BEFORE_REWARD = 'H5GA_EVENT_REWARDED_VIDEO_BEFORE_REWARD',\n  AD_DISMISSED = 'H5GA_EVENT_REWARDED_VIDEO_AD_DISMISSED',\n  AD_VIEWED = 'H5GA_EVENT_REWARDED_VIDEO_AD_VIEWED',\n}"]}