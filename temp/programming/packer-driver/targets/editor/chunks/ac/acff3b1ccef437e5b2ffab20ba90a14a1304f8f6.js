System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _cclegacy._RF.push({}, "3c6b2NJ6vVK/Ik6iUzYURUm", "preroll_callback", undefined);
      /**
       * Interface for Preroll AdBreak API Callback
       */


      _cclegacy._RF.pop();
    }
  };
});
//# sourceMappingURL=acff3b1ccef437e5b2ffab20ba90a14a1304f8f6.js.map