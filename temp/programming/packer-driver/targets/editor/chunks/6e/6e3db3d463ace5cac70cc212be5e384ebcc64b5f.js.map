{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "instantiate", "resources", "UITransform", "director", "ProgressBar", "Label", "<PERSON><PERSON>", "Vec2", "TempData", "CameraFollow", "player", "AIController", "AIPlayer", "<PERSON><PERSON><PERSON><PERSON>", "SceneTransition", "SoundManager", "PaintManager", "GameOverPanel", "GameHUD", "Bullet", "WeaponType", "ccclass", "property", "GameState", "GameManager", "aiPlayers", "currentState", "RUNNING", "gameStartTime", "gameEndTime", "gameDuration", "remainingTime", "playerHP", "playerMaxHP", "enemyCount", "initialEnemyCount", "playerComponent", "paintManager", "bulletRoot", "getInstance", "_instance", "onLoad", "console", "log", "node", "destroy", "start", "instance", "stopBGM", "playSoundEffect", "initializeGame", "bindButtonEvents", "loadLevelAndCar", "update", "deltaTime", "updateCountdown", "onDestroy", "Date", "now", "pausePanel", "active", "gameOverPanel", "gameHUD", "resetHUD", "pauseButton", "on", "EventType", "CLICK", "pauseGame", "resumeButton", "resumeGame", "mainMenuButton", "returnToLevelSelect", "levelId", "selectedLevel", "carId", "selectedCar", "mapNode", "player<PERSON>ode", "load", "err", "prefab", "setPosition", "playGround", "<PERSON><PERSON><PERSON><PERSON>", "autoFindAIPlayers", "notifyAIControllers", "findBulletRoot", "length", "refreshEnemyCount", "err2", "prefab2", "spawnChildren", "spawnPoint", "children", "randomIndex", "Math", "floor", "random", "spawnNode", "spawnPos", "getWorldPosition", "localPos", "canvas", "getComponent", "convertToNodeSpaceAR", "setRotation", "getRotation", "playerScript", "init", "angle", "initializePlayerHealth", "indexOf", "name", "cameraFollow", "camera", "initializePaintSystem", "error", "warn", "scene", "getChildByName", "sceneNode", "carsNode", "carNode", "aiPlayer", "push", "getAIPlayers", "findNodeRecursively", "parent", "child", "found", "aiControllers", "getComponentsInChildren", "aiController", "onScenePrefabLoaded", "getMaxHealth", "refreshPlayerHealthBar", "reducePlayerHP", "amount", "max", "syncPlayerHealth", "getCurrentHealth", "resetPlayerHealth", "restoreVehicle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progress", "count", "enemyCountLabel", "string", "gameOver", "PAUSED", "pause", "resume", "isVictory", "GAME_OVER", "gameResult", "calculateGameResult", "gameOverPanelComponent", "setGameOverInfo", "performance", "reward", "gameTime", "healthPercentage", "stars", "addMoney", "updateLevelProgress", "savePlayerData", "restartGame", "loadScene", "getScene", "stopbattleBGM", "playBGM", "gameTimeSec", "calculateStars", "calculatePerformance", "toFixed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getPaintCountByOwner", "totalPaintCount", "getTotalPaintCount", "player<PERSON><PERSON><PERSON>", "playerPercentage", "destroyedAllEnemies", "currentLevelId", "getCurrentState", "getGameTime", "getPlayerHP", "getPlayerMaxHP", "getEnemyCount", "getComponentInChildren", "clearAll<PERSON><PERSON><PERSON>", "sprayPaint", "paintPrefab", "worldPosition", "vehicleId", "add<PERSON><PERSON><PERSON>", "getAllVehiclePaintRatios", "getAllPaintRatios", "getSortedVehiclePaintRatios", "sorted", "getSortedPaintRatios", "map", "item", "ownerId", "ratio", "getPaintManager", "onCountdownFinished", "<PERSON><PERSON><PERSON><PERSON>", "getRemainingTime", "getFormattedRemainingTime", "totalSeconds", "ceil", "minutes", "seconds", "minutesStr", "toString", "secondsStr", "playerShoot", "shoot", "fireBullet", "bulletPrefab", "position", "direction", "shooterId", "weaponType", "DART", "directions", "dir", "createSingleBullet", "bulletNode", "bulletComponent", "bulletType", "setWorldPosition", "getPlayerComponent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,I,OAAAA,I;;AACtHC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,a,kBAAAA,a;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,M,kBAAAA,M;AAAQC,MAAAA,U,kBAAAA,U;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwB1B,U,GAE9B;;2BACY2B,S,0BAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;eAAAA,S;;;6BAOCC,W,WADZH,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACxB,IAAD,C,UAGRwB,QAAQ,CAACxB,IAAD,C,UAGRwB,QAAQ,CAACxB,IAAD,C,UAGRwB,QAAQ,CAACxB,IAAD,C,UAGRwB,QAAQ,CAAClB,WAAD,C,UAGRkB,QAAQ,CAACjB,KAAD,C,UAGRiB,QAAQ,CAAChB,MAAD,C,UAGRgB,QAAQ,CAACxB,IAAD,C,WAGRwB,QAAQ,CAACxB,IAAD,C,WAGRwB,QAAQ,CAAChB,MAAD,C,WAMRgB,QAAQ,CAAChB,MAAD,C,WA8BRgB,QAAQ;AAAA;AAAA,6B,sCAjEb,MACaE,WADb,SACiC3B,SADjC,CAC2C;AAAA;AAAA;;AAAA;;AAEb;AAFa;;AAKjB;AALiB;;AAQb;AARa;;AAWjB;AAXiB;;AAcD;AAdC;;AAiBP;AAjBO;;AAoBV;AApBU;;AAuBb;AAvBa;;AA0BV;AA1BU;;AA6BT;AAE9B;AACA;AAhCuC;;AAAA,eAsC/B4B,SAtC+B,GAsCP,EAtCO;AAwCvC;AAxCuC,eAyC/BC,YAzC+B,GAyCLH,SAAS,CAACI,OAzCL;AAAA,eA0C/BC,aA1C+B,GA0CP,CA1CO;AAAA,eA2C/BC,WA3C+B,GA2CT,CA3CS;AA6CvC;AA7CuC,eA+CvCC,YA/CuC,GA+ChB,EA/CgB;AA+CZ;AA/CY,eAiD/BC,aAjD+B,GAiDP,EAjDO;AAiDH;AAEpC;AAnDuC,eAoD/BC,QApD+B,GAoDZ,CApDY;AAoDT;AApDS,eAqD/BC,WArD+B,GAqDT,CArDS;AAqDN;AArDM,eAsD/BC,UAtD+B,GAsDV,CAtDU;AAAA,eAuD/BC,iBAvD+B,GAuDH,CAvDG;AAAA,eAwD/BC,eAxD+B,GAwDE,IAxDF;AAwDQ;AAxDR,eA2D/BC,YA3D+B,GA2DK,IA3DL;;AA4DvC;AACA;AAEA;AA/DuC;;AAmEvC;AAnEuC,eAoE/BC,UApE+B,GAoEL,IApEK;AAAA;;AAsEvC;AACA;AACA;AAEA;AACA;AAEyB,eAAXC,WAAW,GAAgB;AACrC,iBAAOf,WAAW,CAACgB,SAAnB;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAIjB,WAAW,CAACgB,SAAhB,EAA2B;AACvBE,YAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACA,iBAAKC,IAAL,CAAUC,OAAV;AACA;AACH;;AACDrB,UAAAA,WAAW,CAACgB,SAAZ,GAAwB,IAAxB;AACH;;AAEDM,QAAAA,KAAK,GAAG;AACJJ,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA;AAAA;AAAA,4CAAaI,QAAb,CAAsBC,OAAtB;AACA;AAAA;AAAA,4CAAaD,QAAb,CAAsBE,eAAtB,CAAsC,YAAtC;AACA,eAAKC,cAAL;AACA,eAAKC,gBAAL;AACA,eAAKC,eAAL;AACH;;AACDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB;AACA,cAAI,KAAK5B,YAAL,KAAsBH,SAAS,CAACI,OAApC,EAA6C;AACzC,iBAAK4B,eAAL,CAAqBD,SAArB;AACH;AACJ;;AAEDE,QAAAA,SAAS,GAAG;AACP,cAAIhC,WAAW,CAACgB,SAAZ,KAA0B,IAA9B,EAAoC;AACjChB,YAAAA,WAAW,CAACgB,SAAZ,GAAwB,IAAxB;AACAE,YAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYO,QAAAA,cAAc,GAAG;AACrB,eAAKxB,YAAL,GAAoBH,SAAS,CAACI,OAA9B;AACA,eAAKC,aAAL,GAAqB6B,IAAI,CAACC,GAAL,EAArB;AACA,eAAK7B,WAAL,GAAmB,CAAnB,CAHqB,CAKrB;;AACA,eAAKE,aAAL,GAAqB,KAAKD,YAA1B,CANqB,CAQrB;;AACA,cAAI,KAAK6B,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AACD,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACH,WAdoB,CAkBrB;;;AACA,cAAI,KAAKE,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaC,QAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYZ,QAAAA,gBAAgB,GAAG;AACvB;AACA,cAAI,KAAKa,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBpB,IAAjB,CAAsBqB,EAAtB,CAAyB3D,MAAM,CAAC4D,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,SAAtD,EAAiE,IAAjE;AACH,WAJsB,CAMvB;;;AACA,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBzB,IAAlB,CAAuBqB,EAAvB,CAA0B3D,MAAM,CAAC4D,SAAP,CAAiBC,KAA3C,EAAkD,KAAKG,UAAvD,EAAmE,IAAnE;AACH,WATsB,CAWvB;AACA;AACA;AAEA;;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoB3B,IAApB,CAAyBqB,EAAzB,CAA4B3D,MAAM,CAAC4D,SAAP,CAAiBC,KAA7C,EAAoD,KAAKK,mBAAzD,EAA8E,IAA9E;AACH;AACJ;;AAEDpB,QAAAA,eAAe,GAAG;AACd,gBAAMqB,OAAO,GAAG;AAAA;AAAA,oCAASC,aAAzB;AACA,gBAAMC,KAAK,GAAG;AAAA;AAAA,oCAASC,WAAvB;AACA,cAAIC,OAAoB,GAAG,IAA3B;AACA,cAAIC,UAAuB,GAAG,IAA9B,CAJc,CAKd;;AACA,cAAIL,OAAJ,EAAa;AACTxE,YAAAA,SAAS,CAAC8E,IAAV,CAAgB,iBAAgBN,OAAQ,EAAxC,EAA2C1E,MAA3C,EAAmD,CAACiF,GAAD,EAAMC,MAAN,KAAiB;AAChE,kBAAI,CAACD,GAAD,IAAQC,MAAZ,EAAoB;AAChBJ,gBAAAA,OAAO,GAAG7E,WAAW,CAACiF,MAAD,CAArB;AACAJ,gBAAAA,OAAO,CAACK,WAAR,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA,qBAAKC,UAAL,CAAgBC,QAAhB,CAAyBP,OAAzB,EAHgB,CAKhB;;AACA,qBAAKQ,iBAAL;AACA,qBAAKC,mBAAL,GAPgB,CAShB;;AACA,qBAAKC,cAAL,GAVgB,CAYhB;;AACA,qBAAKpD,iBAAL,GAAyB,KAAKV,SAAL,CAAe+D,MAAxC;AACA,qBAAKC,iBAAL,CAAuB,KAAKtD,iBAA5B,EAdgB,CAgBhB;;AACA,oBAAIwC,KAAJ,EAAW;AACP1E,kBAAAA,SAAS,CAAC8E,IAAV,CAAgB,eAAcJ,KAAM,EAApC,EAAuC5E,MAAvC,EAA+C,CAAC2F,IAAD,EAAOC,OAAP,KAAmB;AAC9D,wBAAI,CAACD,IAAD,IAASC,OAAb,EAAsB;AAClBb,sBAAAA,UAAU,GAAG9E,WAAW,CAAC2F,OAAD,CAAxB,CADkB,CAElB;;AACA,4BAAMC,aAAa,GAAG,KAAKC,UAAL,CAAgBC,QAAtC;;AACA,0BAAIF,aAAa,CAACJ,MAAd,GAAuB,CAA3B,EAA8B;AAC1B,8BAAMO,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBN,aAAa,CAACJ,MAAzC,CAApB;AACA,8BAAMW,SAAS,GAAGP,aAAa,CAACG,WAAD,CAA/B;AACA,8BAAMK,QAAQ,GAAGD,SAAS,CAACE,gBAAV,EAAjB,CAH0B,CAI1B;;AACA,8BAAMC,QAAQ,GAAG,KAAKC,MAAL,CAAYC,YAAZ,CAAyBtG,WAAzB,EAAsCuG,oBAAtC,CAA2DL,QAA3D,CAAjB;AACAtB,wBAAAA,UAAU,CAACI,WAAX,CAAuBoB,QAAvB;AACAxB,wBAAAA,UAAU,CAAC4B,WAAX,CAAuBP,SAAS,CAACQ,WAAV,EAAvB,EAP0B,CAQ1B;;AACA,8BAAMC,YAAY,GAAG9B,UAAU,CAAC0B,YAAX;AAAA;AAAA,6CAArB;;AACA,4BAAII,YAAJ,EAAkB;AACdA,0BAAAA,YAAY,CAACC,IAAb,CAAkBV,SAAS,CAACW,KAA5B,EADc,CAEd;;AACA,+BAAKC,sBAAL,CAA4BH,YAA5B;AACH,yBAdyB,CAe1B;;;AACA,4BAAI,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,EAA+BI,OAA/B,CAAuCb,SAAS,CAACc,IAAjD,MAA2D,CAAC,CAAhE,EAAmE;AAC/DvE,0BAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAD+D,CAE/D;AACH;AACJ;;AACD,2BAAK4D,MAAL,CAAYnB,QAAZ,CAAqBN,UAArB,EAzBkB,CA0BlB;;AACA,4BAAMoC,YAAY,GAAG,KAAKC,MAAL,CAAYX,YAAZ;AAAA;AAAA,uDAArB;;AACA,0BAAIU,YAAY,IAAIrC,OAAhB,IAA2BC,UAA/B,EAA2C;AACvCoC,wBAAAA,YAAY,CAACL,IAAb,CAAkBhC,OAAlB,EAA2BC,UAA3B;AACH,uBA9BiB,CA+BjB;;;AACD,2BAAKsC,qBAAL;AACH;;AACD,wBAAI1B,IAAJ,EAAU;AACNhD,sBAAAA,OAAO,CAAC2E,KAAR,CAAc,YAAd,EAA4B3B,IAA5B,EAAkCf,KAAlC;AACA;AACH;;AACD,wBAAI,CAACgB,OAAL,EAAc;AACVjD,sBAAAA,OAAO,CAAC2E,KAAR,CAAc,WAAd,EAA2B1C,KAA3B;AACA;AACH;AACJ,mBA3CD;AA4CH;AACJ;;AACD,kBAAIK,GAAJ,EAAS;AACLtC,gBAAAA,OAAO,CAAC2E,KAAR,CAAc,YAAd,EAA4BrC,GAA5B,EAAiCP,OAAjC;AACA;AACH;;AACD,kBAAI,CAACQ,MAAL,EAAa;AACTvC,gBAAAA,OAAO,CAAC2E,KAAR,CAAc,WAAd,EAA2B5C,OAA3B;AACA;AACH;AACJ,aAzED;AA0EH,WAjFa,CAkFd;;;AACA,cAAI;AAAA;AAAA,4CAAa1B,QAAjB,EAA2B;AACvB;AAAA;AAAA,8CAAaA,QAAb,CAAsBE,eAAtB,CAAsC,UAAtC;AACH,WAFD,MAEO;AACHP,YAAAA,OAAO,CAAC4E,IAAR,CAAa,2BAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACWjC,QAAAA,iBAAiB,GAAG;AACvB,eAAK5D,SAAL,GAAiB,EAAjB,CADuB,CAEvB;;AACA,gBAAM8F,KAAK,GAAG,KAAK3E,IAAL,CAAU2E,KAAxB;AACA,cAAI,CAACA,KAAL,EAAY;AACZ,gBAAMhB,MAAM,GAAGgB,KAAK,CAACC,cAAN,CAAqB,QAArB,CAAf;AACA,cAAI,CAACjB,MAAL,EAAa;AACb,gBAAMpB,UAAU,GAAGoB,MAAM,CAACiB,cAAP,CAAsB,YAAtB,CAAnB;AACA,cAAI,CAACrC,UAAL,EAAiB;AACjB,gBAAMsC,SAAS,GAAGtC,UAAU,CAACW,QAAX,CAAoB,CAApB,CAAlB;AACA,cAAI,CAAC2B,SAAL,EAAgB;AAChB,gBAAMC,QAAQ,GAAGD,SAAS,CAACD,cAAV,CAAyB,MAAzB,CAAjB;AACA,cAAI,CAACE,QAAL,EAAe;;AACf,eAAK,MAAMC,OAAX,IAAsBD,QAAQ,CAAC5B,QAA/B,EAAyC;AACrC,kBAAM8B,QAAQ,GAAGD,OAAO,CAACnB,YAAR;AAAA;AAAA,qCAAjB;;AACA,gBAAIoB,QAAJ,EAAc;AACV,mBAAKnG,SAAL,CAAeoG,IAAf,CAAoBD,QAApB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWE,QAAAA,YAAY,GAAe;AAC9B,iBAAO,KAAKrG,SAAZ;AACH;AAED;AACJ;AACA;;;AACY8D,QAAAA,cAAc,GAAS;AAC3B;AACA,gBAAMgC,KAAK,GAAG,KAAK3E,IAAL,CAAU2E,KAAxB;;AACA,cAAI,CAACA,KAAL,EAAY;AACR7E,YAAAA,OAAO,CAAC4E,IAAR,CAAa,OAAb;AACA;AACH,WAN0B,CAQ3B;;;AACA,eAAKhF,UAAL,GAAkBiF,KAAK,CAACC,cAAN,CAAqB,YAArB,KAAsC,KAAKO,mBAAL,CAAyBR,KAAzB,EAAgC,YAAhC,CAAxD;;AAEA,cAAI,KAAKjF,UAAT,EAAqB;AACjBI,YAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,KAAKL,UAAL,CAAgB2E,IAA/C;AACH,WAFD,MAEO;AACHvE,YAAAA,OAAO,CAAC4E,IAAR,CAAa,6BAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYS,QAAAA,mBAAmB,CAACC,MAAD,EAAef,IAAf,EAA0C;AACjE,eAAK,MAAMgB,KAAX,IAAoBD,MAAM,CAAClC,QAA3B,EAAqC;AACjC,gBAAImC,KAAK,CAAChB,IAAN,KAAeA,IAAnB,EAAyB;AACrB,qBAAOgB,KAAP;AACH;;AACD,kBAAMC,KAAK,GAAG,KAAKH,mBAAL,CAAyBE,KAAzB,EAAgChB,IAAhC,CAAd;;AACA,gBAAIiB,KAAJ,EAAW;AACP,qBAAOA,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACY5C,QAAAA,mBAAmB,GAAG;AAC1B,gBAAM6C,aAAa,GAAG,KAAKvF,IAAL,CAAU2E,KAAV,CAAgBa,uBAAhB;AAAA;AAAA,2CAAtB;;AACA,eAAK,MAAMC,YAAX,IAA2BF,aAA3B,EAA0C;AACtCE,YAAAA,YAAY,CAACC,mBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYvB,QAAAA,sBAAsB,CAACH,YAAD,EAAuB;AACjD,eAAKxE,eAAL,GAAuBwE,YAAvB;AACA,eAAK3E,WAAL,GAAmB2E,YAAY,CAAC2B,YAAb,EAAnB;AACA,eAAKvG,QAAL,GAAgB,KAAKC,WAArB;AAEAS,UAAAA,OAAO,CAACC,GAAR,CAAa,cAAa,KAAKX,QAAS,IAAG,KAAKC,WAAY,EAA5D,EALiD,CAOjD;;AACA,eAAKuG,sBAAL;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,cAAc,CAACC,MAAD,EAAiB;AAClChG,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB+F,MAAvB;AACA,eAAK1G,QAAL,GAAgBgE,IAAI,CAAC2C,GAAL,CAAS,CAAT,EAAY,KAAK3G,QAAL,GAAgB0G,MAA5B,CAAhB;AACA,eAAKF,sBAAL,GAHkC,CAKlC;AACA;;AACA,cAAI,KAAKxG,QAAL,IAAiB,CAAjB,IAAsB,KAAKN,YAAL,KAAsBH,SAAS,CAACI,OAA1D,EAAmE;AAC/De,YAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAD+D,CAE/D;AACH;AACJ;AAED;AACJ;AACA;;;AACWiG,QAAAA,gBAAgB,GAAG;AACtB,cAAI,KAAKxG,eAAT,EAA0B;AACtB,iBAAKJ,QAAL,GAAgB,KAAKI,eAAL,CAAqByG,gBAArB,EAAhB;AACA,iBAAKL,sBAAL,GAFsB,CAItB;AACA;;AACA,gBAAI,KAAKxG,QAAL,IAAiB,CAAjB,IAAsB,KAAKN,YAAL,KAAsBH,SAAS,CAACI,OAA1D,EAAmE;AAC/De,cAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAD+D,CAE/D;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWmG,QAAAA,iBAAiB,GAAG;AACvB,cAAI,KAAK1G,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqB2G,cAArB,GADsB,CACiB;;AACvC,iBAAK/G,QAAL,GAAgB,KAAKC,WAArB;AACA,iBAAKuG,sBAAL;AACA9F,YAAAA,OAAO,CAACC,GAAR,CAAa,YAAW,KAAKX,QAAS,IAAG,KAAKC,WAAY,EAA1D;AACH;AACJ;AAED;AACJ;AACA;;;AACWuG,QAAAA,sBAAsB,GAAG;AAC5B,cAAI,KAAKQ,eAAL,IAAwB,KAAK/G,WAAL,GAAmB,CAA/C,EAAkD;AAC9C,iBAAK+G,eAAL,CAAqBC,QAArB,GAAgC,KAAKjH,QAAL,GAAgB,KAAKC,WAArD;AACH;AACJ;AAED;AACJ;AACA;;;AACWwD,QAAAA,iBAAiB,CAACyD,KAAD,EAAgB;AACpC,eAAKhH,UAAL,GAAkBgH,KAAlB;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBC,MAArB,GAA+B,aAAY,KAAKlH,UAAW,EAA3D;AACH,WAJmC,CAMpC;AACA;;;AACA,cAAI,KAAKA,UAAL,IAAmB,CAAnB,IAAwB,KAAKR,YAAL,KAAsBH,SAAS,CAACI,OAAxD,IAAmE,KAAKQ,iBAAL,GAAyB,CAAhG,EAAmG;AAC/FO,YAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;AACA,iBAAK0G,QAAL,CAAc,IAAd,EAF+F,CAE1E;AACxB;AACJ,SA9ZsC,CAgavC;;AAEA;AACJ;AACA;;;AACWjF,QAAAA,SAAS,GAAG;AACf,cAAI,KAAK1C,YAAL,KAAsBH,SAAS,CAACI,OAApC,EAA6C;AAE7C,eAAKD,YAAL,GAAoBH,SAAS,CAAC+H,MAA9B,CAHe,CAKf;;AACAnJ,UAAAA,QAAQ,CAACoJ,KAAT,GANe,CAQf;;AACA,cAAI,KAAK5F,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACH;;AAEDlB,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACH;AAED;AACJ;AACA;;;AACW2B,QAAAA,UAAU,GAAG;AAChB,cAAI,KAAK5C,YAAL,KAAsBH,SAAS,CAAC+H,MAApC,EAA4C;AAE5C,eAAK5H,YAAL,GAAoBH,SAAS,CAACI,OAA9B,CAHgB,CAKhB;;AACAxB,UAAAA,QAAQ,CAACqJ,MAAT,GANgB,CAQhB;;AACA,cAAI,KAAK7F,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AAEDlB,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACW0G,QAAAA,QAAQ,CAACI,SAAD,EAAqB;AAChC,cAAI,KAAK/H,YAAL,KAAsBH,SAAS,CAACmI,SAApC,EAA+C;AAE/C,eAAKhI,YAAL,GAAoBH,SAAS,CAACmI,SAA9B;AACA,eAAK7H,WAAL,GAAmB4B,IAAI,CAACC,GAAL,EAAnB,CAJgC,CAMhC;;AACAvD,UAAAA,QAAQ,CAACoJ,KAAT,GAPgC,CAShC;;AACA,gBAAMI,UAAU,GAAG,KAAKC,mBAAL,CAAyBH,SAAzB,CAAnB,CAVgC,CAYhC;;AACA,cAAI,KAAK5F,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBD,MAAnB,GAA4B,IAA5B,CADoB,CAIpB;;AACA,kBAAMiG,sBAAsB,GAAG,KAAKhG,aAAL,CAAmB2C,YAAnB;AAAA;AAAA,+CAA/B;;AACA,gBAAIqD,sBAAJ,EAA4B;AACxBA,cAAAA,sBAAsB,CAAC1G,gBAAvB;AACA0G,cAAAA,sBAAsB,CAACC,eAAvB,CACIL,SADJ,EAEIE,UAAU,CAACI,WAFf,EAGIJ,UAAU,CAACK,MAHf,EAIIL,UAAU,CAACM,QAJf,EAKIN,UAAU,CAACO,gBALf,EAMIP,UAAU,CAACQ,KANf;AAQH;AACJ,WA9B+B,CAgChC;;;AACA;AAAA;AAAA,8CAAcpH,QAAd,CAAuBqH,QAAvB,CAAgCT,UAAU,CAACK,MAA3C,EAjCgC,CAmChC;;AACA,eAAKK,mBAAL,CAAyBV,UAAU,CAACQ,KAApC,EAA2CR,UAAU,CAACI,WAAtD;AAEA;AAAA;AAAA,8CAAchH,QAAd,CAAuBuH,cAAvB;AAEA5H,UAAAA,OAAO,CAACC,GAAR,CAAY8G,SAAS,GAAG,OAAH,GAAa,OAAlC;AACH;AAED;AACJ;AACA;;;AACWc,QAAAA,WAAW,GAAG;AACjB;AACApK,UAAAA,QAAQ,CAACqJ,MAAT,GAFiB,CAIjB;;AACA;AAAA;AAAA,kDAAgBgB,SAAhB,CAA0BrK,QAAQ,CAACsK,QAAT,GAAqBxD,IAA/C;AACH;AAED;AACJ;AACA;;;AACWzC,QAAAA,mBAAmB,GAAG;AACzB;AACArE,UAAAA,QAAQ,CAACqJ,MAAT,GAFyB,CAIzB;;AACA;AAAA;AAAA,kDAAgBgB,SAAhB,CAA0B,aAA1B;AACA;AAAA;AAAA,4CAAazH,QAAb,CAAsB2H,aAAtB;AACA;AAAA;AAAA,4CAAa3H,QAAb,CAAsB4H,OAAtB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYf,QAAAA,mBAAmB,CAACH,SAAD,EAMzB;AACE;AACA,gBAAMmB,WAAW,GAAG,CAAC,KAAK/I,WAAL,GAAmB,KAAKD,aAAzB,IAA0C,IAA9D,CAFF,CAIE;;AACA,gBAAMsI,gBAAgB,GAAG,KAAKlI,QAAL,GAAgB,KAAKC,WAA9C;;AAEA,cAAI,CAACwH,SAAL,EAAgB;AACZ;AACA,mBAAO;AACHM,cAAAA,WAAW,EAAE,EADV;AAEHC,cAAAA,MAAM,EAAE,EAFL;AAGHC,cAAAA,QAAQ,EAAEW,WAHP;AAIHV,cAAAA,gBAAgB,EAAEA,gBAJf;AAKHC,cAAAA,KAAK,EAAE;AALJ,aAAP;AAOH,WAhBH,CAkBE;;;AACA,gBAAMA,KAAK,GAAG,KAAKU,cAAL,CAAoBD,WAApB,EAAiCV,gBAAjC,CAAd,CAnBF,CAqBE;;AACA,gBAAM;AAAEH,YAAAA,WAAF;AAAeC,YAAAA;AAAf,cAA0B,KAAKc,oBAAL,CAA0BF,WAA1B,EAAuCV,gBAAvC,CAAhC;AAEAxH,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQiI,WAAW,CAACG,OAAZ,CAAoB,CAApB,CAAuB,WAAU,CAACb,gBAAgB,GAAG,GAApB,EAAyBa,OAAzB,CAAiC,CAAjC,CAAoC,UAASZ,KAAM,SAAQJ,WAAY,SAAQC,MAAO,IAA5I;AAEA,iBAAO;AACHD,YAAAA,WADG;AAEHC,YAAAA,MAFG;AAGHC,YAAAA,QAAQ,EAAEW,WAHP;AAIHV,YAAAA,gBAJG;AAKHC,YAAAA;AALG,WAAP;AAOH;AAED;AACJ;AACA;;;AACYU,QAAAA,cAAc,CAACZ,QAAD,EAAmBC,gBAAnB,EAAqD;AACvE;AACA,cAAI,CAAC,KAAK7H,YAAV,EAAwB;AACpB,mBAAO,CAAP,CADoB,CACV;AACb;;AAED,gBAAM2I,gBAAgB,GAAG,KAAK3I,YAAL,CAAkB4I,oBAAlB,CAAuC,QAAvC,CAAzB;AACA,gBAAMC,eAAe,GAAG,KAAK7I,YAAL,CAAkB8I,kBAAlB,EAAxB;;AAEA,cAAID,eAAe,KAAK,CAAxB,EAA2B;AACvB,mBAAO,CAAP,CADuB,CACb;AACb;;AAED,gBAAME,WAAW,GAAGJ,gBAAgB,GAAGE,eAAvC;AACA,gBAAMG,gBAAgB,GAAGD,WAAW,GAAG,GAAvC,CAduE,CAgBvE;;AACA,gBAAME,mBAAmB,GAAG,KAAKpJ,UAAL,IAAmB,CAAnB,IAAwB,KAAKC,iBAAL,GAAyB,CAA7E,CAjBuE,CAmBvE;;AACA,cAAIkJ,gBAAgB,IAAI,EAApB,IAA2BC,mBAA/B,EAAoD;AAChD,mBAAO,CAAP,CADgD,CACtC;AACb,WAFD,MAEO,IAAID,gBAAgB,IAAI,EAAxB,EAA4B;AAC/B,mBAAO,CAAP,CAD+B,CACrB;AACb,WAFM,MAEA,IAAIA,gBAAgB,IAAI,EAAxB,EAA4B;AAC/B,mBAAO,CAAP,CAD+B,CACrB;AACb,WAFM,MAEA;AACH,mBAAO,CAAP,CADG,CACO;AACb;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACYhB,QAAAA,mBAAmB,CAACF,KAAD,EAAgBJ,WAAhB,EAAsC;AAC7D;AACA,gBAAMwB,cAAc,GAAG;AAAA;AAAA,oCAAS7G,aAAhC;;AACA,cAAI,CAAC6G,cAAL,EAAqB;AACjB7I,YAAAA,OAAO,CAAC4E,IAAR,CAAa,YAAb;AACA;AACH,WAN4D,CAQ7D;;;AACA;AAAA;AAAA,8CAAcvE,QAAd,CAAuBsH,mBAAvB,CAA2CkB,cAA3C,EAA2DpB,KAA3D,EAAkEJ,WAAlE;AAEArH,UAAAA,OAAO,CAACC,GAAR,CAAa,YAAW4I,cAAe,SAAQpB,KAAM,EAArD;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,oBAAoB,CAACb,QAAD,EAAmBC,gBAAnB,EAAsF;AAC9G;AACA,gBAAMC,KAAK,GAAG,KAAKU,cAAL,CAAoBZ,QAApB,EAA8BC,gBAA9B,CAAd;AAEA,cAAIH,WAAJ;AACA,cAAIC,MAAJ,CAL8G,CAO9G;;AACA,kBAAQG,KAAR;AACI,iBAAK,CAAL;AACIJ,cAAAA,WAAW,GAAG,GAAd;AACAC,cAAAA,MAAM,GAAG,GAAT;AACA;;AACJ,iBAAK,CAAL;AACID,cAAAA,WAAW,GAAG,GAAd;AACAC,cAAAA,MAAM,GAAG,GAAT;AACA;;AACJ,iBAAK,CAAL;AACID,cAAAA,WAAW,GAAG,GAAd;AACAC,cAAAA,MAAM,GAAG,GAAT;AACA;;AACJ;AACID,cAAAA,WAAW,GAAG,GAAd;AACAC,cAAAA,MAAM,GAAG,EAAT;AACA;AAhBR;;AAmBA,iBAAO;AAAED,YAAAA,WAAF;AAAeC,YAAAA;AAAf,WAAP;AACH,SAjpBsC,CAqpBvC;;AAEA;AACJ;AACA;;;AACWwB,QAAAA,eAAe,GAAc;AAChC,iBAAO,KAAK9J,YAAZ;AACH;AAED;AACJ;AACA;;;AACW+J,QAAAA,WAAW,GAAW;AACzB,cAAI,KAAK5J,WAAL,GAAmB,CAAvB,EAA0B;AACtB,mBAAO,CAAC,KAAKA,WAAL,GAAmB,KAAKD,aAAzB,IAA0C,IAAjD;AACH;;AACD,iBAAO,CAAC6B,IAAI,CAACC,GAAL,KAAa,KAAK9B,aAAnB,IAAoC,IAA3C;AACH;AAED;AACJ;AACA;;;AACW8J,QAAAA,WAAW,GAAW;AACzB,iBAAO,KAAK1J,QAAZ;AACH;AAED;AACJ;AACA;;;AACW2J,QAAAA,cAAc,GAAW;AAC5B,iBAAO,KAAK1J,WAAZ;AACH;AAED;AACJ;AACA;;;AACW2J,QAAAA,aAAa,GAAW;AAC3B,iBAAO,KAAK1J,UAAZ;AACH,SA3rBsC,CA6rBvC;;AAEA;AACJ;AACA;;;AACYkF,QAAAA,qBAAqB,GAAS;AAClC;AACA,eAAK/E,YAAL,GAAoB,KAAKO,IAAL,CAAU2E,KAAV,CAAgBsE,sBAAhB;AAAA;AAAA,2CAApB;AAGH;AAED;AACJ;AACA;;;AACWC,QAAAA,aAAa,GAAS;AACzB,cAAI,KAAKzJ,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkByJ,aAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWC,QAAAA,UAAU,CAACC,WAAD,EAAsBC,aAAtB,EAA2CC,SAA3C,EAAoE;AACjF,cAAI,KAAK7J,YAAL,IAAqB2J,WAAzB,EAAsC;AAClC,iBAAK3J,YAAL,CAAkB8J,QAAlB,CAA2BH,WAA3B,EAAwCC,aAAxC,EAAuDC,SAAvD;AACH,WAFD,MAEO;AACHxJ,YAAAA,OAAO,CAAC4E,IAAR,CAAa,0CAAb;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACW8E,QAAAA,wBAAwB,GAAoC;AAC/D,cAAI,KAAK/J,YAAT,EAAuB;AACnB,mBAAO,KAAKA,YAAL,CAAkBgK,iBAAlB,EAAP;AACH;;AACD,iBAAO,EAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,2BAA2B,GAA+D;AAC7F,cAAI,KAAKjK,YAAT,EAAuB;AACnB,kBAAMkK,MAAM,GAAG,KAAKlK,YAAL,CAAkBmK,oBAAlB,EAAf,CADmB,CAEnB;;AACA,mBAAOD,MAAM,CAACE,GAAP,CAAWC,IAAI,KAAK;AACvBR,cAAAA,SAAS,EAAEQ,IAAI,CAACC,OADO;AAEvBC,cAAAA,KAAK,EAAEF,IAAI,CAACE,KAFW;AAGvB1D,cAAAA,KAAK,EAAEwD,IAAI,CAACxD;AAHW,aAAL,CAAf,CAAP;AAKH;;AACD,iBAAO,EAAP;AACH;AAED;AACJ;AACA;;;AACW2D,QAAAA,eAAe,GAAwB;AAC1C,iBAAO,KAAKxK,YAAZ;AACH,SAjwBsC,CAmwBvC;;AAEA;AACJ;AACA;AACA;;;AACYkB,QAAAA,eAAe,CAACD,SAAD,EAA0B;AAC7C,eAAKvB,aAAL,IAAsBuB,SAAtB,CAD6C,CAG7C;;AACA,cAAI,KAAKvB,aAAL,IAAsB,CAA1B,EAA6B;AACzB,iBAAKA,aAAL,GAAqB,CAArB;AACA,iBAAK+K,mBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYA,QAAAA,mBAAmB,GAAS;AAChCpK,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EADgC,CAEhC;;AACA,eAAK0G,QAAL,CAAc,KAAK0D,eAAL,EAAd;AACH;AAED;AACJ;AACA;AACA;;;AACYA,QAAAA,eAAe,GAAY;AAC/B;AACA,cAAI,KAAK/K,QAAL,IAAiB,CAArB,EAAwB;AACpBU,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACA,mBAAO,KAAP;AACH,WAL8B,CAO/B;;;AACA,cAAI,KAAKT,UAAL,IAAmB,CAAnB,IAAwB,KAAKC,iBAAL,GAAyB,CAArD,EAAwD;AACpDO,YAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ;AACA,mBAAO,IAAP;AACH,WAX8B,CAa/B;;;AACA,cAAI,CAAC,KAAKN,YAAV,EAAwB;AACpB,mBAAO,KAAP,CADoB,CACN;AACjB;;AAED,gBAAM2I,gBAAgB,GAAG,KAAK3I,YAAL,CAAkB4I,oBAAlB,CAAuC,QAAvC,CAAzB;AACA,gBAAMC,eAAe,GAAG,KAAK7I,YAAL,CAAkB8I,kBAAlB,EAAxB;;AAEA,cAAID,eAAe,KAAK,CAAxB,EAA2B;AACvB,mBAAO,KAAP,CADuB,CACT;AACjB;;AAED,gBAAME,WAAW,GAAGJ,gBAAgB,GAAGE,eAAvC;AACAxI,UAAAA,OAAO,CAACC,GAAR,CAAa,WAAU,CAACyI,WAAW,GAAG,GAAf,EAAoBL,OAApB,CAA4B,CAA5B,CAA+B,GAAtD,EA1B+B,CA4B/B;;AACA,cAAIK,WAAW,GAAG,IAAlB,EAAwB;AACpB1I,YAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACA,mBAAO,IAAP;AACH,WAHD,MAGO;AACHD,YAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACWqK,QAAAA,gBAAgB,GAAW;AAC9B,iBAAOhH,IAAI,CAAC2C,GAAL,CAAS,CAAT,EAAY,KAAK5G,aAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWkL,QAAAA,yBAAyB,GAAW;AACvC,gBAAMC,YAAY,GAAGlH,IAAI,CAACmH,IAAL,CAAU,KAAKH,gBAAL,EAAV,CAArB;AACA,gBAAMI,OAAO,GAAGpH,IAAI,CAACC,KAAL,CAAWiH,YAAY,GAAG,EAA1B,CAAhB;AACA,gBAAMG,OAAO,GAAGH,YAAY,GAAG,EAA/B,CAHuC,CAKvC;;AACA,gBAAMI,UAAU,GAAGF,OAAO,GAAG,EAAV,GAAe,MAAMA,OAArB,GAA+BA,OAAO,CAACG,QAAR,EAAlD;AACA,gBAAMC,UAAU,GAAGH,OAAO,GAAG,EAAV,GAAe,MAAMA,OAArB,GAA+BA,OAAO,CAACE,QAAR,EAAlD;AAEA,iBAAQ,GAAED,UAAW,IAAGE,UAAW,EAAnC;AACH,SA51BsC,CA81BvC;;AAEA;AACJ;AACA;;;AACWC,QAAAA,WAAW,GAAS;AACvB,cAAI,KAAKrL,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBsL,KAArB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACWC,QAAAA,UAAU,CAACC,YAAD,EAAuBC,QAAvB,EAAuCC,SAAvC,EAAwDC,SAAxD,EAA2EC,UAA3E,EAAyG;AACtH;AACA,cAAIA,UAAU,KAAK;AAAA;AAAA,wCAAWC,IAA9B,EAAoC;AAChC;AACA,kBAAMC,UAAU,GAAG,CACf,IAAI3N,IAAJ,CAAS,CAAT,EAAY,CAAZ,CADe,EACG;AAClB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAFe,EAEG;AAClB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAC,CAAb,CAHe,EAGG;AAClB,gBAAIA,IAAJ,CAAS,CAAC,CAAV,EAAa,CAAb,CAJe,EAIG;AAClB,gBAAIA,IAAJ,CAAS,CAAC,CAAV,EAAa,CAAC,CAAd,CALe,EAMf,IAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,CANe,EAOf,IAAIA,IAAJ,CAAS,CAAC,CAAV,EAAa,CAAb,CAPe,EAQf,IAAIA,IAAJ,CAAS,CAAT,EAAY,CAAC,CAAb,CARe,CAAnB,CAFgC,CAahC;;AACA,iBAAK,MAAM4N,GAAX,IAAkBD,UAAlB,EAA8B;AAC1B,mBAAKE,kBAAL,CAAwBR,YAAxB,EAAsCC,QAAtC,EAAgDM,GAAhD,EAAqDJ,SAArD,EAAgEC,UAAhE;AACH;AACJ,WAjBD,MAiBO;AACH;AACA,iBAAKI,kBAAL,CAAwBR,YAAxB,EAAsCC,QAAtC,EAAgDC,SAAhD,EAA2DC,SAA3D,EAAsEC,UAAtE;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYI,QAAAA,kBAAkB,CAACR,YAAD,EAAuBC,QAAvB,EAAuCC,SAAvC,EAAwDC,SAAxD,EAA2EC,UAA3E,EAAyG;AAC/H;AACA,gBAAMK,UAAU,GAAGrO,WAAW,CAAC4N,YAAD,CAA9B,CAF+H,CAI/H;;AACA,gBAAMU,eAAe,GAAGD,UAAU,CAAC7H,YAAX;AAAA;AAAA,+BAAxB;;AACA,cAAI8H,eAAJ,EAAqB;AACjB;AACAA,YAAAA,eAAe,CAACzH,IAAhB,CAAqBiH,SAArB,EAAgCC,SAAhC,EAFiB,CAGjB;;AACAO,YAAAA,eAAe,CAACC,UAAhB,GAA6BP,UAA7B;AACH,WAX8H,CAa/H;;;AACA,cAAI,KAAK1L,UAAT,EAAqB;AAAA;;AACjB;AACA,kBAAMgE,QAAQ,4BAAG,KAAKhE,UAAL,CAAgBkE,YAAhB,CAA6BtG,WAA7B,CAAH,qBAAG,sBAA2CuG,oBAA3C,CAAgEoH,QAAhE,CAAjB;;AACA,gBAAIvH,QAAJ,EAAc;AACV+H,cAAAA,UAAU,CAACnJ,WAAX,CAAuBoB,QAAvB;AACH,aAFD,MAEO;AACH5D,cAAAA,OAAO,CAAC4E,IAAR,CAAa,oBAAb;AACA+G,cAAAA,UAAU,CAACG,gBAAX,CAA4BX,QAA5B;AACH;;AACD,iBAAKvL,UAAL,CAAgB8C,QAAhB,CAAyBiJ,UAAzB;AACA3L,YAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACW8L,QAAAA,kBAAkB,GAAkB;AACvC,iBAAO,KAAKrM,eAAZ;AACH;;AAn7BsC,O,UAqCxBI,S,GAAyB,I;;;;;iBAnCrB,I;;;;;;;iBAGJ,I;;;;;;;iBAGI,I;;;;;;;iBAGJ,I;;;;;;;iBAGgB,I;;;;;;;iBAGN,I;;;;;;;iBAGH,I;;;;;;;iBAGH,I;;;;;;;iBAGG,I;;;;;;;iBAGC,I;;;;;;;iBAME,I;;;;;;;iBA8BN,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, instantiate, resources, UITransform, director, ProgressBar, Label, Button, Vec3, Vec2 } from 'cc';\nimport { TempData } from './TempData';\nimport { CameraFollow } from './camera_follow';\nimport { player } from './player';\nimport { AIController } from './AIController';\nimport { AIPlayer } from './AIPlayer';\nimport { PlayerManager } from './PlayerManager';\nimport { SceneTransition } from './SceneTransition';\nimport { SoundManager } from './SoundManager';\nimport { PaintManager } from './PaintManager';\nimport { GameOverPanel } from './GameOverPanel';\nimport { GameHUD } from './GameHUD';\nimport { Bullet, WeaponType } from './Bullet';\n\nconst { ccclass, property } = _decorator;\n\n// 游戏状态枚举\nexport enum GameState {\n    RUNNING = 'running',    // 游戏运行中\n    PAUSED = 'paused',      // 游戏暂停\n    GAME_OVER = 'game_over' // 游戏结束\n}\n\n@ccclass('GameManager')\nexport class GameManager extends Component {\n    @property(Node)\n    playGround: Node = null!; // PlayGround节点\n\n    @property(Node)\n    canvas: Node = null!; // Canvas节点\n\n    @property(Node)\n    spawnPoint: Node = null!; // SpawnPoint节点\n\n    @property(Node)\n    camera: Node = null!; // Camera节点\n\n    @property(ProgressBar)\n    playerHealthBar: ProgressBar = null!; // 玩家血量条\n\n    @property(Label)\n    enemyCountLabel: Label = null!; // 敌人数量Label\n\n    @property(Button)\n    pauseButton: Button = null!; // 暂停按钮\n\n    @property(Node)\n    pausePanel: Node = null!; // 暂停面板\n\n    @property(Node)\n    gameOverPanel: Node = null!; // 游戏结束面板\n\n    @property(Button)\n    resumeButton: Button = null!; // 继续游戏按钮\n\n    // @property(Button)\n    // mainMenuButton2: Button = null!; // \n\n    @property(Button)\n    mainMenuButton: Button = null!; // 返回主菜单按钮\n\n    private static _instance: GameManager = null!;\n    private aiPlayers: AIPlayer[] = [];\n\n    // 游戏状态相关\n    private currentState: GameState = GameState.RUNNING;\n    private gameStartTime: number = 0;\n    private gameEndTime: number = 0;\n\n    // 倒计时相关\n  \n    gameDuration: number = 90; // 游戏时长（秒），默认2分钟\n\n    private remainingTime: number = 90; // 剩余时间\n\n    // 玩家数据\n    private playerHP: number = 0; // 将在player加载完成后初始化\n    private playerMaxHP: number = 0; // 将在player加载完成后初始化\n    private enemyCount: number = 0;\n    private initialEnemyCount: number = 0;\n    private playerComponent: player | null = null; // 玩家组件引用\n\n\n    private paintManager: PaintManager | null = null;\n    // @property(PaintManager)\n    // paintManager: PaintManager = null!;\n\n    // HUD界面\n    @property(GameHUD)\n    gameHUD: GameHUD = null!;\n\n    // 子弹根节点\n    private bulletRoot: Node | null = null;\n\n    // // 游戏结束面板颜料占比显示\n    // @property(Node)\n    // paintRatiosContainer: Node = null!; // 颜料占比显示容器\n\n    // @property(Label)\n    // paintRatiosTitleLabel: Label = null!; // 颜料占比标题\n\n    public static getInstance(): GameManager {\n        return GameManager._instance;\n    }\n\n    onLoad() {\n        if (GameManager._instance) {\n            console.log(\"销毁原有单例\")\n            this.node.destroy();\n            return;\n        }\n        GameManager._instance = this;\n    }\n\n    start() {\n        console.log(\"调用场景内容加载\")\n        SoundManager.instance.stopBGM()\n        SoundManager.instance.playSoundEffect(\"battlebgm1\")\n        this.initializeGame();\n        this.bindButtonEvents();\n        this.loadLevelAndCar();\n    }\n    update(deltaTime: number) {\n        // 只在游戏运行状态下更新倒计时\n        if (this.currentState === GameState.RUNNING) {\n            this.updateCountdown(deltaTime);\n        }\n    }\n\n    onDestroy() {\n         if (GameManager._instance === this) {\n            GameManager._instance = null;\n            console.log(\"GameManager 实例已销毁\");\n        }\n    }\n\n    /**\n     * 初始化游戏\n     */\n    private initializeGame() {\n        this.currentState = GameState.RUNNING;\n        this.gameStartTime = Date.now();\n        this.gameEndTime = 0;\n\n        // 初始化倒计时\n        this.remainingTime = this.gameDuration;\n\n        // 初始化UI面板状态\n        if (this.pausePanel) {\n            this.pausePanel.active = false;\n        }\n        if (this.gameOverPanel) {\n            this.gameOverPanel.active = false;\n        }\n\n       \n\n        // 重置HUD显示\n        if (this.gameHUD) {\n            this.gameHUD.resetHUD();\n        }\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    private bindButtonEvents() {\n        // 暂停按钮\n        if (this.pauseButton) {\n            this.pauseButton.node.on(Button.EventType.CLICK, this.pauseGame, this);\n        }\n\n        // 继续游戏按钮\n        if (this.resumeButton) {\n            this.resumeButton.node.on(Button.EventType.CLICK, this.resumeGame, this);\n        }\n\n        // if (this.mainMenuButton2) {\n        //     this.mainMenuButton2.node.on(Button.EventType.CLICK, this.returnToLevelSelect, this);\n        // }\n\n        // 返回主菜单按钮\n        if (this.mainMenuButton) {\n            this.mainMenuButton.node.on(Button.EventType.CLICK, this.returnToLevelSelect, this);\n        }\n    }\n\n    loadLevelAndCar() {\n        const levelId = TempData.selectedLevel;\n        const carId = TempData.selectedCar;\n        let mapNode: Node | null = null;\n        let playerNode: Node | null = null;\n        // 1. 加载并实例化场景背景\n        if (levelId) {\n            resources.load(`prefab/levels/${levelId}`, Prefab, (err, prefab) => {\n                if (!err && prefab) {\n                    mapNode = instantiate(prefab);\n                    mapNode.setPosition(0, 0, 0);\n                    this.playGround.addChild(mapNode);\n                    \n                    // 场景预制体加载完成，查找AI车辆\n                    this.autoFindAIPlayers();\n                    this.notifyAIControllers();\n\n                    // 查找BulletRoot节点\n                    this.findBulletRoot();\n\n                    // 初始化敌人数量\n                    this.initialEnemyCount = this.aiPlayers.length;\n                    this.refreshEnemyCount(this.initialEnemyCount);\n                    \n                    // 2. 加载并实例化车辆\n                    if (carId) {\n                        resources.load(`prefab/cars/${carId}`, Prefab, (err2, prefab2) => {\n                            if (!err2 && prefab2) {\n                                playerNode = instantiate(prefab2);\n                                // 随机选择一个SpawnPoint的子节点\n                                const spawnChildren = this.spawnPoint.children;\n                                if (spawnChildren.length > 0) {\n                                    const randomIndex = Math.floor(Math.random() * spawnChildren.length);\n                                    const spawnNode = spawnChildren[randomIndex];\n                                    const spawnPos = spawnNode.getWorldPosition();\n                                    // 转换为Canvas的本地坐标\n                                    const localPos = this.canvas.getComponent(UITransform).convertToNodeSpaceAR(spawnPos);\n                                    playerNode.setPosition(localPos);\n                                    playerNode.setRotation(spawnNode.getRotation());\n                                    // 设置初始角度并初始化玩家血量\n                                    const playerScript = playerNode.getComponent(player);\n                                    if (playerScript) {\n                                        playerScript.init(spawnNode.angle);\n                                        // 初始化玩家血量数据\n                                        this.initializePlayerHealth(playerScript);\n                                    }\n                                    // 根据点位名称设置朝向\n                                    if ([\"point4\", \"point5\", \"point6\"].indexOf(spawnNode.name) !== -1) {\n                                        console.log(\"生成车辆在右侧\")\n                                        // playerNode.setRotationFromEuler(0, 0, 90);\n                                    } \n                                } \n                                this.canvas.addChild(playerNode);\n                                // 3. 通知相机\n                                const cameraFollow = this.camera.getComponent(CameraFollow);\n                                if (cameraFollow && mapNode && playerNode) {\n                                    cameraFollow.init(mapNode, playerNode);\n                                }\n                                 // 初始化颜料系统\n                                this.initializePaintSystem();\n                            }\n                            if (err2) {\n                                console.error('加载车辆预制体失败:', err2, carId);\n                                return;\n                            }\n                            if (!prefab2) {\n                                console.error('未找到车辆预制体:', carId);\n                                return;\n                            }\n                        });\n                    }\n                }\n                if (err) {\n                    console.error('加载关卡预制体失败:', err, levelId);\n                    return;\n                }\n                if (!prefab) {\n                    console.error('未找到关卡预制体:', levelId);\n                    return;\n                }\n            });\n        }\n        // 检查SoundManager是否已初始化后再播放音效\n        if (SoundManager.instance) {\n            SoundManager.instance.playSoundEffect('carStart');\n        } else {\n            console.warn('SoundManager 尚未初始化，无法播放音效');\n        }\n    }\n\n    /**\n     * 查找所有AIPlayer组件\n     */\n    public autoFindAIPlayers() {\n        this.aiPlayers = [];\n        // 路径: Canvas → PlayGround → 场景预制体 → cars\n        const scene = this.node.scene;\n        if (!scene) return;\n        const canvas = scene.getChildByName('Canvas');\n        if (!canvas) return;\n        const playGround = canvas.getChildByName('PlayGround');\n        if (!playGround) return;\n        const sceneNode = playGround.children[0];\n        if (!sceneNode) return;\n        const carsNode = sceneNode.getChildByName('cars');\n        if (!carsNode) return;\n        for (const carNode of carsNode.children) {\n            const aiPlayer = carNode.getComponent(AIPlayer);\n            if (aiPlayer) {\n                this.aiPlayers.push(aiPlayer);\n            }\n        }\n    }\n\n    /**\n     * 获取AI车辆列表\n     */\n    public getAIPlayers(): AIPlayer[] {\n        return this.aiPlayers;\n    }\n\n    /**\n     * 查找BulletRoot节点\n     */\n    private findBulletRoot(): void {\n        // 直接在场景中搜索BulletRoot节点（递归搜索所有子节点）\n        const scene = this.node.scene;\n        if (!scene) {\n            console.warn('场景未找到');\n            return;\n        }\n\n        // 使用find方法递归查找BulletRoot节点\n        this.bulletRoot = scene.getChildByName('BulletRoot') || this.findNodeRecursively(scene, 'BulletRoot');\n\n        if (this.bulletRoot) {\n            console.log('BulletRoot节点找到:', this.bulletRoot.name);\n        } else {\n            console.warn('BulletRoot节点未找到，子弹将添加到场景根节点');\n        }\n    }\n\n    /**\n     * 递归查找指定名称的节点\n     */\n    private findNodeRecursively(parent: Node, name: string): Node | null {\n        for (const child of parent.children) {\n            if (child.name === name) {\n                return child;\n            }\n            const found = this.findNodeRecursively(child, name);\n            if (found) {\n                return found;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * 通知所有AIController组件场景预制体已加载完成\n     */\n    private notifyAIControllers() {\n        const aiControllers = this.node.scene.getComponentsInChildren(AIController);\n        for (const aiController of aiControllers) {\n            aiController.onScenePrefabLoaded();\n        }\n    }\n\n    /**\n     * 初始化玩家血量数据\n     */\n    private initializePlayerHealth(playerScript: player) {\n        this.playerComponent = playerScript;\n        this.playerMaxHP = playerScript.getMaxHealth();\n        this.playerHP = this.playerMaxHP;\n\n        console.log(`玩家血量初始化完成: ${this.playerHP}/${this.playerMaxHP}`);\n\n        // 刷新血量UI\n        this.refreshPlayerHealthBar();\n    }\n\n    /**\n     * 减少玩家血量并刷新UI\n     */\n    public reducePlayerHP(amount: number) {\n        console.log('减少玩家血量:', amount);\n        this.playerHP = Math.max(0, this.playerHP - amount);\n        this.refreshPlayerHealthBar();\n\n        // 检查玩家是否死亡，但不立即触发游戏结束\n        // 游戏结束将由玩家车辆的摧毁动画完成后触发\n        if (this.playerHP <= 0 && this.currentState === GameState.RUNNING) {\n            console.log('玩家血量归零，等待摧毁动画完成');\n            // 不在这里调用 gameOver，让 player.ts 的动画完成后调用\n        }\n    }\n\n    /**\n     * 同步玩家血量（从player组件获取最新血量）\n     */\n    public syncPlayerHealth() {\n        if (this.playerComponent) {\n            this.playerHP = this.playerComponent.getCurrentHealth();\n            this.refreshPlayerHealthBar();\n\n            // 检查玩家是否死亡，但不立即触发游戏结束\n            // 游戏结束将由玩家车辆的摧毁动画完成后触发\n            if (this.playerHP <= 0 && this.currentState === GameState.RUNNING) {\n                console.log('玩家血量归零，等待摧毁动画完成');\n                // 不在这里调用 gameOver，让 player.ts 的动画完成后调用\n            }\n        }\n    }\n\n    /**\n     * 重置玩家血量到满血状态\n     */\n    public resetPlayerHealth() {\n        if (this.playerComponent) {\n            this.playerComponent.restoreVehicle(); // 恢复玩家车辆状态\n            this.playerHP = this.playerMaxHP;\n            this.refreshPlayerHealthBar();\n            console.log(`玩家血量已重置: ${this.playerHP}/${this.playerMaxHP}`);\n        }\n    }\n\n    /**\n     * 刷新玩家血量进度条\n     */\n    public refreshPlayerHealthBar() {\n        if (this.playerHealthBar && this.playerMaxHP > 0) {\n            this.playerHealthBar.progress = this.playerHP / this.playerMaxHP;\n        }\n    }\n\n    /**\n     * 刷新剩余敌人数量并刷新UI\n     */\n    public refreshEnemyCount(count: number) {\n        this.enemyCount = count;\n        if (this.enemyCountLabel) {\n            this.enemyCountLabel.string = `opponent: ${this.enemyCount}`;\n        }\n\n        // 检查是否所有敌人都被消灭\n        // 这个方法现在由AI车辆摧毁动画完成后调用，所以可以立即触发游戏结束\n        if (this.enemyCount <= 0 && this.currentState === GameState.RUNNING && this.initialEnemyCount > 0) {\n            console.log('所有AI车辆摧毁动画完成，触发游戏胜利');\n            this.gameOver(true); // 敌人全部消灭，游戏胜利\n        }\n    }\n\n    // ==================== 游戏状态管理方法 ====================\n\n    /**\n     * 暂停游戏\n     */\n    public pauseGame() {\n        if (this.currentState !== GameState.RUNNING) return;\n\n        this.currentState = GameState.PAUSED;\n\n        // 暂停游戏时间\n        director.pause();\n\n        // 显示暂停面板\n        if (this.pausePanel) {\n            this.pausePanel.active = true;\n        }\n\n        console.log('游戏已暂停');\n    }\n\n    /**\n     * 继续游戏\n     */\n    public resumeGame() {\n        if (this.currentState !== GameState.PAUSED) return;\n\n        this.currentState = GameState.RUNNING;\n\n        // 恢复游戏时间\n        director.resume();\n\n        // 隐藏暂停面板\n        if (this.pausePanel) {\n            this.pausePanel.active = false;\n        }\n\n        console.log('游戏已继续');\n    }\n\n    /**\n     * 游戏结束\n     * @param isVictory 是否胜利\n     */\n    public gameOver(isVictory: boolean) {\n        if (this.currentState === GameState.GAME_OVER) return;\n\n        this.currentState = GameState.GAME_OVER;\n        this.gameEndTime = Date.now();\n\n        // 暂停游戏\n        director.pause();\n\n        // 计算游戏结果数据\n        const gameResult = this.calculateGameResult(isVictory);\n\n        // 显示游戏结束面板并传递数据\n        if (this.gameOverPanel) {\n            this.gameOverPanel.active = true;\n            \n\n            // 获取GameOverPanel组件并设置数据\n            const gameOverPanelComponent = this.gameOverPanel.getComponent(GameOverPanel);\n            if (gameOverPanelComponent) {\n                gameOverPanelComponent.bindButtonEvents();\n                gameOverPanelComponent.setGameOverInfo(\n                    isVictory,\n                    gameResult.performance,\n                    gameResult.reward,\n                    gameResult.gameTime,\n                    gameResult.healthPercentage,\n                    gameResult.stars\n                );\n            }\n        }\n\n        // 给予玩家奖励\n        PlayerManager.instance.addMoney(gameResult.reward);\n\n        // 更新关卡进度，不再传递时间参数\n        this.updateLevelProgress(gameResult.stars, gameResult.performance);\n\n        PlayerManager.instance.savePlayerData();\n\n        console.log(isVictory ? '游戏胜利！' : '游戏失败！');\n    }\n\n    /**\n     * 重新开始游戏\n     */\n    public restartGame() {\n        // 恢复游戏时间\n        director.resume();\n\n        // 重新加载当前场景\n        SceneTransition.loadScene(director.getScene()!.name);\n    }\n\n    /**\n     * 返回主菜单\n     */\n    public returnToLevelSelect() {\n        // 恢复游戏时间\n        director.resume();\n\n        // 加载主菜单场景\n        SceneTransition.loadScene('LevelSelect');\n        SoundManager.instance.stopbattleBGM()\n        SoundManager.instance.playBGM()\n    }\n\n    /**\n     * 计算游戏结果数据\n     * @param isVictory 是否胜利\n     * @returns 游戏结果数据\n     */\n    private calculateGameResult(isVictory: boolean): {\n        performance: string;\n        reward: number;\n        gameTime: number;\n        healthPercentage: number;\n        stars: number;\n    } {\n        // 计算游戏时长（秒）\n        const gameTimeSec = (this.gameEndTime - this.gameStartTime) / 1000;\n\n        // 计算生命值百分比\n        const healthPercentage = this.playerHP / this.playerMaxHP;\n\n        if (!isVictory) {\n            // 失败时返回基础数据\n            return {\n                performance: '',\n                reward: 10,\n                gameTime: gameTimeSec,\n                healthPercentage: healthPercentage,\n                stars: 0\n            };\n        }\n\n        // 计算星星数（基于生命值和时间）\n        const stars = this.calculateStars(gameTimeSec, healthPercentage);\n\n        // 计算表现评价和奖励\n        const { performance, reward } = this.calculatePerformance(gameTimeSec, healthPercentage);\n\n        console.log(`游戏时长: ${gameTimeSec.toFixed(1)}秒, 生命值: ${(healthPercentage * 100).toFixed(1)}%, 星星: ${stars}, 评价: ${performance}, 奖励: ${reward}金币`);\n\n        return {\n            performance,\n            reward,\n            gameTime: gameTimeSec,\n            healthPercentage,\n            stars\n        };\n    }\n\n    /**\n     * 计算星星数（基于颜料占比）\n     */\n    private calculateStars(gameTime: number, healthPercentage: number): number {\n        // 获取玩家颜料占比\n        if (!this.paintManager) {\n            return 1; // 如果没有颜料管理器，默认1星\n        }\n\n        const playerPaintCount = this.paintManager.getPaintCountByOwner('player');\n        const totalPaintCount = this.paintManager.getTotalPaintCount();\n\n        if (totalPaintCount === 0) {\n            return 1; // 如果没有颜料，默认1星\n        }\n\n        const playerRatio = playerPaintCount / totalPaintCount;\n        const playerPercentage = playerRatio * 100;\n\n        // 检查是否摧毁了所有AI车辆\n        const destroyedAllEnemies = this.enemyCount <= 0 && this.initialEnemyCount > 0;\n\n        // 根据新的评价规则计算星星\n        if (playerPercentage >= 45  || destroyedAllEnemies) {\n            return 3; // 3星（A级）：颜料数量>=45% 或 摧毁所有AI车辆\n        } else if (playerPercentage >= 35) {\n            return 2; // 2星（B级）：颜料数量>=35%\n        } else if (playerPercentage >= 25) {\n            return 1; // 1星（C级）：颜料数量>=25% \n        } else {\n            return 0; // 不满足任何条件，0星\n        }\n    }\n\n    /**\n     * 更新关卡进度\n     * @param stars 星星数\n     * @param performance 表现评价\n     */\n    private updateLevelProgress(stars: number, performance?: string) {\n        // 获取当前关卡ID\n        const currentLevelId = TempData.selectedLevel;\n        if (!currentLevelId) {\n            console.warn('无法获取当前关卡ID');\n            return;\n        }\n\n        // 更新PlayerManager中的关卡进度（不再传递时间参数）\n        PlayerManager.instance.updateLevelProgress(currentLevelId, stars, performance);\n\n        console.log(`关卡进度已更新: ${currentLevelId}, 星星: ${stars}`);\n    }\n\n    /**\n     * 计算表现评价（基于星星数）\n     */\n    private calculatePerformance(gameTime: number, healthPercentage: number): { performance: string, reward: number } {\n        // 先计算星星数\n        const stars = this.calculateStars(gameTime, healthPercentage);\n\n        let performance: string;\n        let reward: number;\n\n        // 根据星星数确定评价和奖励\n        switch (stars) {\n            case 3:\n                performance = 'S';\n                reward = 300;\n                break;\n            case 2:\n                performance = 'A';\n                reward = 200;\n                break;\n            case 1:\n                performance = 'B';\n                reward = 100;\n                break;\n            default:\n                performance = 'F';\n                reward = 20;\n                break;\n        }\n\n        return { performance, reward };\n    }\n\n\n\n    // ==================== 公共方法 ====================\n\n    /**\n     * 获取当前游戏状态\n     */\n    public getCurrentState(): GameState {\n        return this.currentState;\n    }\n\n    /**\n     * 获取游戏时长（秒）\n     */\n    public getGameTime(): number {\n        if (this.gameEndTime > 0) {\n            return (this.gameEndTime - this.gameStartTime) / 1000;\n        }\n        return (Date.now() - this.gameStartTime) / 1000;\n    }\n\n    /**\n     * 获取玩家当前生命值\n     */\n    public getPlayerHP(): number {\n        return this.playerHP;\n    }\n\n    /**\n     * 获取玩家最大生命值\n     */\n    public getPlayerMaxHP(): number {\n        return this.playerMaxHP;\n    }\n\n    /**\n     * 获取剩余敌人数量\n     */\n    public getEnemyCount(): number {\n        return this.enemyCount;\n    }\n\n    // ==================== 颜料系统 ====================\n\n    /**\n     * 初始化颜料系统\n     */\n    private initializePaintSystem(): void {\n        // 查找或创建PaintManager\n        this.paintManager = this.node.scene.getComponentInChildren(PaintManager);\n\n        \n    }\n\n    /**\n     * 清除所有颜料（游戏重新开始时调用）\n     */\n    public clearAllPaint(): void {\n        if (this.paintManager) {\n            this.paintManager.clearAllPaint();\n        }\n    }\n\n    /**\n     * 车辆喷洒颜料的中介方法\n     * @param paintPrefab 颜料预制体\n     * @param worldPosition 世界坐标位置\n     * @param vehicleId 车辆ID\n     */\n    public sprayPaint(paintPrefab: Prefab, worldPosition: Vec3, vehicleId: string): void {\n        if (this.paintManager && paintPrefab) {\n            this.paintManager.addPaint(paintPrefab, worldPosition, vehicleId);\n        } else {\n            console.warn('GameManager: 无法喷洒颜料，PaintManager或颜料预制体为空');\n        }\n    }\n\n    /**\n     * 获取所有车辆的颜料占比\n     * @returns 包含每个车辆ID和其占比的对象\n     */\n    public getAllVehiclePaintRatios(): { [vehicleId: string]: number } {\n        if (this.paintManager) {\n            return this.paintManager.getAllPaintRatios();\n        }\n        return {};\n    }\n\n    /**\n     * 获取排序后的颜料占比（从高到低）\n     * @returns 按占比排序的数组\n     */\n    public getSortedVehiclePaintRatios(): Array<{ vehicleId: string, ratio: number, count: number }> {\n        if (this.paintManager) {\n            const sorted = this.paintManager.getSortedPaintRatios();\n            // 将ownerId重命名为vehicleId以保持一致性\n            return sorted.map(item => ({\n                vehicleId: item.ownerId,\n                ratio: item.ratio,\n                count: item.count\n            }));\n        }\n        return [];\n    }\n\n    /**\n     * 获取颜料管理器\n     */\n    public getPaintManager(): PaintManager | null {\n        return this.paintManager;\n    }\n\n    // ==================== 倒计时系统 ====================\n\n    /**\n     * 更新倒计时\n     * @param deltaTime 帧时间间隔\n     */\n    private updateCountdown(deltaTime: number): void {\n        this.remainingTime -= deltaTime;\n\n        // 检查是否时间到了\n        if (this.remainingTime <= 0) {\n            this.remainingTime = 0;\n            this.onCountdownFinished();\n        }\n    }\n\n    /**\n     * 倒计时结束处理\n     */\n    private onCountdownFinished(): void {\n        console.log('倒计时结束，游戏结束');\n        // 计算最终的颜料占比并结束游戏\n        this.gameOver(this.determineWinner());\n    }\n\n    /**\n     * 确定获胜者（基于新的胜利条件）\n     * @returns 是否玩家获胜\n     */\n    private determineWinner(): boolean {\n        // 检查玩家是否存活\n        if (this.playerHP <= 0) {\n            console.log('玩家已死亡，游戏失败');\n            return false;\n        }\n\n        // 检查是否所有AI车辆都被摧毁\n        if (this.enemyCount <= 0 && this.initialEnemyCount > 0) {\n            console.log('所有AI车辆已被摧毁，游戏胜利');\n            return true;\n        }\n\n        // 获取玩家颜料占比\n        if (!this.paintManager) {\n            return false; // 如果没有颜料管理器，默认玩家失败\n        }\n\n        const playerPaintCount = this.paintManager.getPaintCountByOwner('player');\n        const totalPaintCount = this.paintManager.getTotalPaintCount();\n\n        if (totalPaintCount === 0) {\n            return false; // 如果没有颜料，默认玩家失败\n        }\n\n        const playerRatio = playerPaintCount / totalPaintCount;\n        console.log(`玩家颜料占比: ${(playerRatio * 100).toFixed(1)}%`);\n\n        // 玩家存活且颜料占比>25%则获胜\n        if (playerRatio > 0.25) {\n            console.log('玩家颜料占比超过25%，游戏胜利');\n            return true;\n        } else {\n            console.log('玩家颜料占比不足25%，游戏失败');\n            return false;\n        }\n    }\n\n    /**\n     * 获取剩余时间\n     * @returns 剩余时间（秒）\n     */\n    public getRemainingTime(): number {\n        return Math.max(0, this.remainingTime);\n    }\n\n    /**\n     * 获取剩余时间的格式化字符串\n     * @returns 格式化的时间字符串 (MM:SS)\n     */\n    public getFormattedRemainingTime(): string {\n        const totalSeconds = Math.ceil(this.getRemainingTime());\n        const minutes = Math.floor(totalSeconds / 60);\n        const seconds = totalSeconds % 60;\n\n        // 手动实现padStart功能以兼容旧版本\n        const minutesStr = minutes < 10 ? '0' + minutes : minutes.toString();\n        const secondsStr = seconds < 10 ? '0' + seconds : seconds.toString();\n\n        return `${minutesStr}:${secondsStr}`;\n    }\n\n    // ==================== 射击系统 ====================\n\n    /**\n     * 玩家射击\n     */\n    public playerShoot(): void {\n        if (this.playerComponent) {\n            this.playerComponent.shoot();\n        }\n    }\n\n    /**\n     * 发射子弹\n     * @param bulletPrefab 子弹预制体\n     * @param position 发射位置\n     * @param direction 发射方向\n     * @param shooterId 发射者ID\n     * @param weaponType 武器类型\n     */\n    public fireBullet(bulletPrefab: Prefab, position: Vec3, direction: Vec2, shooterId: string, weaponType: WeaponType): void {\n        // 特殊处理DART类型子弹，生成4个分别向上下左右的子弹\n        if (weaponType === WeaponType.DART) {\n            // 定义四个方向：上、右、下、左\n            const directions = [\n                new Vec2(0, 1),   // 上\n                new Vec2(1, 0),   // 右\n                new Vec2(0, -1),  // 下\n                new Vec2(-1, 0),  // 左\n                new Vec2(-1, -1),\n                new Vec2(1, 1),\n                new Vec2(-1, 1),\n                new Vec2(1, -1),\n            ];\n\n            // 为每个方向创建一个子弹\n            for (const dir of directions) {\n                this.createSingleBullet(bulletPrefab, position, dir, shooterId, weaponType);\n            }\n        } else {\n            // 其他类型子弹按正常方式处理\n            this.createSingleBullet(bulletPrefab, position, direction, shooterId, weaponType);\n        }\n    }\n\n    /**\n     * 创建单个子弹\n     * @param bulletPrefab 子弹预制体\n     * @param position 发射位置\n     * @param direction 发射方向\n     * @param shooterId 发射者ID\n     * @param weaponType 武器类型\n     */\n    private createSingleBullet(bulletPrefab: Prefab, position: Vec3, direction: Vec2, shooterId: string, weaponType: WeaponType): void {\n        // 实例化子弹\n        const bulletNode = instantiate(bulletPrefab);\n\n        // 获取子弹组件\n        const bulletComponent = bulletNode.getComponent(Bullet);\n        if (bulletComponent) {\n            // 初始化子弹\n            bulletComponent.init(direction, shooterId);\n            // 设置子弹类型（WeaponType和BulletType值相同）\n            bulletComponent.bulletType = weaponType as any;\n        }\n\n        // 将子弹添加到BulletRoot节点或场景中\n        if (this.bulletRoot) {\n            // 转换世界坐标到BulletRoot的本地坐标\n            const localPos = this.bulletRoot.getComponent(UITransform)?.convertToNodeSpaceAR(position);\n            if (localPos) {\n                bulletNode.setPosition(localPos);\n            } else {\n                console.warn('BulletRoot转换失败，请检查');\n                bulletNode.setWorldPosition(position);\n            }\n            this.bulletRoot.addChild(bulletNode);\n            console.log('子弹已添加到BulletRoot节点');\n        }\n    }\n\n    /**\n     * 获取玩家组件\n     */\n    public getPlayerComponent(): player | null {\n        return this.playerComponent;\n    }\n\n}"]}