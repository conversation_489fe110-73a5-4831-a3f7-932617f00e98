{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts"], "names": ["_decorator", "ccclass", "CarProperties", "getCarProperty", "carId", "carPropertiesConfig", "speed", "steering", "durability"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcD,U;AAEpB;AACA;AACA;;AAOA;AACA;AACA;AACA;+BAEaE,a,WADZD,OAAO,CAAC,eAAD,C,2BAAR,MACaC,aADb,CAC2B;AAiCvB;AACJ;AACA;AACA;AACA;AACgC,eAAdC,cAAc,CAACC,KAAD,EAAoC;AAC5D,iBAAO,KAAKC,mBAAL,CAAyBD,KAAzB,KAAmC,IAA1C;AACH;;AAxCsB,O,UAKRC,mB,GAAwD;AACnE,iBAAS;AACLC,UAAAA,KAAK,EAAE,EADF;AAELC,UAAAA,QAAQ,EAAE,EAFL;AAGLC,UAAAA,UAAU,EAAE;AAHP,SAD0D;AAMnE,iBAAS;AACLF,UAAAA,KAAK,EAAE,EADF;AAELC,UAAAA,QAAQ,EAAE,EAFL;AAGLC,UAAAA,UAAU,EAAE;AAHP,SAN0D;AAWnE,iBAAS;AACLF,UAAAA,KAAK,EAAE,EADF;AAELC,UAAAA,QAAQ,EAAE,EAFL;AAGLC,UAAAA,UAAU,EAAE;AAHP,SAX0D;AAgBnE,iBAAS;AACLF,UAAAA,KAAK,EAAE,EADF;AAELC,UAAAA,QAAQ,EAAE,EAFL;AAGLC,UAAAA,UAAU,EAAE;AAHP,SAhB0D;AAqBnE,iBAAS;AACLF,UAAAA,KAAK,EAAE,EADF;AAELC,UAAAA,QAAQ,EAAE,EAFL;AAGLC,UAAAA,UAAU,EAAE;AAHP;AArB0D,O", "sourcesContent": ["import { _decorator } from 'cc';\nconst { ccclass } = _decorator;\n\n/**\n * 车辆属性接口\n */\nexport interface CarProperty {\n    speed: number;      // 速度 (0-100)\n    steering: number;   // 转向 (0-100)\n    durability: number; // 坚硬度/耐久度 (0-100)\n}\n\n/**\n * 车辆属性配置类\n * 管理所有车辆的属性数据\n */\n@ccclass('CarProperties')\nexport class CarProperties {\n    /**\n     * 车辆属性配置表\n     * 键为车辆ID，值为车辆属性\n     */\n    private static carPropertiesConfig: { [carId: string]: CarProperty } = {\n        'car-1': {\n            speed: 25,\n            steering: 60,\n            durability: 40\n        },\n        'car-2': {\n            speed: 40,\n            steering: 40,\n            durability: 80\n        },\n        'car-3': {\n            speed: 60,\n            steering: 60,\n            durability: 60\n        },\n        'car-4': {\n            speed: 70,\n            steering: 70,\n            durability: 50\n        },\n        'car-5': {\n            speed: 65,\n            steering: 80,\n            durability: 60\n        }\n    };\n\n    /**\n     * 获取指定车辆的属性\n     * @param carId 车辆ID\n     * @returns 车辆属性，如果车辆不存在则返回null\n     */\n    public static getCarProperty(carId: string): CarProperty | null {\n        return this.carPropertiesConfig[carId] || null;\n    }\n\n}\n"]}