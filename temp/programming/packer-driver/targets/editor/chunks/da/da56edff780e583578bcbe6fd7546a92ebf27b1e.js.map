{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"], "names": ["_decorator", "Component", "AudioSource", "director", "sys", "ccclass", "property", "SoundManager", "type", "tooltip", "allAudioSources", "instance", "_instance", "onLoad", "destroy", "addPersistRootNode", "node", "bgmAudioSource", "push", "bgmbattleAudioSource", "bgmbattle2AudioSource", "buttonClickAudioSource", "carCollisionAudioSource", "carDestructionAudioSource", "carStartAudioSource", "carDriftAudioSource", "weaponFireAudioSource", "loadState", "start", "scheduleOnce", "playBGM", "clip", "play", "console", "log", "warn", "error", "stopBGM", "stop", "stopbattleBGM", "playSoundEffect", "soundName", "sourceToPlay", "toggleAudio", "muted", "isMuted", "newVolume", "for<PERSON>ach", "source", "volume", "saveState", "state", "localStorage", "setItem", "JSON", "stringify", "stateStr", "getItem", "parse"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA4BC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,G,OAAAA,G;;;;;;;;;OAClE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;8BAGjBO,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMPH,QAAQ,CAAC;AACPE,QAAAA,IAAI,EAAEN,WADC;AAEPO,QAAAA,OAAO,EAAE;AAFF,OAAD,C,UAMRH,QAAQ,CAAC;AACPE,QAAAA,IAAI,EAAEN,WADC;AAEPO,QAAAA,OAAO,EAAE;AAFF,OAAD,C,UAMTH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,sCApEb,MACaF,YADb,SACkCN,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA0EhCS,eA1EgC,GA0EC,EA1ED;AAAA;;AA8Ed,mBAARC,QAAQ,GAAiB;AACvC,iBAAO,KAAKC,SAAZ;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAIN,YAAY,CAACK,SAAjB,EAA4B;AACxB,iBAAKE,OAAL;AACA;AACH;;AACDP,UAAAA,YAAY,CAACK,SAAb,GAAyB,IAAzB;AACAT,UAAAA,QAAQ,CAACY,kBAAT,CAA4B,KAAKC,IAAjC,EANK,CAQL;;AACA,cAAI,KAAKC,cAAT,EAAyB,KAAKP,eAAL,CAAqBQ,IAArB,CAA0B,KAAKD,cAA/B;AACzB,cAAI,KAAKE,oBAAT,EAA+B,KAAKT,eAAL,CAAqBQ,IAArB,CAA0B,KAAKC,oBAA/B;AAC/B,cAAI,KAAKC,qBAAT,EAAgC,KAAKV,eAAL,CAAqBQ,IAArB,CAA0B,KAAKE,qBAA/B;AAChC,cAAI,KAAKC,sBAAT,EAAiC,KAAKX,eAAL,CAAqBQ,IAArB,CAA0B,KAAKG,sBAA/B;AACjC,cAAI,KAAKC,uBAAT,EAAkC,KAAKZ,eAAL,CAAqBQ,IAArB,CAA0B,KAAKI,uBAA/B;AAClC,cAAI,KAAKC,yBAAT,EAAoC,KAAKb,eAAL,CAAqBQ,IAArB,CAA0B,KAAKK,yBAA/B;AACpC,cAAI,KAAKC,mBAAT,EAA8B,KAAKd,eAAL,CAAqBQ,IAArB,CAA0B,KAAKM,mBAA/B;AAC9B,cAAI,KAAKC,mBAAT,EAA8B,KAAKf,eAAL,CAAqBQ,IAArB,CAA0B,KAAKO,mBAA/B;AAC9B,cAAI,KAAKC,qBAAT,EAAgC,KAAKhB,eAAL,CAAqBQ,IAArB,CAA0B,KAAKQ,qBAA/B,EAjB3B,CAkBL;AACA;AACA;;AACA,eAAKC,SAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,OAAL;AACH,WAFD,EAEG,GAFH;AAGH;;AAEDA,QAAAA,OAAO,GAAG;AACN,cAAI;AACA,gBAAI,KAAKb,cAAL,IAAuB,KAAKA,cAAL,CAAoBc,IAA/C,EAAqD;AACjD,mBAAKd,cAAL,CAAoBe,IAApB;AACAC,cAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;AACH,aAHD,MAGO;AACHD,cAAAA,OAAO,CAACE,IAAR,CAAa,iCAAb;AACH;AACJ,WAPD,CAOE,OAAOC,KAAP,EAAc;AACZH,YAAAA,OAAO,CAACG,KAAR,CAAc,oBAAd,EAAoCA,KAApC;AACH;AACJ;;AACDC,QAAAA,OAAO,GAAG;AACN,cAAI;AACA,gBAAI,KAAKpB,cAAT,EAAyB;AACrB,mBAAKA,cAAL,CAAoBqB,IAApB;AACAL,cAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ;AACH,aAHD,MAIK;AACDD,cAAAA,OAAO,CAACE,IAAR,CAAa,yBAAb;AACH;AACJ,WARD,CAQE,OAAOC,KAAP,EAAc;AACZH,YAAAA,OAAO,CAACG,KAAR,CAAc,qBAAd,EAAqCA,KAArC;AACH;AACJ;;AAEDG,QAAAA,aAAa,GAAG;AACZ,cAAI;AACA,gBAAI,KAAKpB,oBAAT,EAA+B;AAC3B,mBAAKA,oBAAL,CAA0BmB,IAA1B;AACAL,cAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACH,aAHD,MAIK,IAAI,KAAKd,qBAAT,EAAgC;AACjC,mBAAKA,qBAAL,CAA2BkB,IAA3B;AACAL,cAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACH,aAHI,MAIA;AACDD,cAAAA,OAAO,CAACE,IAAR,CAAa,gCAAb;AACH;AACJ,WAZD,CAYE,OAAOC,KAAP,EAAc;AACZH,YAAAA,OAAO,CAACG,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;AACH;AACJ;;AAEDI,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAIC,YAAgC,GAAG,IAAvC;;AACA,kBAAQD,SAAR;AACI,iBAAK,aAAL;AACIC,cAAAA,YAAY,GAAG,KAAKrB,sBAApB;AACA;;AACJ,iBAAK,cAAL;AACIqB,cAAAA,YAAY,GAAG,KAAKpB,uBAApB;AACA;;AACJ,iBAAK,gBAAL;AACIoB,cAAAA,YAAY,GAAG,KAAKnB,yBAApB;AACA;;AACJ,iBAAK,UAAL;AACImB,cAAAA,YAAY,GAAG,KAAKlB,mBAApB;AACA;;AACJ,iBAAK,UAAL;AACIkB,cAAAA,YAAY,GAAG,KAAKjB,mBAApB;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,iBAAK,YAAL;AACIiB,cAAAA,YAAY,GAAG,KAAKhB,qBAApB;AACA;;AACJ,iBAAK,YAAL;AACIgB,cAAAA,YAAY,GAAG,KAAKvB,oBAApB;AACA;;AACJ,iBAAK,YAAL;AACIuB,cAAAA,YAAY,GAAG,KAAKtB,qBAApB;AACA;AAjCR;;AAoCA,cAAIsB,YAAJ,EAAkB;AACdA,YAAAA,YAAY,CAACV,IAAb;AACH;AACJ;;AAEDW,QAAAA,WAAW,GAAG;AACV,gBAAMC,KAAK,GAAG,KAAKC,OAAL,EAAd;AACA,gBAAMC,SAAS,GAAGF,KAAK,GAAG,CAAH,GAAO,CAA9B;AACA,eAAKlC,eAAL,CAAqBqC,OAArB,CAA6BC,MAAM,IAAI;AACnC,gBAAIA,MAAM,KAAG,KAAK/B,cAAd,IAAgC+B,MAAM,KAAG,KAAK7B,oBAA9C,IAAsE6B,MAAM,KAAG,KAAK5B,qBAAxF,EAAgH;AACxG4B,cAAAA,MAAM,CAACC,MAAP,GAAgBH,SAAS,GAAG,GAA5B;AACH,aAFL,MAGS,IAAIE,MAAJ,EAAY;AACbA,cAAAA,MAAM,CAACC,MAAP,GAAgBH,SAAhB;AACH;AACR,WAPD;AAQA,eAAKI,SAAL;AACH;;AAEDL,QAAAA,OAAO,GAAY;AACf;AACA,iBAAO,KAAK5B,cAAL,GAAsB,KAAKA,cAAL,CAAoBgC,MAApB,KAA+B,CAArD,GAAyD,KAAhE,CAFe,CAGf;AACH;;AAEOC,QAAAA,SAAS,GAAG;AAChB,gBAAMC,KAAK,GAAG;AACVP,YAAAA,KAAK,EAAE,KAAKC,OAAL;AADG,WAAd;AAGAzC,UAAAA,GAAG,CAACgD,YAAJ,CAAiBC,OAAjB,CAAyB,YAAzB,EAAuCC,IAAI,CAACC,SAAL,CAAeJ,KAAf,CAAvC;AACH;;AAEOxB,QAAAA,SAAS,GAAG;AAChB,gBAAM6B,QAAQ,GAAGpD,GAAG,CAACgD,YAAJ,CAAiBK,OAAjB,CAAyB,YAAzB,CAAjB;;AACA,cAAID,QAAJ,EAAc;AACV,kBAAML,KAAK,GAAGG,IAAI,CAACI,KAAL,CAAWF,QAAX,CAAd;AACA,kBAAMP,MAAM,GAAGE,KAAK,CAACP,KAAN,GAAc,CAAd,GAAkB,CAAjC;AACA,iBAAKlC,eAAL,CAAqBqC,OAArB,CAA6BC,MAAM,IAAI;AACnC,kBAAIA,MAAM,KAAG,KAAK/B,cAAd,IAAgC+B,MAAM,KAAG,KAAK7B,oBAA9C,IAAsE6B,MAAM,KAAG,KAAK5B,qBAAxF,EAAgH;AAC5G4B,gBAAAA,MAAM,CAACC,MAAP,GAAgBA,MAAM,GAAG,GAAzB;AACH,eAFD,MAGK,IAAID,MAAJ,EAAY;AACbA,gBAAAA,MAAM,CAACC,MAAP,GAAgBA,MAAhB;AACH;AACJ,aAPD;AAQH;AACJ;;AAjPuC,O,UA4EzBrC,S,GAA0B,I;;;;;iBAvEJ,I;;;;;;;iBAMM,I;;;;;;;iBAMC,I;;;;;;;iBAMC,I;;;;;;;iBAMC,I;;;;;;;iBAME,I;;;;;;;iBAMN,I;;;;;;;iBAMA,I;;;;;;;iBAME,I;;;;;;;iBAMD,I;;;;;;;iBAMA,I;;;;;;;iBAMF,I", "sourcesContent": ["import { _decorator, Component, Node, AudioClip, AudioSource, director, sys } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('SoundManager')\nexport class SoundManager extends Component {\n    @property({\n        type: AudioSource,\n        tooltip: \"背景音乐\"\n    })\n    public bgmAudioSource: AudioSource = null!;\n\n     @property({\n        type: AudioSource,\n        tooltip: \"战斗音乐1\"\n    })\n    public bgmbattleAudioSource: AudioSource = null!;\n\n     @property({\n        type: AudioSource,\n        tooltip: \"战斗音乐2\"\n    })\n    public bgmbattle2AudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"按钮点击音效\"\n    })\n    public buttonClickAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆碰撞音效\"\n    })\n    public carCollisionAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆毁坏音效\"\n    })\n    public carDestructionAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆启动音效\"\n    })\n    public carStartAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆漂移音效\"\n    })\n    public carDriftAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"武器发射音效\"\n    })\n    public weaponFireAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"火箭命中音效\"\n    })\n    public rocketHitAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"子弹命中音效\"\n    })\n    public bulletHitAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"飞镖命中音效\"\n    })\n    public dartHitAudioSource: AudioSource = null!;\n\n\n    private allAudioSources: AudioSource[] = [];\n\n    private static _instance: SoundManager = null!;\n\n    public static get instance(): SoundManager {\n        return this._instance;\n    }\n\n    onLoad() {\n        if (SoundManager._instance) {\n            this.destroy();\n            return;\n        }\n        SoundManager._instance = this;\n        director.addPersistRootNode(this.node);\n\n        // 将所有音源收集到一个数组中，方便统一管理（只添加非空的音源）\n        if (this.bgmAudioSource) this.allAudioSources.push(this.bgmAudioSource);\n        if (this.bgmbattleAudioSource) this.allAudioSources.push(this.bgmbattleAudioSource);\n        if (this.bgmbattle2AudioSource) this.allAudioSources.push(this.bgmbattle2AudioSource);\n        if (this.buttonClickAudioSource) this.allAudioSources.push(this.buttonClickAudioSource);\n        if (this.carCollisionAudioSource) this.allAudioSources.push(this.carCollisionAudioSource);\n        if (this.carDestructionAudioSource) this.allAudioSources.push(this.carDestructionAudioSource);\n        if (this.carStartAudioSource) this.allAudioSources.push(this.carStartAudioSource);\n        if (this.carDriftAudioSource) this.allAudioSources.push(this.carDriftAudioSource);\n        if (this.weaponFireAudioSource) this.allAudioSources.push(this.weaponFireAudioSource);\n        // if (this.rocketHitAudioSource) this.allAudioSources.push(this.rocketHitAudioSource);\n        // if (this.bulletHitAudioSource) this.allAudioSources.push(this.bulletHitAudioSource);\n        // if (this.dartHitAudioSource) this.allAudioSources.push(this.dartHitAudioSource);\n        this.loadState();\n    }\n\n    start() {\n        // 延迟播放BGM，确保所有系统初始化完成\n        this.scheduleOnce(() => {\n            this.playBGM();\n        }, 0.1);\n    }\n\n    playBGM() {\n        try {\n            if (this.bgmAudioSource && this.bgmAudioSource.clip) {\n                this.bgmAudioSource.play();\n                console.log('BGM started playing');\n            } else {\n                console.warn('BGM AudioSource or clip is null');\n            }\n        } catch (error) {\n            console.error('Error playing BGM:', error);\n        }\n    }\n    stopBGM() {\n        try {\n            if (this.bgmAudioSource) {\n                this.bgmAudioSource.stop();\n                console.log('BGM stopped');\n            } \n            else {\n                console.warn('BGM AudioSource is null');\n            }\n        } catch (error) {\n            console.error('Error stopping BGM:', error);\n        }\n    }\n\n    stopbattleBGM() {\n        try {\n            if (this.bgmbattleAudioSource) {\n                this.bgmbattleAudioSource.stop();\n                console.log('Battle BGM stopped');\n            } \n            else if (this.bgmbattle2AudioSource) {\n                this.bgmbattle2AudioSource.stop();\n                console.log('Battle BGM stopped');\n            }\n            else {\n                console.warn('Battle BGM AudioSource is null');\n            }\n        } catch (error) {\n            console.error('Error stopping Battle BGM:', error);\n        }\n    }\n\n    playSoundEffect(soundName: string) {\n        let sourceToPlay: AudioSource | null = null;\n        switch (soundName) {\n            case 'buttonClick':\n                sourceToPlay = this.buttonClickAudioSource;\n                break;\n            case 'carCollision':\n                sourceToPlay = this.carCollisionAudioSource;\n                break;\n            case 'carDestruction':\n                sourceToPlay = this.carDestructionAudioSource;\n                break;\n            case 'carStart':\n                sourceToPlay = this.carStartAudioSource;\n                break;\n            case 'carDrift':\n                sourceToPlay = this.carDriftAudioSource;\n                break;\n            // case 'bulletHit':\n            //     sourceToPlay = this.bulletHitAudioSource;\n            //     break;\n            // case 'dartHit':\n            //     sourceToPlay = this.dartHitAudioSource;\n            //     break;\n            // case 'explosion':\n            //     sourceToPlay = this.rocketHitAudioSource;\n            //     break;\n            case 'weaponFire':\n                sourceToPlay = this.weaponFireAudioSource;\n                break;\n            case 'battlebgm1':\n                sourceToPlay = this.bgmbattleAudioSource;\n                break;\n            case 'battlebgm2':\n                sourceToPlay = this.bgmbattle2AudioSource;\n                break;\n        }\n\n        if (sourceToPlay) {\n            sourceToPlay.play();\n        }\n    }\n\n    toggleAudio() {\n        const muted = this.isMuted();\n        const newVolume = muted ? 1 : 0;\n        this.allAudioSources.forEach(source => {\n            if (source===this.bgmAudioSource || source===this.bgmbattleAudioSource || source===this.bgmbattle2AudioSource ) {\n                    source.volume = newVolume * 0.3;\n                }\n                else if (source) {\n                    source.volume = newVolume;\n                }\n        });\n        this.saveState();\n    }\n\n    isMuted(): boolean {\n        // 检查BGM音源的音量作为代表\n        return this.bgmAudioSource ? this.bgmAudioSource.volume === 0 : false;\n        // return false;\n    }\n\n    private saveState() {\n        const state = {\n            muted: this.isMuted()\n        };\n        sys.localStorage.setItem('soundState', JSON.stringify(state));\n    }\n\n    private loadState() {\n        const stateStr = sys.localStorage.getItem('soundState');\n        if (stateStr) {\n            const state = JSON.parse(stateStr);\n            const volume = state.muted ? 0 : 1;\n            this.allAudioSources.forEach(source => {\n                if (source===this.bgmAudioSource || source===this.bgmbattleAudioSource || source===this.bgmbattle2AudioSource ) {\n                    source.volume = volume * 0.3;\n                }\n                else if (source) {\n                    source.volume = volume;\n                }\n            });\n        }\n    }\n}"]}