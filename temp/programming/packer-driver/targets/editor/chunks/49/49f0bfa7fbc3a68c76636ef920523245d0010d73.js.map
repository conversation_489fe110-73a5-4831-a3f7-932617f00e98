{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/StartupController.ts"], "names": ["_decorator", "Component", "director", "ccclass", "StartupController", "start", "console", "log", "scheduleOnce", "loadMainMenu", "loadScene", "error"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;;;;;;;;;OAC1B;AAAEC,QAAAA;AAAF,O,GAAcH,U;AAEpB;AACA;AACA;AACA;AACA;;mCAEaI,iB,WADZD,OAAO,CAAC,mBAAD,C,gBAAR,MACaC,iBADb,SACuCH,SADvC,CACiD;AAE7CI,QAAAA,KAAK,GAAG;AACJC,UAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ,EADI,CAGJ;;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,YAAL;AACH,WAFD,EAEG,GAFH;AAGH;;AAEOA,QAAAA,YAAY,GAAG;AACnB,cAAI;AACAH,YAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;AACAL,YAAAA,QAAQ,CAACQ,SAAT,CAAmB,UAAnB;AACH,WAHD,CAGE,OAAOC,KAAP,EAAc;AACZL,YAAAA,OAAO,CAACK,KAAR,CAAc,6CAAd,EAA6DA,KAA7D,EADY,CAEZ;;AACAT,YAAAA,QAAQ,CAACQ,SAAT,CAAmB,aAAnB;AACH;AACJ;;AApB4C,O", "sourcesContent": ["import { _decorator, Component, director } from 'cc';\nconst { ccclass } = _decorator;\n\n/**\n * 启动控制器\n * 用于处理游戏启动时的初始化逻辑\n * 避免在主菜单场景中直接初始化复杂的单例组件\n */\n@ccclass('StartupController')\nexport class StartupController extends Component {\n    \n    start() {\n        console.log('StartupController: Game starting...');\n        \n        // 延迟加载主菜单，确保所有系统准备就绪\n        this.scheduleOnce(() => {\n            this.loadMainMenu();\n        }, 0.5);\n    }\n\n    private loadMainMenu() {\n        try {\n            console.log('StartupController: Loading main menu...');\n            director.loadScene('mainmenu');\n        } catch (error) {\n            console.error('StartupController: Error loading main menu:', error);\n            // 如果主菜单加载失败，直接加载关卡选择\n            director.loadScene('LevelSelect');\n        }\n    }\n}\n"]}