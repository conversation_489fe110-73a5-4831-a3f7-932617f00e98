System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts at runtime.
      throw new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts: Unexpected reserved word 'private'. (132:4)

  130 |      * 碰撞回调
  131 |      */
> 132 |     private onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {
      |     ^
  133 |         const otherNode = otherCollider.node;
  134 |         
  135 |         // 不与发射者碰撞`);
    }
  };
});
//# sourceMappingURL=c8382469d7ce42995c27c2923c2084adef742aeb.js.map