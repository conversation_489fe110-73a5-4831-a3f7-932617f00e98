{"version": 3, "sources": ["data:text/javascript,%0A%2F%2F%20This%20module%20is%20auto-generated%20to%20report%20error%20emitted%20when%20try%20to%20load%20module%20file%3A%2F%2F%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FBullet.ts%20at%20runtime.%0Athrow%20new%20Error(%60SyntaxError%3A%20%2Ffile%3A%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FBullet.ts%3A%20Unexpected%20reserved%20word%20'private'.%20(132%3A4)%0A%0A%20%20130%20%7C%20%20%20%20%20%20*%20%E7%A2%B0%E6%92%9E%E5%9B%9E%E8%B0%83%0A%20%20131%20%7C%20%20%20%20%20%20*%2F%0A%3E%20132%20%7C%20%20%20%20%20private%20onBeginContact(selfCollider%3A%20Collider2D%2C%20otherCollider%3A%20Collider2D%2C%20contact%3A%20IPhysics2DContact)%20%7B%0A%20%20%20%20%20%20%7C%20%20%20%20%20%5E%0A%20%20133%20%7C%20%20%20%20%20%20%20%20%20const%20otherNode%20%3D%20otherCollider.node%3B%0A%20%20134%20%7C%20%20%20%20%20%20%20%20%20%0A%20%20135%20%7C%20%20%20%20%20%20%20%20%20%2F%2F%20%E4%B8%8D%E4%B8%8E%E5%8F%91%E5%B0%84%E8%80%85%E7%A2%B0%E6%92%9E%60)%3B%0A%20%20%20%20%20%20%20%20"], "names": ["Error"], "mappings": ";;;;;;AACA;AACA,YAAM,IAAIA,KAAJ,CAAW;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BARM,CAAN", "sourcesContent": ["\n// This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts at runtime.\nthrow new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts: Unexpected reserved word 'private'. (132:4)\n\n  130 |      * 碰撞回调\n  131 |      */\n> 132 |     private onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {\n      |     ^\n  133 |         const otherNode = otherCollider.node;\n  134 |         \n  135 |         // 不与发射者碰撞`);\n        "]}