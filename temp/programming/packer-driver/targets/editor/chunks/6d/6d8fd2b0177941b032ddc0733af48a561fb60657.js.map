{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA8M,uCAA9M,EAA+Z,uCAA/Z,EAAynB,uCAAznB,EAA+0B,uCAA/0B,EAAoiC,uCAApiC,EAA0vC,uCAA1vC,EAAq6C,uCAAr6C,EAAqlD,uCAArlD,EAAywD,uCAAzwD,EAA07D,wCAA17D,EAAqmE,wCAArmE,EAAywE,wCAAzwE,EAAk3E,wCAAl3E,EAAu9E,wCAAv9E,EAA0jF,wCAA1jF,EAAoqF,wCAApqF,EAAmxF,wCAAnxF,EAAu3F,wCAAv3F,EAA+9F,wCAA/9F,EAAykG,wCAAzkG,EAAirG,wCAAjrG,EAAgyG,wCAAhyG,EAAy4G,wCAAz4G,EAA++G,wCAA/+G,EAAslH,wCAAtlH,EAA+rH,wCAA/rH,EAAyyH,wCAAzyH,EAAm5H,wCAAn5H,EAA+/H,wCAA//H,EAAymI,wCAAzmI,EAAktI,wCAAltI,EAAg0I,wCAAh0I,EAAq6I,wCAAr6I,EAA+gJ,wCAA/gJ,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/StartupController.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts\"), () => import(\"file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}