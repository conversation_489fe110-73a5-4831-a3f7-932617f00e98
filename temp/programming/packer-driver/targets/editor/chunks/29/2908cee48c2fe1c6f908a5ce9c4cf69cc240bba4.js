System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts at runtime.
      throw new Error(`Error: 在加载模块文件 /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts 时发生错误：Error: ENOENT: no such file or directory, open '/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts'`);
    }
  };
});
//# sourceMappingURL=2908cee48c2fe1c6f908a5ce9c4cf69cc240bba4.js.map