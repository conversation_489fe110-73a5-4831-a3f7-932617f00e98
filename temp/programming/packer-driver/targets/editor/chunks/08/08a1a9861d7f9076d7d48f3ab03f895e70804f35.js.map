{"version": 3, "sources": ["data:text/javascript,%0A%2F%2F%20This%20module%20is%20auto-generated%20to%20report%20error%20emitted%20when%20try%20to%20load%20module%20file%3A%2F%2F%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIPlayer.ts%20at%20runtime.%0Athrow%20new%20Error(%60SyntaxError%3A%20%2Ffile%3A%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIPlayer.ts%3A%20Unexpected%20token%2C%20expected%20%22%2C%22%20(329%3A37)%0A%0A%20%20327%20%7C%20%20%20%20%20%20%20%20%20const%20layer%20%3D%20other.node.layer%3B%0A%20%20328%20%7C%20%20%20%20%20%20%20%20%20%2F%2F%20console.log(%22layer%3A%22%2C%20Layers.nameToLayer(%22Block%22))%3B%0A%3E%20329%20%7C%20%20%20%20%20%20%20%20%20console.log(%22%E8%87%AA%E8%BA%AB%EF%BC%9A%22%20%2Cself.name%20%22%E7%A2%B0%E6%92%9E%E5%AF%B9%E8%B1%A1%3A%22%2C%20other.node.name)%3B%0A%20%20%20%20%20%20%7C%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%5E%0A%20%20330%20%7C%20%20%20%20%20%20%20%20%20%0A%20%20331%20%7C%20%20%20%20%20%20%20%20%20%2F%2F%20%E6%A3%80%E6%9F%A5%E5%B1%82%E7%BA%A7%E6%98%AF%E5%90%A6%E6%9C%89%E6%95%88%E5%86%8D%E5%B0%9D%E8%AF%95%E8%8E%B7%E5%8F%96%E5%90%8D%E7%A7%B0%0A%20%20332%20%7C%20%20%20%20%20%20%20%20%20%2F%2F%20let%20layerName%20%3D%20''%3B%60)%3B%0A%20%20%20%20%20%20%20%20"], "names": ["Error"], "mappings": ";;;;;;AACA;AACA,YAAM,IAAIA,KAAJ,CAAW;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCARM,CAAN", "sourcesContent": ["\n// This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts at runtime.\nthrow new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts: Unexpected token, expected \",\" (329:37)\n\n  327 |         const layer = other.node.layer;\n  328 |         // console.log(\"layer:\", Layers.nameToLayer(\"Block\"));\n> 329 |         console.log(\"自身：\" ,self.name \"碰撞对象:\", other.node.name);\n      |                                      ^\n  330 |         \n  331 |         // 检查层级是否有效再尝试获取名称\n  332 |         // let layerName = '';`);\n        "]}