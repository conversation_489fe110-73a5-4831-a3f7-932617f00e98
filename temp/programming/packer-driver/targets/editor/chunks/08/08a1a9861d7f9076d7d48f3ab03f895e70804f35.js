System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts at runtime.
      throw new Error(`SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts: Unexpected token, expected "," (329:37)

  327 |         const layer = other.node.layer;
  328 |         // console.log("layer:", Layers.nameToLayer("Block"));
> 329 |         console.log("自身：" ,self.name "碰撞对象:", other.node.name);
      |                                      ^
  330 |         
  331 |         // 检查层级是否有效再尝试获取名称
  332 |         // let layerName = '';`);
    }
  };
});
//# sourceMappingURL=08a1a9861d7f9076d7d48f3ab03f895e70804f35.js.map