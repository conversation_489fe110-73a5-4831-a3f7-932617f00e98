{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts"], "names": ["_decorator", "Component", "input", "Input", "KeyCode", "Vec2", "Vec3", "RigidBody2D", "ERigidBody2DType", "Contact2DType", "BoxCollider2D", "Sprite", "SpriteFrame", "tween", "Prefab", "AIPlayer", "GameManager", "SoundManager", "Bullet", "WeaponType", "ccclass", "property", "player", "type", "tooltip", "_rigidBody", "_direction", "_accel", "_angle", "_targetAngle", "_lastValidPosition", "_currentHealth", "_isDestroyed", "_originalSprite", "_paintTimer", "_vehicleId", "_canFire", "_fireTimer", "_currentAmmo", "_isReloading", "_reloadTimer", "onLoad", "maxHealth", "maxAmmo", "onEnable", "on", "EventType", "KEY_DOWN", "onKeyDown", "KEY_UP", "onKeyUp", "onDisable", "off", "onDestroy", "collider", "getComponent", "BEGIN_CONTACT", "onBeginContact", "start", "node", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "Dynamic", "allowSleep", "gravityScale", "linearDamping", "angularDamping", "fixedRotation", "worldPosition", "x", "y", "initAngle", "setRotationFromEuler", "sprite", "spriteFrame", "log", "event", "keyCode", "ARROW_UP", "ARROW_DOWN", "ARROW_LEFT", "instance", "playSoundEffect", "ARROW_RIGHT", "SPACE", "shoot", "update", "deltaTime", "currentVelocity", "linearVelocity", "currentSpeed", "length", "currentPos", "turnAmount", "turnSpeed", "angleDiff", "Math", "abs", "rad", "PI", "force", "cos", "acceleration", "sin", "applyForce", "forward", "dot", "brakeForce", "clone", "multiplyScalar", "brakeDeceleration", "reverseForce", "frictionForce", "friction", "maxSpeed", "normalizedVelocity", "normalize", "distanceToLastPos", "distance", "setWorldPosition", "z", "updatePaintSpray", "updateWeaponSystem", "init", "angle", "getRigidBody", "_selfCollider", "otherCollider", "_contact", "name", "otherNode", "aiPlayer", "mySpeed", "aiRigidBody", "aiSpeed", "damageFactor", "aiDamage", "round", "playerDamage", "takeDamage", "recoilForce", "bullet", "bulletDamage", "boundaryDamage", "damage", "max", "gameManager", "getInstance", "syncPlayerHealth", "destroyVehicle", "destroyedSprite", "disableInput", "startDestroyAnimation", "removeVehicleNode", "removeFromParent", "to", "scale", "call", "onDestroyAnimationComplete", "gameOver", "getCurrentHealth", "getMaxHealth", "isDestroyed", "restoreVehicle", "unschedule", "setScale", "paintPrefab", "paintSprayInterval", "sprayPaint", "warn", "getWorldPosition", "fireInterval", "fireRate", "updateAmmoReload", "ammoReloadTime", "startReload", "bulletPrefab", "weaponType", "NORMAL", "normalBulletPrefab", "DART", "dartPrefab", "ROCKET", "rocketPrefab", "direction", "vehicleWorldPos", "offsetDistance", "bulletStartPos", "fireBullet", "getCurrentAmmo", "getMaxAmmo", "isReloading", "getReloadProgress", "setAcceleration", "accel", "setDirection", "getAcceleration", "getDirection"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAsBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,a,OAAAA,a;AAAkCC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAErLC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,U,iBAAAA,U;;;;;;;;;OAJX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;wBAOjBsB,M,WADZF,OAAO,CAAC,QAAD,C,UAkBHC,QAAQ,CAACT,WAAD,C,UAORS,QAAQ,CAACP,MAAD,C,UAORO,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAET,MADA;AAENU,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAET,MADA;AAENU,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAET,MADA;AAENU,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNG,QAAAA,OAAO,EAAE;AADH,OAAD,C,UAKRH,QAAQ,CAAC;AACNE,QAAAA,IAAI;AAAA;AAAA,oCADE;AAENC,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAQRH,QAAQ,CAAC;AACNG,QAAAA,OAAO,EAAE;AADH,OAAD,C,WAKRH,QAAQ,CAAC;AACNG,QAAAA,OAAO,EAAE;AADH,OAAD,C,2BApEb,MACaF,MADb,SAC4BrB,SAD5B,CACsC;AAAA;AAAA;;AAAA;;AAEX;AAFW;;AAIP;AAJO;;AAMD;AANC;;AAQT;AARS;;AAUV;AAVU;;AAYX;AAZW;;AAeT;AAfS;;AAkBI;AAlBJ;;AAqBP;AAE3B;AAvBkC;;AAyBL;AAzBK;;AA4BA;AAElC;AA9BkC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAwExBwB,UAxEwB,GAwEE,IAxEF;AAAA,eAyE1BC,UAzE0B,GAyEL,CAzEK;AAyEF;AAzEE,eA0E1BC,MA1E0B,GA0ET,CA1ES;AA0EN;AA1EM,eA2E1BC,MA3E0B,GA2ET,CAAC,EA3EQ;AA2EJ;AA3EI,eA4E1BC,YA5E0B,GA4EH,CAAC,EA5EE;AA4EE;AA5EF,eA6E1BC,kBA7E0B,GA6EC,IAAIzB,IAAJ,EA7ED;AA6Ea;AAE/C;AA/EkC,eAgF1B0B,cAhF0B,GAgFD,GAhFC;AAgFI;AAhFJ,eAiF1BC,YAjF0B,GAiFF,KAjFE;AAiFK;AAjFL,eAkF1BC,eAlF0B,GAkFK,IAlFL;AAkFY;AAG9C;AArFkC,eAsF1BC,WAtF0B,GAsFJ,CAtFI;AAsFD;AAtFC,eAuF1BC,UAvF0B,GAuFL,QAvFK;AAuFK;AAEvC;AAzFkC,eA0F1BC,QA1F0B,GA0FN,IA1FM;AA0FA;AA1FA,eA2F1BC,UA3F0B,GA2FL,CA3FK;AA2FF;AA3FE,eA4F1BC,YA5F0B,GA4FH,EA5FG;AA4FC;AA5FD,eA6F1BC,YA7F0B,GA6FF,KA7FE;AA6FK;AA7FL,eA8F1BC,YA9F0B,GA8FH,CA9FG;AAAA;;AA8FA;AAElCC,QAAAA,MAAM,GAAG;AACL;AACA,eAAKhB,UAAL,GAAkB,IAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,CAAd;AACA,eAAKC,MAAL,GAAc,CAAC,EAAf;AACA,eAAKC,YAAL,GAAoB,CAAC,EAArB;AACA,eAAKC,kBAAL,GAA0B,IAAIzB,IAAJ,EAA1B,CAPK,CASL;;AACA,eAAK0B,cAAL,GAAsB,KAAKW,SAA3B;AACA,eAAKV,YAAL,GAAoB,KAApB,CAXK,CAaL;;AACA,eAAKE,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,QAAlB,CAfK,CAiBL;;AACA,eAAKC,QAAL,GAAgB,IAAhB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,YAAL,GAAoB,KAAKK,OAAzB;AACA,eAAKJ,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACH;;AAEDI,QAAAA,QAAQ,GAAG;AACP1C,UAAAA,KAAK,CAAC2C,EAAN,CAAS1C,KAAK,CAAC2C,SAAN,CAAgBC,QAAzB,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACA9C,UAAAA,KAAK,CAAC2C,EAAN,CAAS1C,KAAK,CAAC2C,SAAN,CAAgBG,MAAzB,EAAiC,KAAKC,OAAtC,EAA+C,IAA/C;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACRjD,UAAAA,KAAK,CAACkD,GAAN,CAAUjD,KAAK,CAAC2C,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACA9C,UAAAA,KAAK,CAACkD,GAAN,CAAUjD,KAAK,CAAC2C,SAAN,CAAgBG,MAA1B,EAAkC,KAAKC,OAAvC,EAAgD,IAAhD;AACH;;AAEDG,QAAAA,SAAS,GAAG;AAER;AACAnD,UAAAA,KAAK,CAACkD,GAAN,CAAUjD,KAAK,CAAC2C,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACA9C,UAAAA,KAAK,CAACkD,GAAN,CAAUjD,KAAK,CAAC2C,SAAN,CAAgBG,MAA1B,EAAkC,KAAKC,OAAvC,EAAgD,IAAhD,EAJQ,CAMR;;AACA,eAAKzB,UAAL,GAAkB,IAAlB,CAPQ,CAQR;;AACA,gBAAM6B,QAAQ,GAAG,KAAKC,YAAL,CAAkB7C,aAAlB,CAAjB;;AACA,cAAI4C,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACF,GAAT,CAAa3C,aAAa,CAAC+C,aAA3B,EAA0C,KAAKC,cAA/C,EAA+D,IAA/D;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKjC,UAAL,GAAkB,KAAK8B,YAAL,CAAkBhD,WAAlB,CAAlB;;AACA,cAAI,CAAC,KAAKkB,UAAN,IAAoB,CAAC,KAAKkC,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUC,OAAjD,EAA0D;AACtDC,YAAAA,OAAO,CAACC,KAAR,CAAc,sDAAd;AACA;AACH,WANG,CAQJ;;;AACA,eAAKrC,UAAL,CAAgBF,IAAhB,GAAuBf,gBAAgB,CAACuD,OAAxC;AACA,eAAKtC,UAAL,CAAgBuC,UAAhB,GAA6B,KAA7B,CAVI,CAUgC;;AACpC,eAAKvC,UAAL,CAAgBwC,YAAhB,GAA+B,CAA/B,CAXI,CAW8B;;AAClC,eAAKxC,UAAL,CAAgByC,aAAhB,GAAgC,GAAhC,CAZI,CAYiC;;AACrC,eAAKzC,UAAL,CAAgB0C,cAAhB,GAAiC,GAAjC,CAbI,CAakC;;AACtC,eAAK1C,UAAL,CAAgB2C,aAAhB,GAAgC,IAAhC,CAdI,CAckC;AAEtC;;AACA,eAAKtC,kBAAL,GAA0B,IAAIzB,IAAJ,CAAS,KAAKsD,IAAL,CAAUU,aAAV,CAAwBC,CAAjC,EAAoC,KAAKX,IAAL,CAAUU,aAAV,CAAwBE,CAA5D,CAA1B,CAjBI,CAmBJ;;AACA,eAAK3C,MAAL,GAAc,KAAK4C,SAAnB;AACA,eAAK3C,YAAL,GAAoB,KAAK2C,SAAzB;AACA,eAAKb,IAAL,CAAUc,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKD,SAA1C,EAtBI,CAwBJ;;AACA,gBAAME,MAAM,GAAG,KAAKnB,YAAL,CAAkB5C,MAAlB,CAAf;;AACA,cAAI+D,MAAM,IAAIA,MAAM,CAACC,WAArB,EAAkC;AAC9B,iBAAK1C,eAAL,GAAuByC,MAAM,CAACC,WAA9B;AACH,WA5BG,CA8BJ;;;AACA,gBAAMrB,QAAQ,GAAG,KAAKC,YAAL,CAAkB7C,aAAlB,CAAjB;;AACA,cAAI4C,QAAJ,EAAc;AACVO,YAAAA,OAAO,CAACe,GAAR,CAAY,8CAAZ;AACAtB,YAAAA,QAAQ,CAACT,EAAT,CAAYpC,aAAa,CAAC+C,aAA1B,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACH,WAHD,MAGO;AACHI,YAAAA,OAAO,CAACC,KAAR,CAAc,mCAAd;AACH;AACJ;;AAEDd,QAAAA,SAAS,CAAC6B,KAAD,EAAuB;AAC5B,kBAAQA,KAAK,CAACC,OAAd;AACI,iBAAK1E,OAAO,CAAC2E,QAAb;AACI,mBAAKpD,MAAL,GAAc,CAAd;AACA;;AACJ,iBAAKvB,OAAO,CAAC4E,UAAb;AACI,mBAAKrD,MAAL,GAAc,CAAC,CAAf;AACA;;AACJ,iBAAKvB,OAAO,CAAC6E,UAAb;AACI;AAAA;AAAA,gDAAaC,QAAb,CAAsBC,eAAtB,CAAsC,UAAtC;AACA,mBAAKzD,UAAL,GAAkB,CAAC,CAAnB;AACA;;AACJ,iBAAKtB,OAAO,CAACgF,WAAb;AACI;AAAA;AAAA,gDAAaF,QAAb,CAAsBC,eAAtB,CAAsC,UAAtC;AACA,mBAAKzD,UAAL,GAAkB,CAAlB;AACA;;AACJ,iBAAKtB,OAAO,CAACiF,KAAb;AACI,mBAAKC,KAAL;AACA;AAjBR;AAmBH;;AAEDpC,QAAAA,OAAO,CAAC2B,KAAD,EAAuB;AAC1B,kBAAQA,KAAK,CAACC,OAAd;AACI,iBAAK1E,OAAO,CAAC2E,QAAb;AACI,kBAAI,KAAKpD,MAAL,KAAgB,CAApB,EAAuB,KAAKA,MAAL,GAAc,CAAd;AACvB;;AACJ,iBAAKvB,OAAO,CAAC4E,UAAb;AACI,kBAAI,KAAKrD,MAAL,KAAgB,CAAC,CAArB,EAAwB,KAAKA,MAAL,GAAc,CAAd;AACxB;;AACJ,iBAAKvB,OAAO,CAAC6E,UAAb;AACI,kBAAI,KAAKvD,UAAL,KAAoB,CAAC,CAAzB,EAA4B,KAAKA,UAAL,GAAkB,CAAlB;AAC5B;;AACJ,iBAAKtB,OAAO,CAACgF,WAAb;AACI,kBAAI,KAAK1D,UAAL,KAAoB,CAAxB,EAA2B,KAAKA,UAAL,GAAkB,CAAlB;AAC3B;AAZR;AAcH;;AAED6D,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC,KAAK/D,UAAN,IAAoB,CAAC,KAAKkC,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUC,OAAjD,EAA0D,OADpC,CAGtB;;AACA,cAAI,KAAK5B,YAAT,EAAuB;AACnB;AACA;AACH,WAPqB,CAStB;;;AACA,gBAAMyD,eAAe,GAAG,KAAKhE,UAAL,CAAgBiE,cAAxC;AACA,gBAAMC,YAAY,GAAGF,eAAe,CAACG,MAAhB,EAArB;AACA,gBAAMC,UAAU,GAAG,IAAIxF,IAAJ,CAAS,KAAKsD,IAAL,CAAUU,aAAV,CAAwBC,CAAjC,EAAoC,KAAKX,IAAL,CAAUU,aAAV,CAAwBE,CAA5D,CAAnB,CAZsB,CActB;;AACA,cAAI,KAAK7C,UAAL,KAAoB,CAAxB,EAA2B;AACvB,kBAAMoE,UAAU,GAAG,KAAKC,SAAL,GAAiBP,SAAjB,GAA6B,KAAK9D,UAArD;AACA,iBAAKG,YAAL,IAAqBiE,UAArB;AACH,WAlBqB,CAoBtB;;;AACA,gBAAME,SAAS,GAAG,KAAKnE,YAAL,GAAoB,KAAKD,MAA3C;;AACA,cAAIqE,IAAI,CAACC,GAAL,CAASF,SAAT,IAAsB,GAA1B,EAA+B;AAC3B,iBAAKpE,MAAL,IAAeoE,SAAS,GAAG,GAA3B,CAD2B,CACK;AACnC,WAFD,MAEO;AACH,iBAAKpE,MAAL,GAAc,KAAKC,YAAnB;AACH,WA1BqB,CA4BtB;;;AACA,eAAK8B,IAAL,CAAUc,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAK7C,MAA1C,EA7BsB,CA+BtB;;AACA,cAAI,KAAKD,MAAL,KAAgB,CAApB,EAAuB;AACnB;AACA,kBAAMwE,GAAG,GAAG,CAAC,KAAKvE,MAAL,GAAc,EAAf,IAAqBqE,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,kBAAMC,KAAK,GAAG,IAAIhG,IAAJ,CACV4F,IAAI,CAACK,GAAL,CAASH,GAAT,IAAgB,KAAKI,YADX,EAEVN,IAAI,CAACO,GAAL,CAASL,GAAT,IAAgB,KAAKI,YAFX,CAAd;;AAIA,iBAAK9E,UAAL,CAAgBgF,UAAhB,CAA2BJ,KAA3B,EAAkCR,UAAlC,EAA8C,IAA9C;AACH,WARD,CASA;AATA,eAUK,IAAI,KAAKlE,MAAL,KAAgB,CAAC,CAArB,EAAwB;AACzB;AACA,kBAAMwE,GAAG,GAAG,CAAC,KAAKvE,MAAL,GAAc,EAAf,IAAqBqE,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,kBAAMM,OAAO,GAAG,IAAIrG,IAAJ,CAAS4F,IAAI,CAACK,GAAL,CAASH,GAAT,CAAT,EAAwBF,IAAI,CAACO,GAAL,CAASL,GAAT,CAAxB,CAAhB;AACA,kBAAMQ,GAAG,GAAGlB,eAAe,CAACkB,GAAhB,CAAoBD,OAApB,CAAZ;;AAEA,gBAAIC,GAAG,GAAG,CAAV,EAAa;AACT;AACA,oBAAMC,UAAU,GAAGF,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKC,iBAArC,CAAnB;;AACA,mBAAKtF,UAAL,CAAgBgF,UAAhB,CAA2BG,UAA3B,EAAuCf,UAAvC,EAAmD,IAAnD;AACH,aAJD,MAIO;AACH;AACA,oBAAMmB,YAAY,GAAGN,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKP,YAAN,GAAqB,GAApD,CAArB;;AACA,mBAAK9E,UAAL,CAAgBgF,UAAhB,CAA2BO,YAA3B,EAAyCnB,UAAzC,EAAqD,IAArD;AACH;AACJ,WAfI,CAgBL;AAhBK,eAiBA;AACD;AACA,gBAAIF,YAAY,GAAG,CAAnB,EAAsB;AAClB,oBAAMsB,aAAa,GAAGxB,eAAe,CAACoB,KAAhB,GAAwBC,cAAxB,CAAuC,CAAC,KAAKI,QAAN,GAAiB,CAAxD,CAAtB,CADkB,CACgE;;AAClF,mBAAKzF,UAAL,CAAgBgF,UAAhB,CAA2BQ,aAA3B,EAA0CpB,UAA1C,EAAsD,IAAtD;AACH;AACJ,WAjEqB,CAmEtB;;;AACA,cAAIF,YAAY,GAAG,KAAKwB,QAAxB,EAAkC;AAC9B,kBAAMC,kBAAkB,GAAG3B,eAAe,CAACoB,KAAhB,GAAwBQ,SAAxB,EAA3B;AACA,iBAAK5F,UAAL,CAAgBiE,cAAhB,GAAiC0B,kBAAkB,CAACN,cAAnB,CAAkC,KAAKK,QAAvC,CAAjC;AACH,WAvEqB,CAyEtB;;;AACA,cAAIxB,YAAY,GAAG,GAAnB,EAAwB;AACpB;AACA,kBAAM2B,iBAAiB,GAAGjH,IAAI,CAACkH,QAAL,CAAc1B,UAAd,EAA0B,KAAK/D,kBAA/B,CAA1B;;AACA,gBAAIwF,iBAAiB,GAAG,EAAxB,EAA4B;AAAE;AAC1B,mBAAK3D,IAAL,CAAU6D,gBAAV,CAA2B,KAAK1F,kBAAL,CAAwBwC,CAAnD,EAAsD,KAAKxC,kBAAL,CAAwByC,CAA9E,EAAiF,KAAKZ,IAAL,CAAUU,aAAV,CAAwBoD,CAAzG;AACA,mBAAKhG,UAAL,CAAgBiE,cAAhB,GAAiC,IAAIrF,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAjC;AACH;AACJ,WAPD,MAOO;AACH;AACA,iBAAKyB,kBAAL,GAA0B+D,UAAU,CAACgB,KAAX,EAA1B;AACH,WApFqB,CAsFtB;;;AACA,cAAIZ,IAAI,CAACC,GAAL,CAAS,KAAKtE,MAAd,IAAwB,GAA5B,EAAiC;AAC7B,iBAAKA,MAAL,GAAc,KAAKA,MAAL,GAAc,GAA5B;AACA,iBAAKC,YAAL,GAAoB,KAAKA,YAAL,GAAoB,GAAxC;AACH,WA1FqB,CA4FtB;;;AACA,eAAK6F,gBAAL,CAAsBlC,SAAtB,EA7FsB,CA+FtB;;AACA,eAAKmC,kBAAL,CAAwBnC,SAAxB;AACH;;AAEMoC,QAAAA,IAAI,CAACC,KAAD,EAAgB;AACvB,eAAKrD,SAAL,GAAiBqD,KAAjB;AACA,eAAKjG,MAAL,GAAciG,KAAd;AACA,eAAKhG,YAAL,GAAoBgG,KAApB;AACA,eAAKlE,IAAL,CAAUc,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCoD,KAArC;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,YAAY,GAAgB;AAC/B,iBAAO,KAAKrG,UAAZ;AACH;AAED;AACJ;AACA;;;AACIgC,QAAAA,cAAc,CAACsE,aAAD,EAA+BC,aAA/B,EAA6DC,QAA7D,EAAiG;AAC3GpE,UAAAA,OAAO,CAACe,GAAR,CAAY,gBAAZ,EAA8BoD,aAAa,CAACrE,IAAd,CAAmBuE,IAAjD,EAD2G,CAE3G;;AACA,gBAAMC,SAAS,GAAGH,aAAa,CAACrE,IAAhC;AACA,gBAAMyE,QAAQ,GAAGD,SAAS,CAAC5E,YAAV;AAAA;AAAA,mCAAjB;;AACA,cAAI6E,QAAJ,EAAc;AACV;AAAA;AAAA,8CAAalD,QAAb,CAAsBC,eAAtB,CAAsC,cAAtC;AACAtB,YAAAA,OAAO,CAACe,GAAR,CAAY,YAAZ,EAA0BuD,SAAS,CAACD,IAApC,EAFU,CAGV;;AACA,kBAAMG,OAAO,GAAG,KAAK5G,UAAL,CAAgBiE,cAAhB,CAA+BE,MAA/B,EAAhB;;AACA,kBAAM0C,WAAW,GAAGF,QAAQ,CAACzE,IAAT,CAAcJ,YAAd,CAA2BhD,WAA3B,CAApB;AACA,kBAAMgI,OAAO,GAAGD,WAAW,GAAGA,WAAW,CAAC5C,cAAZ,CAA2BE,MAA3B,EAAH,GAAyC,CAApE,CANU,CAOV;;AACA,kBAAM4C,YAAY,GAAG,GAArB,CARU,CAQgB;;AAC1B,kBAAMC,QAAQ,GAAGxC,IAAI,CAACyC,KAAL,CAAWL,OAAO,GAAGG,YAArB,CAAjB;AACA,kBAAMG,YAAY,GAAG1C,IAAI,CAACyC,KAAL,CAAWH,OAAO,GAAGC,YAArB,CAArB,CAVU,CAWV;;AACAJ,YAAAA,QAAQ,CAACQ,UAAT,CAAoBH,QAApB,EAZU,CAcV;;AACA,kBAAMI,WAAW,GAAG,IAAIxI,IAAJ,CAAS,KAAKoB,UAAL,CAAgBiE,cAAhB,CAA+BpB,CAAxC,EAA2C,KAAK7C,UAAL,CAAgBiE,cAAhB,CAA+BnB,CAA1E,CAApB;AACAsE,YAAAA,WAAW,CAACxB,SAAZ,GAhBU,CAgBe;;AACzBwB,YAAAA,WAAW,CAAC/B,cAAZ,CAA2B,CAACuB,OAAD,GAAW,IAAtC,EAjBU,CAiBmC;;AAC7C,iBAAK5G,UAAL,CAAgBiE,cAAhB,GAAiCmD,WAAjC;AAEA,iBAAKD,UAAL,CAAgBD,YAAhB;AACH,WArBD,CAsBA;AAtBA,eAuBK;AACD,kBAAMG,MAAM,GAAGX,SAAS,CAAC5E,YAAV;AAAA;AAAA,iCAAf;;AACA,gBAAIuF,MAAJ,EAAY;AACR;AACA,kBAAIA,MAAM,CAAC,YAAD,CAAN,KAAyB,QAA7B,EAAuC;AACnC;AACAjF,gBAAAA,OAAO,CAACe,GAAR,CAAY,yBAAZ;AACA;AACH,eAJD,MAIO;AACH;AAAA;AAAA,kDAAaM,QAAb,CAAsBC,eAAtB,CAAsC,cAAtC,EADG,CAEH;;AACA,sBAAM4D,YAAY,GAAGD,MAAM,CAAC,QAAD,CAAN,IAAoB,CAAzC,CAHG,CAIH;AAEA;AACA;;AACA,oBAAIA,MAAM,CAAC,YAAD,CAAN,KAAyB,CAA7B,EAAgC;AAAE;AAC9B,wBAAMD,WAAW,GAAG,IAAIxI,IAAJ,CAAS,KAAKoB,UAAL,CAAgBiE,cAAhB,CAA+BpB,CAAxC,EAA2C,KAAK7C,UAAL,CAAgBiE,cAAhB,CAA+BnB,CAA1E,CAApB;AACAsE,kBAAAA,WAAW,CAACxB,SAAZ;AACAwB,kBAAAA,WAAW,CAAC/B,cAAZ,CAA2B,CAACiC,YAAD,GAAgB,GAA3C,EAH4B,CAGqB;;AACjD,uBAAKtH,UAAL,CAAgBiE,cAAhB,GAAiCmD,WAAjC;AACH;;AAEDhF,gBAAAA,OAAO,CAACe,GAAR,CAAY,eAAZ,EAA6BmE,YAA7B;AACH;AACJ,aAvBD,CAwBA;AAxBA,iBAyBK;AACD;AAAA;AAAA,gDAAa7D,QAAb,CAAsBC,eAAtB,CAAsC,cAAtC;;AACA,oBAAMkD,OAAO,GAAG,KAAK5G,UAAL,CAAgBiE,cAAhB,CAA+BE,MAA/B,EAAhB;;AACA,oBAAM4C,YAAY,GAAG,GAArB,CAHC,CAGyB;;AAC1B,oBAAMQ,cAAc,GAAG/C,IAAI,CAACyC,KAAL,CAAWL,OAAO,GAAGG,YAArB,CAAvB,CAJC,CAMD;;AACA,oBAAMK,WAAW,GAAG,IAAIxI,IAAJ,CAAS,KAAKoB,UAAL,CAAgBiE,cAAhB,CAA+BpB,CAAxC,EAA2C,KAAK7C,UAAL,CAAgBiE,cAAhB,CAA+BnB,CAA1E,CAApB;AACAsE,cAAAA,WAAW,CAACxB,SAAZ,GARC,CAQwB;;AACzBwB,cAAAA,WAAW,CAAC/B,cAAZ,CAA2B,CAACuB,OAAD,GAAW,IAAtC,EATC,CAS4C;;AAC7C,mBAAK5G,UAAL,CAAgBiE,cAAhB,GAAiCmD,WAAjC;AAEA,mBAAKD,UAAL,CAAgBI,cAAhB;AACH;AACJ;AACJ,SA3ZiC,CA6ZlC;;AAEA;AACJ;AACA;;;AACWJ,QAAAA,UAAU,CAACK,MAAD,EAAiB;AAC9B,cAAI,KAAKjH,YAAT,EAAuB;AAEvB,eAAKD,cAAL,GAAsBkE,IAAI,CAACiD,GAAL,CAAS,CAAT,EAAY,KAAKnH,cAAL,GAAsBkH,MAAlC,CAAtB;AACApF,UAAAA,OAAO,CAACe,GAAR,CAAa,WAAUqE,MAAO,YAAW,KAAKlH,cAAe,EAA7D,EAJ8B,CAM9B;;AACA,gBAAMoH,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACE,gBAAZ;AACH,WAV6B,CAY9B;;;AACA,cAAI,KAAKtH,cAAL,IAAuB,CAA3B,EAA8B;AAC1B,iBAAKuH,cAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYA,QAAAA,cAAc,GAAG;AACrB,cAAI,KAAKtH,YAAT,EAAuB;AACvB;AAAA;AAAA,4CAAakD,QAAb,CAAsBC,eAAtB,CAAsC,gBAAtC;AACA,eAAKnD,YAAL,GAAoB,IAApB;AACA6B,UAAAA,OAAO,CAACe,GAAR,CAAY,UAAZ,EAJqB,CAMrB;;AACA,cAAI,KAAK2E,eAAT,EAA0B;AACtB,kBAAM7E,MAAM,GAAG,KAAKnB,YAAL,CAAkB5C,MAAlB,CAAf;;AACA,gBAAI+D,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,WAAP,GAAqB,KAAK4E,eAA1B;AACH;AACJ,WAZoB,CAcrB;;;AACA,eAAKC,YAAL,GAfqB,CAiBrB;;AACA,eAAKC,qBAAL,GAlBqB,CAoBrB;AACA;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,YAAY,GAAG;AACnBtJ,UAAAA,KAAK,CAACkD,GAAN,CAAUjD,KAAK,CAAC2C,SAAN,CAAgBC,QAA1B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACA9C,UAAAA,KAAK,CAACkD,GAAN,CAAUjD,KAAK,CAAC2C,SAAN,CAAgBG,MAA1B,EAAkC,KAAKC,OAAvC,EAAgD,IAAhD,EAFmB,CAInB;;AACA,eAAKxB,UAAL,GAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,CAAd;AACH;AAID;AACJ;AACA;;;AACY+H,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK/F,IAAL,IAAa,KAAKA,IAAL,CAAUC,OAA3B,EAAoC;AAChCC,YAAAA,OAAO,CAACe,GAAR,CAAY,UAAZ;AACA,iBAAKjB,IAAL,CAAUgG,gBAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACYF,QAAAA,qBAAqB,GAAG;AAC5B;AACA,cAAI,KAAK9F,IAAT,EAAe;AACX;AACA9C,YAAAA,KAAK,CAAC,KAAK8C,IAAN,CAAL,CACKiG,EADL,CACQ,GADR,EACa;AACLC,cAAAA,KAAK,EAAE,IAAIvJ,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CADF;AACsB;AAC3BuH,cAAAA,KAAK,EAAE,KAAKlE,IAAL,CAAUkE,KAAV,GAAkB,GAFpB,CAEwB;;AAFxB,aADb,EAKKiC,IALL,CAKU,MAAM;AACR;AACA,mBAAKC,0BAAL;AACH,aARL,EASKrG,KATL;AAUH;AACJ;AAED;AACJ;AACA;;;AACYqG,QAAAA,0BAA0B,GAAG;AACjClG,UAAAA,OAAO,CAACe,GAAR,CAAY,mBAAZ;AACA,gBAAMuE,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACa,QAAZ,CAAqB,KAArB,EADa,CACgB;AAChC;AACJ,SApgBiC,CAwgBlC;;AAEA;AACJ;AACA;;;AACWC,QAAAA,gBAAgB,GAAW;AAC9B,iBAAO,KAAKlI,cAAZ;AACH;AAED;AACJ;AACA;;;AACWmI,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAKxH,SAAZ;AACH;AAED;AACJ;AACA;;;AACWyH,QAAAA,WAAW,GAAY;AAC1B,iBAAO,KAAKnI,YAAZ;AACH;AAED;AACJ;AACA;;;AACWoI,QAAAA,cAAc,GAAG;AACpB;AACA,eAAKC,UAAL,CAAgB,KAAKX,iBAArB;AAEA,eAAK1H,YAAL,GAAoB,KAApB;AACA,eAAKD,cAAL,GAAsB,KAAKW,SAA3B,CALoB,CAOpB;;AACA,cAAI,KAAKT,eAAT,EAA0B;AACtB,kBAAMyC,MAAM,GAAG,KAAKnB,YAAL,CAAkB5C,MAAlB,CAAf;;AACA,gBAAI+D,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,WAAP,GAAqB,KAAK1C,eAA1B;AACH;AACJ,WAbmB,CAepB;;;AACA,eAAKW,QAAL,GAhBoB,CAkBpB;;AACA,cAAI,KAAKe,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAU2G,QAAV,CAAmB,CAAnB,EAAsB,CAAtB;AACA,iBAAK3G,IAAL,CAAUkE,KAAV,GAAkB,KAAKrD,SAAvB;AACH,WAtBmB,CAwBpB;;;AACA,cAAI,KAAK/C,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBiE,cAAhB,GAAiC,IAAIrF,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAjC;AACH;;AAEDwD,UAAAA,OAAO,CAACe,GAAR,CAAY,SAAZ;AACH,SAhkBiC,CAkkBlC;;AAEA;AACJ;AACA;AACA;;;AACY8C,QAAAA,gBAAgB,CAAClC,SAAD,EAA0B;AAC9C,cAAI,KAAKxD,YAAL,IAAqB,CAAC,KAAKuI,WAA/B,EAA4C,OADE,CAG9C;;AACA,eAAKrI,WAAL,IAAoBsD,SAApB,CAJ8C,CAM9C;;AACA,cAAI,KAAKtD,WAAL,IAAoB,KAAKsI,kBAA7B,EAAiD;AAC7C,iBAAKC,UAAL;AACA,iBAAKvI,WAAL,GAAmB,CAAnB,CAF6C,CAEvB;AACzB;AACJ;AAED;AACJ;AACA;;;AACYuI,QAAAA,UAAU,GAAS;AACvB,gBAAMtB,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAI,CAACD,WAAL,EAAkB;AACdtF,YAAAA,OAAO,CAAC6G,IAAR,CAAa,uBAAb;AACA;AACH,WALsB,CAOvB;;;AACA,gBAAMrG,aAAa,GAAG,KAAKV,IAAL,CAAUgH,gBAAV,EAAtB,CARuB,CAUvB;;AACAxB,UAAAA,WAAW,CAACsB,UAAZ,CAAuB,KAAKF,WAA5B,EAAyClG,aAAzC,EAAwD,KAAKlC,UAA7D;AACH,SApmBiC,CAsmBlC;;AAEA;AACJ;AACA;AACA;;;AACYwF,QAAAA,kBAAkB,CAACnC,SAAD,EAA0B;AAChD,cAAI,KAAKxD,YAAT,EAAuB,OADyB,CAGhD;;AACA,eAAKK,UAAL,IAAmBmD,SAAnB,CAJgD,CAMhD;;AACA,gBAAMoF,YAAY,GAAG,IAAI,KAAKC,QAA9B;;AACA,cAAI,KAAKxI,UAAL,IAAmBuI,YAAvB,EAAqC;AACjC,iBAAKxI,QAAL,GAAgB,IAAhB;AACH,WAV+C,CAYhD;;;AACA,eAAK0I,gBAAL,CAAsBtF,SAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACYsF,QAAAA,gBAAgB,CAACtF,SAAD,EAA0B;AAC9C,cAAI,CAAC,KAAKjD,YAAV,EAAwB;AAExB,eAAKC,YAAL,IAAqBgD,SAArB;;AACA,cAAI,KAAKhD,YAAL,IAAqB,KAAKuI,cAA9B,EAA8C;AAC1C;AACA,iBAAKzI,YAAL,GAAoB,KAAKK,OAAzB;AACA,iBAAKJ,YAAL,GAAoB,KAApB;AACA,iBAAKC,YAAL,GAAoB,CAApB;AACAqB,YAAAA,OAAO,CAACe,GAAR,CAAY,SAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACWU,QAAAA,KAAK,GAAS;AACjB,cAAI,CAAC,KAAKlD,QAAN,IAAkB,KAAKJ,YAA3B,EAAyC,OADxB,CAGjB;;AACA,cAAI,KAAKM,YAAL,IAAqB,CAAzB,EAA4B;AACxB,gBAAI,CAAC,KAAKC,YAAV,EAAwB;AACpB,mBAAKyI,WAAL;AACH;;AACD;AACH,WATgB,CAWjB;;;AACA,eAAK5I,QAAL,GAAgB,KAAhB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,YAAL,GAdiB,CAgBjB;;AACA,cAAI2I,YAA2B,GAAG,IAAlC;;AACA,kBAAQ,KAAKC,UAAb;AACI,iBAAK;AAAA;AAAA,0CAAWC,MAAhB;AACItH,cAAAA,OAAO,CAACe,GAAR,CAAY,QAAZ;AACAqG,cAAAA,YAAY,GAAG,KAAKG,kBAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,IAAhB;AACIJ,cAAAA,YAAY,GAAG,KAAKK,UAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,MAAhB;AACIN,cAAAA,YAAY,GAAG,KAAKO,YAApB;AACA;AAVR,WAlBiB,CA+BjB;;;AACA,cAAI,CAACP,YAAL,EAAmB;AACfpH,YAAAA,OAAO,CAAC6G,IAAR,CAAa,UAAb,EADe,CAEf;;AACA,iBAAKtI,QAAL,GAAgB,IAAhB;AACA;AACH,WArCgB,CAuCjB;;;AACA,gBAAM+D,GAAG,GAAG,CAAC,KAAKvE,MAAL,GAAc,EAAf,IAAqBqE,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,gBAAMqF,SAAS,GAAG,IAAIpL,IAAJ,CAAS4F,IAAI,CAACK,GAAL,CAASH,GAAT,CAAT,EAAwBF,IAAI,CAACO,GAAL,CAASL,GAAT,CAAxB,CAAlB,CAzCiB,CA2CjB;;AACA,gBAAMuF,eAAe,GAAG,KAAK/H,IAAL,CAAUU,aAAlC;AACA,gBAAMsH,cAAc,GAAG,EAAvB,CA7CiB,CA6CU;;AAC3B,gBAAMC,cAAc,GAAG,IAAItL,IAAJ,CACnBoL,eAAe,CAACpH,CAAhB,GAAoBmH,SAAS,CAACnH,CAAV,GAAcqH,cADf,EAEnBD,eAAe,CAACnH,CAAhB,GAAoBkH,SAAS,CAAClH,CAAV,GAAcoH,cAFf,EAGnBD,eAAe,CAACjE,CAHG,CAAvB,CA9CiB,CAoDjB;;AACA,gBAAM0B,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAAC0C,UAAZ,CAAuBZ,YAAvB,EAAqCW,cAArC,EAAqDH,SAArD,EAAgE,KAAKtJ,UAArE,EAAiF,KAAK+I,UAAtF;AACH,WAxDgB,CA0DjB;;;AACA;AAAA;AAAA,4CAAahG,QAAb,CAAsBC,eAAtB,CAAsC,YAAtC,EA3DiB,CA6DjB;;AACA,cAAI,KAAK7C,YAAL,IAAqB,CAArB,IAA0B,CAAC,KAAKC,YAApC,EAAkD;AAC9C,iBAAKyI,WAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYA,QAAAA,WAAW,GAAS;AACxB,eAAKzI,YAAL,GAAoB,IAApB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACAqB,UAAAA,OAAO,CAACe,GAAR,CAAY,WAAZ;AACH;AAED;AACJ;AACA;;;AACWkH,QAAAA,cAAc,GAAW;AAC5B,iBAAO,KAAKxJ,YAAZ;AACH;AAED;AACJ;AACA;;;AACWyJ,QAAAA,UAAU,GAAW;AACxB,iBAAO,KAAKpJ,OAAZ;AACH;AAED;AACJ;AACA;;;AACWqJ,QAAAA,WAAW,GAAY;AAC1B,iBAAO,KAAKzJ,YAAZ;AACH;AAED;AACJ;AACA;;;AACW0J,QAAAA,iBAAiB,GAAW;AAC/B,cAAI,CAAC,KAAK1J,YAAV,EAAwB,OAAO,CAAP;AACxB,iBAAO,KAAKC,YAAL,GAAoB,KAAKuI,cAAhC;AACH,SAvvBiC,CAyvBlC;;AAEA;AACJ;AACA;AACA;;;AACWmB,QAAAA,eAAe,CAACC,KAAD,EAAsB;AACxCtI,UAAAA,OAAO,CAACe,GAAR,CAAa,kBAAiBuH,KAAM,EAApC;AACA,eAAKxK,MAAL,GAAcwK,KAAd;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,YAAY,CAACX,SAAD,EAA0B;AACzC5H,UAAAA,OAAO,CAACe,GAAR,CAAa,iBAAgB6G,SAAU,EAAvC,EADyC,CAEzC;;AACA,cAAIA,SAAS,KAAK,CAAd,IAAmB,KAAK/J,UAAL,KAAoB,CAA3C,EAA8C;AAC1C;AAAA;AAAA,8CAAawD,QAAb,CAAsBC,eAAtB,CAAsC,UAAtC;AACH;;AACD,eAAKzD,UAAL,GAAkB+J,SAAlB;AACH;AAED;AACJ;AACA;;;AACWY,QAAAA,eAAe,GAAW;AAC7B,iBAAO,KAAK1K,MAAZ;AACH;AAED;AACJ;AACA;;;AACW2K,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAK5K,UAAZ;AACH;;AA7xBiC,O,2EACjCL,Q;;;;;iBACkB,E;;uFAClBA,Q;;;;;iBACsB,E;;4FACtBA,Q;;;;;iBAC2B,G;;oFAC3BA,Q;;;;;iBACmB,G;;mFACnBA,Q;;;;;iBACkB,G;;oFAClBA,Q;;;;;iBACmB,C;;oFAEnBA,Q;;;;;iBACmB,G;;;;;;;iBAGW,I;;sFAE9BA,Q;;;;;iBACqB,G;;;;;;;iBAIA,I;;8FAErBA,Q;;;;;iBAC4B,G;;;;;;;iBAOA,I;;;;;;;iBAMR,I;;;;;;;iBAME,I;;;;;;;iBAKJ,G;;;;;;;iBAMM;AAAA;AAAA,wCAAW8J,M;;;;;;;iBAOlB,E;;;;;;;iBAKO,I", "sourcesContent": ["import { _decorator, Component, input, Input, EventKeyboard, KeyCode, Vec2, Vec3, RigidBody2D, ERigidBody2DType, Contact2DType, IPhysics2DContact, BoxCollider2D, Sprite, SpriteFrame, tween, Prefab } from 'cc';\nconst { ccclass, property } = _decorator;\nimport { AIPlayer } from './AIPlayer';\nimport { GameManager } from './GameManager';\nimport { SoundManager } from './SoundManager';\nimport { Bullet, WeaponType } from './Bullet';\n\n@ccclass('player')\nexport class player extends Component {\n    @property\n    maxSpeed: number = 50; // 最大速度（像素/秒）\n    @property\n    acceleration: number = 50; // 加速度（像素/秒²）\n    @property\n    brakeDeceleration: number = 200; // 刹车减速度\n    @property\n    turnSpeed: number = 200; // 转向速度（度/秒）\n    @property\n    friction: number = 1.5; // 摩擦力系数\n    @property\n    initAngle: number = 0; // 初始角度（度），可由外部设置\n\n    @property\n    maxHealth: number = 200; // 最大生命值\n\n    @property(SpriteFrame)\n    destroyedSprite: SpriteFrame = null!; // 摧毁状态的精灵图\n\n    @property\n    removeDelay: number = 3.0; // 摧毁后移除节点的延迟时间（秒）\n\n    // 颜料喷洒相关属性\n    @property(Prefab)\n    paintPrefab: Prefab = null!; // 颜料预制体\n\n    @property\n    paintSprayInterval: number = 0.2; // 颜料喷洒间隔（秒）\n\n    // 武器系统相关属性\n    @property({\n        type: Prefab,\n        tooltip: \"普通子弹预制体\"\n    })\n    normalBulletPrefab: Prefab = null!;\n\n    @property({\n        type: Prefab,\n        tooltip: \"火焰预制体\"\n    })\n    dartPrefab: Prefab = null!;\n\n    @property({\n        type: Prefab,\n        tooltip: \"火箭弹预制体\"\n    })\n    rocketPrefab: Prefab = null!;\n\n    @property({\n        tooltip: \"射速（发/秒）\"\n    })\n    fireRate: number = 2.0;\n\n    @property({\n        type: WeaponType,\n        tooltip: \"武器类型\"\n    })\n    weaponType: WeaponType = WeaponType.NORMAL;\n\n\n\n    @property({\n        tooltip: \"最大弹药数量\"\n    })\n    maxAmmo: number = 20;\n\n    @property({\n        tooltip: \"弹药补充时间（秒）\"\n    })\n    ammoReloadTime: number = 10.0;\n\n    protected _rigidBody: RigidBody2D = null!;\n    private _direction: number = 0; // -1:左, 0:不转, 1:右\n    private _accel: number = 0; // -1:刹车, 0:无, 1:加速\n    private _angle: number = -90; // 车辆朝向角度（度）\n    private _targetAngle: number = -90; // 目标角度\n    private _lastValidPosition: Vec2 = new Vec2(); // 上次有效位置\n\n    // 生命值和摧毁相关\n    private _currentHealth: number = 100; // 当前生命值\n    private _isDestroyed: boolean = false; // 是否已摧毁\n    private _originalSprite: SpriteFrame = null!; // 原始精灵图\n\n\n    // 颜料喷洒相关私有变量\n    private _paintTimer: number = 0; // 颜料喷洒计时器\n    private _vehicleId: string = 'player'; // 车辆唯一ID\n\n    // 武器系统相关私有变量\n    private _canFire: boolean = true; // 是否可以射击\n    private _fireTimer: number = 0; // 射击计时器\n    private _currentAmmo: number = 20; // 当前弹药数量\n    private _isReloading: boolean = false; // 是否正在补充弹药\n    private _reloadTimer: number = 0; // 弹药补充计时器\n\n    onLoad() {\n        // 确保在组件加载时初始化\n        this._rigidBody = null!;\n        this._direction = 0;\n        this._accel = 0;\n        this._angle = -90;\n        this._targetAngle = -90;\n        this._lastValidPosition = new Vec2();\n\n        // 初始化生命值和摧毁状态\n        this._currentHealth = this.maxHealth;\n        this._isDestroyed = false;\n\n        // 初始化颜料喷洒相关\n        this._paintTimer = 0;\n        this._vehicleId = 'player';\n\n        // 初始化武器系统相关\n        this._canFire = true;\n        this._fireTimer = 0;\n        this._currentAmmo = this.maxAmmo;\n        this._isReloading = false;\n        this._reloadTimer = 0;\n    }\n\n    onEnable() {\n        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);\n    }\n\n    onDisable() {\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);\n    }\n\n    onDestroy() {\n        \n        // 确保在组件销毁时清理所有事件监听\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);\n        \n        // 清理刚体引用\n        this._rigidBody = null!;\n        // 注销碰撞回调\n        const collider = this.getComponent(BoxCollider2D);\n        if (collider) {\n            collider.off(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        }\n    }\n\n    start() {\n        // 获取刚体组件\n        this._rigidBody = this.getComponent(RigidBody2D)!;\n        if (!this._rigidBody || !this.node || !this.node.isValid) {\n            console.error('player requires RigidBody2D component and valid node');\n            return;\n        }\n\n        // 设置刚体属性\n        this._rigidBody.type = ERigidBody2DType.Dynamic;\n        this._rigidBody.allowSleep = false; // 不允许休眠\n        this._rigidBody.gravityScale = 0; // 无重力\n        this._rigidBody.linearDamping = 0.3; // 降低线性阻尼\n        this._rigidBody.angularDamping = 0.9; // 增加角阻尼防止过度旋转\n        this._rigidBody.fixedRotation = true; // 固定旋转，手动控制\n\n        // 记录初始位置\n        this._lastValidPosition = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n\n        // 设置初始角度\n        this._angle = this.initAngle;\n        this._targetAngle = this.initAngle;\n        this.node.setRotationFromEuler(0, 0, this.initAngle);\n\n        // 保存原始精灵图\n        const sprite = this.getComponent(Sprite);\n        if (sprite && sprite.spriteFrame) {\n            this._originalSprite = sprite.spriteFrame;\n        }\n\n        // 注册碰撞回调\n        const collider = this.getComponent(BoxCollider2D);\n        if (collider) {\n            console.log('BoxCollider2D component found and registered');\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);\n        } else {\n            console.error('BoxCollider2D component not found');\n        }\n    }\n\n    onKeyDown(event: EventKeyboard) {\n        switch (event.keyCode) {\n            case KeyCode.ARROW_UP:\n                this._accel = 1;\n                break;\n            case KeyCode.ARROW_DOWN:\n                this._accel = -1;\n                break;\n            case KeyCode.ARROW_LEFT:\n                SoundManager.instance.playSoundEffect('carDrift');\n                this._direction = -1;\n                break;\n            case KeyCode.ARROW_RIGHT:\n                SoundManager.instance.playSoundEffect('carDrift');\n                this._direction = 1;\n                break;\n            case KeyCode.SPACE:\n                this.shoot();\n                break;\n        }\n    }\n\n    onKeyUp(event: EventKeyboard) {\n        switch (event.keyCode) {\n            case KeyCode.ARROW_UP:\n                if (this._accel === 1) this._accel = 0;\n                break;\n            case KeyCode.ARROW_DOWN:\n                if (this._accel === -1) this._accel = 0;\n                break;\n            case KeyCode.ARROW_LEFT:\n                if (this._direction === -1) this._direction = 0;\n                break;\n            case KeyCode.ARROW_RIGHT:\n                if (this._direction === 1) this._direction = 0;\n                break;\n        }\n    }\n\n    update(deltaTime: number) {\n        if (!this._rigidBody || !this.node || !this.node.isValid) return;\n\n        // 如果车辆已摧毁，执行摧毁动画逻辑\n        if (this._isDestroyed) {\n            // this.updateDestroyAnimation();\n            return;\n        }\n\n        // 获取当前速度和位置\n        const currentVelocity = this._rigidBody.linearVelocity;\n        const currentSpeed = currentVelocity.length();\n        const currentPos = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n\n        // 更新目标角度（转向）\n        if (this._direction !== 0) {\n            const turnAmount = this.turnSpeed * deltaTime * this._direction;\n            this._targetAngle -= turnAmount;\n        }\n\n        // 平滑角度插值，防止突然转向\n        const angleDiff = this._targetAngle - this._angle;\n        if (Math.abs(angleDiff) > 0.1) {\n            this._angle += angleDiff * 0.1; // 平滑插值\n        } else {\n            this._angle = this._targetAngle;\n        }\n\n        // 设置节点旋转\n        this.node.setRotationFromEuler(0, 0, this._angle);\n\n        // 前进\n        if (this._accel === 1) {\n            // 正常加速\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const force = new Vec2(\n                Math.cos(rad) * this.acceleration,\n                Math.sin(rad) * this.acceleration\n            );\n            this._rigidBody.applyForce(force, currentPos, true);\n        }\n        // 刹车\n        else if (this._accel === -1) {\n            // 如果当前速度方向与车辆朝向一致，施加反向力（刹车）\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const forward = new Vec2(Math.cos(rad), Math.sin(rad));\n            const dot = currentVelocity.dot(forward);\n            \n            if (dot > 0) {\n                // 施加强力反向力（刹车）\n                const brakeForce = forward.clone().multiplyScalar(-this.brakeDeceleration);\n                this._rigidBody.applyForce(brakeForce, currentPos, true);\n            } else {\n                // 允许倒车\n                const reverseForce = forward.clone().multiplyScalar(-this.acceleration * 0.5);\n                this._rigidBody.applyForce(reverseForce, currentPos, true);\n            }\n        }\n        // 松开加速/刹车键\n        else {\n            // 增大摩擦力，让车辆更快停下来\n            if (currentSpeed > 1) {\n                const frictionForce = currentVelocity.clone().multiplyScalar(-this.friction * 2); // 2倍摩擦\n                this._rigidBody.applyForce(frictionForce, currentPos, true);\n            }\n        }\n\n        // 限制最大速度\n        if (currentSpeed > this.maxSpeed) {\n            const normalizedVelocity = currentVelocity.clone().normalize();\n            this._rigidBody.linearVelocity = normalizedVelocity.multiplyScalar(this.maxSpeed);\n        }\n\n        // 防止车辆卡住或异常位置\n        if (currentSpeed < 0.1) {\n            // 如果速度很小，重置到上次有效位置附近\n            const distanceToLastPos = Vec2.distance(currentPos, this._lastValidPosition);\n            if (distanceToLastPos > 50) { // 如果偏离太远\n                this.node.setWorldPosition(this._lastValidPosition.x, this._lastValidPosition.y, this.node.worldPosition.z);\n                this._rigidBody.linearVelocity = new Vec2(0, 0);\n            }\n        } else {\n            // 更新有效位置\n            this._lastValidPosition = currentPos.clone();\n        }\n\n        // 防止车辆旋转过度\n        if (Math.abs(this._angle) > 360) {\n            this._angle = this._angle % 360;\n            this._targetAngle = this._targetAngle % 360;\n        }\n\n        // 更新颜料喷洒\n        this.updatePaintSpray(deltaTime);\n\n        // 更新武器系统\n        this.updateWeaponSystem(deltaTime);\n    }\n\n    public init(angle: number) {\n        this.initAngle = angle;\n        this._angle = angle;\n        this._targetAngle = angle;\n        this.node.setRotationFromEuler(0, 0, angle);\n    }\n\n    /**\n     * 获取玩家车辆的刚体组件\n     */\n    public getRigidBody(): RigidBody2D {\n        return this._rigidBody;\n    }\n\n    /**\n     * 玩家车辆与AI车辆碰撞时，按双方速度造成伤害\n     */\n    onBeginContact(_selfCollider: BoxCollider2D, otherCollider: BoxCollider2D, _contact: IPhysics2DContact | null) {\n        console.log('玩家车辆发生碰撞，碰撞对象:', otherCollider.node.name);\n        // 判断对方是否为AI车辆\n        const otherNode = otherCollider.node;\n        const aiPlayer = otherNode.getComponent(AIPlayer);\n        if (aiPlayer) {\n            SoundManager.instance.playSoundEffect('carCollision');\n            console.log('碰撞对象是AI车辆:', otherNode.name);\n            // 获取双方速度\n            const mySpeed = this._rigidBody.linearVelocity.length();\n            const aiRigidBody = aiPlayer.node.getComponent(RigidBody2D);\n            const aiSpeed = aiRigidBody ? aiRigidBody.linearVelocity.length() : 0;\n            // 伤害计算：对方受到我速度*系数的伤害，我受到对方速度*系数的伤害\n            const damageFactor = 0.5; // 可调节\n            const aiDamage = Math.round(mySpeed * damageFactor);\n            const playerDamage = Math.round(aiSpeed * damageFactor);\n            // 造成伤害\n            aiPlayer.takeDamage(aiDamage);\n            \n            // 施加反作用力\n            const recoilForce = new Vec2(this._rigidBody.linearVelocity.x, this._rigidBody.linearVelocity.y);\n            recoilForce.normalize(); // 归一化方向\n            recoilForce.multiplyScalar(-mySpeed * 0.05); // 根据速度大小施加反作用力\n            this._rigidBody.linearVelocity = recoilForce;\n            \n            this.takeDamage(playerDamage);\n        }\n        // 判断对方是否为子弹\n        else {\n            const bullet = otherNode.getComponent(Bullet) ;\n            if (bullet) {\n                // 检查是否为自己发射的子弹\n                if (bullet['_shooterId'] === 'player') {\n                    // 自己发射的子弹不造成伤害和反作用力\n                    console.log('玩家与自己发射的子弹碰撞，不造成伤害和反作用力');\n                    return;\n                } else {\n                    SoundManager.instance.playSoundEffect('carCollision');\n                    // 其他子弹造成伤害\n                    const bulletDamage = bullet['damage'] || 5;\n                    // this.takeDamage(bulletDamage);\n                    \n                    // 施加反作用力（只有非普通子弹才施加物理推力）\n                    // 火焰子弹(FLAME)已被注释，所以只检查是否为普通子弹\n                    if (bullet['bulletType'] === 0) { // 0是NORMAL类型\n                        const recoilForce = new Vec2(this._rigidBody.linearVelocity.x, this._rigidBody.linearVelocity.y);\n                        recoilForce.normalize();\n                        recoilForce.multiplyScalar(-bulletDamage * 0.5); // 根据子弹伤害施加反作用力\n                        this._rigidBody.linearVelocity = recoilForce;\n                    }\n                    \n                    console.log('玩家被子弹击中，造成伤害:', bulletDamage);\n                }\n            }\n            // 检测与地图边界的碰撞\n            else {\n                SoundManager.instance.playSoundEffect('carCollision');\n                const mySpeed = this._rigidBody.linearVelocity.length();\n                const damageFactor = 0.3; // 地图边界碰撞的伤害系数\n                const boundaryDamage = Math.round(mySpeed * damageFactor);\n                \n                // 施加反作用力\n                const recoilForce = new Vec2(this._rigidBody.linearVelocity.x, this._rigidBody.linearVelocity.y);\n                recoilForce.normalize(); // 归一化方向\n                recoilForce.multiplyScalar(-mySpeed * 0.05); // 根据速度大小施加反作用力\n                this._rigidBody.linearVelocity = recoilForce;\n                \n                this.takeDamage(boundaryDamage);\n            }\n        }\n    }\n\n    // ==================== 生命值和摧毁系统 ====================\n\n    /**\n     * 受到伤害\n     */\n    public takeDamage(damage: number) {\n        if (this._isDestroyed) return;\n\n        this._currentHealth = Math.max(0, this._currentHealth - damage);\n        console.log(`玩家受到伤害: ${damage}, 剩余生命值: ${this._currentHealth}`);\n\n        // 同步GameManager中的玩家血量显示\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.syncPlayerHealth();\n        }\n\n        // 检查是否死亡\n        if (this._currentHealth <= 0) {\n            this.destroyVehicle();\n        }\n    }\n\n    /**\n     * 摧毁车辆\n     */\n    private destroyVehicle() {\n        if (this._isDestroyed) return;\n        SoundManager.instance.playSoundEffect('carDestruction');\n        this._isDestroyed = true;\n        console.log('玩家车辆被摧毁！');\n\n        // 切换到摧毁状态的精灵图\n        if (this.destroyedSprite) {\n            const sprite = this.getComponent(Sprite);\n            if (sprite) {\n                sprite.spriteFrame = this.destroyedSprite;\n            }\n        }\n\n        // 禁用输入控制\n        this.disableInput();\n\n        // 开始摧毁动画，并在动画完成后触发游戏结束\n        this.startDestroyAnimation();\n\n        // 延迟移除节点（可选，通常玩家车辆不移除）\n        // this.scheduleRemoveNode();\n    }\n\n    /**\n     * 禁用输入控制\n     */\n    private disableInput() {\n        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);\n        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);\n\n        // 重置控制状态\n        this._direction = 0;\n        this._accel = 0;\n    }\n\n\n\n    /**\n     * 移除车辆节点（可选功能，通常玩家车辆不使用）\n     */\n    private removeVehicleNode() {\n        if (this.node && this.node.isValid) {\n            console.log('移除玩家车辆节点');\n            this.node.removeFromParent();\n        }\n    }\n\n    /**\n     * 开始摧毁动画\n     */\n    private startDestroyAnimation() {\n        // 使用缓动动画让车辆逐渐停止并可能添加一些视觉效果\n        if (this.node) {\n            // 可以添加旋转、缩放等效果\n            tween(this.node)\n                .to(2.0, {\n                    scale: new Vec3(1, 1, 1),  // 稍微缩小\n                    angle: this.node.angle + 180 // 旋转180度\n                })\n                .call(() => {\n                    // 动画完成后触发游戏结束\n                    this.onDestroyAnimationComplete();\n                })\n                .start();\n        }\n    }\n\n    /**\n     * 摧毁动画完成回调\n     */\n    private onDestroyAnimationComplete() {\n        console.log('玩家车辆摧毁动画完成，触发游戏结束');\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.gameOver(false); // false表示玩家失败\n        }\n    }\n\n\n\n    // ==================== 公共方法 ====================\n\n    /**\n     * 获取当前生命值\n     */\n    public getCurrentHealth(): number {\n        return this._currentHealth;\n    }\n\n    /**\n     * 获取最大生命值\n     */\n    public getMaxHealth(): number {\n        return this.maxHealth;\n    }\n\n    /**\n     * 是否已摧毁\n     */\n    public isDestroyed(): boolean {\n        return this._isDestroyed;\n    }\n\n    /**\n     * 恢复车辆（用于重新开始游戏）\n     */\n    public restoreVehicle() {\n        // 取消移除节点的计划\n        this.unschedule(this.removeVehicleNode);\n\n        this._isDestroyed = false;\n        this._currentHealth = this.maxHealth;\n\n        // 恢复原始精灵图\n        if (this._originalSprite) {\n            const sprite = this.getComponent(Sprite);\n            if (sprite) {\n                sprite.spriteFrame = this._originalSprite;\n            }\n        }\n\n        // 重新启用输入\n        this.onEnable();\n\n        // 恢复节点状态\n        if (this.node) {\n            this.node.setScale(1, 1);\n            this.node.angle = this.initAngle;\n        }\n\n        // 重置速度\n        if (this._rigidBody) {\n            this._rigidBody.linearVelocity = new Vec2(0, 0);\n        }\n\n        console.log('玩家车辆已恢复');\n    }\n\n    // ==================== 颜料喷洒系统 ====================\n\n    /**\n     * 更新颜料喷洒\n     * @param deltaTime 帧时间间隔\n     */\n    private updatePaintSpray(deltaTime: number): void {\n        if (this._isDestroyed || !this.paintPrefab) return;\n\n        // 更新计时器\n        this._paintTimer += deltaTime;\n\n        // 检查是否到了喷洒时间\n        if (this._paintTimer >= this.paintSprayInterval) {\n            this.sprayPaint();\n            this._paintTimer = 0; // 重置计时器\n        }\n    }\n\n    /**\n     * 喷洒颜料\n     */\n    private sprayPaint(): void {\n        const gameManager = GameManager.getInstance();\n        if (!gameManager) {\n            console.warn('GameManager未找到，无法喷洒颜料');\n            return;\n        }\n\n        // 获取当前车辆的世界位置\n        const worldPosition = this.node.getWorldPosition();\n\n        // 通过GameManager喷洒颜料\n        gameManager.sprayPaint(this.paintPrefab, worldPosition, this._vehicleId);\n    }\n\n    // ==================== 武器系统 ====================\n\n    /**\n     * 更新武器系统\n     * @param deltaTime 帧时间间隔\n     */\n    private updateWeaponSystem(deltaTime: number): void {\n        if (this._isDestroyed) return;\n\n        // 更新射击计时器\n        this._fireTimer += deltaTime;\n\n        // 检查是否可以射击\n        const fireInterval = 1 / this.fireRate;\n        if (this._fireTimer >= fireInterval) {\n            this._canFire = true;\n        }\n\n        // 更新弹药补充\n        this.updateAmmoReload(deltaTime);\n    }\n\n    /**\n     * 更新弹药补充\n     * @param deltaTime 帧时间间隔\n     */\n    private updateAmmoReload(deltaTime: number): void {\n        if (!this._isReloading) return;\n\n        this._reloadTimer += deltaTime;\n        if (this._reloadTimer >= this.ammoReloadTime) {\n            // 补充完成\n            this._currentAmmo = this.maxAmmo;\n            this._isReloading = false;\n            this._reloadTimer = 0;\n            console.log('弹药补充完成！');\n        }\n    }\n\n    /**\n     * 射击方法\n     */\n    public shoot(): void {\n        if (!this._canFire || this._isDestroyed) return;\n\n        // 检查弹药\n        if (this._currentAmmo <= 0) {\n            if (!this._isReloading) {\n                this.startReload();\n            }\n            return;\n        }\n\n        // 重置射击状态\n        this._canFire = false;\n        this._fireTimer = 0;\n        this._currentAmmo--;\n\n        // 根据武器类型选择子弹预制体\n        let bulletPrefab: Prefab | null = null;\n        switch (this.weaponType) {\n            case WeaponType.NORMAL:\n                console.log('发射普通子弹');\n                bulletPrefab = this.normalBulletPrefab;\n                break;\n            case WeaponType.DART:\n                bulletPrefab = this.dartPrefab;\n                break;\n            case WeaponType.ROCKET:\n                bulletPrefab = this.rocketPrefab;\n                break;\n        }\n\n        // 检查预制体是否存在\n        if (!bulletPrefab) {\n            console.warn('子弹预制体未设置');\n            // 允许重新射击\n            this._canFire = true;\n            return;\n        }\n\n        // 获取当前车辆的朝向\n        const rad = (this._angle + 90) * Math.PI / 180;\n        const direction = new Vec2(Math.cos(rad), Math.sin(rad));\n\n        // 计算子弹发射位置（车辆正前方）\n        const vehicleWorldPos = this.node.worldPosition;\n        const offsetDistance = 50; // 子弹发射偏移距离（像素）\n        const bulletStartPos = new Vec3(\n            vehicleWorldPos.x + direction.x * offsetDistance,\n            vehicleWorldPos.y + direction.y * offsetDistance,\n            vehicleWorldPos.z\n        );\n\n        // 获取GameManager实例并发射子弹\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.fireBullet(bulletPrefab, bulletStartPos, direction, this._vehicleId, this.weaponType);\n        }\n\n        // 播放射击音效\n        SoundManager.instance.playSoundEffect('weaponFire');\n\n        // 检查是否需要开始补充弹药\n        if (this._currentAmmo <= 0 && !this._isReloading) {\n            this.startReload();\n        }\n    }\n\n    /**\n     * 开始补充弹药\n     */\n    private startReload(): void {\n        this._isReloading = true;\n        this._reloadTimer = 0;\n        console.log('开始补充弹药...');\n    }\n\n    /**\n     * 获取当前弹药数量\n     */\n    public getCurrentAmmo(): number {\n        return this._currentAmmo;\n    }\n\n    /**\n     * 获取最大弹药数量\n     */\n    public getMaxAmmo(): number {\n        return this.maxAmmo;\n    }\n\n    /**\n     * 是否正在补充弹药\n     */\n    public isReloading(): boolean {\n        return this._isReloading;\n    }\n\n    /**\n     * 获取弹药补充进度（0-1）\n     */\n    public getReloadProgress(): number {\n        if (!this._isReloading) return 1;\n        return this._reloadTimer / this.ammoReloadTime;\n    }\n\n    // ==================== 外部控制接口 ====================\n\n    /**\n     * 设置加速度（外部控制接口）\n     * @param accel 加速度值：1为前进，-1为后退，0为停止\n     */\n    public setAcceleration(accel: number): void {\n        console.log(`Player: 设置加速度为 ${accel}`);\n        this._accel = accel;\n    }\n\n    /**\n     * 设置转向（外部控制接口）\n     * @param direction 转向值：1为右转，-1为左转，0为直行\n     */\n    public setDirection(direction: number): void {\n        console.log(`Player: 设置转向为 ${direction}`);\n        // 如果开始转向，播放漂移音效\n        if (direction !== 0 && this._direction === 0) {\n            SoundManager.instance.playSoundEffect('carDrift');\n        }\n        this._direction = direction;\n    }\n\n    /**\n     * 获取当前加速度状态\n     */\n    public getAcceleration(): number {\n        return this._accel;\n    }\n\n    /**\n     * 获取当前转向状态\n     */\n    public getDirection(): number {\n        return this._direction;\n    }\n\n}\n\n"]}