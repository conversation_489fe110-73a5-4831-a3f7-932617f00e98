{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Node", "Label", "SceneTransition", "SoundManager", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "MainMenuController", "start", "scheduleOnce", "initializeUI", "startGameBtn", "node", "on", "EventType", "CLICK", "onStartGame", "settingBtn", "displaySettingPanel", "closesettingBtn", "hideSettingPanel", "helpButton", "displayHelpPanel", "closehelpBtn", "hideHelpPanel", "audioBtn", "onAudioClick", "resetProgressBtn", "onResetProgress", "confirmResetBtn", "onConfirmReset", "closeResetPanelBtn", "closeResetPanel", "updateAudioButtonLabel", "console", "log", "error", "settingPanel", "active", "helpPanel", "instance", "toggleAudio", "warn", "audioLabel", "string", "isMuted", "playSoundEffect", "loadScene", "showResetConfirmPanel", "resetProgressConfirmPanel", "resetPlayerData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AACrCC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;oCAGjBU,kB,WADZF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACN,IAAD,C,UAGRM,QAAQ,CAACL,KAAD,C,UAIRK,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,WAGRO,QAAQ,CAACN,IAAD,C,WAGRM,QAAQ,CAACP,MAAD,C,WAGRO,QAAQ,CAACN,IAAD,C,WAGRM,QAAQ,CAACP,MAAD,C,WAGRO,QAAQ,CAACP,MAAD,C,2BAvCb,MACaQ,kBADb,SACwCT,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAEhB;AAFgB;;AAAA;;AAAA;;AAAA;;AAclB;AAdkB;;AAiBnB;AAjBmB;;AAAA;;AAAA;;AA2BrB;AA3BqB;;AA8BZ;AA9BY;;AAiCL;AAjCK;;AAoCb;AApCa;AAAA;;AAuCV;AAIpCU,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,YAAL;AACH,WAFD,EAEG,GAFH;AAGH;;AAEOA,QAAAA,YAAY,GAAG;AACnB,cAAI;AACA,gBAAI,KAAKC,YAAT,EAAuB;AACnB,mBAAKA,YAAL,CAAkBC,IAAlB,CAAuBC,EAAvB,CAA0Bd,MAAM,CAACe,SAAP,CAAiBC,KAA3C,EAAkD,KAAKC,WAAvD,EAAoE,IAApE;AACH;;AACD,gBAAG,KAAKC,UAAR,EAAmB;AACf,mBAAKA,UAAL,CAAgBL,IAAhB,CAAqBC,EAArB,CAAwBd,MAAM,CAACe,SAAP,CAAiBC,KAAzC,EAAgD,KAAKG,mBAArD,EAA0E,IAA1E;AACH;;AACD,gBAAG,KAAKC,eAAR,EAAwB;AACpB,mBAAKA,eAAL,CAAqBP,IAArB,CAA0BC,EAA1B,CAA6Bd,MAAM,CAACe,SAAP,CAAiBC,KAA9C,EAAqD,KAAKK,gBAA1D,EAA4E,IAA5E;AACH;;AAED,gBAAG,KAAKC,UAAR,EAAmB;AACf,mBAAKA,UAAL,CAAgBT,IAAhB,CAAqBC,EAArB,CAAwBd,MAAM,CAACe,SAAP,CAAiBC,KAAzC,EAAgD,KAAKO,gBAArD,EAAuE,IAAvE;AACH;;AACD,gBAAG,KAAKC,YAAR,EAAqB;AACjB,mBAAKA,YAAL,CAAkBX,IAAlB,CAAuBC,EAAvB,CAA0Bd,MAAM,CAACe,SAAP,CAAiBC,KAA3C,EAAkD,KAAKS,aAAvD,EAAsE,IAAtE;AACH;;AAED,gBAAG,KAAKC,QAAR,EAAiB;AACb,mBAAKA,QAAL,CAAcb,IAAd,CAAmBC,EAAnB,CAAsBd,MAAM,CAACe,SAAP,CAAiBC,KAAvC,EAA8C,KAAKW,YAAnD,EAAiE,IAAjE;AACH,aApBD,CAsBA;;;AACA,gBAAI,KAAKC,gBAAT,EAA2B;AACvB,mBAAKA,gBAAL,CAAsBf,IAAtB,CAA2BC,EAA3B,CAA8Bd,MAAM,CAACe,SAAP,CAAiBC,KAA/C,EAAsD,KAAKa,eAA3D,EAA4E,IAA5E;AACH,aAzBD,CA2BA;;;AACA,gBAAI,KAAKC,eAAT,EAA0B;AACtB,mBAAKA,eAAL,CAAqBjB,IAArB,CAA0BC,EAA1B,CAA6Bd,MAAM,CAACe,SAAP,CAAiBC,KAA9C,EAAqD,KAAKe,cAA1D,EAA0E,IAA1E;AACH;;AAED,gBAAI,KAAKC,kBAAT,EAA6B;AACzB,mBAAKA,kBAAL,CAAwBnB,IAAxB,CAA6BC,EAA7B,CAAgCd,MAAM,CAACe,SAAP,CAAiBC,KAAjD,EAAwD,KAAKiB,eAA7D,EAA8E,IAA9E;AACH;;AAED,iBAAKC,sBAAL;AACAC,YAAAA,OAAO,CAACC,GAAR,CAAY,gDAAZ;AACH,WAtCD,CAsCE,OAAOC,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,2CAAd,EAA2DA,KAA3D;AACH;AACJ;;AAEDlB,QAAAA,mBAAmB,GAAG;AAClB,eAAKmB,YAAL,CAAkBC,MAAlB,GAA2B,IAA3B;AACH;;AAEDlB,QAAAA,gBAAgB,GAAG;AACf,eAAKiB,YAAL,CAAkBC,MAAlB,GAA2B,KAA3B;AACH;;AAEDhB,QAAAA,gBAAgB,GAAG;AACf,eAAKiB,SAAL,CAAeD,MAAf,GAAwB,IAAxB;AACH;;AAEDd,QAAAA,aAAa,GAAG;AACZ,eAAKe,SAAL,CAAeD,MAAf,GAAwB,KAAxB;AACH;;AAGDZ,QAAAA,YAAY,GAAG;AACX,cAAI;AACA,gBAAI;AAAA;AAAA,8CAAac,QAAjB,EAA2B;AACvB;AAAA;AAAA,gDAAaA,QAAb,CAAsBC,WAAtB;AACA,mBAAKR,sBAAL;AACH,aAHD,MAGO;AACHC,cAAAA,OAAO,CAACQ,IAAR,CAAa,qCAAb;AACH;AACJ,WAPD,CAOE,OAAON,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;AACH;AACJ;;AAEDH,QAAAA,sBAAsB,GAAG;AACrB,cAAI;AACA,gBAAI,KAAKU,UAAL,IAAmB;AAAA;AAAA,8CAAaH,QAApC,EAA8C;AAC1C,mBAAKG,UAAL,CAAgBC,MAAhB,GAAyB;AAAA;AAAA,gDAAaJ,QAAb,CAAsBK,OAAtB,KAAkC,mBAAlC,GAAwD,iBAAjF;AACH;AACJ,WAJD,CAIE,OAAOT,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,oCAAd,EAAoDA,KAApD;AACH;AACJ;;AAGDpB,QAAAA,WAAW,GAAG;AACV,cAAI;AACA,gBAAI;AAAA;AAAA,8CAAawB,QAAjB,EAA2B;AACvB;AAAA;AAAA,gDAAaA,QAAb,CAAsBM,eAAtB,CAAsC,aAAtC;AACH;;AACD;AAAA;AAAA,oDAAgBC,SAAhB,CAA0B,aAA1B;AACH,WALD,CAKE,OAAOX,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,sBAAd,EAAsCA,KAAtC,EADY,CAEZ;;AACA;AAAA;AAAA,oDAAgBW,SAAhB,CAA0B,aAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACInB,QAAAA,eAAe,GAAG;AACd,cAAI;AACA;AACA,gBAAI;AAAA;AAAA,8CAAaY,QAAjB,EAA2B;AACvB;AAAA;AAAA,gDAAaA,QAAb,CAAsBM,eAAtB,CAAsC,aAAtC;AACH,aAJD,CAMA;;;AACA,iBAAKE,qBAAL;AACH,WARD,CAQE,OAAOZ,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;AACH;AACJ;AAED;AACJ;AACA;;;AACIY,QAAAA,qBAAqB,GAAG;AACpB,cAAI,KAAKC,yBAAT,EAAoC;AAChC,iBAAKA,yBAAL,CAA+BX,MAA/B,GAAwC,IAAxC;AACH;AACJ;AAED;AACJ;AACA;;;AACIN,QAAAA,eAAe,GAAG;AACd,cAAI,KAAKiB,yBAAT,EAAoC;AAChC,iBAAKA,yBAAL,CAA+BX,MAA/B,GAAwC,KAAxC;AACH;AACJ;AAED;AACJ;AACA;;;AACIR,QAAAA,cAAc,GAAG;AACb,cAAI;AACA;AACA,gBAAI;AAAA;AAAA,8CAAaU,QAAjB,EAA2B;AACvB;AAAA;AAAA,gDAAaA,QAAb,CAAsBM,eAAtB,CAAsC,aAAtC;AACH,aAJD,CAMA;;;AACA,iBAAKd,eAAL,GAPA,CASA;;AACA,gBAAI;AAAA;AAAA,gDAAcQ,QAAlB,EAA4B;AACxB;AAAA;AAAA,kDAAcA,QAAd,CAAuBU,eAAvB;AACAhB,cAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH,aAHD,MAGO;AACHD,cAAAA,OAAO,CAACQ,IAAR,CAAa,sCAAb;AACH;AACJ,WAhBD,CAgBE,OAAON,KAAP,EAAc;AACZF,YAAAA,OAAO,CAACE,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;AACH;AACJ;;AA/M6C,O;;;;;iBAEvB,I;;;;;;;iBAGF,I;;;;;;;iBAGK,I;;;;;;;iBAGP,I;;;;;;;iBAGE,I;;;;;;;iBAGD,I;;;;;;;iBAIC,I;;;;;;;iBAGE,I;;;;;;;iBAGL,I;;;;;;;iBAGS,I;;;;;;;iBAGO,I;;;;;;;iBAGR,I;;;;;;;iBAGG,I", "sourcesContent": ["import { _decorator, Component, Button, Node, Label } from 'cc';\nimport { SceneTransition } from './SceneTransition';\nimport { SoundManager } from './SoundManager';\nimport { PlayerManager } from './PlayerManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainMenuController')\nexport class MainMenuController extends Component {\n    @property(Button)\n    startGameBtn: Button = null!; // 拖拽你的\"开始游戏\"按钮到这里\n\n    @property(Button)\n    settingBtn: Button = null!; \n\n    @property(Button)\n    closesettingBtn: Button = null!; \n\n    @property(Button)\n    audioBtn: Button = null!; \n\n    @property(Node)\n    settingPanel: Node = null!; // 拖拽你的设置面板节点到这里\n\n    @property(Label)\n    audioLabel: Label = null!; // 拖拽音效按钮的Label组件到这里\n\n\n    @property(Button)\n    helpButton: Button = null!; \n\n    @property(Button)\n    closehelpBtn: Button = null!; \n\n    @property(Node)\n    helpPanel: Node = null!; // 拖拽你的设置面板节点到这里\n\n    @property(Button)\n    resetProgressBtn: Button = null!; // 重置进度按钮\n\n    @property(Node)\n    resetProgressConfirmPanel: Node = null!; // 重置进度确认面板\n\n    @property(Button)\n    confirmResetBtn: Button = null!; // 确认重置按钮\n\n    @property(Button)\n    closeResetPanelBtn: Button = null!; // 关闭重置面板按钮\n\n\n\n    start() {\n        // 延迟初始化，等待SoundManager和PlayerManager准备就绪\n        this.scheduleOnce(() => {\n            this.initializeUI();\n        }, 0.2);\n    }\n\n    private initializeUI() {\n        try {\n            if (this.startGameBtn) {\n                this.startGameBtn.node.on(Button.EventType.CLICK, this.onStartGame, this);\n            }\n            if(this.settingBtn){\n                this.settingBtn.node.on(Button.EventType.CLICK, this.displaySettingPanel, this);\n            }\n            if(this.closesettingBtn){\n                this.closesettingBtn.node.on(Button.EventType.CLICK, this.hideSettingPanel, this);\n            }\n\n            if(this.helpButton){\n                this.helpButton.node.on(Button.EventType.CLICK, this.displayHelpPanel, this);\n            }\n            if(this.closehelpBtn){\n                this.closehelpBtn.node.on(Button.EventType.CLICK, this.hideHelpPanel, this);\n            }\n\n            if(this.audioBtn){\n                this.audioBtn.node.on(Button.EventType.CLICK, this.onAudioClick, this);\n            }\n\n            // 添加重置进度按钮事件监听\n            if (this.resetProgressBtn) {\n                this.resetProgressBtn.node.on(Button.EventType.CLICK, this.onResetProgress, this);\n            }\n\n            // 添加重置确认面板按钮事件监听\n            if (this.confirmResetBtn) {\n                this.confirmResetBtn.node.on(Button.EventType.CLICK, this.onConfirmReset, this);\n            }\n\n            if (this.closeResetPanelBtn) {\n                this.closeResetPanelBtn.node.on(Button.EventType.CLICK, this.closeResetPanel, this);\n            }\n\n            this.updateAudioButtonLabel();\n            console.log('MainMenuController UI initialized successfully');\n        } catch (error) {\n            console.error('Error initializing MainMenuController UI:', error);\n        }\n    }\n\n    displaySettingPanel() {\n        this.settingPanel.active = true;\n    }\n\n    hideSettingPanel() {\n        this.settingPanel.active = false;\n    }\n\n    displayHelpPanel() {\n        this.helpPanel.active = true;\n    }\n\n    hideHelpPanel() {\n        this.helpPanel.active = false;\n    }\n\n\n    onAudioClick() {\n        try {\n            if (SoundManager.instance) {\n                SoundManager.instance.toggleAudio();\n                this.updateAudioButtonLabel();\n            } else {\n                console.warn('SoundManager instance not available');\n            }\n        } catch (error) {\n            console.error('Error toggling audio:', error);\n        }\n    }\n\n    updateAudioButtonLabel() {\n        try {\n            if (this.audioLabel && SoundManager.instance) {\n                this.audioLabel.string = SoundManager.instance.isMuted() ? \"音效:关 \\n sound:off\" : \"音效:开\\n sound:on\";\n            }\n        } catch (error) {\n            console.error('Error updating audio button label:', error);\n        }\n    }\n\n\n    onStartGame() {\n        try {\n            if (SoundManager.instance) {\n                SoundManager.instance.playSoundEffect('buttonClick');\n            }\n            SceneTransition.loadScene(\"LevelSelect\");\n        } catch (error) {\n            console.error('Error starting game:', error);\n            // 如果出错，直接加载场景\n            SceneTransition.loadScene(\"LevelSelect\");\n        }\n    }\n\n    /**\n     * 重置玩家进度\n     * 将玩家的金钱、车辆解锁状态、关卡解锁状态等重置为初始状态\n     */\n    onResetProgress() {\n        try {\n            // 播放按钮点击音效\n            if (SoundManager.instance) {\n                SoundManager.instance.playSoundEffect('buttonClick');\n            }\n\n            // 显示确认面板\n            this.showResetConfirmPanel();\n        } catch (error) {\n            console.error('Error in reset progress:', error);\n        }\n    }\n\n    /**\n     * 显示重置确认面板\n     */\n    showResetConfirmPanel() {\n        if (this.resetProgressConfirmPanel) {\n            this.resetProgressConfirmPanel.active = true;\n        }\n    }\n\n    /**\n     * 关闭重置确认面板\n     */\n    closeResetPanel() {\n        if (this.resetProgressConfirmPanel) {\n            this.resetProgressConfirmPanel.active = false;\n        }\n    }\n\n    /**\n     * 确认重置玩家进度\n     */\n    onConfirmReset() {\n        try {\n            // 播放按钮点击音效\n            if (SoundManager.instance) {\n                SoundManager.instance.playSoundEffect('buttonClick');\n            }\n\n            // 关闭确认面板\n            this.closeResetPanel();\n\n            // 执行重置操作\n            if (PlayerManager.instance) {\n                PlayerManager.instance.resetPlayerData();\n                console.log(\"玩家进度已重置\");\n            } else {\n                console.warn('PlayerManager instance not available');\n            }\n        } catch (error) {\n            console.error('Error confirming reset:', error);\n        }\n    }\n}"]}