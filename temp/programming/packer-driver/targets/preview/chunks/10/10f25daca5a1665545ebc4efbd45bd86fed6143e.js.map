{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts"], "names": ["_decorator", "assert", "CCBoolean", "CCFloat", "CCInteger", "gfx", "Material", "rendering", "Vec3", "Vec4", "EDITOR", "BuiltinPipelineSettings", "BuiltinPipelinePassBuilder", "getPingPongRenderTarget", "ccclass", "disallowMultiple", "executeInEditMode", "menu", "property", "requireComponent", "type", "Color", "LoadOp", "StoreOp", "BuiltinDepthOfFieldPass", "group", "id", "name", "style", "visible", "min", "range", "slide", "_clearColorTransparentBlack", "_cocParams", "_focusPosVec4", "_cocTexSize", "dofEnable", "value", "_enableDof", "_parent", "_tryEnableEditorPreview", "dofMaterial", "_material", "dofMinRange", "_minRange", "dofMaxRange", "_max<PERSON>ange", "dofIntensity", "_intensity", "dofBlurRadius", "_blurRadius", "dofFocusPos", "_focusPos", "getConfigOrder", "config<PERSON><PERSON>r", "getRenderOrder", "renderOrder", "configCamera", "camera", "pplConfigs", "cameraConfigs", "enableDof", "supportDepthSample", "enableStoreSceneDepth", "remainingPasses", "windowResize", "ppl", "window", "renderWindowId", "addR<PERSON><PERSON>arget", "radianceFormat", "width", "height", "setup", "context", "prevRenderPass", "_addDepthOfFieldPasses", "colorName", "depthStencilName", "prefix", "enableShadingScale", "outputRadianceName", "inputRadianceName", "inputRadiance", "inputDepthStencil", "x", "y", "z", "w", "tempRadiance", "blurPass", "addRenderPass", "CLEAR", "STORE", "addTexture", "setVec4", "platform", "addQueue", "QueueHint", "OPAQUE", "addCameraQuad", "<PERSON><PERSON><PERSON><PERSON>", "setMat4", "<PERSON><PERSON><PERSON><PERSON>", "matProjInv", "node", "worldMatrix"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBIA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AACxCC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,Q,OAAAA,Q;AAAoBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAGrCC,MAAAA,M,UAAAA,M;;AAGLC,MAAAA,uB,iBAAAA,uB;;AAIAC,MAAAA,0B,iBAAAA,0B;;AAKAC,MAAAA,uB,iBAAAA,uB;;;;;;AAzCJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAwBM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,gBAAX;AAA6BC,QAAAA,iBAA7B;AAAgDC,QAAAA,IAAhD;AAAsDC,QAAAA,QAAtD;AAAgEC,QAAAA,gBAAhE;AAAkFC,QAAAA;AAAlF,O,GAA2FpB,U;OAE3F;AAAEqB,QAAAA,KAAF;AAASC,QAAAA,MAAT;AAAiBC,QAAAA;AAAjB,O,GAA6BlB,G;;yCAWtBmB,uB,WALZV,OAAO,CAAC,yBAAD,C,UACPG,IAAI,CAAC,mCAAD,C,UACJE,gBAAgB;AAAA;AAAA,6D,UAKZD,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,aAAN;AAAqBC,UAAAA,IAAI,EAAE,eAA3B;AAA4CC,UAAAA,KAAK,EAAE;AAAnD,SADD;AAENR,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAKRc,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,aAAN;AAAqBC,UAAAA,IAAI,EAAE,eAA3B;AAA4CC,UAAAA,KAAK,EAAE;AAAnD,SADD;AAENR,QAAAA,IAAI,EAAEhB;AAFA,OAAD,C,UAsBRc,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAElB,SAFA;AAGN2B,QAAAA,OAAO,EAAE;AAHH,OAAD,C,UAeRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEd,QAFA;AAGNuB,QAAAA,OAAO,EAAE;AAHH,OAAD,C,UAkBRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN2B,QAAAA,GAAG,EAAE,CAHC;AAIND,QAAAA,OAAO,EAAE;AAJH,OAAD,C,UAaRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN2B,QAAAA,GAAG,EAAE,CAHC;AAIND,QAAAA,OAAO,EAAE;AAJH,OAAD,C,WAaRX,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN4B,QAAAA,KAAK,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,IAAT,CAHD;AAINC,QAAAA,KAAK,EAAE,IAJD;AAKNH,QAAAA,OAAO,EAAE;AALH,OAAD,C,WAcRT,IAAI,CAACjB,OAAD,C,WACJe,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEjB,OAFA;AAGN4B,QAAAA,KAAK,EAAE,CAAC,IAAD,EAAO,EAAP,EAAW,IAAX,CAHD;AAINC,QAAAA,KAAK,EAAE,IAJD;AAKNH,QAAAA,OAAO,EAAE;AALH,OAAD,C,WAcRT,IAAI,CAACZ,IAAD,C,WACJU,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,+BAA5B;AAA6DC,UAAAA,KAAK,EAAE;AAApE,SADD;AAENR,QAAAA,IAAI,EAAEZ,IAFA;AAGNqB,QAAAA,OAAO,EAAE;AAHH,OAAD,C,8CAxHZd,gB,UACAC,iB,qBAJD,MAKaQ,uBALb;AAAA;AAAA,oEAM6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA+PzC;AA/PyC,eAgQxBS,2BAhQwB,GAgQM,IAAIZ,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAhQN;AAAA,eAiQxBa,UAjQwB,GAiQX,IAAIzB,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAjQW;AAAA,eAkQxB0B,aAlQwB,GAkQR,IAAI1B,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAlQQ;AAAA,eAmQxB2B,WAnQwB,GAmQV,IAAI3B,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAnQU;AAAA;;AA2BzC;AAMa,YAAT4B,SAAS,CAACC,KAAD,EAAiB;AAC1B,eAAKC,UAAL,GAAkBD,KAAlB;;AACA,cAAI5B,MAAJ,EAAY;AACR,iBAAK8B,OAAL,CAAaC,uBAAb;AACH;AACJ;;AACY,YAATJ,SAAS,GAAY;AACrB,iBAAO,KAAKE,UAAZ;AACH;;AAOc,YAAXG,WAAW,CAACJ,KAAD,EAAkB;AAC7B,cAAI,KAAKK,SAAL,KAAmBL,KAAvB,EAA8B;AAC1B;AACH;;AACD,eAAKK,SAAL,GAAiBL,KAAjB;;AACA,cAAI5B,MAAJ,EAAY;AACR,iBAAK8B,OAAL,CAAaC,uBAAb;AACH;AACJ;;AACc,YAAXC,WAAW,GAAa;AACxB,iBAAO,KAAKC,SAAZ;AACH;;AAQc,YAAXC,WAAW,CAACN,KAAD,EAAgB;AAC3B,eAAKO,SAAL,GAAiBP,KAAjB;AACH;;AACc,YAAXM,WAAW,GAAW;AACtB,iBAAO,KAAKC,SAAZ;AACH;;AAQc,YAAXC,WAAW,CAACR,KAAD,EAAgB;AAC3B,eAAKS,SAAL,GAAiBT,KAAjB;AACH;;AACc,YAAXQ,WAAW,GAAW;AACtB,iBAAO,KAAKC,SAAZ;AACH;;AASe,YAAZC,YAAY,CAACV,KAAD,EAAgB;AAC5B,eAAKW,UAAL,GAAkBX,KAAlB;AACH;;AACe,YAAZU,YAAY,GAAW;AACvB,iBAAO,KAAKC,UAAZ;AACH;;AAUgB,YAAbC,aAAa,CAACZ,KAAD,EAAgB;AAC7B,eAAKa,WAAL,GAAmBb,KAAnB;AACH;;AACgB,YAAbY,aAAa,GAAW;AACxB,iBAAO,KAAKC,WAAZ;AACH;;AAQc,YAAXC,WAAW,CAACd,KAAD,EAAc;AACzB,eAAKe,SAAL,GAAiBf,KAAjB;AACH;;AACc,YAAXc,WAAW,GAAS;AACpB,iBAAO,KAAKC,SAAZ;AACH,SA/HwC,CAiIzC;;;AACAC,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAKC,WAAZ;AACH;;AACDC,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAKC,WAAZ;AACH;;AACDC,QAAAA,YAAY,CACRC,MADQ,EAERC,UAFQ,EAGRC,aAHQ,EAG6C;AACrDA,UAAAA,aAAa,CAACC,SAAd,GAA0BF,UAAU,CAACG,kBAAX,IACnB,KAAKxB,UADc,IAEnB,CAAC,CAAC,KAAKI,SAFd;;AAIA,cAAIkB,aAAa,CAACC,SAAlB,EAA6B;AACzB;AACAD,YAAAA,aAAa,CAACG,qBAAd,GAAsC,IAAtC;AACA,cAAEH,aAAa,CAACI,eAAhB;AACH;AACJ;;AACDC,QAAAA,YAAY,CACRC,GADQ,EAERP,UAFQ,EAGRC,aAHQ,EAIRO,MAJQ,EAI6B;AACrC,cAAM1C,EAAE,GAAG0C,MAAM,CAACC,cAAlB;;AACA,cAAIR,aAAa,CAACC,SAAlB,EAA6B;AACzBK,YAAAA,GAAG,CAACG,eAAJ,iBAAkC5C,EAAlC,EACImC,aAAa,CAACU,cADlB,EAEIV,aAAa,CAACW,KAFlB,EAGIX,aAAa,CAACY,MAHlB;AAIH;AACJ;;AACDC,QAAAA,KAAK,CACDP,GADC,EAEDP,UAFC,EAGDC,aAHC,EAIDF,MAJC,EAKDgB,OALC,EAMDC,cANC,EAMgG;AACjG,cAAI,CAACf,aAAa,CAACC,SAAnB,EAA8B;AAC1B,mBAAOc,cAAP;AACH;;AACD,YAAEf,aAAa,CAACI,eAAhB;AAEAhE,UAAAA,MAAM,CAAC,CAAC,CAAC,KAAK0C,SAAR,CAAN;;AACA,cAAIkB,aAAa,CAACI,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO,KAAKY,sBAAL,CAA4BV,GAA5B,EAAiCP,UAAjC,EACHC,aADG,EACY,KAAKlB,SADjB,EAEHgB,MAFG,EAEKE,aAAa,CAACW,KAFnB,EAE0BX,aAAa,CAACY,MAFxC,EAGHE,OAAO,CAACG,SAHL,EAIHH,OAAO,CAACI,gBAJL,EAKHlB,aAAa,CAACiB,SALX,CAAP;AAMH,WAPD,MAOO;AACH,gBAAME,MAAM,GAAGnB,aAAa,CAACoB,kBAAd,gCAAf;AAGA,gBAAMC,kBAAkB,GAAG;AAAA;AAAA,oEACvBP,OAAO,CAACG,SADe,EACJE,MADI,EACInB,aAAa,CAACQ,cADlB,CAA3B;AAEA,gBAAMc,iBAAiB,GAAGR,OAAO,CAACG,SAAlC;AACAH,YAAAA,OAAO,CAACG,SAAR,GAAoBI,kBAApB;AACA,mBAAO,KAAKL,sBAAL,CAA4BV,GAA5B,EAAiCP,UAAjC,EACHC,aADG,EACY,KAAKlB,SADjB,EAEHgB,MAFG,EAEKE,aAAa,CAACW,KAFnB,EAE0BX,aAAa,CAACY,MAFxC,EAGHU,iBAHG,EAIHR,OAAO,CAACI,gBAJL,EAKHG,kBALG,CAAP;AAMH;AACJ;;AACOL,QAAAA,sBAAsB,CAC1BV,GAD0B,EAE1BP,UAF0B,EAG1BC,aAH0B,EAI1BnB,WAJ0B,EAK1BiB,MAL0B,EAM1Ba,KAN0B,EAO1BC,MAP0B,EAQ1BW,aAR0B,EAS1BC,iBAT0B,EAU1BH,kBAV0B,EAWM;AAChC,eAAKhD,UAAL,CAAgBoD,CAAhB,GAAoB,KAAKzC,SAAzB;AACA,eAAKX,UAAL,CAAgBqD,CAAhB,GAAoB,KAAKxC,SAAzB;AACA,eAAKb,UAAL,CAAgBsD,CAAhB,GAAoB,KAAKrC,WAAzB;AACA,eAAKjB,UAAL,CAAgBuD,CAAhB,GAAoB,KAAKxC,UAAzB;AACA,eAAKd,aAAL,CAAmBmD,CAAnB,GAAuB,KAAKjC,SAAL,CAAeiC,CAAtC;AACA,eAAKnD,aAAL,CAAmBoD,CAAnB,GAAuB,KAAKlC,SAAL,CAAekC,CAAtC;AACA,eAAKpD,aAAL,CAAmBqD,CAAnB,GAAuB,KAAKnC,SAAL,CAAemC,CAAtC;AACA,eAAKpD,WAAL,CAAiBkD,CAAjB,GAAqB,MAAMd,KAA3B;AACA,eAAKpC,WAAL,CAAiBmD,CAAjB,GAAqB,MAAMd,MAA3B;AACA,eAAKrC,WAAL,CAAiBoD,CAAjB,GAAqBhB,KAArB;AACA,eAAKpC,WAAL,CAAiBqD,CAAjB,GAAqBhB,MAArB;AAEA,cAAM/C,EAAE,GAAGmC,aAAa,CAACQ,cAAzB;AACA,cAAMqB,YAAY,mBAAiBhE,EAAnC,CAdgC,CAgBhC;;AACA,cAAMiE,QAAQ,GAAGxB,GAAG,CAACyB,aAAJ,CAAkBpB,KAAlB,EAAyBC,MAAzB,EAAiC,aAAjC,CAAjB;AACAkB,UAAAA,QAAQ,CAACrB,eAAT,CAAyBoB,YAAzB,EAAuCpE,MAAM,CAACuE,KAA9C,EAAqDtE,OAAO,CAACuE,KAA7D,EAAoE,KAAK7D,2BAAzE;AACA0D,UAAAA,QAAQ,CAACI,UAAT,CAAoBX,aAApB,EAAmC,WAAnC;AACAO,UAAAA,QAAQ,CAACK,OAAT,CAAiB,YAAjB,EAA+BpC,UAAU,CAACqC,QAA1C;AACAN,UAAAA,QAAQ,CAACK,OAAT,CAAiB,YAAjB,EAA+B,KAAK9D,UAApC;AACAyD,UAAAA,QAAQ,CAACK,OAAT,CAAiB,kBAAjB,EAAqC,KAAK5D,WAA1C;AACAuD,UAAAA,QAAQ,CACHO,QADL,CACc3F,SAAS,CAAC4F,SAAV,CAAoBC,MADlC,EAEKC,aAFL,CAEmB1C,MAFnB,EAE2BjB,WAF3B,EAEwC,CAFxC,EAvBgC,CAyBY;AAC5C;;AACA,cAAM4D,OAAO,GAAGnC,GAAG,CAACyB,aAAJ,CAAkBpB,KAAlB,EAAyBC,MAAzB,EAAiC,YAAjC,CAAhB;AACA6B,UAAAA,OAAO,CAAChC,eAAR,CAAwBY,kBAAxB,EAA4C5D,MAAM,CAACuE,KAAnD,EAA0DtE,OAAO,CAACuE,KAAlE,EAAyE,KAAK7D,2BAA9E;AACAqE,UAAAA,OAAO,CAACP,UAAR,CAAmBL,YAAnB,EAAiC,UAAjC;AACAY,UAAAA,OAAO,CAACP,UAAR,CAAmBV,iBAAnB,EAAsC,UAAtC;AACAiB,UAAAA,OAAO,CAACP,UAAR,CAAmBX,aAAnB,EAAkC,WAAlC;AACAkB,UAAAA,OAAO,CAACN,OAAR,CAAgB,YAAhB,EAA8BpC,UAAU,CAACqC,QAAzC;AACAK,UAAAA,OAAO,CAACC,OAAR,CAAgB,MAAhB,EAAwB5C,MAAM,CAAC6C,OAA/B;AACAF,UAAAA,OAAO,CAACC,OAAR,CAAgB,SAAhB,EAA2B5C,MAAM,CAAC8C,UAAlC;AACAH,UAAAA,OAAO,CAACC,OAAR,CAAgB,YAAhB,EAA8B5C,MAAM,CAAC+C,IAAP,CAAYC,WAA1C;AACAL,UAAAA,OAAO,CAACN,OAAR,CAAgB,WAAhB,EAA6B,KAAK9D,UAAlC;AACAoE,UAAAA,OAAO,CAACN,OAAR,CAAgB,OAAhB,EAAyB,KAAK7D,aAA9B;AACAmE,UAAAA,OAAO,CACFJ,QADL,CACc3F,SAAS,CAAC4F,SAAV,CAAoBC,MADlC,EAEKC,aAFL,CAEmB1C,MAFnB,EAE2BjB,WAF3B,EAEwC,CAFxC;AAIA,iBAAO4D,OAAP;AACH;;AA7PwC,O;;;;;iBAK3B,C;;;;;;;iBAKA,G;;qFAEbpF,Q;;;;;iBACoB,K;;oFACpBA,Q;;;;;iBACoC,I;;oFACpCA,Q;;;;;iBACmB,C;;oFACnBA,Q;;;;;iBACmB,C;;sFACnBA,Q;;;;;iBACqB,C;;qFACrBA,Q;;;;;iBACoB,C;;oFACpBA,Q;;;;;iBACmB,IAAIV,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,C", "sourcesContent": ["/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com/\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\nimport {\n    _decorator, assert, CCBoolean, CCFloat, CCInteger,\n    gfx, Material, renderer, rendering, Vec3, Vec4,\n} from 'cc';\n\nimport { EDITOR } from 'cc/env';\n\nimport {\n    BuiltinPipelineSettings,\n} from './builtin-pipeline-settings';\n\nimport {\n    BuiltinPipelinePassBuilder,\n} from './builtin-pipeline-pass';\n\nimport {\n    CameraConfigs,\n    getPingPongRenderTarget,\n    PipelineConfigs,\n    PipelineContext,\n} from './builtin-pipeline';\n\nconst { ccclass, disallowMultiple, executeInEditMode, menu, property, requireComponent, type } = _decorator;\n\nconst { Color, LoadOp, StoreOp } = gfx;\n\nexport interface DofPassConfigs {\n    enableDof: boolean;\n}\n\n@ccclass('BuiltinDepthOfFieldPass')\n@menu('Rendering/BuiltinDepthOfFieldPass')\n@requireComponent(BuiltinPipelineSettings)\n@disallowMultiple\n@executeInEditMode\nexport class BuiltinDepthOfFieldPass extends BuiltinPipelinePassBuilder\n    implements rendering.PipelinePassBuilder {\n    @property({\n        group: { id: 'BuiltinPass', name: 'Pass Settings', style: 'section' },\n        type: CCInteger,\n    })\n    configOrder = 0;\n    @property({\n        group: { id: 'BuiltinPass', name: 'Pass Settings', style: 'section' },\n        type: CCInteger,\n    })\n    renderOrder = 150;\n\n    @property\n    private _enableDof = false;\n    @property\n    private _material: Material | null = null;\n    @property\n    private _minRange = 0;\n    @property\n    private _maxRange = 2;\n    @property\n    private _blurRadius = 1;\n    @property\n    private _intensity = 1;\n    @property\n    private _focusPos = new Vec3(0, 0, 0);\n\n    // DepthOfField\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n        visible: true,\n    })\n    set dofEnable(value: boolean) {\n        this._enableDof = value;\n        if (EDITOR) {\n            this._parent._tryEnableEditorPreview();\n        }\n    }\n    get dofEnable(): boolean {\n        return this._enableDof;\n    }\n\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: Material,\n        visible: true,\n    })\n    set dofMaterial(value: Material) {\n        if (this._material === value) {\n            return;\n        }\n        this._material = value;\n        if (EDITOR) {\n            this._parent._tryEnableEditorPreview();\n        }\n    }\n    get dofMaterial(): Material {\n        return this._material!;\n    }\n\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        min: 0,\n        visible: true,\n    })\n    set dofMinRange(value: number) {\n        this._minRange = value;\n    }\n    get dofMinRange(): number {\n        return this._minRange;\n    }\n\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        min: 0,\n        visible: true,\n    })\n    set dofMaxRange(value: number) {\n        this._maxRange = value;\n    }\n    get dofMaxRange(): number {\n        return this._maxRange;\n    }\n\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        range: [0.0, 2, 0.01],\n        slide: true,\n        visible: true,\n    })\n    set dofIntensity(value: number) {\n        this._intensity = value;\n    }\n    get dofIntensity(): number {\n        return this._intensity;\n    }\n\n    @type(CCFloat)\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        range: [0.01, 10, 0.01],\n        slide: true,\n        visible: true,\n    })\n    set dofBlurRadius(value: number) {\n        this._blurRadius = value;\n    }\n    get dofBlurRadius(): number {\n        return this._blurRadius;\n    }\n\n    @type(Vec3)\n    @property({\n        group: { id: 'DepthOfField', name: 'DepthOfField (PostProcessing)', style: 'section' },\n        type: Vec3,\n        visible: true,\n    })\n    set dofFocusPos(value: Vec3) {\n        this._focusPos = value;\n    }\n    get dofFocusPos(): Vec3 {\n        return this._focusPos;\n    }\n\n    // PipelinePassBuilder\n    getConfigOrder(): number {\n        return this.configOrder;\n    }\n    getRenderOrder(): number {\n        return this.renderOrder;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & DofPassConfigs): void {\n        cameraConfigs.enableDof = pplConfigs.supportDepthSample\n            && this._enableDof\n            && !!this._material;\n\n        if (cameraConfigs.enableDof) {\n            // Output scene depth, this is allowed but has performance impact\n            cameraConfigs.enableStoreSceneDepth = true;\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & DofPassConfigs>,\n        window: renderer.RenderWindow): void {\n        const id = window.renderWindowId;\n        if (cameraConfigs.enableDof) {\n            ppl.addRenderTarget(`DofRadiance${id}`,\n                cameraConfigs.radianceFormat,\n                cameraConfigs.width,\n                cameraConfigs.height);\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & Readonly<DofPassConfigs>,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder): rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableDof) {\n            return prevRenderPass;\n        }\n        --cameraConfigs.remainingPasses;\n\n        assert(!!this._material);\n        if (cameraConfigs.remainingPasses === 0) {\n            return this._addDepthOfFieldPasses(ppl, pplConfigs,\n                cameraConfigs, this._material,\n                camera, cameraConfigs.width, cameraConfigs.height,\n                context.colorName,\n                context.depthStencilName,\n                cameraConfigs.colorName);\n        } else {\n            const prefix = cameraConfigs.enableShadingScale\n                ? `ScaledRadiance`\n                : `Radiance`;\n            const outputRadianceName = getPingPongRenderTarget(\n                context.colorName, prefix, cameraConfigs.renderWindowId);\n            const inputRadianceName = context.colorName;\n            context.colorName = outputRadianceName;\n            return this._addDepthOfFieldPasses(ppl, pplConfigs,\n                cameraConfigs, this._material,\n                camera, cameraConfigs.width, cameraConfigs.height,\n                inputRadianceName,\n                context.depthStencilName,\n                outputRadianceName);\n        }\n    }\n    private _addDepthOfFieldPasses(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & Readonly<DofPassConfigs>,\n        dofMaterial: Material,\n        camera: renderer.scene.Camera,\n        width: number,\n        height: number,\n        inputRadiance: string,\n        inputDepthStencil: string,\n        outputRadianceName: string,\n    ): rendering.BasicRenderPassBuilder {\n        this._cocParams.x = this._minRange;\n        this._cocParams.y = this._maxRange;\n        this._cocParams.z = this._blurRadius;\n        this._cocParams.w = this._intensity;\n        this._focusPosVec4.x = this._focusPos.x;\n        this._focusPosVec4.y = this._focusPos.y;\n        this._focusPosVec4.z = this._focusPos.z;\n        this._cocTexSize.x = 1.0 / width;\n        this._cocTexSize.y = 1.0 / height;\n        this._cocTexSize.z = width;\n        this._cocTexSize.w = height;\n\n        const id = cameraConfigs.renderWindowId;\n        const tempRadiance = `DofRadiance${id}`;\n\n        // Blur Pass\n        const blurPass = ppl.addRenderPass(width, height, 'cc-dof-blur');\n        blurPass.addRenderTarget(tempRadiance, LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\n        blurPass.addTexture(inputRadiance, 'screenTex');\n        blurPass.setVec4('g_platform', pplConfigs.platform);\n        blurPass.setVec4('blurParams', this._cocParams);\n        blurPass.setVec4('mainTexTexelSize', this._cocTexSize);\n        blurPass\n            .addQueue(rendering.QueueHint.OPAQUE)\n            .addCameraQuad(camera, dofMaterial, 0); // addCameraQuad will set camera related UBOs\n        // coc pass\n        const cocPass = ppl.addRenderPass(width, height, 'cc-dof-coc');\n        cocPass.addRenderTarget(outputRadianceName, LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\n        cocPass.addTexture(tempRadiance, 'colorTex');\n        cocPass.addTexture(inputDepthStencil, \"DepthTex\");\n        cocPass.addTexture(inputRadiance, \"screenTex\");\n        cocPass.setVec4('g_platform', pplConfigs.platform);\n        cocPass.setMat4('proj', camera.matProj);\n        cocPass.setMat4('invProj', camera.matProjInv);\n        cocPass.setMat4('viewMatInv', camera.node.worldMatrix);\n        cocPass.setVec4('cocParams', this._cocParams);\n        cocPass.setVec4('focus', this._focusPosVec4);\n        cocPass\n            .addQueue(rendering.QueueHint.OPAQUE)\n            .addCameraQuad(camera, dofMaterial, 1);\n\n        return cocPass;\n    }\n\n    // Runtime members\n    private readonly _clearColorTransparentBlack = new Color(0, 0, 0, 0);\n    private readonly _cocParams = new Vec4(0, 0, 0, 0);\n    private readonly _focusPosVec4 = new Vec4(0, 0, 0, 1);\n    private readonly _cocTexSize = new Vec4(0, 0, 0, 0);\n}\n"]}