{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Node", "Label", "SceneTransition", "SoundManager", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "MainMenuController", "start", "startGameBtn", "node", "on", "EventType", "CLICK", "onStartGame", "settingBtn", "displaySettingPanel", "closesettingBtn", "hideSettingPanel", "helpButton", "displayHelpPanel", "closehelpBtn", "hideHelpPanel", "audioBtn", "onAudioClick", "resetProgressBtn", "onResetProgress", "confirmResetBtn", "onConfirmReset", "closeResetPanelBtn", "closeResetPanel", "updateAudioButtonLabel", "settingPanel", "active", "helpPanel", "instance", "toggleAudio", "audioLabel", "string", "isMuted", "playSoundEffect", "loadScene", "showResetConfirmPanel", "resetProgressConfirmPanel", "resetPlayerData", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AACrCC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;oCAGjBU,kB,WADZF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACN,IAAD,C,UAGRM,QAAQ,CAACL,KAAD,C,UAIRK,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,WAGRO,QAAQ,CAACN,IAAD,C,WAGRM,QAAQ,CAACP,MAAD,C,WAGRO,QAAQ,CAACN,IAAD,C,WAGRM,QAAQ,CAACP,MAAD,C,WAGRO,QAAQ,CAACP,MAAD,C,2BAvCb,MACaQ,kBADb,SACwCT,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAEhB;AAFgB;;AAAA;;AAAA;;AAAA;;AAclB;AAdkB;;AAiBnB;AAjBmB;;AAAA;;AAAA;;AA2BrB;AA3BqB;;AA8BZ;AA9BY;;AAiCL;AAjCK;;AAoCb;AApCa;AAAA;;AAuCV;AAIpCU,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBC,IAAlB,CAAuBC,EAAvB,CAA0BZ,MAAM,CAACa,SAAP,CAAiBC,KAA3C,EAAkD,KAAKC,WAAvD,EAAoE,IAApE;AACH;;AACD,cAAG,KAAKC,UAAR,EAAmB;AACf,iBAAKA,UAAL,CAAgBL,IAAhB,CAAqBC,EAArB,CAAwBZ,MAAM,CAACa,SAAP,CAAiBC,KAAzC,EAAgD,KAAKG,mBAArD,EAA0E,IAA1E;AACH;;AACD,cAAG,KAAKC,eAAR,EAAwB;AACpB,iBAAKA,eAAL,CAAqBP,IAArB,CAA0BC,EAA1B,CAA6BZ,MAAM,CAACa,SAAP,CAAiBC,KAA9C,EAAqD,KAAKK,gBAA1D,EAA4E,IAA5E;AACH;;AAED,cAAG,KAAKC,UAAR,EAAmB;AACf,iBAAKA,UAAL,CAAgBT,IAAhB,CAAqBC,EAArB,CAAwBZ,MAAM,CAACa,SAAP,CAAiBC,KAAzC,EAAgD,KAAKO,gBAArD,EAAuE,IAAvE;AACH;;AACD,cAAG,KAAKC,YAAR,EAAqB;AACjB,iBAAKA,YAAL,CAAkBX,IAAlB,CAAuBC,EAAvB,CAA0BZ,MAAM,CAACa,SAAP,CAAiBC,KAA3C,EAAkD,KAAKS,aAAvD,EAAsE,IAAtE;AACH;;AAED,cAAG,KAAKC,QAAR,EAAiB;AACb,iBAAKA,QAAL,CAAcb,IAAd,CAAmBC,EAAnB,CAAsBZ,MAAM,CAACa,SAAP,CAAiBC,KAAvC,EAA8C,KAAKW,YAAnD,EAAiE,IAAjE;AACH,WApBG,CAsBJ;;;AACA,cAAI,KAAKC,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBf,IAAtB,CAA2BC,EAA3B,CAA8BZ,MAAM,CAACa,SAAP,CAAiBC,KAA/C,EAAsD,KAAKa,eAA3D,EAA4E,IAA5E;AACH,WAzBG,CA2BJ;;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBjB,IAArB,CAA0BC,EAA1B,CAA6BZ,MAAM,CAACa,SAAP,CAAiBC,KAA9C,EAAqD,KAAKe,cAA1D,EAA0E,IAA1E;AACH;;AAED,cAAI,KAAKC,kBAAT,EAA6B;AACzB,iBAAKA,kBAAL,CAAwBnB,IAAxB,CAA6BC,EAA7B,CAAgCZ,MAAM,CAACa,SAAP,CAAiBC,KAAjD,EAAwD,KAAKiB,eAA7D,EAA8E,IAA9E;AACH;;AAED,eAAKC,sBAAL;AACH;;AAEDf,QAAAA,mBAAmB,GAAG;AAClB,eAAKgB,YAAL,CAAkBC,MAAlB,GAA2B,IAA3B;AACH;;AAEDf,QAAAA,gBAAgB,GAAG;AACf,eAAKc,YAAL,CAAkBC,MAAlB,GAA2B,KAA3B;AACH;;AAEDb,QAAAA,gBAAgB,GAAG;AACf,eAAKc,SAAL,CAAeD,MAAf,GAAwB,IAAxB;AACH;;AAEDX,QAAAA,aAAa,GAAG;AACZ,eAAKY,SAAL,CAAeD,MAAf,GAAwB,KAAxB;AACH;;AAGDT,QAAAA,YAAY,GAAG;AACZ;AAAA;AAAA,4CAAaW,QAAb,CAAsBC,WAAtB;AACA,eAAKL,sBAAL;AACF;;AAEDA,QAAAA,sBAAsB,GAAG;AACrB,cAAI,KAAKM,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,MAAhB,GAAyB;AAAA;AAAA,8CAAaH,QAAb,CAAsBI,OAAtB,KAAkC,mBAAlC,GAAwD,iBAAjF;AACH;AACJ;;AAGDzB,QAAAA,WAAW,GAAG;AACV;AAAA;AAAA,4CAAaqB,QAAb,CAAsBK,eAAtB,CAAsC,aAAtC;AACA;AAAA;AAAA,kDAAgBC,SAAhB,CAA0B,aAA1B,EAFU,CAGV;AACH;AAED;AACJ;AACA;AACA;;;AACIf,QAAAA,eAAe,GAAG;AACd;AACA;AAAA;AAAA,4CAAaS,QAAb,CAAsBK,eAAtB,CAAsC,aAAtC,EAFc,CAId;;AACA,eAAKE,qBAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,qBAAqB,GAAG;AACpB,cAAI,KAAKC,yBAAT,EAAoC;AAChC,iBAAKA,yBAAL,CAA+BV,MAA/B,GAAwC,IAAxC;AACH;AACJ;AAED;AACJ;AACA;;;AACIH,QAAAA,eAAe,GAAG;AACd,cAAI,KAAKa,yBAAT,EAAoC;AAChC,iBAAKA,yBAAL,CAA+BV,MAA/B,GAAwC,KAAxC;AACH;AACJ;AAED;AACJ;AACA;;;AACIL,QAAAA,cAAc,GAAG;AACb;AACA;AAAA;AAAA,4CAAaO,QAAb,CAAsBK,eAAtB,CAAsC,aAAtC,EAFa,CAIb;;AACA,eAAKV,eAAL,GALa,CAOb;;AACA,cAAI;AAAA;AAAA,8CAAcK,QAAlB,EAA4B;AACxB;AAAA;AAAA,gDAAcA,QAAd,CAAuBS,eAAvB;AACAC,YAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAFwB,CAIxB;AACA;AACH;AACJ;;AArK6C,O;;;;;iBAEvB,I;;;;;;;iBAGF,I;;;;;;;iBAGK,I;;;;;;;iBAGP,I;;;;;;;iBAGE,I;;;;;;;iBAGD,I;;;;;;;iBAIC,I;;;;;;;iBAGE,I;;;;;;;iBAGL,I;;;;;;;iBAGS,I;;;;;;;iBAGO,I;;;;;;;iBAGR,I;;;;;;;iBAGG,I", "sourcesContent": ["import { _decorator, Component, Button, Node, Label } from 'cc';\nimport { SceneTransition } from './SceneTransition';\nimport { SoundManager } from './SoundManager';\nimport { PlayerManager } from './PlayerManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainMenuController')\nexport class MainMenuController extends Component {\n    @property(Button)\n    startGameBtn: Button = null!; // 拖拽你的\"开始游戏\"按钮到这里\n\n    @property(Button)\n    settingBtn: Button = null!; \n\n    @property(Button)\n    closesettingBtn: Button = null!; \n\n    @property(Button)\n    audioBtn: Button = null!; \n\n    @property(Node)\n    settingPanel: Node = null!; // 拖拽你的设置面板节点到这里\n\n    @property(Label)\n    audioLabel: Label = null!; // 拖拽音效按钮的Label组件到这里\n\n\n    @property(Button)\n    helpButton: Button = null!; \n\n    @property(Button)\n    closehelpBtn: Button = null!; \n\n    @property(Node)\n    helpPanel: Node = null!; // 拖拽你的设置面板节点到这里\n\n    @property(Button)\n    resetProgressBtn: Button = null!; // 重置进度按钮\n\n    @property(Node)\n    resetProgressConfirmPanel: Node = null!; // 重置进度确认面板\n\n    @property(Button)\n    confirmResetBtn: Button = null!; // 确认重置按钮\n\n    @property(Button)\n    closeResetPanelBtn: Button = null!; // 关闭重置面板按钮\n\n\n\n    start() {\n        if (this.startGameBtn) {\n            this.startGameBtn.node.on(Button.EventType.CLICK, this.onStartGame, this);\n        }\n        if(this.settingBtn){\n            this.settingBtn.node.on(Button.EventType.CLICK, this.displaySettingPanel, this);\n        }\n        if(this.closesettingBtn){\n            this.closesettingBtn.node.on(Button.EventType.CLICK, this.hideSettingPanel, this);\n        }\n\n        if(this.helpButton){\n            this.helpButton.node.on(Button.EventType.CLICK, this.displayHelpPanel, this);\n        }\n        if(this.closehelpBtn){\n            this.closehelpBtn.node.on(Button.EventType.CLICK, this.hideHelpPanel, this);\n        }\n\n        if(this.audioBtn){\n            this.audioBtn.node.on(Button.EventType.CLICK, this.onAudioClick, this);\n        }\n        \n        // 添加重置进度按钮事件监听\n        if (this.resetProgressBtn) {\n            this.resetProgressBtn.node.on(Button.EventType.CLICK, this.onResetProgress, this);\n        }\n\n        // 添加重置确认面板按钮事件监听\n        if (this.confirmResetBtn) {\n            this.confirmResetBtn.node.on(Button.EventType.CLICK, this.onConfirmReset, this);\n        }\n\n        if (this.closeResetPanelBtn) {\n            this.closeResetPanelBtn.node.on(Button.EventType.CLICK, this.closeResetPanel, this);\n        }\n        \n        this.updateAudioButtonLabel();\n    }\n\n    displaySettingPanel() {\n        this.settingPanel.active = true;\n    }\n\n    hideSettingPanel() {\n        this.settingPanel.active = false;\n    }\n\n    displayHelpPanel() {\n        this.helpPanel.active = true;\n    }\n\n    hideHelpPanel() {\n        this.helpPanel.active = false;\n    }\n\n\n    onAudioClick() {\n       SoundManager.instance.toggleAudio();\n       this.updateAudioButtonLabel();\n    }\n\n    updateAudioButtonLabel() {\n        if (this.audioLabel) {\n            this.audioLabel.string = SoundManager.instance.isMuted() ? \"音效:关 \\n sound:off\" : \"音效:开\\n sound:on\";\n        }\n    }\n\n\n    onStartGame() {\n        SoundManager.instance.playSoundEffect('buttonClick');\n        SceneTransition.loadScene(\"LevelSelect\");\n        // director.loadScene(\"gamescene\");\n    }\n\n    /**\n     * 重置玩家进度\n     * 将玩家的金钱、车辆解锁状态、关卡解锁状态等重置为初始状态\n     */\n    onResetProgress() {\n        // 播放按钮点击音效\n        SoundManager.instance.playSoundEffect('buttonClick');\n        \n        // 显示确认面板\n        this.showResetConfirmPanel();\n    }\n\n    /**\n     * 显示重置确认面板\n     */\n    showResetConfirmPanel() {\n        if (this.resetProgressConfirmPanel) {\n            this.resetProgressConfirmPanel.active = true;\n        }\n    }\n\n    /**\n     * 关闭重置确认面板\n     */\n    closeResetPanel() {\n        if (this.resetProgressConfirmPanel) {\n            this.resetProgressConfirmPanel.active = false;\n        }\n    }\n\n    /**\n     * 确认重置玩家进度\n     */\n    onConfirmReset() {\n        // 播放按钮点击音效\n        SoundManager.instance.playSoundEffect('buttonClick');\n        \n        // 关闭确认面板\n        this.closeResetPanel();\n        \n        // 执行重置操作\n        if (PlayerManager.instance) {\n            PlayerManager.instance.resetPlayerData();\n            console.log(\"玩家进度已重置\");\n            \n            // 如果有UI提示组件，可以在这里显示重置成功的提示\n            // 例如：this.showToast(\"玩家进度已重置\");\n        }\n    }\n}"]}