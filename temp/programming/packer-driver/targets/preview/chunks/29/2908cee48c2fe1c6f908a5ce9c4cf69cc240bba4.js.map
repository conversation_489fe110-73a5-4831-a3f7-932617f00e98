{"version": 3, "sources": ["data:text/javascript,%0A%2F%2F%20This%20module%20is%20auto-generated%20to%20report%20error%20emitted%20when%20try%20to%20load%20module%20file%3A%2F%2F%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIController.ts%20at%20runtime.%0Athrow%20new%20Error(%60Error%3A%20%E5%9C%A8%E5%8A%A0%E8%BD%BD%E6%A8%A1%E5%9D%97%E6%96%87%E4%BB%B6%20%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIController.ts%20%E6%97%B6%E5%8F%91%E7%94%9F%E9%94%99%E8%AF%AF%EF%BC%9AError%3A%20ENOENT%3A%20no%20such%20file%20or%20directory%2C%20open%20'%2FUsers%2Fzeruili%2Fprojects%2Fcocos_project%2FdriftClash%2Fassets%2Fscripts%2FAIController.ts'%60)%3B%0A%20%20%20%20%20%20%20%20"], "names": ["Error"], "mappings": ";;;;;;AACA;AACA,YAAM,IAAIA,KAAJ,0SAAN", "sourcesContent": ["\n// This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts at runtime.\nthrow new Error(`Error: 在加载模块文件 /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts 时发生错误：Error: ENOENT: no such file or directory, open '/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts'`);\n        "]}