System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts at runtime.
      throw new Error("Error: \u5728\u52A0\u8F7D\u6A21\u5757\u6587\u4EF6 /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts \u65F6\u53D1\u751F\u9519\u8BEF\uFF1AError: ENOENT: no such file or directory, open '/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts'");
    }
  };
});
//# sourceMappingURL=2908cee48c2fe1c6f908a5ce9c4cf69cc240bba4.js.map