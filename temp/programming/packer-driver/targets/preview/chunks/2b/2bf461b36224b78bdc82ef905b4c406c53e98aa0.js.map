{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Label", "Sprite", "Color", "GameManager", "ccclass", "property", "GameOverPanel", "type", "tooltip", "start", "bindButtonEvents", "restartButton", "node", "on", "EventType", "CLICK", "onRestartClick", "LevelSelectButton", "onLevelSelectClick", "updatePaintRatios", "gameManager", "getInstance", "allRatios", "getAllVehiclePaintRatios", "player<PERSON><PERSON><PERSON>", "playerPercentage", "Math", "round", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "string", "sortedRatios", "getSortedVehiclePaintRatios", "aiRatios", "filter", "item", "vehicleId", "aiLabels", "ai1RatioLabel", "ai2RatioLabel", "ai3RatioLabel", "ai4RatioLabel", "for<PERSON>ach", "ratioData", "index", "length", "percentage", "ratio", "displayName", "getAIDisplayName", "i", "startsWith", "parts", "split", "updateStarSprites", "stars", "starSprites", "star1Sprite", "star2Sprite", "star3Sprite", "sprite", "color", "console", "log", "restartGame", "returnToLevelSelect", "setGameOverInfo", "isVictory", "performance", "reward", "gameTime", "healthPercentage", "titleLabel", "performance<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "gameTimeLabel", "toFixed", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAC9CC,MAAAA,W,iBAAAA,W;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;+BAGjBS,a,WADZF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACL,KAAD,C,UAGRK,QAAQ,CAACL,KAAD,C,UAGRK,QAAQ,CAACL,KAAD,C,UAGRK,QAAQ,CAACL,KAAD,C,UAKRK,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,MADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,MADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,MADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAORH,QAAQ,CAACL,KAAD,C,WAIRK,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,KADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,KADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,KADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,KADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAACN,MAAD,C,WAGRM,QAAQ,CAACN,MAAD,C,2BAlEb,MACaO,aADb,SACmCR,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAEd;AAFc;;AAKR;AALQ;;AAQb;AARa;;AAWX;AAG9B;AAdyC;;AAAA;;AAAA;;AAiCzC;AAjCyC;;AAqCzC;AArCyC;;AAAA;;AAAA;;AAAA;;AAAA;;AA+DV;AA/DU;AAAA;;AAkEN;AAEnCW,QAAAA,KAAK,GAAG,CAEJ;AACA;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,gBAAgB,GAAG;AACtB,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,IAAnB,CAAwBC,EAAxB,CAA2Bd,MAAM,CAACe,SAAP,CAAiBC,KAA5C,EAAmD,KAAKC,cAAxD,EAAwE,IAAxE;AACH;;AAED,cAAI,KAAKC,iBAAT,EAA4B;AACxB,iBAAKA,iBAAL,CAAuBL,IAAvB,CAA4BC,EAA5B,CAA+Bd,MAAM,CAACe,SAAP,CAAiBC,KAAhD,EAAuD,KAAKG,kBAA5D,EAAgF,IAAhF;AACH;AACJ;AAID;AACJ;AACA;;;AACYC,QAAAA,iBAAiB,GAAS;AAC9B,cAAMC,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;AACA,cAAI,CAACD,WAAL,EAAkB,OAFY,CAI9B;;AACA,cAAME,SAAS,GAAGF,WAAW,CAACG,wBAAZ,EAAlB,CAL8B,CAO9B;;AACA,cAAMC,WAAW,GAAGF,SAAS,CAAC,QAAD,CAAT,IAAuB,CAA3C;AACA,cAAMG,gBAAgB,GAAGC,IAAI,CAACC,KAAL,CAAWH,WAAW,GAAG,GAAzB,CAAzB;;AACA,cAAI,KAAKI,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBC,MAAtB,gBAA0CJ,gBAA1C;AACH,WAZ6B,CAc9B;;;AACA,cAAMK,YAAY,GAAGV,WAAW,CAACW,2BAAZ,EAArB;AACA,cAAMC,QAAQ,GAAGF,YAAY,CAACG,MAAb,CAAoBC,IAAI,IAAIA,IAAI,CAACC,SAAL,KAAmB,QAA/C,CAAjB,CAhB8B,CAkB9B;;AACA,cAAMC,QAAQ,GAAG,CAAC,KAAKC,aAAN,EAAqB,KAAKC,aAA1B,EAAyC,KAAKC,aAA9C,EAA6D,KAAKC,aAAlE,CAAjB,CAnB8B,CAqB9B;;AACAR,UAAAA,QAAQ,CAACS,OAAT,CAAiB,CAACC,SAAD,EAAYC,KAAZ,KAAsB;AACnC,gBAAIA,KAAK,GAAGP,QAAQ,CAACQ,MAAjB,IAA2BR,QAAQ,CAACO,KAAD,CAAvC,EAAgD;AAC5C,kBAAME,UAAU,GAAGnB,IAAI,CAACC,KAAL,CAAWe,SAAS,CAACI,KAAV,GAAkB,GAA7B,CAAnB;AACA,kBAAMC,WAAW,GAAG,KAAKC,gBAAL,CAAsBN,SAAS,CAACP,SAAhC,CAApB;AACAC,cAAAA,QAAQ,CAACO,KAAD,CAAR,CAAgBd,MAAhB,GAA4BkB,WAA5B,UAA4CF,UAA5C;AACH;AACJ,WAND,EAtB8B,CA8B9B;;AACA,eAAK,IAAII,CAAC,GAAGjB,QAAQ,CAACY,MAAtB,EAA8BK,CAAC,GAAGb,QAAQ,CAACQ,MAA3C,EAAmDK,CAAC,EAApD,EAAwD;AACpD,gBAAIb,QAAQ,CAACa,CAAD,CAAZ,EAAiB;AACbb,cAAAA,QAAQ,CAACa,CAAD,CAAR,CAAYpB,MAAZ,GAAqB,EAArB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACYmB,QAAAA,gBAAgB,CAACb,SAAD,EAA4B;AAChD;AACA,cAAIA,SAAS,CAACe,UAAV,CAAqB,KAArB,CAAJ,EAAiC;AAC7B,gBAAMC,KAAK,GAAGhB,SAAS,CAACiB,KAAV,CAAgB,GAAhB,CAAd;;AACA,gBAAID,KAAK,CAACP,MAAN,IAAgB,CAApB,EAAuB;AACnB,6BAAaO,KAAK,CAAC,CAAD,CAAlB;AACH;AACJ;;AACD,iBAAOhB,SAAP;AACH;AAED;AACJ;AACA;AACA;;;AACYkB,QAAAA,iBAAiB,CAACC,KAAD,EAAsB;AAC3C,cAAMC,WAAW,GAAG,CAAC,KAAKC,WAAN,EAAmB,KAAKC,WAAxB,EAAqC,KAAKC,WAA1C,CAApB;AAEAH,UAAAA,WAAW,CAACd,OAAZ,CAAoB,CAACkB,MAAD,EAAShB,KAAT,KAAmB;AACnC,gBAAIgB,MAAJ,EAAY;AACR;AACA,kBAAIhB,KAAK,GAAGW,KAAZ,EAAmB;AACf;AACAK,gBAAAA,MAAM,CAACC,KAAP,GAAe,IAAI1D,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAf;AACH,eAHD,MAGO;AACH;AACAyD,gBAAAA,MAAM,CAACC,KAAP,GAAe,IAAI1D,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAf;AACH,eARO,CAUR;AACA;;AACH;AACJ,WAdD;AAgBA2D,UAAAA,OAAO,CAACC,GAAR,4CAAuBR,KAAvB;AACH;AAED;AACJ;AACA;;;AACYtC,QAAAA,cAAc,GAAG;AACrB,cAAMI,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAAC2C,WAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACY7C,QAAAA,kBAAkB,GAAG;AACzB,cAAME,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAAC4C,mBAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,eAAe,CAClBC,SADkB,EAElBC,WAFkB,EAGlBC,MAHkB,EAIlBC,QAJkB,EAKlBC,gBALkB,EAMlBhB,KANkB,EAOpB;AACE;AACA,cAAI,KAAKiB,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB1C,MAAhB,GAAyBqC,SAAS,GAAG,QAAH,GAAc,OAAhD;AACH,WAJH,CAME;;;AACA,cAAI,KAAKM,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsB3C,MAAtB,qBAA+CsC,WAA/C;AACH,WATH,CAWE;;;AACA,cAAI,KAAKM,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB5C,MAAjB,gBAAqCuC,MAArC;AACH,WAdH,CAgBE;;;AACA,cAAI,KAAKM,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB7C,MAAnB,cAAqCwC,QAAQ,CAACM,OAAT,CAAiB,CAAjB,CAArC;AACH,WAnBH,CAqBE;;;AACA,eAAKtB,iBAAL,CAAuBC,KAAvB,EAtBF,CAwBE;;AACA,eAAKnC,iBAAL;AACH;;AAEDyD,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKjE,aAAL,IAAsB,KAAKA,aAAL,CAAmBC,IAA7C,EAAmD;AAC/C,iBAAKD,aAAL,CAAmBC,IAAnB,CAAwBiE,GAAxB,CAA4B9E,MAAM,CAACe,SAAP,CAAiBC,KAA7C,EAAoD,KAAKC,cAAzD,EAAyE,IAAzE;AACH;;AACD,cAAI,KAAKC,iBAAL,IAA0B,KAAKA,iBAAL,CAAuBL,IAArD,EAA2D;AACvD,iBAAKK,iBAAL,CAAuBL,IAAvB,CAA4BiE,GAA5B,CAAgC9E,MAAM,CAACe,SAAP,CAAiBC,KAAjD,EAAwD,KAAKG,kBAA7D,EAAiF,IAAjF;AACH;AACJ;;AA9OwC,O;;;;;iBAErB,I;;;;;;;iBAGM,I;;;;;;;iBAGL,I;;;;;;;iBAGE,I;;;;;;;iBAQD,I;;;;;;;iBAMA,I;;;;;;;iBAMA,I;;;;;;;iBAII,I;;;;;;;iBAOH,I;;;;;;;iBAMA,I;;;;;;;iBAMA,I;;;;;;;iBAMA,I;;;;;;;iBAGC,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Component, Button, Label, Sprite, Color } from 'cc';\nimport { GameManager } from './GameManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('GameOverPanel')\nexport class GameOverPanel extends Component {\n    @property(Label)\n    titleLabel: Label = null!; // 游戏结束标题\n\n    @property(Label)\n    performanceLabel: Label = null!; // 表现评价标签\n\n    @property(Label)\n    rewardLabel: Label = null!; // 奖励金币标签\n\n    @property(Label)\n    gameTimeLabel: Label = null!; // 游戏时长标签\n\n\n    // 星星精灵节点（手动拖拽设置）\n    @property({\n        type: Sprite,\n        tooltip: '第1颗星星精灵'\n    })\n    star1Sprite: Sprite = null!;\n\n    @property({\n        type: Sprite,\n        tooltip: '第2颗星星精灵'\n    })\n    star2Sprite: Sprite = null!;\n\n    @property({\n        type: Sprite,\n        tooltip: '第3颗星星精灵'\n    })\n    star3Sprite: Sprite = null!;\n\n    // 玩家颜料占比显示\n    @property(Label)\n    playerRatioLabel: Label = null!;\n\n    // AI颜料占比显示标签（手动拖拽设置）\n    @property({\n        type: Label,\n        tooltip: 'AI车辆1的颜料占比显示标签'\n    })\n    ai1RatioLabel: Label = null!;\n\n    @property({\n        type: Label,\n        tooltip: 'AI车辆2的颜料占比显示标签'\n    })\n    ai2RatioLabel: Label = null!;\n\n    @property({\n        type: Label,\n        tooltip: 'AI车辆3的颜料占比显示标签'\n    })\n    ai3RatioLabel: Label = null!;\n\n    @property({\n        type: Label,\n        tooltip: 'AI车辆4的颜料占比显示标签'\n    })\n    ai4RatioLabel: Label = null!;\n\n    @property(Button)\n    restartButton: Button = null!; // 重新开始按钮\n\n    @property(Button)\n    LevelSelectButton: Button = null!; // 返回主菜单按钮\n\n    start() {\n\n        // this.bindButtonEvents();\n        // 注意：不在start中更新数据，而是等待GameManager调用setGameOverInfo\n    }\n\n    /**\n     * 绑定按钮事件\n     */\n    public bindButtonEvents() {\n        if (this.restartButton) {\n            this.restartButton.node.on(Button.EventType.CLICK, this.onRestartClick, this);\n        }\n\n        if (this.LevelSelectButton) {\n            this.LevelSelectButton.node.on(Button.EventType.CLICK, this.onLevelSelectClick, this);\n        }\n    }\n\n\n\n    /**\n     * 更新颜料占比显示\n     */\n    private updatePaintRatios(): void {\n        const gameManager = GameManager.getInstance();\n        if (!gameManager) return;\n\n        // 获取所有车辆的颜料占比\n        const allRatios = gameManager.getAllVehiclePaintRatios();\n\n        // 更新玩家占比\n        const playerRatio = allRatios['player'] || 0;\n        const playerPercentage = Math.round(playerRatio * 100);\n        if (this.playerRatioLabel) {\n            this.playerRatioLabel.string = `player: ${playerPercentage}%`;\n        }\n\n        // 获取排序后的AI占比数据\n        const sortedRatios = gameManager.getSortedVehiclePaintRatios();\n        const aiRatios = sortedRatios.filter(item => item.vehicleId !== 'player');\n\n        // 获取AI标签数组\n        const aiLabels = [this.ai1RatioLabel, this.ai2RatioLabel, this.ai3RatioLabel, this.ai4RatioLabel];\n\n        // 更新每个AI的显示\n        aiRatios.forEach((ratioData, index) => {\n            if (index < aiLabels.length && aiLabels[index]) {\n                const percentage = Math.round(ratioData.ratio * 100);\n                const displayName = this.getAIDisplayName(ratioData.vehicleId);\n                aiLabels[index].string = `${displayName}: ${percentage}%`;\n            }\n        });\n\n        // 清空未使用的标签\n        for (let i = aiRatios.length; i < aiLabels.length; i++) {\n            if (aiLabels[i]) {\n                aiLabels[i].string = '';\n            }\n        }\n    }\n\n    /**\n     * 获取AI的显示名称\n     * @param vehicleId AI车辆ID\n     * @returns 显示名称\n     */\n    private getAIDisplayName(vehicleId: string): string {\n        // 从vehicleId中提取简化的显示名称\n        if (vehicleId.startsWith('ai_')) {\n            const parts = vehicleId.split('_');\n            if (parts.length >= 2) {\n                return `AI-${parts[1]}`;\n            }\n        }\n        return vehicleId;\n    }\n\n    /**\n     * 更新星星精灵显示\n     * @param stars 获得的星星数量\n     */\n    private updateStarSprites(stars: number): void {\n        const starSprites = [this.star1Sprite, this.star2Sprite, this.star3Sprite];\n\n        starSprites.forEach((sprite, index) => {\n            if (sprite) {\n                // 根据获得的星星数量设置精灵的透明度\n                if (index < stars) {\n                    // 亮起的星星：完全不透明\n                    sprite.color = new Color(255, 255, 255, 255);\n                } else {\n                    // 暗淡的星星：半透明\n                    sprite.color = new Color(255, 255, 255, 100);\n                }\n\n                // 可以选择完全隐藏未获得的星星\n                // sprite.node.active = index < stars;\n            }\n        });\n\n        console.log(`更新星星显示: ${stars}/3 颗星星亮起`);\n    }\n\n    /**\n     * 重新开始按钮点击\n     */\n    private onRestartClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.restartGame();\n        }\n    }\n\n    /**\n     * 返回主菜单按钮点击\n     */\n    private onLevelSelectClick() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            gameManager.returnToLevelSelect();\n        }\n    }\n\n    /**\n     * 设置游戏结束信息\n     */\n    public setGameOverInfo(\n        isVictory: boolean,\n        performance: string,\n        reward: number,\n        gameTime: number,\n        healthPercentage: number,\n        stars: number\n    ) {\n        // 更新标题\n        if (this.titleLabel) {\n            this.titleLabel.string = isVictory ? 'winner' : 'loser';\n        }\n\n        // 更新表现评价\n        if (this.performanceLabel) {\n            this.performanceLabel.string = `performance: ${performance}`;\n        }\n\n        // 更新奖励金币\n        if (this.rewardLabel) {\n            this.rewardLabel.string = `reward: ${reward}`;\n        }\n\n        // 更新游戏时长\n        if (this.gameTimeLabel) {\n            this.gameTimeLabel.string = `time: ${gameTime.toFixed(1)}S`;\n        }\n\n        // 更新星星精灵显示\n        this.updateStarSprites(stars);\n\n        // 更新颜料占比显示\n        this.updatePaintRatios();\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.restartButton && this.restartButton.node) {\n            this.restartButton.node.off(Button.EventType.CLICK, this.onRestartClick, this);\n        }\n        if (this.LevelSelectButton && this.LevelSelectButton.node) {\n            this.LevelSelectButton.node.off(Button.EventType.CLICK, this.onLevelSelectClick, this);\n        }\n    }\n}\n"]}