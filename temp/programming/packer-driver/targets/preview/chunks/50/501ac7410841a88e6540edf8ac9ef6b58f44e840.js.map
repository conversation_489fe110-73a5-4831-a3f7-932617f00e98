{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts"], "names": ["_decorator", "Component", "Node", "Vec3", "Camera", "view", "UITransform", "ccclass", "property", "CameraFollow", "_tempPos", "_mapWidth", "_mapHeight", "onLoad", "onDestroy", "target", "_detectMapSize", "node", "parent", "mapNode", "children", "find", "child", "name", "uiTransform", "getComponentInChildren", "contentSize", "width", "scale", "x", "height", "y", "console", "log", "update", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "_updateCameraPosition", "_clampToMapBounds", "visibleWidth", "visibleHeight", "canvasWorldX", "worldPosition", "canvasWorldY", "mapLeft", "mapRight", "mapBottom", "mapTop", "minX", "maxX", "minY", "maxY", "cx", "cy", "instant", "getWorldPosition", "camera", "getComponent", "deviceAspect", "getVisibleSize", "orthoHeight", "usedVisibleWidth", "usedVisibleHeight", "centerX", "centerY", "camPos", "setWorldPosition", "z", "Math", "pow", "smooth", "init", "player<PERSON>ode"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;;;;;;;;OACpD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;8BAGjBS,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAACN,IAAD,C,2BAFb,MACaO,YADb,SACkCR,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAElB;AAFkB;;AAKjB;AALiB,eAOhCS,QAPgC,GAOrB,IAAIP,IAAJ,EAPqB;AAAA,eAQhCQ,SARgC,GAQZ,CARY;AAAA,eAShCC,UATgC,GASX,CATW;AAAA;;AAWxCC,QAAAA,MAAM,GAAG;AACL;AACA,eAAKH,QAAL,GAAgB,IAAIP,IAAJ,EAAhB;AACA,eAAKQ,SAAL,GAAiB,CAAjB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACH;;AAEDE,QAAAA,SAAS,GAAG;AACR;AACA,eAAKC,MAAL,GAAc,IAAd;AACA,eAAKL,QAAL,GAAgB,IAAhB;AACH,SAtBuC,CAwBxC;AACA;AAEA;AACA;AAEA;AACA;;;AAEQM,QAAAA,cAAc,GAAG;AACrB,cAAI,CAAC,KAAKC,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUC,MAA7B,EAAqC,OADhB,CAGrB;;AACA,cAAMC,OAAO,GAAG,KAAKF,IAAL,CAAUC,MAAV,CAAiBE,QAAjB,CAA0BC,IAA1B,CAA+BC,KAAK,IAChDA,KAAK,CAACC,IAAN,KAAe,YADH,CAAhB;;AAIA,cAAIJ,OAAJ,EAAa;AACT,gBAAMK,WAAW,GAAGL,OAAO,CAACM,sBAAR,CAA+BnB,WAA/B,CAApB;;AACA,gBAAIkB,WAAJ,EAAiB;AACb,mBAAKb,SAAL,GAAiBa,WAAW,CAACE,WAAZ,CAAwBC,KAAxB,GAAgCR,OAAO,CAACS,KAAR,CAAcC,CAA/D;AACA,mBAAKjB,UAAL,GAAkBY,WAAW,CAACE,WAAZ,CAAwBI,MAAxB,GAAiCX,OAAO,CAACS,KAAR,CAAcG,CAAjE;AACH;AACJ,WAdoB,CAgBrB;;;AACA,cAAI,KAAKpB,SAAL,KAAmB,CAAnB,IAAwB,KAAKC,UAAL,KAAoB,CAAhD,EAAmD;AAC/CoB,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACA,iBAAKtB,SAAL,GAAiB,IAAjB;AACA,iBAAKC,UAAL,GAAkB,IAAlB;AACH;AACJ;;AAEDsB,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC,KAAKlB,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUmB,OAA7B,EAAsC;;AACtC,eAAKC,qBAAL,CAA2BF,SAA3B,EAAsC,KAAtC;AACH;;AAEOG,QAAAA,iBAAiB,CAACT,CAAD,EAAYE,CAAZ,EAAuBQ,YAAvB,EAA6CC,aAA7C,EAAsF;AAC3G,cAAI,CAAC,KAAKvB,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUC,MAA7B,EAAqC,OAAO,CAACW,CAAD,EAAIE,CAAJ,CAAP,CADsE,CAG3G;;AACA,cAAMU,YAAY,GAAG,KAAKxB,IAAL,CAAUC,MAAV,CAAiBwB,aAAjB,CAA+Bb,CAApD;AACA,cAAMc,YAAY,GAAG,KAAK1B,IAAL,CAAUC,MAAV,CAAiBwB,aAAjB,CAA+BX,CAApD,CAL2G,CAM3G;;AACA,cAAIa,OAAO,GAAGH,YAAY,GAAG,KAAK9B,SAAL,GAAiB,CAA9C;AACA,cAAIkC,QAAQ,GAAGJ,YAAY,GAAG,KAAK9B,SAAL,GAAiB,CAA/C;AACA,cAAImC,SAAS,GAAGH,YAAY,GAAG,KAAK/B,UAAL,GAAkB,CAAjD;AACA,cAAImC,MAAM,GAAGJ,YAAY,GAAG,KAAK/B,UAAL,GAAkB,CAA9C,CAV2G,CAY3G;AACA;;AACA,cAAIoC,IAAI,GAAGJ,OAAO,GAAGL,YAAY,GAAG,CAApC;AACA,cAAIU,IAAI,GAAGJ,QAAQ,GAAGN,YAAY,GAAG,CAArC;AACA,cAAIW,IAAI,GAAGJ,SAAS,GAAGN,aAAa,GAAG,CAAvC;AACA,cAAIW,IAAI,GAAGJ,MAAM,GAAGP,aAAa,GAAG,CAApC;AAEA,cAAIY,EAAE,GAAGvB,CAAT;AAAA,cAAYwB,EAAE,GAAGtB,CAAjB,CAnB2G,CAqB3G;;AACA,cAAIiB,IAAI,GAAGC,IAAX,EAAiB;AACbG,YAAAA,EAAE,GAAGX,YAAL;AACH,WAFD,MAEO;AACH;AACA,gBAAIZ,CAAC,GAAGmB,IAAR,EAAc;AACVI,cAAAA,EAAE,GAAGJ,IAAL;AACH,aAFD,MAEO,IAAInB,CAAC,GAAGoB,IAAR,EAAc;AACjBG,cAAAA,EAAE,GAAGH,IAAL;AACH,aAFM,MAEA;AACHG,cAAAA,EAAE,GAAGvB,CAAL;AACH;AACJ;;AAED,cAAIqB,IAAI,GAAGC,IAAX,EAAiB;AACbE,YAAAA,EAAE,GAAGV,YAAL;AACH,WAFD,MAEO;AACH;AACA,gBAAIZ,CAAC,GAAGmB,IAAR,EAAc;AACVG,cAAAA,EAAE,GAAGH,IAAL;AACH,aAFD,MAEO,IAAInB,CAAC,GAAGoB,IAAR,EAAc;AACjBE,cAAAA,EAAE,GAAGF,IAAL;AACH,aAFM,MAEA;AACHE,cAAAA,EAAE,GAAGtB,CAAL;AACH;AACJ;;AAED,iBAAO,CAACqB,EAAD,EAAKC,EAAL,CAAP;AACH;;AAEOhB,QAAAA,qBAAqB,CAACF,SAAD,EAAoBmB,OAApB,EAAsC;AAC/D,cAAI,CAAC,KAAKvC,MAAN,IAAgB,CAAC,KAAKE,IAAtB,IAA8B,CAAC,KAAKA,IAAL,CAAUmB,OAA7C,EAAsD;AACtD,eAAKrB,MAAL,CAAYwC,gBAAZ,CAA6B,KAAK7C,QAAlC;AACA,cAAM8C,MAAM,GAAG,KAAKC,YAAL,CAAkBrD,MAAlB,CAAf;AACA,cAAI,CAACoD,MAAL,EAAa,OAJkD,CAM/D;;AACA,cAAME,YAAY,GAAGrD,IAAI,CAACsD,cAAL,GAAsBhC,KAAtB,GAA8BtB,IAAI,CAACsD,cAAL,GAAsB7B,MAAzE,CAP+D,CAS/D;AACA;;AACA,cAAMU,aAAa,GAAGgB,MAAM,CAACI,WAA7B;AACA,cAAMrB,YAAY,GAAGC,aAAa,GAAGkB,YAArC,CAZ+D,CAc/D;;AACA,cAAMG,gBAAgB,GAAGtB,YAAzB;AACA,cAAMuB,iBAAiB,GAAGtB,aAA1B,CAhB+D,CAkB/D;;AACA,cAAM,CAACuB,OAAD,EAAUC,OAAV,IAAqB,KAAK1B,iBAAL,CAAuB,KAAK5B,QAAL,CAAcmB,CAArC,EAAwC,KAAKnB,QAAL,CAAcqB,CAAtD,EAAyD8B,gBAAzD,EAA2EC,iBAA3E,CAA3B;;AAEA,cAAIG,MAAM,GAAG,KAAKhD,IAAL,CAAUyB,aAAvB;;AACA,cAAIY,OAAJ,EAAa;AACT,iBAAKrC,IAAL,CAAUiD,gBAAV,CAA2BH,OAA3B,EAAoCC,OAApC,EAA6CC,MAAM,CAACE,CAApD;AACH,WAFD,MAEO;AACH,iBAAKlD,IAAL,CAAUiD,gBAAV,CACID,MAAM,CAACpC,CAAP,GAAW,CAACkC,OAAO,GAAGE,MAAM,CAACpC,CAAlB,KAAwB,IAAIuC,IAAI,CAACC,GAAL,CAAS,IAAI,KAAKC,MAAlB,EAA0BnC,SAAS,GAAG,EAAtC,CAA5B,CADf,EAEI8B,MAAM,CAAClC,CAAP,GAAW,CAACiC,OAAO,GAAGC,MAAM,CAAClC,CAAlB,KAAwB,IAAIqC,IAAI,CAACC,GAAL,CAAS,IAAI,KAAKC,MAAlB,EAA0BnC,SAAS,GAAG,EAAtC,CAA5B,CAFf,EAGI8B,MAAM,CAACE,CAHX;AAKH;AACJ;;AAEMI,QAAAA,IAAI,CAACpD,OAAD,EAAgBqD,UAAhB,EAAkC;AACzC,eAAKzD,MAAL,GAAcyD,UAAd;;AACA,cAAIrD,OAAJ,EAAa;AACT,gBAAMK,WAAW,GAAGL,OAAO,CAACsC,YAAR,CAAqBnD,WAArB,CAApB;;AACA,gBAAIkB,WAAJ,EAAiB;AACb,mBAAKb,SAAL,GAAiBa,WAAW,CAACE,WAAZ,CAAwBC,KAAxB,GAAgCR,OAAO,CAACS,KAAR,CAAcC,CAA/D;AACA,mBAAKjB,UAAL,GAAkBY,WAAW,CAACE,WAAZ,CAAwBI,MAAxB,GAAiCX,OAAO,CAACS,KAAR,CAAcG,CAAjE;AACH;AACJ;AACJ;;AA3JuC,O;;;;;iBAEzB,I;;iFAEdvB,Q;;;;;iBACgB,I", "sourcesContent": ["import { _decorator, Component, Node, Vec3, Camera, view, UITransform } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('CameraFollow')\nexport class CameraFollow extends Component {\n    @property(Node)\n    target: Node = null!; // 需要跟随的目标节点\n\n    @property\n    smooth: number = 0.15; // 跟随平滑度，0为瞬间跟随，越大越慢\n\n    private _tempPos = new Vec3();\n    private _mapWidth: number = 0;\n    private _mapHeight: number = 0;\n\n    onLoad() {\n        // 确保在组件加载时初始化\n        this._tempPos = new Vec3();\n        this._mapWidth = 0;\n        this._mapHeight = 0;\n    }\n\n    onDestroy() {\n        // 确保在组件销毁时清理引用\n        this.target = null!;\n        this._tempPos = null!;\n    }\n\n    // start() {\n    //     if (!this.node || !this.node.isValid) return;\n        \n    //     // 自动检测地图尺寸\n    //     this._detectMapSize();\n        \n    //     this._updateCameraPosition(0, true);\n    // }\n\n    private _detectMapSize() {\n        if (!this.node || !this.node.parent) return;\n        \n        // 方法1：通过 Map 节点检测\n        const mapNode = this.node.parent.children.find(child => \n            child.name === 'PlayGround'\n        );\n        \n        if (mapNode) {\n            const uiTransform = mapNode.getComponentInChildren(UITransform);\n            if (uiTransform) {\n                this._mapWidth = uiTransform.contentSize.width * mapNode.scale.x;\n                this._mapHeight = uiTransform.contentSize.height * mapNode.scale.y;\n            }\n        } \n        \n        // 如果没检测到，使用默认值\n        if (this._mapWidth === 0 || this._mapHeight === 0) {\n            console.log(\"没有检测到地图尺寸\");\n            this._mapWidth = 2160;\n            this._mapHeight = 1440;\n        }\n    }\n\n    update(deltaTime: number) {\n        if (!this.node || !this.node.isValid) return;\n        this._updateCameraPosition(deltaTime, false);\n    }\n\n    private _clampToMapBounds(x: number, y: number, visibleWidth: number, visibleHeight: number): [number, number] {\n        if (!this.node || !this.node.parent) return [x, y];\n        \n        // Canvas世界坐标\n        const canvasWorldX = this.node.parent.worldPosition.x;\n        const canvasWorldY = this.node.parent.worldPosition.y;\n        // 地图中心为canvas中心\n        let mapLeft = canvasWorldX - this._mapWidth / 2;\n        let mapRight = canvasWorldX + this._mapWidth / 2;\n        let mapBottom = canvasWorldY - this._mapHeight / 2;\n        let mapTop = canvasWorldY + this._mapHeight / 2;\n\n        // 计算摄像机中心允许的移动范围\n        // 确保摄像机可视范围不超出地图边界\n        let minX = mapLeft + visibleWidth / 2;\n        let maxX = mapRight - visibleWidth / 2;\n        let minY = mapBottom + visibleHeight / 2;\n        let maxY = mapTop - visibleHeight / 2;\n\n        let cx = x, cy = y;\n        \n        // 如果地图比可视范围小，摄像机居中\n        if (minX > maxX) {\n            cx = canvasWorldX;\n        } else {\n            // 限制摄像机位置，确保不显示地图外的黑边\n            if (x < minX) {\n                cx = minX;\n            } else if (x > maxX) {\n                cx = maxX;\n            } else {\n                cx = x;\n            }\n        }\n        \n        if (minY > maxY) {\n            cy = canvasWorldY;\n        } else {\n            // 限制摄像机位置，确保不显示地图外的黑边\n            if (y < minY) {\n                cy = minY;\n            } else if (y > maxY) {\n                cy = maxY;\n            } else {\n                cy = y;\n            }\n        }\n        \n        return [cx, cy];\n    }\n\n    private _updateCameraPosition(deltaTime: number, instant: boolean) {\n        if (!this.target || !this.node || !this.node.isValid) return;\n        this.target.getWorldPosition(this._tempPos);\n        const camera = this.getComponent(Camera);\n        if (!camera) return;\n\n        // 获取设备实际宽高比\n        const deviceAspect = view.getVisibleSize().width / view.getVisibleSize().height;\n        \n        // 正确的可视范围计算方法\n        // orthoHeight就是摄像机可视范围的完整高度\n        const visibleHeight = camera.orthoHeight;\n        const visibleWidth = visibleHeight * deviceAspect;\n\n        // 使用正确的可视范围\n        const usedVisibleWidth = visibleWidth;\n        const usedVisibleHeight = visibleHeight;\n\n        // clamp摄像机中心，保证可视范围始终在地图内\n        const [centerX, centerY] = this._clampToMapBounds(this._tempPos.x, this._tempPos.y, usedVisibleWidth, usedVisibleHeight);\n\n        let camPos = this.node.worldPosition;\n        if (instant) {\n            this.node.setWorldPosition(centerX, centerY, camPos.z);\n        } else {\n            this.node.setWorldPosition(\n                camPos.x + (centerX - camPos.x) * (1 - Math.pow(1 - this.smooth, deltaTime * 60)),\n                camPos.y + (centerY - camPos.y) * (1 - Math.pow(1 - this.smooth, deltaTime * 60)),\n                camPos.z\n            );\n        }\n    }\n\n    public init(mapNode: Node, playerNode: Node) {\n        this.target = playerNode;\n        if (mapNode) {\n            const uiTransform = mapNode.getComponent(UITransform);\n            if (uiTransform) {\n                this._mapWidth = uiTransform.contentSize.width * mapNode.scale.x;\n                this._mapHeight = uiTransform.contentSize.height * mapNode.scale.y;\n            }\n        }\n    }\n} "]}