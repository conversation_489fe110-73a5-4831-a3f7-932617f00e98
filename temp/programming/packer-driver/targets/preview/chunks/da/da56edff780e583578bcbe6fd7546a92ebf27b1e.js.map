{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"], "names": ["_decorator", "Component", "AudioSource", "director", "sys", "ccclass", "property", "SoundManager", "type", "tooltip", "allAudioSources", "instance", "_instance", "onLoad", "destroy", "addPersistRootNode", "node", "bgmAudioSource", "push", "buttonClickAudioSource", "carCollisionAudioSource", "carDestructionAudioSource", "carStartAudioSource", "carAccelerateAudioSource", "carDriftAudioSource", "loadState", "start", "scheduleOnce", "playBGM", "clip", "play", "console", "log", "warn", "error", "playSoundEffect", "soundName", "sourceToPlay", "toggleAudio", "muted", "isMuted", "newVolume", "for<PERSON>ach", "source", "volume", "saveState", "state", "localStorage", "setItem", "JSON", "stringify", "stateStr", "getItem", "parse"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA4BC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,G,OAAAA,G;;;;;;;;;OAClE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;8BAGjBO,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,sCAtCb,MACaF,YADb,SACkCN,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA2ChCS,eA3CgC,GA2CC,EA3CD;AAAA;;AA+Cd,mBAARC,QAAQ,GAAiB;AACvC,iBAAO,KAAKC,SAAZ;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAIN,YAAY,CAACK,SAAjB,EAA4B;AACxB,iBAAKE,OAAL;AACA;AACH;;AACDP,UAAAA,YAAY,CAACK,SAAb,GAAyB,IAAzB;AACAT,UAAAA,QAAQ,CAACY,kBAAT,CAA4B,KAAKC,IAAjC,EANK,CAQL;;AACA,cAAI,KAAKC,cAAT,EAAyB,KAAKP,eAAL,CAAqBQ,IAArB,CAA0B,KAAKD,cAA/B;AACzB,cAAI,KAAKE,sBAAT,EAAiC,KAAKT,eAAL,CAAqBQ,IAArB,CAA0B,KAAKC,sBAA/B;AACjC,cAAI,KAAKC,uBAAT,EAAkC,KAAKV,eAAL,CAAqBQ,IAArB,CAA0B,KAAKE,uBAA/B;AAClC,cAAI,KAAKC,yBAAT,EAAoC,KAAKX,eAAL,CAAqBQ,IAArB,CAA0B,KAAKG,yBAA/B;AACpC,cAAI,KAAKC,mBAAT,EAA8B,KAAKZ,eAAL,CAAqBQ,IAArB,CAA0B,KAAKI,mBAA/B;AAC9B,cAAI,KAAKC,wBAAT,EAAmC,KAAKb,eAAL,CAAqBQ,IAArB,CAA0B,KAAKK,wBAA/B;AACnC,cAAI,KAAKC,mBAAT,EAA8B,KAAKd,eAAL,CAAqBQ,IAArB,CAA0B,KAAKM,mBAA/B;AAE9B,eAAKC,SAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,OAAL;AACH,WAFD,EAEG,GAFH;AAGH;;AAEDA,QAAAA,OAAO,GAAG;AACN,cAAI;AACA,gBAAI,KAAKX,cAAL,IAAuB,KAAKA,cAAL,CAAoBY,IAA/C,EAAqD;AACjD,mBAAKZ,cAAL,CAAoBa,IAApB;AACAC,cAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;AACH,aAHD,MAGO;AACHD,cAAAA,OAAO,CAACE,IAAR,CAAa,iCAAb;AACH;AACJ,WAPD,CAOE,OAAOC,KAAP,EAAc;AACZH,YAAAA,OAAO,CAACG,KAAR,CAAc,oBAAd,EAAoCA,KAApC;AACH;AACJ;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAIC,YAAgC,GAAG,IAAvC;;AACA,kBAAQD,SAAR;AACI,iBAAK,aAAL;AACIC,cAAAA,YAAY,GAAG,KAAKlB,sBAApB;AACA;;AACJ,iBAAK,cAAL;AACIkB,cAAAA,YAAY,GAAG,KAAKjB,uBAApB;AACA;;AACJ,iBAAK,gBAAL;AACIiB,cAAAA,YAAY,GAAG,KAAKhB,yBAApB;AACA;;AACJ,iBAAK,UAAL;AACIgB,cAAAA,YAAY,GAAG,KAAKf,mBAApB;AACA;;AACJ,iBAAK,eAAL;AACIe,cAAAA,YAAY,GAAG,KAAKd,wBAApB;AACA;;AACJ,iBAAK,UAAL;AACIc,cAAAA,YAAY,GAAG,KAAKb,mBAApB;AACA;AAlBR;;AAqBA,cAAIa,YAAJ,EAAkB;AACdA,YAAAA,YAAY,CAACP,IAAb;AACH;AACJ;;AAEDQ,QAAAA,WAAW,GAAG;AACV,cAAMC,KAAK,GAAG,KAAKC,OAAL,EAAd;AACA,cAAMC,SAAS,GAAGF,KAAK,GAAG,CAAH,GAAO,CAA9B;AACA,eAAK7B,eAAL,CAAqBgC,OAArB,CAA6BC,MAAM,IAAI;AACnC,gBAAIA,MAAM,KAAG,KAAK1B,cAAlB,EAAkC;AAC1B0B,cAAAA,MAAM,CAACC,MAAP,GAAgBH,SAAS,GAAG,GAA5B;AACH,aAFL,MAGS,IAAIE,MAAJ,EAAY;AACbA,cAAAA,MAAM,CAACC,MAAP,GAAgBH,SAAhB;AACH;AACR,WAPD;AAQA,eAAKI,SAAL;AACH;;AAEDL,QAAAA,OAAO,GAAY;AACf;AACA,iBAAO,KAAKvB,cAAL,GAAsB,KAAKA,cAAL,CAAoB2B,MAApB,KAA+B,CAArD,GAAyD,KAAhE;AACH;;AAEOC,QAAAA,SAAS,GAAG;AAChB,cAAMC,KAAK,GAAG;AACVP,YAAAA,KAAK,EAAE,KAAKC,OAAL;AADG,WAAd;AAGApC,UAAAA,GAAG,CAAC2C,YAAJ,CAAiBC,OAAjB,CAAyB,YAAzB,EAAuCC,IAAI,CAACC,SAAL,CAAeJ,KAAf,CAAvC;AACH;;AAEOrB,QAAAA,SAAS,GAAG;AAChB,cAAM0B,QAAQ,GAAG/C,GAAG,CAAC2C,YAAJ,CAAiBK,OAAjB,CAAyB,YAAzB,CAAjB;;AACA,cAAID,QAAJ,EAAc;AACV,gBAAML,KAAK,GAAGG,IAAI,CAACI,KAAL,CAAWF,QAAX,CAAd;AACA,gBAAMP,MAAM,GAAGE,KAAK,CAACP,KAAN,GAAc,CAAd,GAAkB,CAAjC;AACA,iBAAK7B,eAAL,CAAqBgC,OAArB,CAA6BC,MAAM,IAAI;AACnC,kBAAIA,MAAM,KAAG,KAAK1B,cAAlB,EAAkC;AAC9B0B,gBAAAA,MAAM,CAACC,MAAP,GAAgBA,MAAM,GAAG,GAAzB;AACH,eAFD,MAGK,IAAID,MAAJ,EAAY;AACbA,gBAAAA,MAAM,CAACC,MAAP,GAAgBA,MAAhB;AACH;AACJ,aAPD;AAQH;AACJ;;AA/JuC,O,UA6CzBhC,S,GAA0B,I;;;;;iBAxCJ,I;;;;;;;iBAMQ,I;;;;;;;iBAMC,I;;;;;;;iBAME,I;;;;;;;iBAMN,I;;;;;;;iBAMK,I;;;;;;;iBAML,I", "sourcesContent": ["import { _decorator, Component, Node, AudioClip, AudioSource, director, sys } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('SoundManager')\nexport class SoundManager extends Component {\n    @property({\n        type: AudioSource,\n        tooltip: \"背景音乐\"\n    })\n    public bgmAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"按钮点击音效\"\n    })\n    public buttonClickAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆碰撞音效\"\n    })\n    public carCollisionAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆毁坏音效\"\n    })\n    public carDestructionAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆启动音效\"\n    })\n    public carStartAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆加速音效\"\n    })\n    public carAccelerateAudioSource: AudioSource = null!;\n\n    @property({\n        type: AudioSource,\n        tooltip: \"车辆漂移音效\"\n    })\n    public carDriftAudioSource: AudioSource = null!;\n\n    private allAudioSources: AudioSource[] = [];\n\n    private static _instance: SoundManager = null!;\n\n    public static get instance(): SoundManager {\n        return this._instance;\n    }\n\n    onLoad() {\n        if (SoundManager._instance) {\n            this.destroy();\n            return;\n        }\n        SoundManager._instance = this;\n        director.addPersistRootNode(this.node);\n\n        // 将所有音源收集到一个数组中，方便统一管理（只添加非空的音源）\n        if (this.bgmAudioSource) this.allAudioSources.push(this.bgmAudioSource);\n        if (this.buttonClickAudioSource) this.allAudioSources.push(this.buttonClickAudioSource);\n        if (this.carCollisionAudioSource) this.allAudioSources.push(this.carCollisionAudioSource);\n        if (this.carDestructionAudioSource) this.allAudioSources.push(this.carDestructionAudioSource);\n        if (this.carStartAudioSource) this.allAudioSources.push(this.carStartAudioSource);\n        if (this.carAccelerateAudioSource) this.allAudioSources.push(this.carAccelerateAudioSource);\n        if (this.carDriftAudioSource) this.allAudioSources.push(this.carDriftAudioSource);\n\n        this.loadState();\n    }\n\n    start() {\n        // 延迟播放BGM，确保所有系统初始化完成\n        this.scheduleOnce(() => {\n            this.playBGM();\n        }, 0.1);\n    }\n\n    playBGM() {\n        try {\n            if (this.bgmAudioSource && this.bgmAudioSource.clip) {\n                this.bgmAudioSource.play();\n                console.log('BGM started playing');\n            } else {\n                console.warn('BGM AudioSource or clip is null');\n            }\n        } catch (error) {\n            console.error('Error playing BGM:', error);\n        }\n    }\n\n    playSoundEffect(soundName: string) {\n        let sourceToPlay: AudioSource | null = null;\n        switch (soundName) {\n            case 'buttonClick':\n                sourceToPlay = this.buttonClickAudioSource;\n                break;\n            case 'carCollision':\n                sourceToPlay = this.carCollisionAudioSource;\n                break;\n            case 'carDestruction':\n                sourceToPlay = this.carDestructionAudioSource;\n                break;\n            case 'carStart':\n                sourceToPlay = this.carStartAudioSource;\n                break;\n            case 'carAccelerate':\n                sourceToPlay = this.carAccelerateAudioSource;\n                break;\n            case 'carDrift':\n                sourceToPlay = this.carDriftAudioSource;\n                break;\n        }\n\n        if (sourceToPlay) {\n            sourceToPlay.play();\n        }\n    }\n\n    toggleAudio() {\n        const muted = this.isMuted();\n        const newVolume = muted ? 1 : 0;\n        this.allAudioSources.forEach(source => {\n            if (source===this.bgmAudioSource) {\n                    source.volume = newVolume * 0.3;\n                }\n                else if (source) {\n                    source.volume = newVolume;\n                }\n        });\n        this.saveState();\n    }\n\n    isMuted(): boolean {\n        // 检查BGM音源的音量作为代表\n        return this.bgmAudioSource ? this.bgmAudioSource.volume === 0 : false;\n    }\n\n    private saveState() {\n        const state = {\n            muted: this.isMuted()\n        };\n        sys.localStorage.setItem('soundState', JSON.stringify(state));\n    }\n\n    private loadState() {\n        const stateStr = sys.localStorage.getItem('soundState');\n        if (stateStr) {\n            const state = JSON.parse(stateStr);\n            const volume = state.muted ? 0 : 1;\n            this.allAudioSources.forEach(source => {\n                if (source===this.bgmAudioSource) {\n                    source.volume = volume * 0.3;\n                }\n                else if (source) {\n                    source.volume = volume;\n                }\n            });\n        }\n    }\n}"]}