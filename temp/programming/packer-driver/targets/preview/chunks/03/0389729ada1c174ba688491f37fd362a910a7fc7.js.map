{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts"], "names": ["_decorator", "Component", "Sprite", "ccclass", "property", "PaintSpot", "sprite", "originalAlpha", "creationTime", "onLoad", "getComponent", "color", "a", "Date", "now", "start", "playSpawnAnimation", "update", "_deltaTime", "node", "setScale", "scheduleOnce", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;;;;;;;;OAC1B;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;AAE9B;AACA;AACA;AACA;;2BAEaK,S,WADZF,OAAO,CAAC,WAAD,C,2BAAR,MACaE,SADb,SAC+BJ,SAD/B,CACyC;AAAA;AAAA;;AAAA;;AAGZ;AAHY;;AAMR;AANQ,eAQ7BK,MAR6B,GAQZ,IARY;AAAA,eAS7BC,aAT6B,GASL,GATK;AAAA,eAU7BC,YAV6B,GAUN,CAVM;AAAA;;AAYrCC,QAAAA,MAAM,GAAG;AACL,eAAKH,MAAL,GAAc,KAAKI,YAAL,CAAkBR,MAAlB,CAAd;;AACA,cAAI,KAAKI,MAAT,EAAiB;AACb,iBAAKC,aAAL,GAAqB,KAAKD,MAAL,CAAYK,KAAZ,CAAkBC,CAAlB,GAAsB,GAA3C;AACH;;AACD,eAAKJ,YAAL,GAAoBK,IAAI,CAACC,GAAL,EAApB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,kBAAL;AACH;;AAEDC,QAAAA,MAAM,CAACC,UAAD,EAAqB,CACvB;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,kBAAkB,GAAS;AAC/B;AACA,cAAI,KAAKG,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUC,QAAV,CAAmB,GAAnB,EAAwB,GAAxB,EAA6B,CAA7B,EADW,CAEX;AACA;;AACA,iBAAKC,YAAL,CAAkB,MAAM;AACpB,kBAAI,KAAKF,IAAL,IAAa,KAAKA,IAAL,CAAUG,OAA3B,EAAoC;AAChC,qBAAKH,IAAL,CAAUC,QAAV,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB;AACH;AACJ,aAJD,EAIG,GAJH;AAKH;AACJ;AAED;AACJ;AACA;AACI;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AArEqC,O,2EAEpChB,Q;;;;;iBACkB,I;;qFAElBA,Q;;;;;iBACqB,K", "sourcesContent": ["import { _decorator, Component, Sprite } from 'cc';\nconst { ccclass, property } = _decorator;\n\n/**\n * 颜料斑点组件\n * 用于表示车辆喷洒的颜料\n */\n@ccclass('PaintSpot')\nexport class PaintSpot extends Component {\n    \n    @property\n    fadeTime: number = 30.0; // 颜料淡化时间（秒）\n    \n    @property\n    enableFade: boolean = false; // 是否启用淡化效果\n    \n    private sprite: Sprite = null!;\n    private originalAlpha: number = 1.0;\n    private creationTime: number = 0;\n    \n    onLoad() {\n        this.sprite = this.getComponent(Sprite);\n        if (this.sprite) {\n            this.originalAlpha = this.sprite.color.a / 255;\n        }\n        this.creationTime = Date.now();\n    }\n\n    start() {\n        // 可以在这里添加颜料出现的动画效果\n        this.playSpawnAnimation();\n    }\n\n    update(_deltaTime: number) {\n        // if (this.enableFade && this.sprite) {\n        //     this.updateFadeEffect();\n        // }\n    }\n\n    /**\n     * 播放生成动画\n     */\n    private playSpawnAnimation(): void {\n        // 简单的缩放动画\n        if (this.node) {\n            this.node.setScale(0.1, 0.1, 1);\n            // 使用tween动画放大到正常大小\n            // 注意：这里需要导入tween相关模块，暂时用简单的方式\n            this.scheduleOnce(() => {\n                if (this.node && this.node.isValid) {\n                    this.node.setScale(1, 1, 1);\n                }\n            }, 0.1);\n        }\n    }\n\n    /**\n     * 更新淡化效果\n     */\n    // private updateFadeEffect(): void {\n    //     const currentTime = Date.now();\n    //     const elapsedTime = (currentTime - this.creationTime) / 1000; // 转换为秒\n        \n    //     if (elapsedTime >= this.fadeTime) {\n    //         // 时间到了，销毁颜料\n    //         this.node.destroy();\n    //         return;\n    //     }\n        \n    //     // 计算淡化程度\n    //     const fadeProgress = elapsedTime / this.fadeTime;\n    //     const currentAlpha = this.originalAlpha * (1 - fadeProgress);\n        \n    //     // 应用淡化效果\n    //     const currentColor = this.sprite.color.clone();\n    //     currentColor.a = Math.max(0, currentAlpha * 255);\n    //     this.sprite.color = currentColor;\n    // }\n\n}\n"]}