{"version": 3, "sources": ["file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts"], "names": ["showPrerollAd", "game_instance", "game", "adBreak", "type", "adBreakDone", "placementInfo", "emit", "PrerollAdEvent", "AD_BREAK_DONE", "e", "console", "log", "showInterstitialAd", "name", "gameInstance", "beforeAd", "InterstitialAdEvent", "BEFORE_AD", "afterAd", "AFTER_AD", "requestRewardedAd", "RewardedVideoAdEvent", "beforeReward", "showAdFn", "showRewardedAdFn", "BEFORE_REWARD", "adDismissed", "AD_DISMISSED", "adViewed", "AD_VIEWED", "showRewardedAd", "ADS_BY_GOOGLE", "window", "adsbygoogle", "o", "push"], "mappings": ";;;;;AAoBA;AACA;AACA;AACA;AACO,WAAUA,aAAV,GAAyB;AAC5B,QAAI;AACA,UAAMC,aAAa,GAAGC,IAAtB;AACAC,MAAAA,OAAO,CAAC;AACJC,QAAAA,IAAI,EAAE,SADF;AAEJC,QAAAA,WAAW,EAAGC,aAAD,IAAwB;AACjCL,UAAAA,aAAa,CAACM,IAAd,CAAmBC,cAAc,CAACC,aAAlC;AACH;AAJG,OAAD,CAAP;AAMH,KARD,CAQE,OAAMC,CAAN,EAAS;AACPC,MAAAA,OAAO,CAACC,GAAR,CAAYF,CAAZ;AACH;AACJ;AAED;AACA;AACA;AACA;;;AACO,WAASG,kBAAT,CAA4BT,IAA5B,EAAoDU,IAApD,EAAkE;AACvE,QAAI;AACF,UAAMC,YAAY,GAAGb,IAArB;AACAC,MAAAA,OAAO,CAAC;AACNC,QAAAA,IADM;AAENU,QAAAA,IAFM;AAGNE,QAAAA,QAAQ,EAAE,MAAM;AACdD,UAAAA,YAAY,CAACR,IAAb,CAAkBU,mBAAmB,CAACC,SAAtC;AACD,SALK;AAMNC,QAAAA,OAAO,EAAE,MAAM;AACbJ,UAAAA,YAAY,CAACR,IAAb,CAAkBU,mBAAmB,CAACG,QAAtC;AACD,SARK;AASNf,QAAAA,WAAW,EAAGC,aAAD,IAA4B;AACvCS,UAAAA,YAAY,CAACR,IAAb,CAAkBU,mBAAmB,CAACR,aAAtC;AACD;AAXK,OAAD,CAAP;AAaD,KAfD,CAeE,OAAOC,CAAP,EAAU;AACVC,MAAAA,OAAO,CAACC,GAAR,CAAYF,CAAZ;AACD;AACF;AAED;AACA;AACA;AACA;AACA;;;AACO,WAASW,iBAAT,CAA2BP,IAA3B,EAAyC;AAC9C,QAAI;AACF,UAAMC,YAAY,GAAGb,IAArB;AACAC,MAAAA,OAAO,CAAC;AACNC,QAAAA,IAAI,EAAE,QADA;AAENU,QAAAA,IAFM;AAGNE,QAAAA,QAAQ,EAAE,MAAM;AACdD,UAAAA,YAAY,CAACR,IAAb,CAAkBe,oBAAoB,CAACJ,SAAvC;AACD,SALK;AAMNC,QAAAA,OAAO,EAAE,MAAM;AACbJ,UAAAA,YAAY,CAACR,IAAb,CAAkBe,oBAAoB,CAACF,QAAvC;AACD,SARK;AASNf,QAAAA,WAAW,EAAGC,aAAD,IAA4B;AACvCS,UAAAA,YAAY,CAACR,IAAb,CAAkBe,oBAAoB,CAACb,aAAvC;AACD,SAXK;AAYNc,QAAAA,YAAY,EAAGC,QAAD,IAA0B;AACtCC,UAAAA,gBAAgB,GAAGD,QAAnB;AACAT,UAAAA,YAAY,CAACR,IAAb,CAAkBe,oBAAoB,CAACI,aAAvC;AACD,SAfK;AAgBNC,QAAAA,WAAW,EAAE,MAAM;AACjBZ,UAAAA,YAAY,CAACR,IAAb,CAAkBe,oBAAoB,CAACM,YAAvC;AACD,SAlBK;AAmBNC,QAAAA,QAAQ,EAAE,MAAM;AACdd,UAAAA,YAAY,CAACR,IAAb,CAAkBe,oBAAoB,CAACQ,SAAvC;AACD;AArBK,OAAD,CAAP;AAuBD,KAzBD,CAyBE,OAAOpB,CAAP,EAAU;AACVC,MAAAA,OAAO,CAACC,GAAR,CAAYF,CAAZ;AACD;AACF;AAED;AACA;AACA;;;AACO,WAASqB,cAAT,GAA0B;AAC/B,QAAI,CAACN,gBAAL,EAAuB;AACrBd,MAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACA;AACD;;AACDa,IAAAA,gBAAgB;AAChBA,IAAAA,gBAAgB,GAAG,IAAnB;AACD;;;mBArFgBzB,a;wBAkBDa,kB;uBA0BAQ,iB;oBAkCAU;;;;;;;;AAtGR7B,MAAAA,I,OAAAA,I;;AAKAe,MAAAA,mB,gBAAAA,mB;AAAqBT,MAAAA,c,gBAAAA,c;AAAgBc,MAAAA,oB,gBAAAA,oB;;;;;;;AAG7C;AACA;AACMU,MAAAA,a,GAAiBC,MAAD,CAAgBC,W;;AAGhC/B,MAAAA,O,GACLgC,CAAD,IAAwB;AACtBH,QAAAA,aAAa,CAACI,IAAd,CAAmBD,CAAnB;AACD,O", "sourcesContent": ["import {game} from 'cc';\n\nimport {InterstitialCallback} from './interstitial_callback';\nimport {RewardedCallback} from './rewarded_callback';\nimport {PrerollCallback} from './preroll_callback';\nimport {InterstitialAdEvent, PrerollAdEvent, RewardedVideoAdEvent} from './ad_event';\nimport {InterstitialType} from './interstitial_type';\n\n// This is needed since there're no definition for AdSense object here.\n// tslint:disable-next-line:no-any\nconst ADS_BY_GOOGLE = (window as any).adsbygoogle;\ntype AdBreakCallback = InterstitialCallback|RewardedCallback|PrerollCallback;\n\nconst adBreak =\n(o: AdBreakCallback) => {\n  ADS_BY_GOOGLE.push(o);\n};\n\nlet showRewardedAdFn : (() => void) | null;\n\n/**\n * API to show Preroll Ad when available.\n * If there are no ad available this function will request an ad.\n */\nexport function  showPrerollAd(){\n    try {\n        const game_instance = game;\n        adBreak({\n            type: 'preroll',\n            adBreakDone: (placementInfo: any) => {\n                game_instance.emit(PrerollAdEvent.AD_BREAK_DONE);\n            },\n         });\n    } catch(e) {\n        console.log(e)\n    }\n}\n\n/**\n * API to show Interstitial Ad when available.\n * If there are no ad available this function will request an ad.\n */\nexport function showInterstitialAd(type: InterstitialType, name: string) {\n  try {\n    const gameInstance = game;\n    adBreak({\n      type,\n      name,\n      beforeAd: () => {\n        gameInstance.emit(InterstitialAdEvent.BEFORE_AD);\n      },\n      afterAd: () => {\n        gameInstance.emit(InterstitialAdEvent.AFTER_AD);\n      },\n      adBreakDone: (placementInfo: unknown) => {\n        gameInstance.emit(InterstitialAdEvent.AD_BREAK_DONE);\n      },\n    });\n  } catch (e) {\n    console.log(e);\n  }\n}\n\n/**\n * API to request Rewarded Ad.\n * To show the Rewarded Ad you could call showRewardedAd after\n * beforeReward callback.\n */\nexport function requestRewardedAd(name: string) {\n  try {\n    const gameInstance = game;\n    adBreak({\n      type: 'reward',\n      name,\n      beforeAd: () => {\n        gameInstance.emit(RewardedVideoAdEvent.BEFORE_AD);\n      },\n      afterAd: () => {\n        gameInstance.emit(RewardedVideoAdEvent.AFTER_AD);\n      },\n      adBreakDone: (placementInfo: unknown) => {\n        gameInstance.emit(RewardedVideoAdEvent.AD_BREAK_DONE);\n      },\n      beforeReward: (showAdFn: () => void) => {\n        showRewardedAdFn = showAdFn;\n        gameInstance.emit(RewardedVideoAdEvent.BEFORE_REWARD);\n      },\n      adDismissed: () => {\n        gameInstance.emit(RewardedVideoAdEvent.AD_DISMISSED);\n      },\n      adViewed: () => {\n        gameInstance.emit(RewardedVideoAdEvent.AD_VIEWED);\n      },\n    });\n  } catch (e) {\n    console.log(e);\n  }\n}\n\n/**\n * API to show Rewarded Ad when available.\n */\nexport function showRewardedAd() {\n  if (!showRewardedAdFn) {\n    console.log('No Rewarded Ad available');\n    return;\n  }\n  showRewardedAdFn();\n  showRewardedAdFn = null;\n}\n"]}