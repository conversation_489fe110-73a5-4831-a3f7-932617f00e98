System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts at runtime.
      throw new Error("SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts: Unexpected token, expected \",\" (329:37)\n\n  327 |         const layer = other.node.layer;\n  328 |         // console.log(\"layer:\", Layers.nameToLayer(\"Block\"));\n> 329 |         console.log(\"\u81EA\u8EAB\uFF1A\" ,self.name \"\u78B0\u649E\u5BF9\u8C61:\", other.node.name);\n      |                                      ^\n  330 |         \n  331 |         // \u68C0\u67E5\u5C42\u7EA7\u662F\u5426\u6709\u6548\u518D\u5C1D\u8BD5\u83B7\u53D6\u540D\u79F0\n  332 |         // let layerName = '';");
    }
  };
});
//# sourceMappingURL=08a1a9861d7f9076d7d48f3ab03f895e70804f35.js.map