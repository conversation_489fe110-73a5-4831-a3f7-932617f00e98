System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _cclegacy._RF.push({}, "fd80am1W2FMAq8T5lCOZkT/", "interstitial_callback", undefined);
      /**
       * Interface for Interstitial AdBreak API Callback
       */


      _cclegacy._RF.pop();
    }
  };
});
//# sourceMappingURL=51e06e09d5eeb7ade648d1be05b2015c9ba69d68.js.map