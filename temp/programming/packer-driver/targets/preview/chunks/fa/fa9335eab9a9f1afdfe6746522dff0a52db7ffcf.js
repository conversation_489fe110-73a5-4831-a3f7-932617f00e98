System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, TempData, _crd;

  _export("TempData", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4702aLSiftLiKCAlxKVKQG+", "TempData", undefined);

      _export("TempData", TempData = class TempData {});

      TempData.selectedLevel = '';
      TempData.selectedCar = '';

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js.map