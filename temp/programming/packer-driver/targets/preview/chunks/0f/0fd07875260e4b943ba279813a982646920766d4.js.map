{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIPlayer.ts"], "names": ["_decorator", "Component", "Vec2", "Vec3", "RigidBody2D", "ERigidBody2DType", "BoxCollider2D", "Contact2DType", "ProgressBar", "Sprite", "SpriteFrame", "tween", "player", "GameManager", "SoundManager", "ccclass", "property", "AIPlayer", "_rigidBody", "_direction", "_accel", "_angle", "_targetAngle", "_lastValidPosition", "_currentHealth", "_isDestroyed", "_originalSprite", "_destroyAnimationSpeed", "onLoad", "start", "getComponent", "node", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "type", "Dynamic", "allowSleep", "gravityScale", "linearDamping", "angularDamping", "fixedRotation", "worldPosition", "x", "y", "initAngle", "setRotationFromEuler", "initHealthBar", "sprite", "spriteFrame", "collider", "log", "on", "BEGIN_CONTACT", "onCollisionEnter", "maxHealth", "updateHealthBar", "healthBar", "progress", "update", "deltaTime", "currentVelocity", "linearVelocity", "currentSpeed", "length", "currentPos", "turnAmount", "turnSpeed", "angleDiff", "Math", "abs", "rad", "PI", "force", "cos", "acceleration", "sin", "applyForce", "forward", "dot", "brakeForce", "clone", "multiplyScalar", "brakeDeceleration", "reverseForce", "frictionForce", "friction", "maxSpeed", "normalizedVelocity", "normalize", "distanceToLastPos", "distance", "setWorldPosition", "z", "ZERO", "setAccel", "accel", "setDirection", "direction", "setTargetAngle", "angle", "getCurrentAngle", "init", "setHealth", "health", "max", "min", "takeDamage", "damage", "destroyVehicle", "heal", "amount", "getHealth", "getMaxHealth", "isDead", "other", "self", "playerComponent", "playerRigidBody", "getRigidBody", "impactForce", "instance", "playSoundEffect", "destroyedSprite", "active", "startDestroyAnimation", "updateEnemyCount", "scheduleRemoveNode", "gameManager", "getInstance", "allAIPlayers", "getAIPlayers", "aliveCount", "filter", "ai", "isDestroyed", "refreshEnemyCount", "scheduleOnce", "removeVehicleNode", "<PERSON><PERSON><PERSON><PERSON>", "aiPlayers", "index", "indexOf", "splice", "removeFromParent", "to", "scale"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,gB,OAAAA,gB;AAAwBC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AAE9IC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OADH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;0BAIjBiB,Q,WADZF,OAAO,CAAC,UAAD,C,UAkBHC,QAAQ,CAACR,WAAD,C,UAGRQ,QAAQ,CAACN,WAAD,C,2BArBb,MACaO,QADb,SAC8BhB,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAeZ;AAfY;;AAkBJ;AAlBI;;AAqBE;AArBF;;AAwBT;AAxBS,eA0B5BiB,UA1B4B,GA0BF,IA1BE;AAAA,eA2B5BC,UA3B4B,GA2BP,CA3BO;AA2BJ;AA3BI,eA4B5BC,MA5B4B,GA4BX,CA5BW;AA4BR;AA5BQ,eA6B5BC,MA7B4B,GA6BX,CA7BW;AAAA,eA8B5BC,YA9B4B,GA8BL,CA9BK;AAAA,eA+B5BC,kBA/B4B,GA+BD,IAAIrB,IAAJ,EA/BC;AAAA,eAgC5BsB,cAhC4B,GAgCH,GAhCG;AAgCE;AAEtC;AAlCoC,eAmC5BC,YAnC4B,GAmCJ,KAnCI;AAmCG;AAnCH,eAoC5BC,eApC4B,GAoCG,IApCH;AAoCU;AApCV,eAqC5BC,sBArC4B,GAqCK,IArCL;AAAA;;AAqCW;AAE/CC,QAAAA,MAAM,GAAG;AACL,eAAKV,UAAL,GAAkB,IAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,CAAd;AACA,eAAKC,MAAL,GAAc,CAAd;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKC,kBAAL,GAA0B,IAAIrB,IAAJ,EAA1B,CANK,CAQL;;AACA,eAAKuB,YAAL,GAAoB,KAApB;AACH;;AAEDI,QAAAA,KAAK,GAAG;AACJ,eAAKX,UAAL,GAAkB,KAAKY,YAAL,CAAkB1B,WAAlB,CAAlB;;AACA,cAAI,CAAC,KAAKc,UAAN,IAAoB,CAAC,KAAKa,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUC,OAAjD,EAA0D;AACtDC,YAAAA,OAAO,CAACC,KAAR,CAAc,wDAAd;AACA;AACH;;AACD,eAAKhB,UAAL,CAAgBiB,IAAhB,GAAuB9B,gBAAgB,CAAC+B,OAAxC;AACA,eAAKlB,UAAL,CAAgBmB,UAAhB,GAA6B,KAA7B;AACA,eAAKnB,UAAL,CAAgBoB,YAAhB,GAA+B,CAA/B;AACA,eAAKpB,UAAL,CAAgBqB,aAAhB,GAAgC,GAAhC;AACA,eAAKrB,UAAL,CAAgBsB,cAAhB,GAAiC,GAAjC;AACA,eAAKtB,UAAL,CAAgBuB,aAAhB,GAAgC,IAAhC;AACA,eAAKlB,kBAAL,GAA0B,IAAIrB,IAAJ,CAAS,KAAK6B,IAAL,CAAUW,aAAV,CAAwBC,CAAjC,EAAoC,KAAKZ,IAAL,CAAUW,aAAV,CAAwBE,CAA5D,CAA1B;AACA,eAAKvB,MAAL,GAAc,KAAKwB,SAAnB;AACA,eAAKvB,YAAL,GAAoB,KAAKuB,SAAzB;AACA,eAAKd,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKD,SAA1C,EAfI,CAiBJ;;AACA,eAAKE,aAAL,GAlBI,CAoBJ;;AACA,cAAMC,MAAM,GAAG,KAAKlB,YAAL,CAAkBrB,MAAlB,CAAf;;AACA,cAAIuC,MAAM,IAAIA,MAAM,CAACC,WAArB,EAAkC;AAC9B,iBAAKvB,eAAL,GAAuBsB,MAAM,CAACC,WAA9B;AACH,WAxBG,CA0BJ;;;AACA,cAAMC,QAAQ,GAAG,KAAKpB,YAAL,CAAkBxB,aAAlB,CAAjB;;AACA,cAAI4C,QAAJ,EAAc;AACVjB,YAAAA,OAAO,CAACkB,GAAR,CAAY,wCAAZ;AACAD,YAAAA,QAAQ,CAACE,EAAT,CAAY7C,aAAa,CAAC8C,aAA1B,EAAyC,KAAKC,gBAA9C,EAAgE,IAAhE;AACH,WAHD,MAGO;AACHrB,YAAAA,OAAO,CAACC,KAAR,CAAc,4CAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACYa,QAAAA,aAAa,GAAG;AACpB,eAAKvB,cAAL,GAAsB,KAAK+B,SAA3B,CADoB,CAGpB;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,eAAe,GAAG;AACtB,cAAI,KAAKC,SAAT,EAAoB;AAEhB,iBAAKA,SAAL,CAAeC,QAAf,GAA0B,KAAKlC,cAAL,GAAsB,KAAK+B,SAArD;AACAtB,YAAAA,OAAO,CAACkB,GAAR,CAAY,+BAAZ,EAA6C,KAAK3B,cAAL,GAAsB,KAAK+B,SAAxE;AACH;AACJ;;AAEDI,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC,KAAK1C,UAAN,IAAoB,CAAC,KAAKa,IAA1B,IAAkC,CAAC,KAAKA,IAAL,CAAUC,OAAjD,EAA0D,OADpC,CAGtB;;AACA,cAAI,KAAKP,YAAT,EAAuB;AAEnB;AACH;;AAED,cAAMoC,eAAe,GAAG,KAAK3C,UAAL,CAAgB4C,cAAxC;AACA,cAAMC,YAAY,GAAGF,eAAe,CAACG,MAAhB,EAArB;AACA,cAAMC,UAAU,GAAG,IAAI/D,IAAJ,CAAS,KAAK6B,IAAL,CAAUW,aAAV,CAAwBC,CAAjC,EAAoC,KAAKZ,IAAL,CAAUW,aAAV,CAAwBE,CAA5D,CAAnB,CAXsB,CAatB;;AACA,cAAI,KAAKzB,UAAL,KAAoB,CAAxB,EAA2B;AACvB,gBAAM+C,UAAU,GAAG,KAAKC,SAAL,GAAiBP,SAAjB,GAA6B,KAAKzC,UAArD;AACA,iBAAKG,YAAL,IAAqB4C,UAArB;AACH,WAjBqB,CAmBtB;;;AACA,cAAME,SAAS,GAAG,KAAK9C,YAAL,GAAoB,KAAKD,MAA3C;;AACA,cAAIgD,IAAI,CAACC,GAAL,CAASF,SAAT,IAAsB,GAA1B,EAA+B;AAC3B,iBAAK/C,MAAL,IAAe+C,SAAS,GAAG,GAA3B,CAD2B,CACK;AACnC,WAFD,MAEO;AACH,iBAAK/C,MAAL,GAAc,KAAKC,YAAnB;AACH,WAzBqB,CA2BtB;;;AACA,eAAKS,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKzB,MAA1C,EA5BsB,CA8BtB;;AACA,cAAI,KAAKD,MAAL,KAAgB,CAApB,EAAuB;AACnB;AACA,gBAAMmD,GAAG,GAAG,CAAC,KAAKlD,MAAL,GAAc,EAAf,IAAqBgD,IAAI,CAACG,EAA1B,GAA+B,GAA3C;AACA,gBAAMC,KAAK,GAAG,IAAIvE,IAAJ,CACVmE,IAAI,CAACK,GAAL,CAASH,GAAT,IAAgB,KAAKI,YADX,EAEVN,IAAI,CAACO,GAAL,CAASL,GAAT,IAAgB,KAAKI,YAFX,CAAd;;AAIA,iBAAKzD,UAAL,CAAgB2D,UAAhB,CAA2BJ,KAA3B,EAAkCR,UAAlC,EAA8C,IAA9C;AACH,WARD,CASA;AATA,eAUK,IAAI,KAAK7C,MAAL,KAAgB,CAAC,CAArB,EAAwB;AACzB;AACA,gBAAMmD,IAAG,GAAG,CAAC,KAAKlD,MAAL,GAAc,EAAf,IAAqBgD,IAAI,CAACG,EAA1B,GAA+B,GAA3C;;AACA,gBAAMM,OAAO,GAAG,IAAI5E,IAAJ,CAASmE,IAAI,CAACK,GAAL,CAASH,IAAT,CAAT,EAAwBF,IAAI,CAACO,GAAL,CAASL,IAAT,CAAxB,CAAhB;AACA,gBAAMQ,GAAG,GAAGlB,eAAe,CAACkB,GAAhB,CAAoBD,OAApB,CAAZ;;AAEA,gBAAIC,GAAG,GAAG,CAAV,EAAa;AACT;AACA,kBAAMC,UAAU,GAAGF,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKC,iBAArC,CAAnB;;AACA,mBAAKjE,UAAL,CAAgB2D,UAAhB,CAA2BG,UAA3B,EAAuCf,UAAvC,EAAmD,IAAnD;AACH,aAJD,MAIO;AACH;AACA,kBAAMmB,YAAY,GAAGN,OAAO,CAACG,KAAR,GAAgBC,cAAhB,CAA+B,CAAC,KAAKP,YAAN,GAAqB,GAApD,CAArB;;AACA,mBAAKzD,UAAL,CAAgB2D,UAAhB,CAA2BO,YAA3B,EAAyCnB,UAAzC,EAAqD,IAArD;AACH;AAEJ,WAhBI,CAiBL;AAjBK,eAkBA;AACD;AACA,gBAAIF,YAAY,GAAG,CAAnB,EAAsB;AAClB,kBAAMsB,aAAa,GAAGxB,eAAe,CAACoB,KAAhB,GAAwBC,cAAxB,CAAuC,CAAC,KAAKI,QAAN,GAAiB,CAAxD,CAAtB,CADkB,CACgE;;AAClF,mBAAKpE,UAAL,CAAgB2D,UAAhB,CAA2BQ,aAA3B,EAA0CpB,UAA1C,EAAsD,IAAtD;AACH;AACJ,WAjEqB,CAmEtB;;;AACA,cAAIF,YAAY,GAAG,KAAKwB,QAAxB,EAAkC;AAC9B,gBAAMC,kBAAkB,GAAG3B,eAAe,CAACoB,KAAhB,GAAwBQ,SAAxB,EAA3B;AACA,iBAAKvE,UAAL,CAAgB4C,cAAhB,GAAiC0B,kBAAkB,CAACN,cAAnB,CAAkC,KAAKK,QAAvC,CAAjC;AACH,WAvEqB,CAyEtB;;;AACA,cAAIxB,YAAY,GAAG,GAAnB,EAAwB;AACpB;AACA,gBAAM2B,iBAAiB,GAAGxF,IAAI,CAACyF,QAAL,CAAc1B,UAAd,EAA0B,KAAK1C,kBAA/B,CAA1B;;AACA,gBAAImE,iBAAiB,GAAG,EAAxB,EAA4B;AAAE;AAC1B,mBAAK3D,IAAL,CAAU6D,gBAAV,CAA2B,KAAKrE,kBAAL,CAAwBoB,CAAnD,EAAsD,KAAKpB,kBAAL,CAAwBqB,CAA9E,EAAiF,KAAKb,IAAL,CAAUW,aAAV,CAAwBmD,CAAzG;AACA,mBAAK3E,UAAL,CAAgB4C,cAAhB,GAAiC5D,IAAI,CAAC4F,IAAtC;AACH;AACJ,WAPD,MAOO;AACH;AACA,iBAAKvE,kBAAL,GAA0B0C,UAAU,CAACgB,KAAX,EAA1B;AACH,WApFqB,CAsFtB;;;AACA,cAAIZ,IAAI,CAACC,GAAL,CAAS,KAAKjD,MAAd,IAAwB,GAA5B,EAAiC;AAC7B,iBAAKA,MAAL,GAAc,KAAKA,MAAL,GAAc,GAA5B;AACA,iBAAKC,YAAL,GAAoB,KAAKA,YAAL,GAAoB,GAAxC;AACH;AACJ,SAjNmC,CAmNpC;;;AACOyE,QAAAA,QAAQ,CAACC,KAAD,EAAgB;AAC3B,eAAK5E,MAAL,GAAc4E,KAAd;AACH;;AAEMC,QAAAA,YAAY,CAACC,SAAD,EAAoB;AACnC,eAAK/E,UAAL,GAAkB+E,SAAlB;AACH;;AAEMC,QAAAA,cAAc,CAACC,KAAD,EAAgB;AACjC,eAAK9E,YAAL,GAAoB8E,KAApB;AACH;;AAEMC,QAAAA,eAAe,GAAW;AAC7B,iBAAO,KAAKhF,MAAZ;AACH;;AAEMiF,QAAAA,IAAI,CAACF,KAAD,EAAgB;AACvB,eAAKvD,SAAL,GAAiBuD,KAAjB;AACA,eAAK/E,MAAL,GAAc+E,KAAd;AACA,eAAK9E,YAAL,GAAoB8E,KAApB;AACA,eAAKrE,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCsD,KAArC;AACH,SAzOmC,CA2OpC;;AACA;AACJ;AACA;;;AACWG,QAAAA,SAAS,CAACC,MAAD,EAAiB;AAC7B,eAAKhF,cAAL,GAAsB6C,IAAI,CAACoC,GAAL,CAAS,CAAT,EAAYpC,IAAI,CAACqC,GAAL,CAASF,MAAT,EAAiB,KAAKjD,SAAtB,CAAZ,CAAtB;AACA,eAAKC,eAAL;AACH;AAED;AACJ;AACA;;;AACWmD,QAAAA,UAAU,CAACC,MAAD,EAAiB;AAC9B,cAAI,KAAKnF,YAAT,EAAuB;AAEvBQ,UAAAA,OAAO,CAACkB,GAAR,CAAY,yBAAZ,EAAuCyD,MAAvC;AACA,eAAKL,SAAL,CAAe,KAAK/E,cAAL,GAAsBoF,MAArC;AACA,eAAKpD,eAAL,GAL8B,CAO9B;;AACA,cAAI,KAAKhC,cAAL,IAAuB,CAA3B,EAA8B;AAC1B,iBAAKqF,cAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACxB,eAAKR,SAAL,CAAe,KAAK/E,cAAL,GAAsBuF,MAArC;AACA,eAAKvD,eAAL;AACH;AAED;AACJ;AACA;;;AACWwD,QAAAA,SAAS,GAAW;AACvB,iBAAO,KAAKxF,cAAZ;AACH;AAED;AACJ;AACA;;;AACWyF,QAAAA,YAAY,GAAW;AAC1B,iBAAO,KAAK1D,SAAZ;AACH;AAED;AACJ;AACA;;;AACW2D,QAAAA,MAAM,GAAY;AACrB,iBAAO,KAAK1F,cAAL,IAAuB,CAA9B;AACH;AAED;AACJ;AACA;;;AACI8B,QAAAA,gBAAgB,CAAC6D,KAAD,EAAuBC,IAAvB,EAA4C;AACxDnF,UAAAA,OAAO,CAACkB,GAAR,CAAY,kCAAZ;AACA,cAAMkE,eAAe,GAAGF,KAAK,CAACpF,IAAN,CAAWD,YAAX;AAAA;AAAA,+BAAxB;;AACA,cAAIuF,eAAJ,EAAqB;AACjBpF,YAAAA,OAAO,CAACkB,GAAR,CAAY,kBAAZ;AACA,gBAAMmE,eAAe,GAAGD,eAAe,CAACE,YAAhB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB,kBAAME,WAAW,GAAG,IAAItH,IAAJ,CAASoH,eAAe,CAACxD,cAAhB,CAA+BnB,CAAxC,EAA2C2E,eAAe,CAACxD,cAAhB,CAA+BlB,CAA1E,CAApB;AACA4E,cAAAA,WAAW,CAAC/B,SAAZ,GAFiB,CAEQ;;AACzB+B,cAAAA,WAAW,CAACtC,cAAZ,CAA2B,GAA3B,EAHiB,CAGgB;;AACjC,mBAAKhE,UAAL,CAAgB4C,cAAhB,GAAiC0D,WAAjC;AACH;AAEJ;AACJ,SAlTmC,CAoTpC;;AAEA;AACJ;AACA;;;AACYX,QAAAA,cAAc,GAAG;AACrB,cAAI,KAAKpF,YAAT,EAAuB;AAEvB,eAAKA,YAAL,GAAoB,IAApB;AACAQ,UAAAA,OAAO,CAACkB,GAAR,CAAY,UAAZ;AACA;AAAA;AAAA,4CAAasE,QAAb,CAAsBC,eAAtB,CAAsC,gBAAtC,EALqB,CAOrB;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,gBAAM3E,MAAM,GAAG,KAAKlB,YAAL,CAAkBrB,MAAlB,CAAf;;AACA,gBAAIuC,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,WAAP,GAAqB,KAAK0E,eAA1B;AACH;AACJ,WAboB,CAerB;;;AACA,cAAI,KAAKlE,SAAL,IAAkB,KAAKA,SAAL,CAAe1B,IAArC,EAA2C;AACvC,iBAAK0B,SAAL,CAAe1B,IAAf,CAAoB6F,MAApB,GAA6B,KAA7B;AACH,WAlBoB,CAoBrB;;;AACA,eAAKC,qBAAL,GArBqB,CAuBrB;;AACA,eAAKC,gBAAL,GAxBqB,CA0BrB;;AACA,eAAKC,kBAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,GAAG;AACvB,cAAME,WAAW,GAAG;AAAA;AAAA,0CAAYC,WAAZ,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACb;AACA,gBAAME,YAAY,GAAGF,WAAW,CAACG,YAAZ,EAArB;AACA,gBAAMC,UAAU,GAAGF,YAAY,CAACG,MAAb,CAAqBC,EAAD,IAAkB,CAACA,EAAE,CAACC,WAAH,EAAvC,EAAyDvE,MAA5E;AACAgE,YAAAA,WAAW,CAACQ,iBAAZ,CAA8BJ,UAA9B;AACH;AACJ;AAED;AACJ;AACA;;;AACYL,QAAAA,kBAAkB,GAAG;AACzB,cAAI,KAAKhG,IAAL,IAAa,KAAKA,IAAL,CAAUC,OAA3B,EAAoC;AAChC;AACA,iBAAKyG,YAAL,CAAkB,MAAM;AACpB,mBAAKC,iBAAL;AACH,aAFD,EAEG,KAAKC,WAFR;AAGH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK3G,IAAL,IAAa,KAAKA,IAAL,CAAUC,OAA3B,EAAoC;AAChCC,YAAAA,OAAO,CAACkB,GAAR,CAAY,UAAZ,EADgC,CAGhC;;AACA,gBAAM6E,WAAW,GAAG;AAAA;AAAA,4CAAYC,WAAZ,EAApB;;AACA,gBAAID,WAAJ,EAAiB;AACb,kBAAMY,SAAS,GAAGZ,WAAW,CAACG,YAAZ,EAAlB;AACA,kBAAMU,KAAK,GAAGD,SAAS,CAACE,OAAV,CAAkB,IAAlB,CAAd;;AACA,kBAAID,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdD,gBAAAA,SAAS,CAACG,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH,eALY,CAOb;;;AACAb,cAAAA,WAAW,CAACQ,iBAAZ,CAA8BI,SAAS,CAAC5E,MAAxC;AACH,aAd+B,CAgBhC;;;AACA,iBAAKjC,IAAL,CAAUiH,gBAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACYnB,QAAAA,qBAAqB,GAAG;AAC5B,cAAI,KAAK9F,IAAT,EAAe;AACX;AACApB,YAAAA,KAAK,CAAC,KAAKoB,IAAN,CAAL,CACKkH,EADL,CACQ,GADR,EACa;AACLC,cAAAA,KAAK,EAAE,IAAI/I,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,CAAnB,CADF,CAC0B;AAC/B;;AAFK,aADb,EAKK0B,KALL;AAMH;AACJ;AAED;AACJ;AACA;;AAGI;AACJ;AACA;;;AACW0G,QAAAA,WAAW,GAAY;AAC1B,iBAAO,KAAK9G,YAAZ;AACH;AAED;AACJ;AACA;AACI;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;;;AA1coC,O,2EACnCT,Q;;;;;iBACkB,E;;uFAClBA,Q;;;;;iBACsB,E;;4FACtBA,Q;;;;;iBAC2B,G;;oFAC3BA,Q;;;;;iBACmB,E;;mFACnBA,Q;;;;;iBACkB,G;;oFAClBA,Q;;;;;iBACmB,C;;oFAEnBA,Q;;;;;iBACmB,E;;;;;;;iBAGK,I;;;;;;;iBAGM,I;;uFAE9BA,Q;;;;;iBACqB,G", "sourcesContent": ["import { _decorator, Component, Node, Vec2, Vec3, RigidBody2D, ERigidBody2DType, math, BoxCollider2D, Contact2DType, ProgressBar, Sprite, SpriteFrame, tween } from 'cc';\nimport { HealthBarUI } from './HealthBarUI';\nimport { player } from './player';\nimport { GameManager } from './GameManager';\nconst { ccclass, property } = _decorator;\nimport { SoundManager } from './SoundManager';\n\n@ccclass('AIPlayer')\nexport class AIPlayer extends Component {\n    @property\n    maxSpeed: number = 50;\n    @property\n    acceleration: number = 50;\n    @property\n    brakeDeceleration: number = 200;\n    @property\n    turnSpeed: number = 50;\n    @property\n    friction: number = 1.5;\n    @property\n    initAngle: number = 0;\n\n    @property\n    maxHealth: number = 30; // 最大生命值\n\n    @property(ProgressBar)\n    healthBar: ProgressBar = null!; // 血条UI组件\n\n    @property(SpriteFrame)\n    destroyedSprite: SpriteFrame = null!; // 摧毁状态的精灵图\n\n    @property\n    removeDelay: number = 3.0; // 摧毁后移除节点的延迟时间（秒）\n\n    private _rigidBody: RigidBody2D = null!;\n    private _direction: number = 0; // -1:左, 0:不转, 1:右\n    private _accel: number = 0; // -1:刹车, 0:无, 1:加速\n    private _angle: number = 0;\n    private _targetAngle: number = 0;\n    private _lastValidPosition: Vec2 = new Vec2();\n    private _currentHealth: number = 100; // 当前生命值\n\n    // 摧毁相关\n    private _isDestroyed: boolean = false; // 是否已摧毁\n    private _originalSprite: SpriteFrame = null!; // 原始精灵图\n    private _destroyAnimationSpeed: number = 0.95; // 摧毁动画速度衰减系数\n\n    onLoad() {\n        this._rigidBody = null!;\n        this._direction = 0;\n        this._accel = 0;\n        this._angle = 0;\n        this._targetAngle = 0;\n        this._lastValidPosition = new Vec2();\n\n        // 初始化摧毁状态\n        this._isDestroyed = false;\n    }\n\n    start() {\n        this._rigidBody = this.getComponent(RigidBody2D)!;\n        if (!this._rigidBody || !this.node || !this.node.isValid) {\n            console.error('AIPlayer requires RigidBody2D component and valid node');\n            return;\n        }\n        this._rigidBody.type = ERigidBody2DType.Dynamic;\n        this._rigidBody.allowSleep = false;\n        this._rigidBody.gravityScale = 0;\n        this._rigidBody.linearDamping = 0.3;\n        this._rigidBody.angularDamping = 0.9;\n        this._rigidBody.fixedRotation = true;\n        this._lastValidPosition = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n        this._angle = this.initAngle;\n        this._targetAngle = this.initAngle;\n        this.node.setRotationFromEuler(0, 0, this.initAngle);\n\n        // 初始化血条\n        this.initHealthBar();\n\n        // 保存原始精灵图\n        const sprite = this.getComponent(Sprite);\n        if (sprite && sprite.spriteFrame) {\n            this._originalSprite = sprite.spriteFrame;\n        }\n\n        // 检查BoxCollider2D组件是否存在\n        const collider = this.getComponent(BoxCollider2D);\n        if (collider) {\n            console.log('AIPlayer BoxCollider2D component found');\n            collider.on(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);\n        } else {\n            console.error('AIPlayer BoxCollider2D component not found');\n        }\n    }\n\n    /**\n     * 初始化血条\n     */\n    private initHealthBar() {\n        this._currentHealth = this.maxHealth;\n        \n        // 如果没有手动设置血条UI，尝试自动查找\n        // if (!this.healthBarUI) {\n        //     this.healthBarUI = this.node.getComponentInChildren(HealthBarUI);\n        // }\n        \n        // if (this.healthBarUI) {\n        //     // 设置血条的目标为当前AI车辆\n        //     this.healthBarUI.setTarget(this.node);\n        //     this.updateHealthBar();\n        // } else {\n        //     console.warn('AIPlayer: 未找到HealthBarUI组件');\n        // }\n    }\n\n    /**\n     * 更新血条显示\n     */\n    private updateHealthBar() {\n        if (this.healthBar) {\n            \n            this.healthBar.progress = this._currentHealth / this.maxHealth;\n            console.log('AIPlayer updating health bar:', this._currentHealth / this.maxHealth);\n        }\n    }\n\n    update(deltaTime: number) {\n        if (!this._rigidBody || !this.node || !this.node.isValid) return;\n\n        // 如果车辆已摧毁，执行摧毁动画逻辑\n        if (this._isDestroyed) {\n\n            return;\n        }\n        \n        const currentVelocity = this._rigidBody.linearVelocity;\n        const currentSpeed = currentVelocity.length();\n        const currentPos = new Vec2(this.node.worldPosition.x, this.node.worldPosition.y);\n\n        // 更新目标角度（转向）\n        if (this._direction !== 0) {\n            const turnAmount = this.turnSpeed * deltaTime * this._direction;\n            this._targetAngle -= turnAmount;\n        }\n        \n        // 平滑角度插值，防止突然转向\n        const angleDiff = this._targetAngle - this._angle;\n        if (Math.abs(angleDiff) > 0.1) {\n            this._angle += angleDiff * 0.1; // 平滑插值\n        } else {\n            this._angle = this._targetAngle;\n        }\n        \n        // 设置节点旋转\n        this.node.setRotationFromEuler(0, 0, this._angle);\n\n        // 前进\n        if (this._accel === 1) {\n            // 正常加速\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const force = new Vec2(\n                Math.cos(rad) * this.acceleration,\n                Math.sin(rad) * this.acceleration\n            );\n            this._rigidBody.applyForce(force, currentPos, true);\n        }\n        // 刹车\n        else if (this._accel === -1) {\n            // 如果当前速度方向与车辆朝向一致，施加反向力（刹车）\n            const rad = (this._angle + 90) * Math.PI / 180;\n            const forward = new Vec2(Math.cos(rad), Math.sin(rad));\n            const dot = currentVelocity.dot(forward);\n            \n            if (dot > 0) {\n                // 施加强力反向力（刹车）\n                const brakeForce = forward.clone().multiplyScalar(-this.brakeDeceleration);\n                this._rigidBody.applyForce(brakeForce, currentPos, true);\n            } else {\n                // 允许倒车\n                const reverseForce = forward.clone().multiplyScalar(-this.acceleration * 0.5);\n                this._rigidBody.applyForce(reverseForce, currentPos, true);\n            }\n\n        }\n        // 松开加速/刹车键\n        else {\n            // 增大摩擦力，让车辆更快停下来\n            if (currentSpeed > 1) {\n                const frictionForce = currentVelocity.clone().multiplyScalar(-this.friction * 2); // 2倍摩擦\n                this._rigidBody.applyForce(frictionForce, currentPos, true);\n            }\n        }\n\n        // 限制最大速度\n        if (currentSpeed > this.maxSpeed) {\n            const normalizedVelocity = currentVelocity.clone().normalize();\n            this._rigidBody.linearVelocity = normalizedVelocity.multiplyScalar(this.maxSpeed);\n        }\n\n        // 防止车辆卡住或异常位置\n        if (currentSpeed < 0.1) {\n            // 如果速度很小，重置到上次有效位置附近\n            const distanceToLastPos = Vec2.distance(currentPos, this._lastValidPosition);\n            if (distanceToLastPos > 50) { // 如果偏离太远\n                this.node.setWorldPosition(this._lastValidPosition.x, this._lastValidPosition.y, this.node.worldPosition.z);\n                this._rigidBody.linearVelocity = Vec2.ZERO;\n            }\n        } else {\n            // 更新有效位置\n            this._lastValidPosition = currentPos.clone();\n        }\n\n        // 防止车辆旋转过度\n        if (Math.abs(this._angle) > 360) {\n            this._angle = this._angle % 360;\n            this._targetAngle = this._targetAngle % 360;\n        }\n    }\n\n    // 供AI控制器调用的接口\n    public setAccel(accel: number) {\n        this._accel = accel;\n    }\n    \n    public setDirection(direction: number) {\n        this._direction = direction;\n    }\n    \n    public setTargetAngle(angle: number) {\n        this._targetAngle = angle;\n    }\n    \n    public getCurrentAngle(): number {\n        return this._angle;\n    }\n    \n    public init(angle: number) {\n        this.initAngle = angle;\n        this._angle = angle;\n        this._targetAngle = angle;\n        this.node.setRotationFromEuler(0, 0, angle);\n    }\n\n    // 血量管理接口\n    /**\n     * 设置当前生命值\n     */\n    public setHealth(health: number) {\n        this._currentHealth = Math.max(0, Math.min(health, this.maxHealth));\n        this.updateHealthBar();\n    }\n\n    /**\n     * 减少生命值\n     */\n    public takeDamage(damage: number) {\n        if (this._isDestroyed) return;\n\n        console.log('AIPlayer taking damage:', damage);\n        this.setHealth(this._currentHealth - damage);\n        this.updateHealthBar();\n\n        // 检查是否死亡\n        if (this._currentHealth <= 0) {\n            this.destroyVehicle();\n        }\n    }\n\n    /**\n     * 恢复生命值\n     */\n    public heal(amount: number) {\n        this.setHealth(this._currentHealth + amount);\n        this.updateHealthBar();\n    }\n\n    /**\n     * 获取当前生命值\n     */\n    public getHealth(): number {\n        return this._currentHealth;\n    }\n\n    /**\n     * 获取最大生命值\n     */\n    public getMaxHealth(): number {\n        return this.maxHealth;\n    }\n\n    /**\n     * 检查是否死亡\n     */\n    public isDead(): boolean {\n        return this._currentHealth <= 0;\n    }\n\n    /**\n     * 碰撞事件处理\n     */\n    onCollisionEnter(other: BoxCollider2D, self: BoxCollider2D) {\n        console.log('AIPlayer collided with something');\n        const playerComponent = other.node.getComponent<player>(player);\n        if (playerComponent) {\n            console.log('AIPlayer 被玩家车辆撞击');\n            const playerRigidBody = playerComponent.getRigidBody();\n            if (playerRigidBody) {\n                const impactForce = new Vec2(playerRigidBody.linearVelocity.x, playerRigidBody.linearVelocity.y);\n                impactForce.normalize(); // 归一化方向\n                impactForce.multiplyScalar(100); // 增加冲力强度\n                this._rigidBody.linearVelocity = impactForce;\n            }\n\n        }\n    }\n\n    // ==================== 摧毁系统 ====================\n\n    /**\n     * 摧毁车辆\n     */\n    private destroyVehicle() {\n        if (this._isDestroyed) return;\n\n        this._isDestroyed = true;\n        console.log('AI车辆被摧毁！');\n        SoundManager.instance.playSoundEffect('carDestruction');\n\n        // 切换到摧毁状态的精灵图\n        if (this.destroyedSprite) {\n            const sprite = this.getComponent(Sprite);\n            if (sprite) {\n                sprite.spriteFrame = this.destroyedSprite;\n            }\n        }\n\n        // 隐藏血条\n        if (this.healthBar && this.healthBar.node) {\n            this.healthBar.node.active = false;\n        }\n\n        // 开始摧毁动画\n        this.startDestroyAnimation();\n\n        // 立即更新敌人数量（不等待节点移除）\n        this.updateEnemyCount();\n\n        // 延迟移除节点\n        this.scheduleRemoveNode();\n    }\n\n    /**\n     * 更新敌人数量\n     */\n    private updateEnemyCount() {\n        const gameManager = GameManager.getInstance();\n        if (gameManager) {\n            // 计算当前存活的AI数量\n            const allAIPlayers = gameManager.getAIPlayers();\n            const aliveCount = allAIPlayers.filter((ai: AIPlayer) => !ai.isDestroyed()).length;\n            gameManager.refreshEnemyCount(aliveCount);\n        }\n    }\n\n    /**\n     * 安排移除节点\n     */\n    private scheduleRemoveNode() {\n        if (this.node && this.node.isValid) {\n            // 使用scheduleOnce在指定时间后执行移除\n            this.scheduleOnce(() => {\n                this.removeVehicleNode();\n            }, this.removeDelay);\n        }\n    }\n\n    /**\n     * 移除车辆节点\n     */\n    private removeVehicleNode() {\n        if (this.node && this.node.isValid) {\n            console.log('移除AI车辆节点');\n\n            // 从GameManager的AI列表中移除\n            const gameManager = GameManager.getInstance();\n            if (gameManager) {\n                const aiPlayers = gameManager.getAIPlayers();\n                const index = aiPlayers.indexOf(this);\n                if (index !== -1) {\n                    aiPlayers.splice(index, 1);\n                }\n\n                // 再次更新敌人数量（基于实际存在的AI数量）\n                gameManager.refreshEnemyCount(aiPlayers.length);\n            }\n\n            // 移除节点\n            this.node.removeFromParent();\n        }\n    }\n\n    /**\n     * 开始摧毁动画\n     */\n    private startDestroyAnimation() {\n        if (this.node) {\n            // 添加摧毁动画效果\n            tween(this.node)\n                .to(2.0, {\n                    scale: new Vec3(1.1, 1.1, 1),  // 稍微缩小\n                    // angle: this.node.angle + 180 // 旋转180度\n                })\n                .start();\n        }\n    }\n\n    /**\n     * 更新摧毁动画\n     */\n   \n\n    /**\n     * 是否已摧毁\n     */\n    public isDestroyed(): boolean {\n        return this._isDestroyed;\n    }\n\n    /**\n     * 恢复车辆（用于重新开始游戏）\n     */\n    // public restoreVehicle() {\n    //     // 取消移除节点的计划\n    //     this.unschedule(this.removeVehicleNode);\n\n    //     this._isDestroyed = false;\n    //     this._currentHealth = this.maxHealth;\n\n    //     // 恢复原始精灵图\n    //     if (this._originalSprite) {\n    //         const sprite = this.getComponent(Sprite);\n    //         if (sprite) {\n    //             sprite.spriteFrame = this._originalSprite;\n    //         }\n    //     }\n\n    //     // 显示血条\n    //     if (this.healthBar && this.healthBar.node) {\n    //         this.healthBar.node.active = true;\n    //     }\n\n    //     // 更新血条\n    //     this.updateHealthBar();\n\n    //     // 恢复节点状态\n    //     if (this.node) {\n    //         this.node.setScale(1, 1);\n    //         this.node.angle = this.initAngle;\n    //     }\n\n    //     // 重置速度\n    //     if (this._rigidBody) {\n    //         this._rigidBody.linearVelocity = Vec2.ZERO;\n    //     }\n\n    //     console.log('AI车辆已恢复');\n    // }\n}"]}