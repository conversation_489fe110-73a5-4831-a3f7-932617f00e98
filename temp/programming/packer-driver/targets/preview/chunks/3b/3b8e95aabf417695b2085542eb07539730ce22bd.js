System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts at runtime.
      throw new Error("SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts: Unterminated string constant. (194:29)\n\n  192 |\n  193 |         if (!levelToggle || !carToggle) {\n> 194 |             this.showMessage(\"\u8BF7\u9009\u62E9\u8F66\u8F86\uFF01\n please select a car\uFF01\n      |                              ^\n  195 |             // \u4F60\u53EF\u4EE5\u5728\u8FD9\u91CC\u5F39\u7A97\u63D0\u793A\"\u8BF7\u9009\u62E9\u5173\u5361\u548C\u8F66\u8F86\"\n  196 |             return;\n  197 |         }");
    }
  };
});
//# sourceMappingURL=3b8e95aabf417695b2085542eb07539730ce22bd.js.map