{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts"], "names": ["_decorator", "Component", "ProgressBar", "tween", "CarProperties", "ccclass", "property", "CarPropertyDisplay", "type", "tooltip", "onLoad", "autoFindProgressBars", "speedProgressBar", "speedNode", "node", "getChildByName", "getComponent", "steeringProgressBar", "steeringNode", "durabilityProgressBar", "durabilityNode", "showCarProperties", "carId", "carProperty", "getCarProperty", "console", "warn", "updatePropertyDisplay", "active", "hideAllProperties", "updateProgressBar", "speed", "steering", "durability", "progressBar", "value", "targetProgress", "enableAnimation", "to", "animationDuration", "progress", "start"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AACpCC,MAAAA,a,iBAAAA,a;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;AAE9B;AACA;AACA;AACA;;oCAEaO,kB,WADZF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNG,QAAAA,OAAO,EAAE;AADH,OAAD,C,UAKRH,QAAQ,CAAC;AACNG,QAAAA,OAAO,EAAE;AADH,OAAD,C,2BAzBb,MACaF,kBADb,SACwCN,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AA6B9CS,QAAAA,MAAM,GAAG;AACL;AACA,eAAKC,oBAAL,GAFK,CAIL;AACA;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,oBAAoB,GAAS;AACjC,cAAI,CAAC,KAAKC,gBAAV,EAA4B;AACxB,gBAAMC,SAAS,GAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,OAAzB,KAAqC,KAAKD,IAAL,CAAUC,cAAV,CAAyB,OAAzB,CAAvD;;AACA,gBAAIF,SAAJ,EAAe;AACX,mBAAKD,gBAAL,GAAwBC,SAAS,CAACG,YAAV,CAAuBd,WAAvB,CAAxB;AACH;AACJ;;AAED,cAAI,CAAC,KAAKe,mBAAV,EAA+B;AAC3B,gBAAMC,YAAY,GAAG,KAAKJ,IAAL,CAAUC,cAAV,CAAyB,MAAzB,KAAoC,KAAKD,IAAL,CAAUC,cAAV,CAAyB,MAAzB,CAAzD;;AACA,gBAAIG,YAAJ,EAAkB;AACd,mBAAKD,mBAAL,GAA2BC,YAAY,CAACF,YAAb,CAA0Bd,WAA1B,CAA3B;AACH;AACJ;;AAED,cAAI,CAAC,KAAKiB,qBAAV,EAAiC;AAC7B,gBAAMC,cAAc,GAAG,KAAKN,IAAL,CAAUC,cAAV,CAAyB,OAAzB,KAAqC,KAAKD,IAAL,CAAUC,cAAV,CAAyB,OAAzB,CAA5D;;AACA,gBAAIK,cAAJ,EAAoB;AAChB,mBAAKD,qBAAL,GAA6BC,cAAc,CAACJ,YAAf,CAA4Bd,WAA5B,CAA7B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACWmB,QAAAA,iBAAiB,CAACC,KAAD,EAAsB;AAC1C,cAAMC,WAAW,GAAG;AAAA;AAAA,8CAAcC,cAAd,CAA6BF,KAA7B,CAApB;;AACA,cAAI,CAACC,WAAL,EAAkB;AACdE,YAAAA,OAAO,CAACC,IAAR,qCAAsBJ,KAAtB,sCADc,CAEd;;AACA;AACH;;AAED,eAAKK,qBAAL,CAA2BJ,WAA3B,EAR0C,CAU1C;;AACA,eAAKT,IAAL,CAAUc,MAAV,GAAmB,IAAnB;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,iBAAiB,GAAS;AAC7B,eAAKf,IAAL,CAAUc,MAAV,GAAmB,KAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACYD,QAAAA,qBAAqB,CAACrB,QAAD,EAA8B;AACvD;AACA,cAAI,KAAKM,gBAAT,EAA2B;AACvB,iBAAKkB,iBAAL,CAAuB,KAAKlB,gBAA5B,EAA8CN,QAAQ,CAACyB,KAAvD;AACH,WAJsD,CAMvD;;;AACA,cAAI,KAAKd,mBAAT,EAA8B;AAC1B,iBAAKa,iBAAL,CAAuB,KAAKb,mBAA5B,EAAiDX,QAAQ,CAAC0B,QAA1D;AACH,WATsD,CAWvD;;;AACA,cAAI,KAAKb,qBAAT,EAAgC;AAC5B,iBAAKW,iBAAL,CAAuB,KAAKX,qBAA5B,EAAmDb,QAAQ,CAAC2B,UAA5D;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACYH,QAAAA,iBAAiB,CAACI,WAAD,EAA2BC,KAA3B,EAAgD;AACrE,cAAMC,cAAc,GAAGD,KAAK,GAAG,GAA/B,CADqE,CACjC;;AAEpC,cAAI,KAAKE,eAAT,EAA0B;AACtB;AACAlC,YAAAA,KAAK,CAAC+B,WAAD,CAAL,CACKI,EADL,CACQ,KAAKC,iBADb,EACgC;AAAEC,cAAAA,QAAQ,EAAEJ;AAAZ,aADhC,EAEKK,KAFL;AAGH,WALD,MAKO;AACH;AACAP,YAAAA,WAAW,CAACM,QAAZ,GAAuBJ,cAAvB;AACH;AACJ;;AA9H6C,O;;;;;iBAKd,I;;;;;;;iBAMG,I;;;;;;;iBAME,I;;;;;;;iBAKV,I;;;;;;;iBAKC,G", "sourcesContent": ["import { _decorator, Component, ProgressBar, tween } from 'cc';\nimport { CarProperties, CarProperty } from './CarProperties';\nconst { ccclass, property } = _decorator;\n\n/**\n * 车辆属性显示组件\n * 负责在UI中显示车辆的速度、转向、坚硬度属性\n */\n@ccclass('CarPropertyDisplay')\nexport class CarPropertyDisplay extends Component {\n    @property({\n        type: ProgressBar,\n        tooltip: '速度进度条'\n    })\n    speedProgressBar: ProgressBar = null!;\n\n    @property({\n        type: ProgressBar,\n        tooltip: '转向进度条'\n    })\n    steeringProgressBar: ProgressBar = null!;\n\n    @property({\n        type: ProgressBar,\n        tooltip: '坚硬度进度条'\n    })\n    durabilityProgressBar: ProgressBar = null!;\n\n    @property({\n        tooltip: '是否启用动画效果'\n    })\n    enableAnimation: boolean = true;\n\n    @property({\n        tooltip: '动画持续时间（秒）'\n    })\n    animationDuration: number = 0.5;\n\n    onLoad() {\n        // 自动查找进度条（如果没有手动设置）\n        this.autoFindProgressBars();\n\n        // // 初始化时隐藏所有内容\n        // this.hideAllProperties();\n    }\n\n    /**\n     * 自动查找进度条组件\n     */\n    private autoFindProgressBars(): void {\n        if (!this.speedProgressBar) {\n            const speedNode = this.node.getChildByName('speed') || this.node.getChildByName('Speed');\n            if (speedNode) {\n                this.speedProgressBar = speedNode.getComponent(ProgressBar);\n            }\n        }\n\n        if (!this.steeringProgressBar) {\n            const steeringNode = this.node.getChildByName('turn') || this.node.getChildByName('turn');\n            if (steeringNode) {\n                this.steeringProgressBar = steeringNode.getComponent(ProgressBar);\n            }\n        }\n\n        if (!this.durabilityProgressBar) {\n            const durabilityNode = this.node.getChildByName('tough') || this.node.getChildByName('tough');\n            if (durabilityNode) {\n                this.durabilityProgressBar = durabilityNode.getComponent(ProgressBar);\n            }\n        }\n    }\n\n    /**\n     * 显示指定车辆的属性\n     * @param carId 车辆ID\n     */\n    public showCarProperties(carId: string): void {\n        const carProperty = CarProperties.getCarProperty(carId);\n        if (!carProperty) {\n            console.warn(`未找到车辆 ${carId} 的属性配置`);\n            // this.hideAllProperties();\n            return;\n        }\n\n        this.updatePropertyDisplay(carProperty);\n\n        // 显示属性面板\n        this.node.active = true;\n    }\n\n    /**\n     * 隐藏所有属性\n     */\n    public hideAllProperties(): void {\n        this.node.active = false;\n    }\n\n    /**\n     * 更新属性显示\n     * @param property 车辆属性\n     */\n    private updatePropertyDisplay(property: CarProperty): void {\n        // 更新速度\n        if (this.speedProgressBar) {\n            this.updateProgressBar(this.speedProgressBar, property.speed);\n        }\n\n        // 更新转向\n        if (this.steeringProgressBar) {\n            this.updateProgressBar(this.steeringProgressBar, property.steering);\n        }\n\n        // 更新坚硬度\n        if (this.durabilityProgressBar) {\n            this.updateProgressBar(this.durabilityProgressBar, property.durability);\n        }\n    }\n\n    /**\n     * 更新进度条\n     * @param progressBar 进度条组件\n     * @param value 数值 (0-100)\n     */\n    private updateProgressBar(progressBar: ProgressBar, value: number): void {\n        const targetProgress = value / 100; // 转换为0-1范围\n\n        if (this.enableAnimation) {\n            // 使用动画效果\n            tween(progressBar)\n                .to(this.animationDuration, { progress: targetProgress })\n                .start();\n        } else {\n            // 直接设置\n            progressBar.progress = targetProgress;\n        }\n    }\n}\n"]}