{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/HealthBarUI.ts"], "names": ["_decorator", "Component", "ccclass", "property", "HealthBarUI", "targetNode", "canvas", "isInitialized", "start", "setup<PERSON><PERSON><PERSON>", "node", "parent", "separateFromParent", "console", "log", "scene", "getChildByName", "error", "currentWorldPos", "worldPosition", "setParent", "setWorldPosition", "update", "updatePosition", "targetWorldPos", "x", "y", "offsetY", "z", "setRotationFromEuler", "setOffsetY", "offset", "setVisible", "visible", "active", "getOffsetY", "<PERSON><PERSON><PERSON><PERSON>", "target", "destroyHealthBar", "destroy"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;6BAGjBI,W,WADZF,OAAO,CAAC,aAAD,C,2BAAR,MACaE,WADb,SACiCH,SADjC,CAC2C;AAAA;AAAA;;AAAA;;AAEjB;AAFiB,eAK/BI,UAL+B,GAKZ,IALY;AAKL;AALK,eAM/BC,MAN+B,GAMhB,IANgB;AAMT;AANS,eAO/BC,aAP+B,GAON,KAPM;AAAA;;AAQvC;AAEAC,QAAAA,KAAK,GAAG;AACJ,eAAKC,WAAL;AACA,eAAKJ,UAAL,GAAkB,KAAKK,IAAL,CAAUC,MAA5B;AACA,eAAKC,kBAAL;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACH;AAED;AACJ;AACA;AACI;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACJ;AACA;;;AACYL,QAAAA,WAAW,GAAG;AAClB,eAAKH,MAAL,GAAc,KAAKI,IAAL,CAAUK,KAAV,CAAgBC,cAAhB,CAA+B,QAA/B,CAAd;;AACA,cAAI,CAAC,KAAKV,MAAV,EAAkB;AACdO,YAAAA,OAAO,CAACI,KAAR,CAAc,wBAAd;AACA;AACH;AACJ;AAED;AACJ;AACA;;;AACYL,QAAAA,kBAAkB,GAAG;AACzB,cAAI,CAAC,KAAKN,MAAN,IAAgB,CAAC,KAAKD,UAA1B,EAAsC,OADb,CAGzB;;AACA,cAAMa,eAAe,GAAG,KAAKR,IAAL,CAAUS,aAAlC,CAJyB,CAMzB;;AACA,eAAKT,IAAL,CAAUU,SAAV,CAAoB,KAAKd,MAAzB,EAPyB,CASzB;;AACA,eAAKI,IAAL,CAAUW,gBAAV,CAA2BH,eAA3B;AAEA,eAAKX,aAAL,GAAqB,IAArB;AACAM,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACH;;AAEDQ,QAAAA,MAAM,GAAG;AACL,cAAI,CAAC,KAAKf,aAAN,IAAuB,CAAC,KAAKF,UAA7B,IAA2C,CAAC,KAAKC,MAArD,EAA6D;AACzD;AACH;;AAED,eAAKiB,cAAL;AACH;AAED;AACJ;AACA;AACA;;;AACYA,QAAAA,cAAc,GAAG;AACrB;AACA,cAAMC,cAAc,GAAG,KAAKnB,UAAL,CAAgBc,aAAvC,CAFqB,CAIrB;;AACA,eAAKT,IAAL,CAAUW,gBAAV,CACIG,cAAc,CAACC,CADnB,EAEID,cAAc,CAACE,CAAf,GAAmB,KAAKC,OAF5B,EAGIH,cAAc,CAACI,CAHnB,EALqB,CAWrB;;AACA,eAAKlB,IAAL,CAAUmB,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,UAAU,CAACC,MAAD,EAAiB;AAC9B,eAAKJ,OAAL,GAAeI,MAAf;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,eAAKvB,IAAL,CAAUwB,MAAV,GAAmBD,OAAnB;AACH;AAED;AACJ;AACA;;;AACWE,QAAAA,UAAU,GAAW;AACxB,iBAAO,KAAKR,OAAZ;AACH;AAED;AACJ;AACA;;;AACWS,QAAAA,SAAS,CAACC,MAAD,EAAe;AAC3B,eAAKhC,UAAL,GAAkBgC,MAAlB;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,gBAAgB,GAAG;AACtB,eAAK5B,IAAL,CAAU6B,OAAV;AACH;;AA3HsC,O,0EACtCpC,Q;;;;;iBACiB,E", "sourcesContent": ["import { _decorator, Component, Node, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('HealthBarUI')\nexport class HealthBarUI extends Component {\n    @property\n    offsetY: number = 50; // 血条在车辆上方的偏移距离\n    \n\n    private targetNode: Node = null!; // 目标AI车辆节点\n    private canvas: Node = null!; // Canvas节点\n    private isInitialized: boolean = false;\n    // private _progress: number = 1.0; // 当前进度（0-1）\n\n    start() {\n        this.setupCanvas();\n        this.targetNode = this.node.parent!;\n        this.separateFromParent();\n        console.log('血条UI初始化完成');\n    }\n\n    /**\n     * 获取当前进度\n     */\n    // public get progress(): number {\n    //     return this.progress;\n    // }\n\n    // /**\n    //  * 设置当前进度（0-1）\n    //  */\n    // public set progress(value: number) {\n    //     this.progress = Math.max(0, Math.min(1, value));\n    // }\n\n\n\n    /**\n     * 设置Canvas引用\n     */\n    private setupCanvas() {\n        this.canvas = this.node.scene.getChildByName('Canvas');\n        if (!this.canvas) {\n            console.error('未找到Canvas节点，血条可能无法正确显示');\n            return;\n        }\n    }\n\n    /**\n     * 将血条从父节点中分离，移动到Canvas下\n     */\n    private separateFromParent() {\n        if (!this.canvas || !this.targetNode) return;\n\n        // 记录当前世界位置\n        const currentWorldPos = this.node.worldPosition;\n        \n        // 将血条移动到Canvas下\n        this.node.setParent(this.canvas);\n        \n        // 保持世界位置不变\n        this.node.setWorldPosition(currentWorldPos);\n        \n        this.isInitialized = true;\n        console.log('血条已分离到Canvas下');\n    }\n\n    update() {\n        if (!this.isInitialized || !this.targetNode || !this.canvas) {\n            return;\n        }\n\n        this.updatePosition();\n    }\n\n    /**\n     * 更新血条位置\n     * 血条现在是Canvas的子节点，完全独立于AI车辆\n     */\n    private updatePosition() {\n        // 获取AI车辆的世界位置\n        const targetWorldPos = this.targetNode.worldPosition;\n        \n        // 设置血条位置（在AI车辆上方）\n        this.node.setWorldPosition(\n            targetWorldPos.x,\n            targetWorldPos.y + this.offsetY,\n            targetWorldPos.z\n        );\n        \n        // 确保血条始终面向屏幕（不随AI车辆旋转）\n        this.node.setRotationFromEuler(0, 0, 0);\n    }\n\n    /**\n     * 设置血条在车辆上方的偏移距离\n     */\n    public setOffsetY(offset: number) {\n        this.offsetY = offset;\n    }\n\n    /**\n     * 显示或隐藏血条\n     */\n    public setVisible(visible: boolean) {\n        this.node.active = visible;\n    }\n\n    /**\n     * 获取当前偏移距离\n     */\n    public getOffsetY(): number {\n        return this.offsetY;\n    }\n\n    /**\n     * 设置目标节点\n     */\n    public setTarget(target: Node) {\n        this.targetNode = target;\n    }\n\n    /**\n     * 销毁血条\n     */\n    public destroyHealthBar() {\n        this.node.destroy();\n    }\n} "]}