{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts"], "names": ["_decorator", "Component", "Label", "<PERSON><PERSON>", "ProgressBar", "Node", "GameManager", "ccclass", "property", "GameHUD", "type", "tooltip", "updateTimer", "gameManager", "onLoad", "start", "getInstance", "console", "error", "initializeAIRatioDisplay", "initializeShootButton", "initializeTouchControlButtons", "update", "deltaTime", "updateInterval", "updateCountdownDisplay", "updatePaintRatioDisplay", "updateAmmoDisplay", "countdown<PERSON><PERSON>l", "formattedTime", "getFormattedRemainingTime", "string", "remainingTime", "getRemainingTime", "color", "lerp", "constructor", "allRatios", "getAllVehiclePaintRatios", "player<PERSON><PERSON><PERSON>", "updatePlayerRatioDisplay", "updateAIRatiosDisplay", "ratio", "percentage", "Math", "round", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "_allRatios", "sortedRatios", "getSortedVehiclePaintRatios", "aiRatios", "filter", "item", "vehicleId", "aiLabels", "ai1RatioLabel", "ai2RatioLabel", "ai3RatioLabel", "ai4RatioLabel", "for<PERSON>ach", "ratioData", "index", "length", "displayName", "getAIDisplayName", "i", "startsWith", "parts", "split", "label", "warn", "resetHUD", "shootButton", "node", "on", "EventType", "CLICK", "onShootButtonClicked", "playerShoot", "playerComponent", "getPlayerComponent", "ammoLabel", "currentAmmo", "getCurrentAmmo", "maxAmmo", "getMaxAmmo", "reloadProgressBar", "isReloading", "active", "progress", "getReloadProgress", "upButton", "TOUCH_START", "onUpButtonPressed", "TOUCH_END", "onUpButtonReleased", "TOUCH_CANCEL", "downButton", "onDownButtonPressed", "onDownButtonReleased", "leftButton", "onLeftButtonPressed", "onLeftButtonReleased", "rightB<PERSON>on", "onRightButtonPressed", "onRightButtonReleased", "log", "setAcceleration", "setDirection"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAEnDC,MAAAA,W,iBAAAA,W;;;;;;;;;OADH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;AAG9B;AACA;AACA;AACA;yBAEaS,O,WADZF,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAACN,KAAD,C,UAIRM,QAAQ,CAACN,KAAD,C,UAIRM,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAER,KADA;AAENS,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAER,KADA;AAENS,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAER,KADA;AAENS,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAER,KADA;AAENS,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAORH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,MADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,UAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAER,KADA;AAENS,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEN,WADA;AAENO,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAORH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,MADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,MADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,MADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,WAMRH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEP,MADA;AAENQ,QAAAA,OAAO,EAAE;AAFH,OAAD,C,2BA1Eb,MACaF,OADb,SAC6BR,SAD7B,CACuC;AAAA;AAAA;;AAEnC;AAFmC;;AAMnC;AANmC;;AAUnC;AAVmC;;AAAA;;AAAA;;AAAA;;AAmCnC;AAnCmC;;AAAA;;AAAA;;AAsDnC;AAtDmC;;AAAA;;AAAA;;AAAA;;AA+EnC;AA/EmC;;AAiFL;AAjFK,eAmF3BW,WAnF2B,GAmFL,CAnFK;AAAA,eAoF3BC,WApF2B,GAoFA,IApFA;AAAA;;AAsFnCC,QAAAA,MAAM,GAAG;AACL,eAAKF,WAAL,GAAmB,CAAnB;AACH;;AAEDG,QAAAA,KAAK,GAAG;AACJ,eAAKF,WAAL,GAAmB;AAAA;AAAA,0CAAYG,WAAZ,EAAnB;;AACA,cAAI,CAAC,KAAKH,WAAV,EAAuB;AACnBI,YAAAA,OAAO,CAACC,KAAR,CAAc,yBAAd;AACA;AACH,WALG,CAOJ;;;AACA,eAAKC,wBAAL,GARI,CAUJ;;AACA,eAAKC,qBAAL,GAXI,CAaJ;;AACA,eAAKC,6BAAL;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,CAAC,KAAKV,WAAV,EAAuB;AAEvB,eAAKD,WAAL,IAAoBW,SAApB,CAHsB,CAKtB;;AACA,cAAI,KAAKX,WAAL,IAAoB,KAAKY,cAA7B,EAA6C;AACzC,iBAAKC,sBAAL;AACA,iBAAKC,uBAAL;AACA,iBAAKC,iBAAL;AACA,iBAAKf,WAAL,GAAmB,CAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACYa,QAAAA,sBAAsB,GAAS;AACnC,cAAI,KAAKG,cAAT,EAAyB;AACrB,gBAAMC,aAAa,GAAG,KAAKhB,WAAL,CAAiBiB,yBAAjB,EAAtB;AACA,iBAAKF,cAAL,CAAoBG,MAApB,GAA6BF,aAA7B,CAFqB,CAIrB;;AACA,gBAAMG,aAAa,GAAG,KAAKnB,WAAL,CAAiBoB,gBAAjB,EAAtB;;AACA,gBAAID,aAAa,IAAI,EAArB,EAAyB;AACrB,mBAAKJ,cAAL,CAAoBM,KAApB,GAA4B,KAAKN,cAAL,CAAoBM,KAApB,CAA0BC,IAA1B,CACxB,IAAK,KAAKP,cAAL,CAAoBM,KAApB,CAA0BE,WAA/B,CAAmD,GAAnD,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,GAA9D,CADwB,EAExB,GAFwB,CAA5B;AAIH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYV,QAAAA,uBAAuB,GAAS;AACpC,cAAMW,SAAS,GAAG,KAAKxB,WAAL,CAAiByB,wBAAjB,EAAlB,CADoC,CAGpC;;AACA,cAAMC,WAAW,GAAGF,SAAS,CAAC,QAAD,CAAT,IAAuB,CAA3C;AACA,eAAKG,wBAAL,CAA8BD,WAA9B,EALoC,CAOpC;;AACA,eAAKE,qBAAL,CAA2BJ,SAA3B;AACH;AAED;AACJ;AACA;AACA;;;AACYG,QAAAA,wBAAwB,CAACE,KAAD,EAAsB;AAClD,cAAMC,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWH,KAAK,GAAG,GAAnB,CAAnB;;AAEA,cAAI,KAAKI,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBf,MAAtB,gBAA0CY,UAA1C;AACH,WALiD,CAOlD;AACA;AACA;;AACH;AAED;AACJ;AACA;AACA;;;AACYF,QAAAA,qBAAqB,CAACM,UAAD,EAAoD;AAC7E;AACA,cAAMC,YAAY,GAAG,KAAKnC,WAAL,CAAiBoC,2BAAjB,EAArB,CAF6E,CAI7E;;AACA,cAAMC,QAAQ,GAAGF,YAAY,CAACG,MAAb,CAAoBC,IAAI,IAAIA,IAAI,CAACC,SAAL,KAAmB,QAA/C,CAAjB,CAL6E,CAO7E;;AACA,cAAMC,QAAQ,GAAG,CAAC,KAAKC,aAAN,EAAqB,KAAKC,aAA1B,EAAyC,KAAKC,aAA9C,EAA6D,KAAKC,aAAlE,CAAjB,CAR6E,CAU7E;;AACAR,UAAAA,QAAQ,CAACS,OAAT,CAAiB,CAACC,SAAD,EAAYC,KAAZ,KAAsB;AACnC,gBAAIA,KAAK,GAAGP,QAAQ,CAACQ,MAAjB,IAA2BR,QAAQ,CAACO,KAAD,CAAvC,EAAgD;AAC5C,kBAAMlB,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWe,SAAS,CAAClB,KAAV,GAAkB,GAA7B,CAAnB;AACA,kBAAMqB,WAAW,GAAG,KAAKC,gBAAL,CAAsBJ,SAAS,CAACP,SAAhC,CAApB;AACAC,cAAAA,QAAQ,CAACO,KAAD,CAAR,CAAgB9B,MAAhB,GAA4BgC,WAA5B,UAA4CpB,UAA5C;AACH;AACJ,WAND,EAX6E,CAmB7E;;AACA,eAAK,IAAIsB,CAAC,GAAGf,QAAQ,CAACY,MAAtB,EAA8BG,CAAC,GAAGX,QAAQ,CAACQ,MAA3C,EAAmDG,CAAC,EAApD,EAAwD;AACpD,gBAAIX,QAAQ,CAACW,CAAD,CAAZ,EAAiB;AACbX,cAAAA,QAAQ,CAACW,CAAD,CAAR,CAAYlC,MAAZ,GAAqB,EAArB;AACH;AACJ;AACJ;AAID;AACJ;AACA;AACA;AACA;;;AACYiC,QAAAA,gBAAgB,CAACX,SAAD,EAA4B;AAChD;AACA,cAAIA,SAAS,CAACa,UAAV,CAAqB,KAArB,CAAJ,EAAiC;AAC7B,gBAAMC,KAAK,GAAGd,SAAS,CAACe,KAAV,CAAgB,GAAhB,CAAd;;AACA,gBAAID,KAAK,CAACL,MAAN,IAAgB,CAApB,EAAuB;AACnB,6BAAaK,KAAK,CAAC,CAAD,CAAlB;AACH;AACJ;;AACD,iBAAOd,SAAP;AACH;AAED;AACJ;AACA;;;AACYlC,QAAAA,wBAAwB,GAAS;AACrC;AACA,cAAMmC,QAAQ,GAAG,CAAC,KAAKC,aAAN,EAAqB,KAAKC,aAA1B,EAAyC,KAAKC,aAA9C,EAA6D,KAAKC,aAAlE,CAAjB;AAEAJ,UAAAA,QAAQ,CAACK,OAAT,CAAiB,CAACU,KAAD,EAAQR,KAAR,KAAkB;AAC/B,gBAAIQ,KAAJ,EAAW;AACPA,cAAAA,KAAK,CAACtC,MAAN,YAAqB8B,KAAK,GAAG,CAA7B;AACH,aAFD,MAEO;AACH5C,cAAAA,OAAO,CAACqD,IAAR,kBAA2BT,KAAK,GAAG,CAAnC;AACH;AACJ,WAND;AAOH;AAED;AACJ;AACA;;;AACWU,QAAAA,QAAQ,GAAS;AACpB,cAAI,KAAK3C,cAAT,EAAyB;AACrB,iBAAKA,cAAL,CAAoBG,MAApB,GAA6B,OAA7B;AACA,iBAAKH,cAAL,CAAoBM,KAApB,GAA4B,IAAK,KAAKN,cAAL,CAAoBM,KAApB,CAA0BE,WAA/B,CAAmD,GAAnD,EAAwD,GAAxD,EAA6D,GAA7D,EAAkE,GAAlE,CAA5B;AACH;;AAED,cAAI,KAAKU,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBf,MAAtB,GAA+B,YAA/B;AACH;;AAED,eAAKZ,wBAAL;AACH,SAzPkC,CA2PnC;;AAEA;AACJ;AACA;;;AACYC,QAAAA,qBAAqB,GAAS;AAClC,cAAI,KAAKoD,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBvE,MAAM,CAACwE,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,oBAAtD,EAA4E,IAA5E;AACH,WAFD,MAEO;AACH5D,YAAAA,OAAO,CAACqD,IAAR,CAAa,kBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYO,QAAAA,oBAAoB,GAAS;AACjC;AACA,cAAI,KAAKhE,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBiE,WAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACYnD,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,CAAC,KAAKd,WAAV,EAAuB;AAEvB,cAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;AACA,cAAI,CAACD,eAAL,EAAsB,OAJQ,CAM9B;;AACA,cAAI,KAAKE,SAAT,EAAoB;AAChB,gBAAMC,WAAW,GAAGH,eAAe,CAACI,cAAhB,EAApB;AACA,gBAAMC,OAAO,GAAGL,eAAe,CAACM,UAAhB,EAAhB;AACA,iBAAKJ,SAAL,CAAelD,MAAf,GAA2BmD,WAA3B,SAA0CE,OAA1C;AACH,WAX6B,CAa9B;;;AACA,cAAI,KAAKE,iBAAT,EAA4B;AACxB,gBAAIP,eAAe,CAACQ,WAAhB,EAAJ,EAAmC;AAC/B,mBAAKD,iBAAL,CAAuBb,IAAvB,CAA4Be,MAA5B,GAAqC,IAArC;AACA,mBAAKF,iBAAL,CAAuBG,QAAvB,GAAkCV,eAAe,CAACW,iBAAhB,EAAlC;AACH,aAHD,MAGO;AACH,mBAAKJ,iBAAL,CAAuBb,IAAvB,CAA4Be,MAA5B,GAAqC,KAArC;AACH;AACJ;AACJ,SA3SkC,CA6SnC;;AAEA;AACJ;AACA;;;AACYnE,QAAAA,6BAA6B,GAAS;AAC1C;AACA,cAAI,KAAKsE,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAclB,IAAd,CAAmBC,EAAnB,CAAsBrE,IAAI,CAACsE,SAAL,CAAeiB,WAArC,EAAkD,KAAKC,iBAAvD,EAA0E,IAA1E;AACA,iBAAKF,QAAL,CAAclB,IAAd,CAAmBC,EAAnB,CAAsBrE,IAAI,CAACsE,SAAL,CAAemB,SAArC,EAAgD,KAAKC,kBAArD,EAAyE,IAAzE;AACA,iBAAKJ,QAAL,CAAclB,IAAd,CAAmBC,EAAnB,CAAsBrE,IAAI,CAACsE,SAAL,CAAeqB,YAArC,EAAmD,KAAKD,kBAAxD,EAA4E,IAA5E;AACH,WAJD,MAIO;AACH9E,YAAAA,OAAO,CAACqD,IAAR,CAAa,kBAAb;AACH,WARyC,CAU1C;;;AACA,cAAI,KAAK2B,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBxB,IAAhB,CAAqBC,EAArB,CAAwBrE,IAAI,CAACsE,SAAL,CAAeiB,WAAvC,EAAoD,KAAKM,mBAAzD,EAA8E,IAA9E;AACA,iBAAKD,UAAL,CAAgBxB,IAAhB,CAAqBC,EAArB,CAAwBrE,IAAI,CAACsE,SAAL,CAAemB,SAAvC,EAAkD,KAAKK,oBAAvD,EAA6E,IAA7E;AACA,iBAAKF,UAAL,CAAgBxB,IAAhB,CAAqBC,EAArB,CAAwBrE,IAAI,CAACsE,SAAL,CAAeqB,YAAvC,EAAqD,KAAKG,oBAA1D,EAAgF,IAAhF;AACH,WAJD,MAIO;AACHlF,YAAAA,OAAO,CAACqD,IAAR,CAAa,kBAAb;AACH,WAjByC,CAmB1C;;;AACA,cAAI,KAAK8B,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgB3B,IAAhB,CAAqBC,EAArB,CAAwBrE,IAAI,CAACsE,SAAL,CAAeiB,WAAvC,EAAoD,KAAKS,mBAAzD,EAA8E,IAA9E;AACA,iBAAKD,UAAL,CAAgB3B,IAAhB,CAAqBC,EAArB,CAAwBrE,IAAI,CAACsE,SAAL,CAAemB,SAAvC,EAAkD,KAAKQ,oBAAvD,EAA6E,IAA7E;AACA,iBAAKF,UAAL,CAAgB3B,IAAhB,CAAqBC,EAArB,CAAwBrE,IAAI,CAACsE,SAAL,CAAeqB,YAAvC,EAAqD,KAAKM,oBAA1D,EAAgF,IAAhF;AACH,WAJD,MAIO;AACHrF,YAAAA,OAAO,CAACqD,IAAR,CAAa,kBAAb;AACH,WA1ByC,CA4B1C;;;AACA,cAAI,KAAKiC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB9B,IAAjB,CAAsBC,EAAtB,CAAyBrE,IAAI,CAACsE,SAAL,CAAeiB,WAAxC,EAAqD,KAAKY,oBAA1D,EAAgF,IAAhF;AACA,iBAAKD,WAAL,CAAiB9B,IAAjB,CAAsBC,EAAtB,CAAyBrE,IAAI,CAACsE,SAAL,CAAemB,SAAxC,EAAmD,KAAKW,qBAAxD,EAA+E,IAA/E;AACA,iBAAKF,WAAL,CAAiB9B,IAAjB,CAAsBC,EAAtB,CAAyBrE,IAAI,CAACsE,SAAL,CAAeqB,YAAxC,EAAsD,KAAKS,qBAA3D,EAAkF,IAAlF;AACH,WAJD,MAIO;AACHxF,YAAAA,OAAO,CAACqD,IAAR,CAAa,kBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYuB,QAAAA,iBAAiB,GAAS;AAC9B5E,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,oBAAZ;AACA3B,cAAAA,eAAe,CAAC4B,eAAhB,CAAgC,CAAhC,EAFiB,CAEmB;AACvC,aAHD,MAGO;AACH1F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYyB,QAAAA,kBAAkB,GAAS;AAC/B9E,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,oBAAZ;AACA3B,cAAAA,eAAe,CAAC4B,eAAhB,CAAgC,CAAhC,EAFiB,CAEmB;AACvC,aAHD,MAGO;AACH1F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACY4B,QAAAA,mBAAmB,GAAS;AAChCjF,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,qBAAZ;AACA3B,cAAAA,eAAe,CAAC4B,eAAhB,CAAgC,CAAC,CAAjC,EAFiB,CAEoB;AACxC,aAHD,MAGO;AACH1F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACY6B,QAAAA,oBAAoB,GAAS;AACjClF,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,oBAAZ;AACA3B,cAAAA,eAAe,CAAC4B,eAAhB,CAAgC,CAAhC,EAFiB,CAEmB;AACvC,aAHD,MAGO;AACH1F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACY+B,QAAAA,mBAAmB,GAAS;AAChCpF,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,oBAAZ;AACA3B,cAAAA,eAAe,CAAC6B,YAAhB,CAA6B,CAAC,CAA9B,EAFiB,CAEiB;AACrC,aAHD,MAGO;AACH3F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYgC,QAAAA,oBAAoB,GAAS;AACjCrF,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,mBAAZ;AACA3B,cAAAA,eAAe,CAAC6B,YAAhB,CAA6B,CAA7B,EAFiB,CAEgB;AACpC,aAHD,MAGO;AACH3F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYkC,QAAAA,oBAAoB,GAAS;AACjCvF,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,mBAAZ;AACA3B,cAAAA,eAAe,CAAC6B,YAAhB,CAA6B,CAA7B,EAFiB,CAEgB;AACpC,aAHD,MAGO;AACH3F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACYmC,QAAAA,qBAAqB,GAAS;AAClCxF,UAAAA,OAAO,CAACyF,GAAR,CAAY,kBAAZ;;AACA,cAAI,KAAK7F,WAAT,EAAsB;AAClB,gBAAMkE,eAAe,GAAG,KAAKlE,WAAL,CAAiBmE,kBAAjB,EAAxB;;AACA,gBAAID,eAAJ,EAAqB;AACjB9D,cAAAA,OAAO,CAACyF,GAAR,CAAY,mBAAZ;AACA3B,cAAAA,eAAe,CAAC6B,YAAhB,CAA6B,CAA7B,EAFiB,CAEgB;AACpC,aAHD,MAGO;AACH3F,cAAAA,OAAO,CAACqD,IAAR,CAAa,mBAAb;AACH;AACJ,WARD,MAQO;AACHrD,YAAAA,OAAO,CAACqD,IAAR,CAAa,yBAAb;AACH;AACJ;;AAtekC,O;;;;;iBAIX,I;;;;;;;iBAIE,I;;;;;;;iBAOH,I;;;;;;;iBAMA,I;;;;;;;iBAMA,I;;;;;;;iBAMA,I;;;;;;;iBAOD,I;;;;;;;iBAMH,I;;;;;;;iBAMc,I;;;;;;;iBAOd,I;;;;;;;iBAME,I;;;;;;;iBAMA,I;;;;;;;iBAMC,I;;0FAGrB9D,Q;;;;;iBACwB,G", "sourcesContent": ["import { _decorator, Component, Label, Button, ProgressBar, Node } from 'cc';\nconst { ccclass, property } = _decorator;\nimport { GameManager } from './GameManager';\n\n/**\n * 游戏内HUD界面\n * 显示倒计时和颜料占比信息\n */\n@ccclass('GameHUD')\nexport class GameHUD extends Component {\n\n    // 倒计时显示\n    @property(Label)\n    countdownLabel: Label = null!;\n\n    // 玩家颜料占比显示\n    @property(Label)\n    playerRatioLabel: Label = null!;\n\n    // AI颜料占比显示标签（手动拖拽设置）\n    @property({\n        type: Label,\n        tooltip: 'AI车辆1的颜料占比显示标签'\n    })\n    ai1RatioLabel: Label = null!;\n\n    @property({\n        type: Label,\n        tooltip: 'AI车辆2的颜料占比显示标签'\n    })\n    ai2RatioLabel: Label = null!;\n\n    @property({\n        type: Label,\n        tooltip: 'AI车辆3的颜料占比显示标签'\n    })\n    ai3RatioLabel: Label = null!;\n\n    @property({\n        type: Label,\n        tooltip: 'AI车辆4的颜料占比显示标签'\n    })\n    ai4RatioLabel: Label = null!;\n\n    // 射击系统UI\n    @property({\n        type: Button,\n        tooltip: '射击按钮'\n    })\n    shootButton: Button = null!;\n\n    @property({\n        type: Label,\n        tooltip: '弹药数量显示标签'\n    })\n    ammoLabel: Label = null!;\n\n    @property({\n        type: ProgressBar,\n        tooltip: '弹药补充进度条'\n    })\n    reloadProgressBar: ProgressBar = null!;\n\n    // 触摸控制按钮\n    @property({\n        type: Button,\n        tooltip: '向上移动按钮'\n    })\n    upButton: Button = null!;\n\n    @property({\n        type: Button,\n        tooltip: '向下移动按钮'\n    })\n    downButton: Button = null!;\n\n    @property({\n        type: Button,\n        tooltip: '向左移动按钮'\n    })\n    leftButton: Button = null!;\n\n    @property({\n        type: Button,\n        tooltip: '向右移动按钮'\n    })\n    rightButton: Button = null!;\n    \n    // 更新频率控制\n    @property\n    updateInterval: number = 0.1; // 每0.1秒更新一次\n    \n    private updateTimer: number = 0;\n    private gameManager: GameManager = null!;\n\n    onLoad() {\n        this.updateTimer = 0;\n    }\n\n    start() {\n        this.gameManager = GameManager.getInstance();\n        if (!this.gameManager) {\n            console.error('GameHUD: GameManager未找到');\n            return;\n        }\n\n        // 初始化AI占比显示\n        this.initializeAIRatioDisplay();\n\n        // 初始化射击按钮\n        this.initializeShootButton();\n\n        // 初始化触摸控制按钮\n        this.initializeTouchControlButtons();\n    }\n\n    update(deltaTime: number) {\n        if (!this.gameManager) return;\n        \n        this.updateTimer += deltaTime;\n        \n        // 按设定频率更新UI\n        if (this.updateTimer >= this.updateInterval) {\n            this.updateCountdownDisplay();\n            this.updatePaintRatioDisplay();\n            this.updateAmmoDisplay();\n            this.updateTimer = 0;\n        }\n    }\n\n    /**\n     * 更新倒计时显示\n     */\n    private updateCountdownDisplay(): void {\n        if (this.countdownLabel) {\n            const formattedTime = this.gameManager.getFormattedRemainingTime();\n            this.countdownLabel.string = formattedTime;\n            \n            // 当时间少于30秒时，可以改变颜色提醒\n            const remainingTime = this.gameManager.getRemainingTime();\n            if (remainingTime <= 30) {\n                this.countdownLabel.color = this.countdownLabel.color.lerp(\n                    new (this.countdownLabel.color.constructor as any)(255, 0, 0, 255),\n                    0.5\n                );\n            }\n        }\n    }\n\n    /**\n     * 更新颜料占比显示\n     */\n    private updatePaintRatioDisplay(): void {\n        const allRatios = this.gameManager.getAllVehiclePaintRatios();\n        \n        // 更新玩家占比\n        const playerRatio = allRatios['player'] || 0;\n        this.updatePlayerRatioDisplay(playerRatio);\n        \n        // 更新AI占比\n        this.updateAIRatiosDisplay(allRatios);\n    }\n\n    /**\n     * 更新玩家占比显示\n     * @param ratio 占比（0-1）\n     */\n    private updatePlayerRatioDisplay(ratio: number): void {\n        const percentage = Math.round(ratio * 100);\n        \n        if (this.playerRatioLabel) {\n            this.playerRatioLabel.string = `player: ${percentage}%`;\n        }\n        \n        // if (this.playerRatioBar) {\n        //     this.playerRatioBar.progress = ratio;\n        // }\n    }\n\n    /**\n     * 更新AI占比显示\n     * @param allRatios 所有车辆的占比\n     */\n    private updateAIRatiosDisplay(_allRatios: { [vehicleId: string]: number }): void {\n        // 获取排序后的占比数据\n        const sortedRatios = this.gameManager.getSortedVehiclePaintRatios();\n\n        // 只显示AI车辆（排除玩家）\n        const aiRatios = sortedRatios.filter(item => item.vehicleId !== 'player');\n\n        // 获取AI标签数组\n        const aiLabels = [this.ai1RatioLabel, this.ai2RatioLabel, this.ai3RatioLabel, this.ai4RatioLabel];\n\n        // 更新每个AI的显示\n        aiRatios.forEach((ratioData, index) => {\n            if (index < aiLabels.length && aiLabels[index]) {\n                const percentage = Math.round(ratioData.ratio * 100);\n                const displayName = this.getAIDisplayName(ratioData.vehicleId);\n                aiLabels[index].string = `${displayName}: ${percentage}%`;\n            }\n        });\n\n        // 清空未使用的标签\n        for (let i = aiRatios.length; i < aiLabels.length; i++) {\n            if (aiLabels[i]) {\n                aiLabels[i].string = '';\n            }\n        }\n    }\n\n\n\n    /**\n     * 获取AI的显示名称\n     * @param vehicleId AI车辆ID\n     * @returns 显示名称\n     */\n    private getAIDisplayName(vehicleId: string): string {\n        // 从vehicleId中提取简化的显示名称\n        if (vehicleId.startsWith('ai_')) {\n            const parts = vehicleId.split('_');\n            if (parts.length >= 2) {\n                return `AI-${parts[1]}`;\n            }\n        }\n        return vehicleId;\n    }\n\n    /**\n     * 初始化AI占比显示\n     */\n    private initializeAIRatioDisplay(): void {\n        // 初始化所有AI标签为空字符串\n        const aiLabels = [this.ai1RatioLabel, this.ai2RatioLabel, this.ai3RatioLabel, this.ai4RatioLabel];\n\n        aiLabels.forEach((label, index) => {\n            if (label) {\n                label.string = `AI-${index + 1}: 0%`;\n            } else {\n                console.warn(`GameHUD: AI${index + 1}RatioLabel未设置`);\n            }\n        });\n    }\n\n    /**\n     * 重置HUD显示\n     */\n    public resetHUD(): void {\n        if (this.countdownLabel) {\n            this.countdownLabel.string = \"02:00\";\n            this.countdownLabel.color = new (this.countdownLabel.color.constructor as any)(255, 255, 255, 255);\n        }\n\n        if (this.playerRatioLabel) {\n            this.playerRatioLabel.string = \"player: 0%\";\n        }\n\n        this.initializeAIRatioDisplay();\n    }\n\n    // ==================== 射击系统UI ====================\n\n    /**\n     * 初始化射击按钮\n     */\n    private initializeShootButton(): void {\n        if (this.shootButton) {\n            this.shootButton.node.on(Button.EventType.CLICK, this.onShootButtonClicked, this);\n        } else {\n            console.warn('GameHUD: 射击按钮未设置');\n        }\n    }\n\n    /**\n     * 射击按钮点击事件处理\n     */\n    private onShootButtonClicked(): void {\n        // 通知GameManager执行射击\n        if (this.gameManager) {\n            this.gameManager.playerShoot();\n        }\n    }\n\n    /**\n     * 更新弹药显示\n     */\n    private updateAmmoDisplay(): void {\n        if (!this.gameManager) return;\n\n        const playerComponent = this.gameManager.getPlayerComponent();\n        if (!playerComponent) return;\n\n        // 更新弹药数量显示\n        if (this.ammoLabel) {\n            const currentAmmo = playerComponent.getCurrentAmmo();\n            const maxAmmo = playerComponent.getMaxAmmo();\n            this.ammoLabel.string = `${currentAmmo}/${maxAmmo}`;\n        }\n\n        // 更新弹药补充进度条\n        if (this.reloadProgressBar) {\n            if (playerComponent.isReloading()) {\n                this.reloadProgressBar.node.active = true;\n                this.reloadProgressBar.progress = playerComponent.getReloadProgress();\n            } else {\n                this.reloadProgressBar.node.active = false;\n            }\n        }\n    }\n\n    // ==================== 触摸控制系统 ====================\n\n    /**\n     * 初始化触摸控制按钮\n     */\n    private initializeTouchControlButtons(): void {\n        // 初始化上移按钮\n        if (this.upButton) {\n            this.upButton.node.on(Node.EventType.TOUCH_START, this.onUpButtonPressed, this);\n            this.upButton.node.on(Node.EventType.TOUCH_END, this.onUpButtonReleased, this);\n            this.upButton.node.on(Node.EventType.TOUCH_CANCEL, this.onUpButtonReleased, this);\n        } else {\n            console.warn('GameHUD: 上移按钮未设置');\n        }\n\n        // 初始化下移按钮\n        if (this.downButton) {\n            this.downButton.node.on(Node.EventType.TOUCH_START, this.onDownButtonPressed, this);\n            this.downButton.node.on(Node.EventType.TOUCH_END, this.onDownButtonReleased, this);\n            this.downButton.node.on(Node.EventType.TOUCH_CANCEL, this.onDownButtonReleased, this);\n        } else {\n            console.warn('GameHUD: 下移按钮未设置');\n        }\n\n        // 初始化左移按钮\n        if (this.leftButton) {\n            this.leftButton.node.on(Node.EventType.TOUCH_START, this.onLeftButtonPressed, this);\n            this.leftButton.node.on(Node.EventType.TOUCH_END, this.onLeftButtonReleased, this);\n            this.leftButton.node.on(Node.EventType.TOUCH_CANCEL, this.onLeftButtonReleased, this);\n        } else {\n            console.warn('GameHUD: 左移按钮未设置');\n        }\n\n        // 初始化右移按钮\n        if (this.rightButton) {\n            this.rightButton.node.on(Node.EventType.TOUCH_START, this.onRightButtonPressed, this);\n            this.rightButton.node.on(Node.EventType.TOUCH_END, this.onRightButtonReleased, this);\n            this.rightButton.node.on(Node.EventType.TOUCH_CANCEL, this.onRightButtonReleased, this);\n        } else {\n            console.warn('GameHUD: 右移按钮未设置');\n        }\n    }\n\n    /**\n     * 上移按钮按下事件\n     */\n    private onUpButtonPressed(): void {\n        console.log('GameHUD: 上移按钮被按下');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家加速度为1');\n                playerComponent.setAcceleration(1); // 向前加速\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 上移按钮释放事件\n     */\n    private onUpButtonReleased(): void {\n        console.log('GameHUD: 上移按钮被释放');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家加速度为0');\n                playerComponent.setAcceleration(0); // 停止加速\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 下移按钮按下事件\n     */\n    private onDownButtonPressed(): void {\n        console.log('GameHUD: 下移按钮被按下');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家加速度为-1');\n                playerComponent.setAcceleration(-1); // 向后减速\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 下移按钮释放事件\n     */\n    private onDownButtonReleased(): void {\n        console.log('GameHUD: 下移按钮被释放');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家加速度为0');\n                playerComponent.setAcceleration(0); // 停止减速\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 左移按钮按下事件\n     */\n    private onLeftButtonPressed(): void {\n        console.log('GameHUD: 左移按钮被按下');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家转向为-1');\n                playerComponent.setDirection(-1); // 向左转向\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 左移按钮释放事件\n     */\n    private onLeftButtonReleased(): void {\n        console.log('GameHUD: 左移按钮被释放');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家转向为0');\n                playerComponent.setDirection(0); // 停止转向\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 右移按钮按下事件\n     */\n    private onRightButtonPressed(): void {\n        console.log('GameHUD: 右移按钮被按下');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家转向为1');\n                playerComponent.setDirection(1); // 向右转向\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n\n    /**\n     * 右移按钮释放事件\n     */\n    private onRightButtonReleased(): void {\n        console.log('GameHUD: 右移按钮被释放');\n        if (this.gameManager) {\n            const playerComponent = this.gameManager.getPlayerComponent();\n            if (playerComponent) {\n                console.log('GameHUD: 设置玩家转向为0');\n                playerComponent.setDirection(0); // 停止转向\n            } else {\n                console.warn('GameHUD: 无法获取玩家组件');\n            }\n        } else {\n            console.warn('GameHUD: GameManager未找到');\n        }\n    }\n}\n"]}