{"modules": {"cce:/internal/x/cc": {"mTimestamp": 2743.551583, "chunkId": "93ba276ea7b26ffcdc433fab14afc1ed6f05647b", "imports": [{"value": "cce:/internal/x/cc-fu/2d", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/sorting", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 14}, "end": {"line": 2, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/affine-transform", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 14}, "end": {"line": 3, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/animation", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/audio", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 14}, "end": {"line": 5, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/base", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/dragon-bones", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 50}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl2", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/graphics", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/intersection-2d", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/mask", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/particle-2d", "resolved": "__unresolved_13", "loc": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d", "resolved": "__unresolved_14", "loc": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-framework", "resolved": "__unresolved_15", "loc": {"start": {"line": 16, "column": 14}, "end": {"line": 16, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/profiler", "resolved": "__unresolved_16", "loc": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/rich-text", "resolved": "__unresolved_17", "loc": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/spine", "resolved": "__unresolved_18", "loc": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/tiled-map", "resolved": "__unresolved_19", "loc": {"start": {"line": 20, "column": 14}, "end": {"line": 20, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/tween", "resolved": "__unresolved_20", "loc": {"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/ui", "resolved": "__unresolved_21", "loc": {"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/video", "resolved": "__unresolved_22", "loc": {"start": {"line": 23, "column": 14}, "end": {"line": 23, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/webview", "resolved": "__unresolved_23", "loc": {"start": {"line": 24, "column": 14}, "end": {"line": 24, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/sorting"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/affine-transform"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/audio"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/base"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/dragon-bones"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl2"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/graphics"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/intersection-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/mask"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/particle-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-framework"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/profiler"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/rich-text"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/spine"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tiled-map"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tween"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/video"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/webview"}, "messages": []}]}, "cce:/internal/x/prerequisite-imports": {"mTimestamp": 26116190.961167, "chunkId": "6d8fd2b0177941b032ddc0733af48a561fb60657", "imports": [{"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts", "resolved": "__unresolved_0", "loc": {"start": {"line": 4, "column": 7}, "end": {"line": 4, "column": 196}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts", "resolved": "__unresolved_1", "loc": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": 200}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts", "resolved": "__unresolved_2", "loc": {"start": {"line": 6, "column": 7}, "end": {"line": 6, "column": 209}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts", "resolved": "__unresolved_3", "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": 205}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts", "resolved": "__unresolved_4", "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 8, "column": 204}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts", "resolved": "__unresolved_5", "loc": {"start": {"line": 9, "column": 7}, "end": {"line": 9, "column": 205}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts", "resolved": "__unresolved_6", "loc": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": 162}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts", "resolved": "__unresolved_7", "loc": {"start": {"line": 11, "column": 7}, "end": {"line": 11, "column": 167}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts", "resolved": "__unresolved_8", "loc": {"start": {"line": 12, "column": 7}, "end": {"line": 12, "column": 171}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts", "resolved": "__unresolved_9", "loc": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": 168}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts", "resolved": "__unresolved_10", "loc": {"start": {"line": 14, "column": 7}, "end": {"line": 14, "column": 162}}}, {"value": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts", "resolved": "__unresolved_11", "loc": {"start": {"line": 15, "column": 7}, "end": {"line": 15, "column": 155}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts", "resolved": "__unresolved_12", "loc": {"start": {"line": 16, "column": 7}, "end": {"line": 16, "column": 96}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts", "resolved": "__unresolved_13", "loc": {"start": {"line": 17, "column": 7}, "end": {"line": 17, "column": 92}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts", "resolved": "__unresolved_14", "loc": {"start": {"line": 18, "column": 7}, "end": {"line": 18, "column": 90}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts", "resolved": "__unresolved_15", "loc": {"start": {"line": 19, "column": 7}, "end": {"line": 19, "column": 97}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts", "resolved": "__unresolved_16", "loc": {"start": {"line": 20, "column": 7}, "end": {"line": 20, "column": 102}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts", "resolved": "__unresolved_17", "loc": {"start": {"line": 21, "column": 7}, "end": {"line": 21, "column": 91}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts", "resolved": "__unresolved_18", "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": 95}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts", "resolved": "__unresolved_19", "loc": {"start": {"line": 23, "column": 7}, "end": {"line": 23, "column": 97}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts", "resolved": "__unresolved_20", "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": 95}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts", "resolved": "__unresolved_21", "loc": {"start": {"line": 25, "column": 7}, "end": {"line": 25, "column": 102}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts", "resolved": "__unresolved_22", "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 26, "column": 96}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts", "resolved": "__unresolved_23", "loc": {"start": {"line": 27, "column": 7}, "end": {"line": 27, "column": 93}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts", "resolved": "__unresolved_24", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 94}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts", "resolved": "__unresolved_25", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 96}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts", "resolved": "__unresolved_26", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 97}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts", "resolved": "__unresolved_27", "loc": {"start": {"line": 31, "column": 7}, "end": {"line": 31, "column": 97}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts", "resolved": "__unresolved_28", "loc": {"start": {"line": 32, "column": 7}, "end": {"line": 32, "column": 99}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts", "resolved": "__unresolved_29", "loc": {"start": {"line": 33, "column": 7}, "end": {"line": 33, "column": 97}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts", "resolved": "__unresolved_30", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 96}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts", "resolved": "__unresolved_31", "loc": {"start": {"line": 35, "column": 7}, "end": {"line": 35, "column": 92}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts", "resolved": "__unresolved_32", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 97}}}, {"value": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts", "resolved": "__unresolved_33", "loc": {"start": {"line": 37, "column": 7}, "end": {"line": 37, "column": 90}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts": {"mTimestamp": {"mtime": 1742837200000, "uuid": "11f3130e-c08c-47bb-a209-71d114594e6d"}, "chunkId": "10f25daca5a1665545ebc4efbd45bd86fed6143e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 36}}}, {"value": "./builtin-pipeline-pass", "resolved": "__unresolved_2", "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 38, "column": 32}}}, {"value": "./builtin-pipeline", "resolved": "__unresolved_3", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 27}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts": {"mTimestamp": {"mtime": 1742837200000, "uuid": "6f94083c-fc92-438b-a15b-a20ec61666c7"}, "chunkId": "936a458df8c410426dcbc8860ee5a32e64530057", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 11}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 32, "column": 40}, "end": {"line": 32, "column": 69}}}, {"value": "cc/env", "loc": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts": {"mTimestamp": {"mtime": 1742837200000, "uuid": "de1c2107-70c8-4021-8459-6399f24d01c6"}, "chunkId": "de51ae29bdc33d4721612db5b322a18ddcf7dd5c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts": {"mTimestamp": {"mtime": 1742837200000, "uuid": "cbf30902-517f-40dc-af90-a550bac27cf1"}, "chunkId": "f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 31, "column": 41}, "end": {"line": 31, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts": {"mTimestamp": {"mtime": 1742837200000, "uuid": "ff9b0199-ce04-4cfe-86cc-6c719f08d6e4"}, "chunkId": "60dfea53e1c17502cf4f0661946a964ad3ff0296", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 31, "column": 30}, "end": {"line": 31, "column": 38}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts": {"mTimestamp": {"mtime": 1742837200000, "uuid": "b2bd1fa7-8d7c-49c5-a158-df29a6d3a594"}, "chunkId": "c107986e77a987fe49f2e4b1c0dcc489194dbd3f", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 220}, "end": {"line": 1, "column": 224}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts": {"mTimestamp": {"mtime": 1754494573004.8481, "uuid": "f240925f-9753-4a81-ac73-a2a8210575a2"}, "chunkId": "b4ba6f604644fedf452e826105d75ce74a068d1f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts": {"mTimestamp": {"mtime": 1755579092415.3596, "uuid": "91c08d91-821d-4705-b576-bca425d89d56"}, "chunkId": "9b583689b76a894a84c2dd298f4c18778bad0e35", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 174}, "end": {"line": 1, "column": 178}}}, {"value": "./player", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}, {"value": "./AIController", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 45}}}, {"value": "./Bullet", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 37}}}, {"value": "./SoundManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPurchaseTest.ts": {"mTimestamp": {"mtime": 1752984193663.6465, "uuid": "4b4e4d80-e536-48c9-811a-9601e0bf5362"}, "chunkId": "b3579c24d3ff1737e64230f634d3755a64945629", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 61}, "end": {"line": 1, "column": 65}}}, {"value": "./PlayerManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 30}, "end": {"line": 2, "column": 47}}}, {"value": "./SelectManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 30}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts": {"mTimestamp": {"mtime": 1755696884223.6292, "uuid": "b67f4523-8daa-464a-8546-d89befbb2977"}, "chunkId": "d99ffa78595ef86f45405646d5cc14ad1480dee0", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 139}, "end": {"line": 1, "column": 143}}}, {"value": "./TempData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 37}}}, {"value": "./camera_follow", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 46}}}, {"value": "./player", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./AIController", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}, {"value": "./AIPlayer", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 37}}}, {"value": "./PlayerManager", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 47}}}, {"value": "./SceneTransition", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 32}, "end": {"line": 8, "column": 51}}}, {"value": "./SoundManager", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 45}}}, {"value": "./PaintManager", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 45}}}, {"value": "./GameOverPanel", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 47}}}, {"value": "./GameHUD", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 35}}}, {"value": "./Bullet", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts": {"mTimestamp": {"mtime": 1751880804437.5667, "uuid": "8e647ccd-65a0-4fe9-9a1f-6d0bf9e6510b"}, "chunkId": "def60514d455c04e33b1ba9bb9bba09a3d2891f2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 97}, "end": {"line": 1, "column": 101}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 39}, "end": {"line": 2, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManagerExample.ts 为起点找不到模块 \"./GameManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts": {"mTimestamp": {"mtime": 1754798392340.3665, "uuid": "850968dc-a180-4230-a0c6-b9496fca7201"}, "chunkId": "60586318bbf26c095fcfd4f6c478aec79fca8ba9", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 68}, "end": {"line": 1, "column": 72}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts": {"mTimestamp": {"mtime": 1751886004994.6763, "uuid": "79d6f812-9554-4262-84e1-fd743e611bf9"}, "chunkId": "26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/LevelGradeTest.ts": {"mTimestamp": {"mtime": 1752079216948.198, "uuid": "4296e978-bdb0-4cb9-9cdd-5f8311ca5d88"}, "chunkId": "00767e9b88c10342f88c206c15295b04b3da3147", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 61}, "end": {"line": 1, "column": 65}}}, {"value": "./PlayerManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 42}, "end": {"line": 2, "column": 59}}}, {"value": "./SelectManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 30}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts": {"mTimestamp": {"mtime": 1755699653826.926, "uuid": "0cf6405c-918a-40f1-b383-68a9fc739b7f"}, "chunkId": "2a304f0f942d7336e9dc095417a34a706c892e8f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "./SceneTransition", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 32}, "end": {"line": 2, "column": 51}}}, {"value": "./SoundManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 45}}}, {"value": "./PlayerManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts": {"mTimestamp": {"mtime": 1751880715966.924, "uuid": "5a320709-c4da-48a6-9e0a-f1f0884117fb"}, "chunkId": "9e5cb90874896d4d482aab7771a29bec1c95f34b", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 52}, "end": {"line": 1, "column": 56}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts": {"mTimestamp": {"mtime": 1754805724106.8003, "uuid": "66fbc94f-5230-4bee-8600-db121d4f07fe"}, "chunkId": "5747e286ea7bb225640df61cf460ffb63f9b975e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./PlayerManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 42}, "end": {"line": 2, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts": {"mTimestamp": {"mtime": 1755698486011.6167, "uuid": "1b6d6687-2973-4618-af7b-db52ef67b8c1"}, "chunkId": "0cef8fa3cb540388a42b92cbc6d47180cf46622f", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManagerExample.ts": {"mTimestamp": {"mtime": 1751106880479.3552, "uuid": "20b5e8dd-5831-40bc-a3c3-2eba62b0f2b3"}, "chunkId": "dd8654737f782dc7d2f444b1277fa4b83bb7ca70", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "./PlayerManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 42}, "end": {"line": 2, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts": {"mTimestamp": {"mtime": 1755699078438.1467, "uuid": "be7b2dc0-da35-4de9-a81c-30690fdcd28f"}, "chunkId": "b9c16df5862bbcca6e642da38ede09779782ac3e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 105}, "end": {"line": 1, "column": 109}}}, {"value": "./TempData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 37}}}, {"value": "./PlayerManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 30}, "end": {"line": 3, "column": 47}}}, {"value": "./SceneTransition", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 51}}}, {"value": "./CarPropertyDisplay", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 5, "column": 57}}}, {"value": "./PurchasePanel", "resolved": "__unresolved_5", "loc": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts": {"mTimestamp": {"mtime": 1751728327354.985, "uuid": "4702a2d2-89fb-4b88-a080-9712952901be"}, "chunkId": "91db69cfc7638e790a8b4a74e68aa446e097c982", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts": {"mTimestamp": {"mtime": 1751901119716.8408, "uuid": "01c08043-bfa2-4c83-957c-a194c966ef09"}, "chunkId": "17e79dac0666d01c9e57d34e63c22e4ed42135ef", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 97}, "end": {"line": 1, "column": 101}}}, {"value": "./player", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts 为起点找不到模块 \"./player\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/VehicleDestructionTest.ts 为起点找不到模块 \"./GameManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts": {"mTimestamp": {"mtime": 1751727963848.3662, "uuid": "daf21382-6b58-4c9b-aeb4-ace960c8ca29"}, "chunkId": "60f6ecf5cc17c1da6598d7f9202a78b81dc8873a", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 77}, "end": {"line": 1, "column": 81}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts": {"mTimestamp": {"mtime": 1755619268014.4219, "uuid": "b3924dc4-f7fe-491c-9736-a65419c312f5"}, "chunkId": "43ffc2180738b118a8d4d533f2af70ab9b24d0aa", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 204}, "end": {"line": 1, "column": 208}}}, {"value": "./AIPlayer", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}, {"value": "./Bullet", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 35}, "end": {"line": 6, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts"}, "messages": []}]}, "cce:/internal/code-quality/cr.mjs": {"mTimestamp": 1755707166905, "chunkId": "6a5019a719a9014c047e67aa1cf34453ab8392ce", "imports": [], "type": "esm", "resolutions": []}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts": {"mTimestamp": {"mtime": 1752764000376.482, "uuid": "8f875fcf-118c-49ad-b54d-b63acc93af0c"}, "chunkId": "f8773f37c39066f1e669860ad1c60516e354bc05", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 116}, "end": {"line": 1, "column": 120}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFaderTest.ts": {"mTimestamp": {"mtime": 1752762103518.6096, "uuid": "7163ca71-f80d-48df-86e8-8885f6dce345"}, "chunkId": "9c46c306d949fec57901ba361b2178b220f0fe43", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 53}, "end": {"line": 1, "column": 57}}}, {"value": "./SceneFader", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneFader.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts": {"mTimestamp": {"mtime": 1754151184867.3506, "uuid": "c161466d-78da-494f-9ff2-6a5b9ae13301"}, "chunkId": "74948618388cf8fc1977e55f6b358c932c03ec6a", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 74}, "end": {"line": 1, "column": 78}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransitionTest.ts": {"mTimestamp": {"mtime": 1753802056963.453, "uuid": "ffd93a01-4ecb-4372-9d0c-2ac2f1306c24"}, "chunkId": "4256ecfeb63147a2ab4609fba45c0f175b2236f4", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 63}, "end": {"line": 1, "column": 67}}}, {"value": "./SceneTransition", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 32}, "end": {"line": 2, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts": {"mTimestamp": {"mtime": 1754791136160.962, "uuid": "c4e943de-6ca3-46a0-9231-17cb43e8e456"}, "chunkId": "aaafdbc479e118e05b87b66f5a036fc792fd6c50", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 84}, "end": {"line": 2, "column": 88}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts": {"mTimestamp": {"mtime": 1755611718084.6843, "uuid": "7c385d18-836f-4804-ae30-5bd158cfb7f5"}, "chunkId": "ab66a6622ee4b8eeb035aa78898b50aa594decd6", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 83}, "end": {"line": 1, "column": 87}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/playerCar.ts": {"mTimestamp": {"mtime": 1753280881932.4333, "uuid": "b3924dc4-f7fe-491c-9736-a65419c312f5"}, "chunkId": "1e2ef3b58fd3ec5135ca8e627ac3e85cebd7302b", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 214}, "end": {"line": 1, "column": 218}}}, {"value": "./AIPlayer", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts": {"mTimestamp": {"mtime": 1755655155148.0142, "uuid": "796e3776-c599-4f75-9591-35aaeaae7fb6"}, "chunkId": "7ace6a5335481e920b9baff76698aea45fdf9be1", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts": {"mTimestamp": {"mtime": 1755611467905.7456, "uuid": "add913cb-f124-45b3-9292-5dcf066e9b47"}, "chunkId": "d1cee1a3354c09e396b1a69dbe10e9cb83fedf20", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 58}, "end": {"line": 1, "column": 62}}}, {"value": "./CarProperties", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 2, "column": 60}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/ConfigManager.ts": {"mTimestamp": {"mtime": 1753284657778.7651, "uuid": "c3a028ad-7e42-4812-9c4d-81c538024cd1"}, "chunkId": "1ed6239e7fc7d809d99c59b09a18453a697e539c", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 49}, "end": {"line": 1, "column": 53}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIController.ts": {"mTimestamp": {"mtime": 1751820288895.0928, "uuid": "922b0d89-4cee-4a0e-b072-f593d4a178c3"}, "chunkId": "1d1b7214d95e843352036965276d60ccca03c68e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIPlayer.ts": {"mTimestamp": {"mtime": 1753279544673.4695, "uuid": "ff477e7e-ceef-42c8-804f-c97803369c7b"}, "chunkId": "0fd07875260e4b943ba279813a982646920766d4", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 164}, "end": {"line": 1, "column": 168}}}, {"value": "./player", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIPlayer.ts 为起点找不到模块 \"./SoundManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts": {"mTimestamp": {"mtime": 1753194781431.8997, "uuid": "b67f4523-8daa-464a-8546-d89befbb2977"}, "chunkId": "e48173cf272181b55252156e11f5fe3df84e6598", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 127}, "end": {"line": 1, "column": 131}}}, {"value": "./TempData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 37}}}, {"value": "./camera_follow", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 46}}}, {"value": "./player", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./AIController", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}, {"value": "./AIPlayer", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 37}}}, {"value": "./PlayerManager", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 47}}}, {"value": "./SceneTransition", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 32}, "end": {"line": 8, "column": 51}}}, {"value": "./SoundManager", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts 为起点找不到模块 \"./TempData\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/camera_follow.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts 为起点找不到模块 \"./PlayerManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts 为起点找不到模块 \"./SceneTransition\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts 为起点找不到模块 \"./SoundManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameOverPanel.ts": {"mTimestamp": {"mtime": 1751880733138.6445, "uuid": "62b903fa-d761-4f2b-8016-ee288919f17a"}, "chunkId": "f1a0a0ef212d9da7bb0e2f101a7a556911c8315c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/HealthBarUI.ts": {"mTimestamp": {"mtime": 1751886004994.6763, "uuid": "79d6f812-9554-4262-84e1-fd743e611bf9"}, "chunkId": "35718309b6ce1e0f2356650c5e999fcf03a4056b", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/PausePanel.ts": {"mTimestamp": {"mtime": 1751880715966.924, "uuid": "5a320709-c4da-48a6-9e0a-f1f0884117fb"}, "chunkId": "435cfae2de1a6ab2118e992341a8cbc490385f11", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 52}, "end": {"line": 1, "column": 56}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/PlayerInfoUI.ts": {"mTimestamp": {"mtime": 1751376224317.8184, "uuid": "66fbc94f-5230-4bee-8600-db121d4f07fe"}, "chunkId": "79c60e6ab7d6c4ef7a423176b5223f78c6da12c8", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./PlayerManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 42}, "end": {"line": 2, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/PlayerInfoUI.ts 为起点找不到模块 \"./PlayerManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/camera_follow.ts": {"mTimestamp": {"mtime": 1751727963848.3662, "uuid": "daf21382-6b58-4c9b-aeb4-ace960c8ca29"}, "chunkId": "e57f0ab529ed1c91c618b5e19f8ddb5a0efa8afc", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 77}, "end": {"line": 1, "column": 81}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/player.ts": {"mTimestamp": {"mtime": 1753280881932.4333, "uuid": "b3924dc4-f7fe-491c-9736-a65419c312f5"}, "chunkId": "4471c727301985a91451c15d762e6bce4bbd3aef", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 214}, "end": {"line": 1, "column": 218}}}, {"value": "./AIPlayer", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/GameManager.ts"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/gamescene/player.ts 为起点找不到模块 \"./SoundManager\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts": {"mTimestamp": {"mtime": 1754746550522.3418, "uuid": "7dd7ac9d-7d90-4745-bb56-3c99a1d345df"}, "chunkId": "9c055d4007d002c90b7c1036ef6d1c7ec7d50367", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 112}, "end": {"line": 1, "column": 116}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts": {"mTimestamp": {"mtime": 1753796381049.1853, "uuid": "e9c7f25f-e46f-4cf1-9025-dae477143ca9"}, "chunkId": "140b5316d14e00f2df5f23208cfd6cf8dd64b85b", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 46}, "end": {"line": 1, "column": 50}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts": {"mTimestamp": {"mtime": 1754728500452.841, "uuid": "8d994a71-0534-4deb-9268-d898d9e0c007"}, "chunkId": "07f6d5c86652bda9da01d2590bb16b9aa9cca3d7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts": {"mTimestamp": {"mtime": 1755335566943.675, "uuid": "91ffccd6-b692-4a87-a2fe-51adeaa3b229"}, "chunkId": "d90bd5f660cca2468e55c522be6e08ed7db4093d", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 174}, "end": {"line": 1, "column": 178}}}, {"value": "./player", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}, {"value": "./AIPlayer", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 45}}}, {"value": "./GameManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintCoordinateTest.ts": {"mTimestamp": {"mtime": 1754730619028.834, "uuid": "949304f8-d71e-4d74-96be-bba313c71dbd"}, "chunkId": "6df7a6a48b1ef2c1800f13d516648cd0b0fef594", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 90}, "end": {"line": 1, "column": 94}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts": {"mTimestamp": {"mtime": 1754494573004.8481, "uuid": "f240925f-9753-4a81-ac73-a2a8210575a2"}, "chunkId": "754c8adada1366d19c52f9e4051a45ea2591d86e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts": {"mTimestamp": {"mtime": 1755579092415.3596, "uuid": "91c08d91-821d-4705-b576-bca425d89d56"}, "chunkId": "bffa694b6a42ea502801eb3e252af84ebdf912e3", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 174}, "end": {"line": 1, "column": 178}}}, {"value": "./player", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}, {"value": "./AIController", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 45}}}, {"value": "./Bullet", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 37}}}, {"value": "./SoundManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts": {"mTimestamp": {"mtime": 1755335566943.675, "uuid": "91ffccd6-b692-4a87-a2fe-51adeaa3b229"}, "chunkId": "9627b0d9d58257818228bafb8d66e482f9701299", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 174}, "end": {"line": 1, "column": 178}}}, {"value": "./player", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}, {"value": "./AIPlayer", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 45}}}, {"value": "./GameManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts": {"mTimestamp": {"mtime": 1755655155148.0142, "uuid": "796e3776-c599-4f75-9591-35aaeaae7fb6"}, "chunkId": "3a063ae6ce429763dd1200933f2c0af587c74ad8", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts": {"mTimestamp": {"mtime": 1755611467905.7456, "uuid": "add913cb-f124-45b3-9292-5dcf066e9b47"}, "chunkId": "90df3cbac4939c24c528c1e36504a3db0c6ac7df", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 58}, "end": {"line": 1, "column": 62}}}, {"value": "./CarProperties", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 2, "column": 60}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarProperties.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts": {"mTimestamp": {"mtime": 1754728500452.841, "uuid": "8d994a71-0534-4deb-9268-d898d9e0c007"}, "chunkId": "f5d0d9de8e98f9ba667b19cb4e3228cbc440561f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts": {"mTimestamp": {"mtime": 1755705412181.0977, "uuid": "b67f4523-8daa-464a-8546-d89befbb2977"}, "chunkId": "6e3db3d463ace5cac70cc212be5e384ebcc64b5f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 139}, "end": {"line": 1, "column": 143}}}, {"value": "./TempData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 37}}}, {"value": "./camera_follow", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 46}}}, {"value": "./player", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./AIController", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}, {"value": "./AIPlayer", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 37}}}, {"value": "./PlayerManager", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 47}}}, {"value": "./SceneTransition", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 32}, "end": {"line": 8, "column": 51}}}, {"value": "./SoundManager", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 45}}}, {"value": "./PaintManager", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 45}}}, {"value": "./GameOverPanel", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 47}}}, {"value": "./GameHUD", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 35}}}, {"value": "./Bullet", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts": {"mTimestamp": {"mtime": 1754798392340.3665, "uuid": "850968dc-a180-4230-a0c6-b9496fca7201"}, "chunkId": "2bf461b36224b78bdc82ef905b4c406c53e98aa0", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 68}, "end": {"line": 1, "column": 72}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/HealthBarUI.ts": {"mTimestamp": {"mtime": 1751886004994.6763, "uuid": "79d6f812-9554-4262-84e1-fd743e611bf9"}, "chunkId": "8d571f891c0e57353a549f6c7f2dfb0cd3e6d844", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/MainMenuController.ts": {"mTimestamp": {"mtime": 1755699653826.926, "uuid": "0cf6405c-918a-40f1-b383-68a9fc739b7f"}, "chunkId": "054d9434f81bde272393a693c0acd2cc7f26001f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "./SceneTransition", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 32}, "end": {"line": 2, "column": 51}}}, {"value": "./SoundManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 45}}}, {"value": "./PlayerManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintManager.ts": {"mTimestamp": {"mtime": 1754746550522.3418, "uuid": "7dd7ac9d-7d90-4745-bb56-3c99a1d345df"}, "chunkId": "d00576e9277e6b88b985f2cfb67d72a5b2888d19", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 112}, "end": {"line": 1, "column": 116}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PaintSpot.ts": {"mTimestamp": {"mtime": 1753796381049.1853, "uuid": "e9c7f25f-e46f-4cf1-9025-dae477143ca9"}, "chunkId": "0389729ada1c174ba688491f37fd362a910a7fc7", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 46}, "end": {"line": 1, "column": 50}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PausePanel.ts": {"mTimestamp": {"mtime": 1751880715966.924, "uuid": "5a320709-c4da-48a6-9e0a-f1f0884117fb"}, "chunkId": "7db7c970c834bcbcb366e98d02ffbb216840294d", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 52}, "end": {"line": 1, "column": 56}}}, {"value": "./GameManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerInfoUI.ts": {"mTimestamp": {"mtime": 1754805724106.8003, "uuid": "66fbc94f-5230-4bee-8600-db121d4f07fe"}, "chunkId": "1f9fb870b3a7c6e0f41857918e51ef5c063f0494", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./PlayerManager", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 42}, "end": {"line": 2, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts": {"mTimestamp": {"mtime": 1755698486011.6167, "uuid": "1b6d6687-2973-4618-af7b-db52ef67b8c1"}, "chunkId": "6031a6df382439e751708d8e1ff225136ce4b996", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts": {"mTimestamp": {"mtime": 1754791136160.962, "uuid": "c4e943de-6ca3-46a0-9231-17cb43e8e456"}, "chunkId": "097e43139f5ae17c456828e701fc64d3b48ad47c", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 84}, "end": {"line": 2, "column": 88}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts": {"mTimestamp": {"mtime": 1754151184867.3506, "uuid": "c161466d-78da-494f-9ff2-6a5b9ae13301"}, "chunkId": "7b4b511aca644805dad617e0005683d5fa26b665", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 74}, "end": {"line": 1, "column": 78}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SelectManager.ts": {"mTimestamp": {"mtime": 1755699078438.1467, "uuid": "be7b2dc0-da35-4de9-a81c-30690fdcd28f"}, "chunkId": "8c188c8a6d0fb2c10532d0c76cb976b42adb07da", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 105}, "end": {"line": 1, "column": 109}}}, {"value": "./TempData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 37}}}, {"value": "./PlayerManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 30}, "end": {"line": 3, "column": 47}}}, {"value": "./SceneTransition", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 51}}}, {"value": "./CarPropertyDisplay", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 5, "column": 57}}}, {"value": "./PurchasePanel", "resolved": "__unresolved_5", "loc": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PlayerManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SceneTransition.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/CarPropertyDisplay.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/PurchasePanel.ts"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts": {"mTimestamp": {"mtime": 1755611718084.6843, "uuid": "7c385d18-836f-4804-ae30-5bd158cfb7f5"}, "chunkId": "da56edff780e583578bcbe6fd7546a92ebf27b1e", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 83}, "end": {"line": 1, "column": 87}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/TempData.ts": {"mTimestamp": {"mtime": 1751728327354.985, "uuid": "4702a2d2-89fb-4b88-a080-9712952901be"}, "chunkId": "fa9335eab9a9f1afdfe6746522dff0a52db7ffcf", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/camera_follow.ts": {"mTimestamp": {"mtime": 1751727963848.3662, "uuid": "daf21382-6b58-4c9b-aeb4-ace960c8ca29"}, "chunkId": "501ac7410841a88e6540edf8ac9ef6b58f44e840", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 77}, "end": {"line": 1, "column": 81}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts": {"mTimestamp": {"mtime": 1755619268014.4219, "uuid": "b3924dc4-f7fe-491c-9736-a65419c312f5"}, "chunkId": "5226b2c7ce6b19f4f28975e17d285b490ae9ad7c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 204}, "end": {"line": 1, "column": 208}}}, {"value": "./AIPlayer", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./GameManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./SoundManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 45}}}, {"value": "./Bullet", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 35}, "end": {"line": 6, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/SoundManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/ad_event.ts": {"mTimestamp": {"mtime": 1742837477000, "uuid": "b8d97018-1648-4f7f-b2c7-941dcbde2a17"}, "chunkId": "88ac7a162973380edd5431d80072fce16cd402cc", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts": {"mTimestamp": {"mtime": 1742837477000, "uuid": "b502a825-f7c2-4ee4-b75e-605528f25515"}, "chunkId": "d00e783fd1173ca00d34f0d5b6fe7e9ce66e02b0", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 19}, "end": {"line": 1, "column": 23}}}, {"value": "./ad_event", "resolved": "__unresolved_0", "loc": {"start": {"line": 6, "column": 72}, "end": {"line": 6, "column": 84}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "error", "text": "Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 \"./ad_event\""}, "messages": [{"level": "warn", "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"}]}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_callback.ts": {"mTimestamp": {"mtime": 1742837477000, "uuid": "fd80a9b5-5b61-4c02-af13-e6508e6644ff"}, "chunkId": "51e06e09d5eeb7ade648d1be05b2015c9ba69d68", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/interstitial_type.ts": {"mTimestamp": {"mtime": 1742837477000, "uuid": "e7ecae40-ddb1-4ac1-8abf-0382e9d40f02"}, "chunkId": "53eae608936e28859e698ba758f57c7e80f2a164", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/preroll_callback.ts": {"mTimestamp": {"mtime": 1742837477000, "uuid": "3c6b2349-eaf5-4afc-893a-894cd8511526"}, "chunkId": "acff3b1ccef437e5b2ffab20ba90a14a1304f8f6", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/rewarded_callback.ts": {"mTimestamp": {"mtime": 1742837477000, "uuid": "e0c4e2c7-5823-4fba-8095-6ad5a9830da6"}, "chunkId": "b69cf2900308d0879070a4914a7efa2857266e96", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}}}