[{"__type__": "cc.AnimationClip", "_name": "explosion", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 0.85, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 6}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 16}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channel": {"__id__": 4}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "spriteFrame"]}, {"__type__": "cc.animation.ComponentPath", "component": "cc.Sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.ObjectCurve", "_times": [0, 0.08333333333333333, 0.16666666666666666, 0.26666666666666666, 0.38333333333333336, 0.4666666666666667, 0.5666666666666667, 0.6833333333333333, 0.8166666666666667], "_values": [{"__uuid__": "7b3a54f4-3125-441b-a76e-6a0810ddb098@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "0162263e-21bb-48c5-b6c0-6d97af2328db@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "fc931ecf-e981-48cc-999f-132edca72fa6@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "7c7529e4-5710-4e94-ad74-45ccf3f84bbc@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "08078969-ceec-46d6-b660-82dd792138d6@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "91cc176a-7fae-4efa-a97b-53ccb18d63f9@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "1935062c-ea1c-43a3-9e21-c964c071ecec@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "0ac23244-9b49-4e66-b826-aba411c9a168@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "80d65b5e-2b49-4010-99da-69256f876ad5@f9941", "__expectedType__": "cc.SpriteFrame"}]}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 7}, "proxy": null}, "_channels": [{"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["scale"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [0, 0.20000000298023224, 0.3333333432674408, 0.8500000238418579], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [0, 0.20000000298023224, 0.3333333432674408, 0.8500000238418579], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "_times": [0, 0.20000000298023224, 0.3333333432674408, 0.8500000238418579], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]