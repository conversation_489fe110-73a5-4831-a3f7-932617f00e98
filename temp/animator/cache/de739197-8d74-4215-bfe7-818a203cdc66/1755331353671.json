[{"__type__": "cc.AnimationClip", "_name": "dart", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 2, "enableTrsBlending": false, "_duration": 0.4, "_hash": 500763545, "_tracks": [{"__id__": 1}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 11}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channels": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["eulerAngles"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 4}}, {"__type__": "cc.RealCurve", "_times": [0, 0.03333333333333333, 0.06666666666666667, 0.1, 0.13333333333333333, 0.16666666666666666, 0.2, 0.23333333333333334, 0.26666666666666666, 0.3, 0.3333333333333333, 0.36666666666666664, 0.4], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 6}}, {"__type__": "cc.RealCurve", "_times": [0, 0.03333333333333333, 0.06666666666666667, 0.1, 0.13333333333333333, 0.16666666666666666, 0.2, 0.23333333333333334, 0.26666666666666666, 0.3, 0.3333333333333333, 0.36666666666666664, 0.4], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 8}}, {"__type__": "cc.RealCurve", "_times": [0, 0.03333333333333333, 0.06666666666666667, 0.1, 0.13333333333333333, 0.16666666666666666, 0.2, 0.23333333333333334, 0.26666666666666666, 0.3, 0.3333333333333333, 0.36666666666666664, 0.4], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 30, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 60, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 90, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 120, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 150, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 180, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 210, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 240, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 270, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 300, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 330, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 360, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 10}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]