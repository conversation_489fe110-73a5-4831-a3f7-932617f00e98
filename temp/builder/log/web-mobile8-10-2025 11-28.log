2025-8-10 11:28:41 - debug: =================================== build Task (web-mobile) Start ================================
2025-8-10 11:28:41 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"web-mobile","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"includeModules":{"gfx-webgl2":"on","physics":"inherit-project-setting","physics-2d":"inherit-project-setting"},"macroConfig":{"cleanupImageCache":"inherit-project-setting"}},"nativeCodeBundleMode":"both","polyfills":{"asyncFunctions":true},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"091c5c0e-b72a-4cad-ab68-635bc57ff236","outputName":"web-mobile","taskName":"web-mobile","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"web-mobile":{"useWebGPU":false,"orientation":"landscape","embedWebDebugger":false,"__version__":"1.0.1"},"adsense-h5g-plugin":{"enableAdsense":false,"enableTestAd":false,"__version__":"1.0.1","AFPHostPropertyCode":"ca-host-pub-5396158963872751","AFPHostDomain":"douyougame.com","otherAFPHostPropertyCode":"","otherAFPDomain":""},"cocos-service":{"configID":"e495ea","services":[]}},"__version__":"1.3.9","logDest":"project://temp/builder/log/web-mobile8-10-2025 11-28.log"}
2025-8-10 11:28:41 - debug: Build with Cocos Creator 3.8.6
2025-8-10 11:28:41 - debug: cocos-service:(onBeforeBuild) start..., progress: 0%
2025-8-10 11:28:41 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-10 11:28:42 - debug: // ---- build task cocos-service：onBeforeBuild ---- (233ms)
2025-8-10 11:28:42 - debug: cocos-service:(onBeforeBuild) in 233 ms ✓, progress: 2%
2025-8-10 11:28:42 - debug: scene:(onBeforeBuild) start..., progress: 2%
2025-8-10 11:28:42 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-10 11:28:42 - debug: // ---- build task scene：onBeforeBuild ---- (8ms)
2025-8-10 11:28:42 - debug: scene:(onBeforeBuild) in 8 ms ✓, progress: 4%
2025-8-10 11:28:42 - debug: Start lock asset db..., progress: 4%
2025-8-10 11:28:42 - log: Asset DB is paused with build!
2025-8-10 11:28:42 - debug: Query all assets info in project
2025-8-10 11:28:42 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-10 11:28:42 - debug: web-mobile:(onAfterInit) start..., progress: 4%
2025-8-10 11:28:42 - debug: // ---- build task web-mobile：onAfterInit ----
2025-8-10 11:28:42 - debug: // ---- build task web-mobile：onAfterInit ---- (3ms)
2025-8-10 11:28:42 - debug: web-mobile:(onAfterInit) in 3 ms ✓, progress: 5%
2025-8-10 11:28:42 - debug: cocos-service:(onAfterInit) start..., progress: 5%
2025-8-10 11:28:42 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-10 11:28:42 - debug: // ---- build task cocos-service：onAfterInit ---- (155ms)
2025-8-10 11:28:42 - debug: cocos-service:(onAfterInit) in 155 ms ✓, progress: 7%
2025-8-10 11:28:42 - debug: Skip compress image, progress: 0%
2025-8-10 11:28:42 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 7%
2025-8-10 11:28:42 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-10 11:28:42 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-10 11:28:42 - debug: [adsense-h5g-plugin] remove script success
2025-8-10 11:28:42 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (10ms)
2025-8-10 11:28:42 - debug(2): adsense-h5g-plugin:(onBeforeBundleInit) in 10 ms ✓, progress: 7%
2025-8-10 11:28:42 - debug(2): Init all bundles start..., progress: 7%
2025-8-10 11:28:42 - debug(2): Num of bundles: 3..., progress: 7%
2025-8-10 11:28:42 - debug(2): web-mobile:(onAfterBundleInit) start..., progress: 7%
2025-8-10 11:28:42 - debug: // ---- build task web-mobile：onAfterBundleInit ----
2025-8-10 11:28:42 - debug: // ---- build task web-mobile：onAfterBundleInit ---- (8ms)
2025-8-10 11:28:42 - debug: web-mobile:(onAfterBundleInit) in 8 ms ✓, progress: 7%
2025-8-10 11:28:42 - debug: web-mobile:(onAfterBundleInit) in 8 ms ✓, progress: 13%
2025-8-10 11:28:42 - debug: 查询 Asset Bundle start, progress: 7%
2025-8-10 11:28:42 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-10 11:28:42 - debug: Init bundle root assets start..., progress: 7%
2025-8-10 11:28:42 - debug: Init bundle root assets start..., progress: 13%
2025-8-10 11:28:42 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-10 11:28:42 - debug:   Number of all scenes: 3
2025-8-10 11:28:42 - debug:   Number of all scripts: 28
2025-8-10 11:28:42 - debug:   Number of other assets: 579
2025-8-10 11:28:42 - debug: Init bundle root assets success..., progress: 7%
2025-8-10 11:28:42 - debug: Init bundle root assets success..., progress: 13%
2025-8-10 11:28:42 - debug: reload all scripts.
2025-8-10 11:28:42 - debug: [[Executor]] reload before lock
2025-8-10 11:28:42 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-10 11:28:42 - groupCollapsed: Invalidate all modules
2025-8-10 11:28:42 - debug: Unregister BuiltinPipelineSettings
2025-8-10 11:28:42 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-10 11:28:42 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-10 11:28:42 - debug: Unregister DebugViewRuntimeControl
2025-8-10 11:28:42 - debug: Unregister CameraFollow
2025-8-10 11:28:42 - debug: Unregister SoundManager
2025-8-10 11:28:42 - debug: Unregister AIPlayer
2025-8-10 11:28:42 - debug: Unregister Bullet
2025-8-10 11:28:42 - debug: Unregister player
2025-8-10 11:28:42 - debug: Unregister PlayerManager
2025-8-10 11:28:42 - debug: Unregister SceneTransition
2025-8-10 11:28:42 - debug: Unregister PaintManager
2025-8-10 11:28:42 - debug: Unregister GameOverPanel
2025-8-10 11:28:42 - debug: Unregister GameHUD
2025-8-10 11:28:42 - debug: Unregister GameManager
2025-8-10 11:28:42 - debug: Unregister AIController
2025-8-10 11:28:42 - debug: Unregister CarProperties
2025-8-10 11:28:42 - debug: Unregister CarPropertyDisplay
2025-8-10 11:28:42 - debug: Unregister HealthBarUI
2025-8-10 11:28:42 - debug: Unregister MainMenuController
2025-8-10 11:28:42 - debug: Unregister PaintSpot
2025-8-10 11:28:42 - debug: Unregister PausePanel
2025-8-10 11:28:42 - debug: Unregister PlayerInfoUI
2025-8-10 11:28:42 - debug: Unregister PurchasePanel
2025-8-10 11:28:42 - debug: Unregister SelectManager
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js'
2025-8-10 11:28:42 - debug: Invalidating 'pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js'
2025-8-10 11:28:42 - groupEnd: Invalidate all modules
2025-8-10 11:28:42 - groupCollapsed: Imports all modules
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js is not in module cache!
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js is not in module cache!
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js is not in module cache!
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js is not in module cache!
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register CameraFollow
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register SoundManager
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register AIPlayer
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register Bullet
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register player
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register PlayerManager
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register SceneTransition
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register PaintManager
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register GameOverPanel
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register GameHUD
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register GameManager
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register AIController
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register CarProperties
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register HealthBarUI
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js" loaded.
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js is not in module cache!
2025-8-10 11:28:42 - debug: [[Executor]] Register MainMenuController
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register PaintSpot
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register PausePanel
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Register PlayerInfoUI
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js" loaded.
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js is not in module cache!
2025-8-10 11:28:42 - debug: [[Executor]] Register PurchasePanel
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js" loaded.
2025-8-10 11:28:42 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js is not in module cache!
2025-8-10 11:28:42 - debug: [[Executor]] Register SelectManager
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js" loaded.
2025-8-10 11:28:42 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-10 11:28:42 - groupEnd: Imports all modules
2025-8-10 11:28:42 - debug: [[Executor]] after unlock
2025-8-10 11:28:42 - debug: Incremental keys: 
2025-8-10 11:28:42 - debug: Init bundle share assets start..., progress: 7%
2025-8-10 11:28:42 - debug: Init bundle share assets start..., progress: 13%
2025-8-10 11:28:42 - debug: Init bundle share assets success..., progress: 7%
2025-8-10 11:28:42 - debug: Init bundle share assets success..., progress: 13%
2025-8-10 11:28:42 - debug: handle json group in bundle internal
2025-8-10 11:28:42 - debug: handle json group in bundle internal success
2025-8-10 11:28:42 - debug: handle json group in bundle resources
2025-8-10 11:28:42 - debug: handle json group in bundle main
2025-8-10 11:28:42 - debug: init image compress task 0 in bundle internal
2025-8-10 11:28:42 - debug: handle json group in bundle main success
2025-8-10 11:28:42 - debug: init image compress task 0 in bundle main
2025-8-10 11:28:42 - debug: handle json group in bundle resources success
2025-8-10 11:28:42 - debug: init image compress task 0 in bundle resources
2025-8-10 11:28:42 - debug: // ---- build task 查询 Asset Bundle ---- (49ms)
2025-8-10 11:28:42 - log: run build task 查询 Asset Bundle success in 49 ms√, progress: 12%
2025-8-10 11:28:42 - debug: [Build Memory track]: 查询 Asset Bundle start:240.86MB, end 243.75MB, increase: 2.89MB
2025-8-10 11:28:42 - debug: 查询 Asset Bundle start, progress: 12%
2025-8-10 11:28:42 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-10 11:28:42 - debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-10 11:28:42 - log: run build task 查询 Asset Bundle success in 4 ms√, progress: 17%
2025-8-10 11:28:42 - debug: [Build Memory track]: 查询 Asset Bundle start:243.78MB, end 242.58MB, increase: -1227.39KB
2025-8-10 11:28:42 - debug: 打包脚本 start, progress: 17%
2025-8-10 11:28:42 - debug: // ---- build task 打包脚本 ----
2025-8-10 11:28:42 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:28:42 - log: [build-script]enter sub process 92657, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-10 11:28:43 - debug: excute-script over with build-script 1426ms
2025-8-10 11:28:43 - debug: Generate systemJs..., progress: 17%
2025-8-10 11:28:43 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:28:44 - debug: excute-script over with build-script 814ms
2025-8-10 11:28:44 - debug: 构建项目脚本 start..., progress: 17%
2025-8-10 11:28:44 - debug: Build script in bundle start, progress: 17%
2025-8-10 11:28:44 - debug: Build script in bundle start, progress: 13%
2025-8-10 11:28:44 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:28:45 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts


2025-8-10 11:28:45 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts


2025-8-10 11:28:45 - debug: excute-script over with build-script 1326ms
2025-8-10 11:28:45 - debug: Copy externalScripts success!
2025-8-10 11:28:45 - debug: Build script in bundle success, progress: 17%
2025-8-10 11:28:45 - debug: Build script in bundle success, progress: 13%
2025-8-10 11:28:45 - debug: 构建项目脚本 in (1347 ms) √, progress: 17%
2025-8-10 11:28:45 - debug: 构建引擎脚本 start..., progress: 17%
2025-8-10 11:28:45 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-10 11:28:45 - debug: Engine cache (/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63) does not exist.
2025-8-10 11:28:45 - debug: mangleProperties is disabled, platform: HTML5
2025-8-10 11:28:45 - debug: Cache is invalid, start build engine with options: {
  "incremental": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63.watch-files.json",
  "engine": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
  "out": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63",
  "moduleFormat": "system",
  "compress": true,
  "split": false,
  "nativeCodeBundleMode": "both",
  "assetURLFormat": "runtime-resolved",
  "sourceMap": false,
  "loose": true,
  "features": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "gfx-webgl",
    "gfx-webgl2",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "platform": "HTML5",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false,
    "WEBGPU": false
  },
  "mode": "BUILD",
  "metaFile": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63.meta/meta.json",
  "wasmCompressionMode": false,
  "inlineEnum": true,
  "mangleProperties": false,
  "mangleConfigJsonMtime": 0
}

2025-8-10 11:28:45 - debug: md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"gfx-webgl",
"gfx-webgl2",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="HTML5",
split=false,
nativeCodeBundleMode="both",
targets=undefined,
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat="runtime-resolved",
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false,
"WEBGPU":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-10 11:28:45 - log: Run build task(build-engine) in child, see: chrome://inspect/#devices
2025-8-10 11:28:46 - log: [build-engine]enter sub process 92695, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-10 11:28:46 - log: [build-engine]start build engine with options: {"engine":"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine","out":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63","platform":"HTML5","moduleFormat":"system","compress":true,"split":false,"nativeCodeBundleMode":"both","assetURLFormat":"runtime-resolved","noDeprecatedFeatures":false,"sourceMap":false,"features":["2d","affine-transform","animation","audio","base","custom-pipeline","dragon-bones","gfx-webgl","gfx-webgl2","graphics","intersection-2d","mask","particle-2d","physics-2d-box2d","profiler","rich-text","spine-3.8","tiled-map","tween","ui","video","webview","custom-pipeline-builtin-scripts"],"loose":true,"mode":"BUILD","flags":{"DEBUG":false,"LOAD_BULLET_MANUALLY":false,"LOAD_SPINE_MANUALLY":false,"WEBGPU":false},"metaFile":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63.meta/meta.json","mangleProperties":false,"inlineEnum":true,"incremental":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63.watch-files.json","wasmCompressionMode":false,"mangleConfigJsonMtime":0}


2025-8-10 11:28:46 - log: [build-engine]Module source "internal-constants":
function tryDefineGlobal (name, value) {
    const _global = typeof window === 'undefined' ? global : window;
    if (typeof _global[name] === 'undefined') {
        return (_global[name] = value);
    } else {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return _global[name];
    }
}
export const HTML5 = true;

export const NATIVE = false;

export const ANDROID = false;

export const IOS = false;

export const MAC = false;

export const WINDOWS = false;

export const LINUX = false;

export const OHOS = false;

export const OPEN_HARMONY = false;

export const WECHAT = false;
tryDefineGlobal('CC_WECHAT', false);

export const WECHAT_MINI_PROGRAM = false;

export const XIAOMI = false;
tryDefineGlobal('CC_XIAOMI', false);

export const ALIPAY = false;
tryDefineGlobal('CC_ALIPAY', false);

export const TAOBAO = false;

export const TAOBAO_MINIGAME = false;

export const BYTEDANCE = false;
tryDefineGlobal('CC_BYTEDANCE', false);

export const OPPO = false;
tryDefineGlobal('CC_OPPO', false);

export const VIVO = false;
tryDefineGlobal('CC_VIVO', false);

export const HUAWEI = false;
tryDefineGlobal('CC_HUAWEI', false);

export const MIGU = false;
tryDefineGlobal('CC_MIGU', false);

export const HONOR = false;
tryDefineGlobal('CC_HONOR', false);

export const COCOS_RUNTIME = false;
tryDefineGlobal('CC_COCOS_RUNTIME', false);

export const EDITOR = false;
tryDefineGlobal('CC_EDITOR', false);

export const EDITOR_NOT_IN_PREVIEW = false;

export const PREVIEW = false;
tryDefineGlobal('CC_PREVIEW', false);

export const BUILD = true;
tryDefineGlobal('CC_BUILD', true);

export const TEST = false;
tryDefineGlobal('CC_TEST', false);

export const DEBUG = false;
tryDefineGlobal('CC_DEBUG', false);

export const SERVER_MODE = false;

export const DEV = false;
tryDefineGlobal('CC_DEV', false);

export const MINIGAME = false;
tryDefineGlobal('CC_MINIGAME', false);

export const RUNTIME_BASED = false;
tryDefineGlobal('CC_RUNTIME_BASED', false);

export const SUPPORT_JIT = true;
tryDefineGlobal('CC_SUPPORT_JIT', true);

export const JSB = false;
tryDefineGlobal('CC_JSB', false);

export const NOT_PACK_PHYSX_LIBS = false;

export const NET_MODE = 0;

export const WEBGPU = false;

export const NATIVE_CODE_BUNDLE_MODE = 2;

export const WASM_SUBPACKAGE = false;

export const CULL_MESHOPT = true;

export const LOAD_SPINE_MANUALLY = false;

export const LOAD_BOX2D_MANUALLY = false;

export const LOAD_BULLET_MANUALLY = false;

export const LOAD_PHYSX_MANUALLY = false;

export const USE_3D = false;

export const USE_UI_SKEW = false;

export const USE_XR = false;




2025-8-10 11:28:46 - log: [build-engine]Module source "cc":
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/sorting.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/affine-transform.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/animation.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/audio.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/base.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/custom-pipeline.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/dragon-bones.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/gfx-webgl.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/gfx-webgl2.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/graphics.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/intersection-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/mask.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/particle-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-box2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-framework.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/profiler.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/rich-text.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/spine.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tiled-map.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tween.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/ui.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/video.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/webview.ts';


2025-8-10 11:28:54 - log: [build-engine]Redirect module internal:native to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/native-binding/impl.ts


2025-8-10 11:28:55 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d-empty.ts


2025-8-10 11:28:55 - log: [build-engine]Redirect module pal/system-info to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/system-info/web/system-info.ts


2025-8-10 11:28:55 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version-3.8.ts


2025-8-10 11:28:55 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/runtime-exports.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/index-empty.ts


2025-8-10 11:28:56 - log: [build-engine]Redirect module pal/audio to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/audio/web/player.ts


2025-8-10 11:28:56 - log: [build-engine]Redirect module pal/screen-adapter to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/screen-adapter/web/screen-adapter.ts


2025-8-10 11:28:56 - log: [build-engine]Redirect module pal/env to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/env/web/env.ts
Redirect module pal/pacer to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/pacer/pacer-web.ts


2025-8-10 11:28:57 - log: [build-engine]Redirect module pal/input to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/input/web/index.ts


2025-8-10 11:28:57 - log: [build-engine]Redirect module pal/minigame to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/minigame/non-minigame.ts


2025-8-10 11:28:58 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-instantiate.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-instantiate-3.8.ts


2025-8-10 11:28:58 - log: [build-engine]Redirect module pal/wasm to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/wasm/wasm-web.ts


2025-8-10 11:28:59 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-10 11:28:59 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-10 11:29:01 - log: [build-engine]==== Performance ====
{"# BUILD":[14057.690999999999,1388572572,1529864572],"## initialize":[5844.488708,423268436,563914244],"- plugin 6 (node-resolve) - buildStart":[0.038291999999955806,-252864,140661804],"- plugin 8 (commonjs) - buildStart":[0.013500000000021828,3360,140666008],"- plugin 9 (@cocos/typescript) - buildStart":[5844.401458,423245888,563912252],"## generate module graph":[6974.629667,888225376,1452140208],"- plugin 0 (commonjs--resolver) - resolveId":[7.538616000026195,6731760,1450922948],"- plugin 1 (@cocos/ccbuild|external-loader) - resolveId":[5.187073000012788,-3410044,1450924648],"- plugin 2 (@cocos/ccbuild|module-overrides) - resolveId":[4.503411000049709,-322136,1450931176],"- plugin 3 (virtual) - resolveId":[9.359965999972701,-74444,1450932972],"- plugin 1 (@cocos/ccbuild|external-loader) - load":[1.0798299999887604,692272,1450554372],"- plugin 2 (@cocos/ccbuild|module-overrides) - load":[2.1722260000096867,542184,1450555188],"- plugin 3 (virtual) - load":[0.7431190000006609,129648,1450555684],"- plugin 7 (json) - transform":[1.4833299999827432,-354372,1450560328],"- plugin 8 (commonjs) - transform":[103.3759170000194,19761864,1450561660],"- plugin 10 (babel) - transform":[25.652758000016547,8168200,1450956360],"generate ast":[389.9160990000337,220702820,1451610812],"- plugin 4 (@cocos/ccbuild|module-query-plugin) - resolveId":[4.7490520000174,3481700,1450963036],"- plugin 5 (ts-paths) - resolveId":[4.235142999957134,-3116920,1451038000],"- plugin 9 (@cocos/typescript) - resolveId":[338.646505000027,77676100,1451121836],"- plugin 10 (babel) - resolveId":[0.693780999999035,271044,1451136272],"- plugin 6 (node-resolve) - resolveId":[1.09232799999063,427436,1340972448],"- plugin 6 (node-resolve) - load":[0.7276099999908183,213948,1450556188],"- plugin 8 (commonjs) - load":[5.44629900003747,147048,1450557296],"- plugin 9 (@cocos/typescript) - load":[7.950473999976566,2301712,1340998080],"- plugin 10 (babel) - load":[59.04220899999564,10403332,1340998740],"## sort and bind modules":[141.9838749999999,14199972,1466340252],"## mark included statements":[1092.0932080000002,62846052,1529186336],"treeshaking pass 1":[327.6442499999994,49553532,1516118656],"treeshaking pass 2":[138.4107499999991,4823288,1520941976],"treeshaking pass 3":[63.96691699999974,2896520,1523838528],"treeshaking pass 4":[49.844750000000204,2973908,1526812468],"treeshaking pass 5":[52.743375000000015,-4250008,1522562492],"treeshaking pass 6":[46.1511249999985,5994028,1528556552],"treeshaking pass 7":[46.99695800000154,-1655992,1526900592],"treeshaking pass 8":[42.853792000001704,-104940,1526795684],"treeshaking pass 9":[96.93691699999908,3262596,1530058312],"treeshaking pass 10":[54.37737500000003,1986256,1532044600],"treeshaking pass 11":[43.85241700000006,-1314256,1530730376],"treeshaking pass 12":[43.365625000000364,-2651164,1528079244],"treeshaking pass 13":[42.64466700000048,4825020,1532904296],"treeshaking pass 14":[41.718832999998995,-3718256,1529186072],"- plugin 8 (commonjs) - buildEnd":[0.004166999999142718,244,1529189020],"- plugin 9 (@cocos/typescript) - buildEnd":[4.345124999999825,674476,1529863528]}
====             ====


2025-8-10 11:29:07 - debug: excute-script over with build-engine 21845ms
2025-8-10 11:29:07 - debug: build engine done: output: /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63
2025-8-10 11:29:07 - debug: 构建引擎脚本 in (21856 ms) √, progress: 17%
2025-8-10 11:29:07 - debug: Copy plugin script ..., progress: 17%
2025-8-10 11:29:07 - debug: Generate import-map..., progress: 17%
2025-8-10 11:29:07 - debug: // ---- build task 打包脚本 ---- (25456ms)
2025-8-10 11:29:07 - log: run build task 打包脚本 success in 25 s√, progress: 22%
2025-8-10 11:29:07 - debug: [Build Memory track]: 打包脚本 start:242.69MB, end 243.83MB, increase: 1.15MB
2025-8-10 11:29:07 - debug: Build Assets start, progress: 22%
2025-8-10 11:29:07 - debug: // ---- build task Build Assets ----
2025-8-10 11:29:07 - debug: Build bundles..., progress: 22%
2025-8-10 11:29:07 - debug: Pack Images start, progress: 22%
2025-8-10 11:29:07 - debug: Pack Images start, progress: 13%
2025-8-10 11:29:07 - debug: builder:pack-auto-atlas-image (8ms)
2025-8-10 11:29:07 - debug: Pack Images success, progress: 22%
2025-8-10 11:29:07 - debug: Pack Images success, progress: 13%
2025-8-10 11:29:07 - debug: Compress image start..., progress: 22%
2025-8-10 11:29:07 - debug: Compress image start..., progress: 13%
2025-8-10 11:29:07 - group: Compress image...
2025-8-10 11:29:07 - debug: sort compress task {}
2025-8-10 11:29:07 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-10 11:29:07 - debug: No image need to compress
2025-8-10 11:29:07 - groupEnd: Compress image...
2025-8-10 11:29:07 - debug: Compress image success..., progress: 22%
2025-8-10 11:29:07 - debug: Compress image success..., progress: 13%
2025-8-10 11:29:07 - debug: Output asset in bundles start, progress: 22%
2025-8-10 11:29:07 - debug: Output asset in bundles start, progress: 13%
2025-8-10 11:29:07 - debug: Handle all json groups in bundle internal
2025-8-10 11:29:07 - debug: handle json group
2025-8-10 11:29:07 - debug: Handle all json groups in bundle resources
2025-8-10 11:29:07 - debug: handle json group
2025-8-10 11:29:07 - debug: Handle all json groups in bundle main
2025-8-10 11:29:07 - debug: handle json group
2025-8-10 11:29:07 - debug: Json group(05b737039) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(0d0eedbd0) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(0af50bc43) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(06585a170) compile success，json number: 6
2025-8-10 11:29:07 - debug: handle single json
2025-8-10 11:29:07 - debug: Json group(0be0c6543) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(0fe72bac9) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(01344d7e0) compile success，json number: 6
2025-8-10 11:29:07 - debug: handle single json
2025-8-10 11:29:07 - debug: Json group(0113159d2) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(023d923cb) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(068713cf2) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(01debcabb) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(079c47cc5) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(018dea86c) compile success，json number: 6
2025-8-10 11:29:07 - debug: Json group(0f116e97b) compile success，json number: 6
2025-8-10 11:29:07 - debug: handle single json
2025-8-10 11:29:07 - debug: Output asset in bundles success, progress: 22%
2025-8-10 11:29:07 - debug: Output asset in bundles success, progress: 13%
2025-8-10 11:29:07 - debug: Output asset in bundles start, progress: 22%
2025-8-10 11:29:07 - debug: Output asset in bundles start, progress: 13%
2025-8-10 11:29:07 - debug: compress config of bundle internal...
2025-8-10 11:29:07 - debug: compress config of bundle internal success
2025-8-10 11:29:07 - debug: compress config of bundle resources...
2025-8-10 11:29:07 - debug: compress config of bundle resources success
2025-8-10 11:29:07 - debug: compress config of bundle main...
2025-8-10 11:29:07 - debug: compress config of bundle main success
2025-8-10 11:29:07 - debug: output config of bundle internal
2025-8-10 11:29:07 - debug: output config of bundle internal success
2025-8-10 11:29:07 - debug: output config of bundle resources
2025-8-10 11:29:07 - debug: output config of bundle resources success
2025-8-10 11:29:07 - debug: output config of bundle main
2025-8-10 11:29:07 - debug: output config of bundle main success
2025-8-10 11:29:07 - debug: Output asset in bundles success, progress: 22%
2025-8-10 11:29:07 - debug: Output asset in bundles success, progress: 13%
2025-8-10 11:29:07 - debug: // ---- build task Build Assets ---- (161ms)
2025-8-10 11:29:07 - log: run build task Build Assets success in 161 ms√, progress: 27%
2025-8-10 11:29:07 - debug: [Build Memory track]: Build Assets start:243.86MB, end 245.95MB, increase: 2.09MB
2025-8-10 11:29:07 - debug: 整理部分构建选项内数据到 settings.json start, progress: 27%
2025-8-10 11:29:07 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-10 11:29:07 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-10 11:29:07 - log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 29%
2025-8-10 11:29:07 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:246.03MB, end 246.08MB, increase: 57.41KB
2025-8-10 11:29:07 - debug: 填充脚本数据到 settings.json start, progress: 29%
2025-8-10 11:29:07 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-10 11:29:07 - debug: // ---- build task 填充脚本数据到 settings.json ---- (4ms)
2025-8-10 11:29:07 - log: run build task 填充脚本数据到 settings.json success in 4 ms√, progress: 31%
2025-8-10 11:29:07 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:246.11MB, end 246.16MB, increase: 56.59KB
2025-8-10 11:29:07 - debug: 整理部分构建选项内数据到 settings.json start, progress: 31%
2025-8-10 11:29:07 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-10 11:29:07 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (17ms)
2025-8-10 11:29:07 - log: run build task 整理部分构建选项内数据到 settings.json success in 17 ms√, progress: 32%
2025-8-10 11:29:07 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:246.19MB, end 246.35MB, increase: 165.39KB
2025-8-10 11:29:07 - debug: web-mobile:(onBeforeCompressSettings) start..., progress: 32%
2025-8-10 11:29:07 - debug: // ---- build task web-mobile：onBeforeCompressSettings ----
2025-8-10 11:29:08 - debug: // ---- build task web-mobile：onBeforeCompressSettings ---- (5ms)
2025-8-10 11:29:08 - debug: web-mobile:(onBeforeCompressSettings) in 5 ms ✓, progress: 34%
2025-8-10 11:29:08 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 34%
2025-8-10 11:29:08 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-10 11:29:08 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (257ms)
2025-8-10 11:29:08 - debug: cocos-service:(onBeforeCompressSettings) in 257 ms ✓, progress: 36%
2025-8-10 11:29:08 - debug: 整理静态模板文件 start, progress: 36%
2025-8-10 11:29:08 - debug: // ---- build task 整理静态模板文件 ----
2025-8-10 11:29:08 - debug: // ---- build task 整理静态模板文件 ---- (24ms)
2025-8-10 11:29:08 - log: run build task 整理静态模板文件 success in 24 ms√, progress: 41%
2025-8-10 11:29:08 - debug: [Build Memory track]: 整理静态模板文件 start:246.56MB, end 247.65MB, increase: 1.09MB
2025-8-10 11:29:08 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 41%
2025-8-10 11:29:08 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-10 11:29:08 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (170ms)
2025-8-10 11:29:08 - debug: cocos-service:(onAfterCompressSettings) in 170 ms ✓, progress: 43%
2025-8-10 11:29:08 - debug: web-mobile:(onBeforeCopyBuildTemplate) start..., progress: 43%
2025-8-10 11:29:08 - debug: // ---- build task web-mobile：onBeforeCopyBuildTemplate ----
2025-8-10 11:29:08 - debug: // ---- build task web-mobile：onBeforeCopyBuildTemplate ---- (21ms)
2025-8-10 11:29:08 - debug: web-mobile:(onBeforeCopyBuildTemplate) in 21 ms ✓, progress: 45%
2025-8-10 11:29:08 - debug: 给所有的资源加上 MD5 后缀 start, progress: 45%
2025-8-10 11:29:08 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-10 11:29:08 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (4ms)
2025-8-10 11:29:08 - log: run build task 给所有的资源加上 MD5 后缀 success in 4 ms√, progress: 55%
2025-8-10 11:29:08 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:249.13MB, end 249.19MB, increase: 56.21KB
2025-8-10 11:29:08 - debug: cocos-service:(onAfterBuild) start..., progress: 55%
2025-8-10 11:29:08 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-10 11:29:08 - debug: // ---- build task cocos-service：onAfterBuild ---- (77ms)
2025-8-10 11:29:08 - debug: cocos-service:(onAfterBuild) in 77 ms ✓, progress: 56%
2025-8-10 11:29:08 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 56%
2025-8-10 11:29:08 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-10 11:29:08 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (6ms)
2025-8-10 11:29:08 - debug: adsense-h5g-plugin:(onAfterBuild) in 6 ms ✓, progress: 58%
2025-8-10 11:29:08 - log: Asset DB is resume!
2025-8-10 11:29:08 - debug: builder:build-project-total (26701ms)
2025-8-10 11:29:08 - debug: build success in 26701!
2025-8-10 11:29:08 - debug: [Build Memory track]: builder:build-project-total start:240.52MB, end 250.38MB, increase: 9.86MB
2025-8-10 11:29:08 - debug: ================================ build Task (web-mobile) Finished in (26 s)ms ================================
2025-8-10 11:32:22 - debug: =================================== build -> run Task (web-mobile) Start ================================
2025-8-10 11:32:22 - debug: Start build task, options:
{"nextStages":["run"],"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"web-mobile","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"includeModules":{"gfx-webgl2":"on","physics":"inherit-project-setting","physics-2d":"inherit-project-setting"},"macroConfig":{"cleanupImageCache":"inherit-project-setting"}},"nativeCodeBundleMode":"both","polyfills":{"asyncFunctions":true},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"091c5c0e-b72a-4cad-ab68-635bc57ff236","outputName":"web-mobile","taskName":"web-mobile","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"web-mobile":{"useWebGPU":false,"orientation":"landscape","embedWebDebugger":false,"__version__":"1.0.1"},"adsense-h5g-plugin":{"enableAdsense":false,"enableTestAd":false,"__version__":"1.0.1","AFPHostPropertyCode":"ca-host-pub-5396158963872751","AFPHostDomain":"douyougame.com","otherAFPHostPropertyCode":"","otherAFPDomain":""},"cocos-service":{"configID":"e495ea","services":[]}},"__version__":"1.3.9","logDest":"project://temp/builder/log/web-mobile8-10-2025 11-28.log","buildStageGroup":{"build":["run"]}}
2025-8-10 11:32:22 - debug: Build with Cocos Creator 3.8.6
2025-8-10 11:32:22 - debug: cocos-service:(onBeforeBuild) start..., progress: 0%
2025-8-10 11:32:22 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-10 11:32:23 - debug: // ---- build task cocos-service：onBeforeBuild ---- (223ms)
2025-8-10 11:32:23 - debug: cocos-service:(onBeforeBuild) in 223 ms ✓, progress: 1%
2025-8-10 11:32:23 - debug: scene:(onBeforeBuild) start..., progress: 1%
2025-8-10 11:32:23 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-10 11:32:23 - debug: // ---- build task scene：onBeforeBuild ---- (13ms)
2025-8-10 11:32:23 - debug: scene:(onBeforeBuild) in 13 ms ✓, progress: 2%
2025-8-10 11:32:23 - debug: Start lock asset db..., progress: 2%
2025-8-10 11:32:23 - log: Asset DB is paused with build!
2025-8-10 11:32:23 - debug: Query all assets info in project
2025-8-10 11:32:23 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-10 11:32:23 - debug: web-mobile:(onAfterInit) start..., progress: 2%
2025-8-10 11:32:23 - debug: // ---- build task web-mobile：onAfterInit ----
2025-8-10 11:32:23 - debug: // ---- build task web-mobile：onAfterInit ---- (6ms)
2025-8-10 11:32:23 - debug: web-mobile:(onAfterInit) in 6 ms ✓, progress: 3%
2025-8-10 11:32:23 - debug: cocos-service:(onAfterInit) start..., progress: 3%
2025-8-10 11:32:23 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-10 11:32:23 - debug: // ---- build task cocos-service：onAfterInit ---- (75ms)
2025-8-10 11:32:23 - debug: cocos-service:(onAfterInit) in 75 ms ✓, progress: 4%
2025-8-10 11:32:23 - debug: Skip compress image, progress: 0%
2025-8-10 11:32:23 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 4%
2025-8-10 11:32:23 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-10 11:32:23 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-10 11:32:23 - debug: [adsense-h5g-plugin] remove script success
2025-8-10 11:32:23 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (26ms)
2025-8-10 11:32:23 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 26 ms ✓, progress: 4%
2025-8-10 11:32:23 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 26 ms ✓, progress: 7%
2025-8-10 11:32:23 - debug: Init all bundles start..., progress: 4%
2025-8-10 11:32:23 - debug: Init all bundles start..., progress: 7%
2025-8-10 11:32:23 - debug: Num of bundles: 3..., progress: 4%
2025-8-10 11:32:23 - debug: Num of bundles: 3..., progress: 7%
2025-8-10 11:32:23 - debug: web-mobile:(onAfterBundleInit) start..., progress: 4%
2025-8-10 11:32:23 - debug: web-mobile:(onAfterBundleInit) start..., progress: 7%
2025-8-10 11:32:23 - debug: // ---- build task web-mobile：onAfterBundleInit ----
2025-8-10 11:32:23 - debug: // ---- build task web-mobile：onAfterBundleInit ---- (14ms)
2025-8-10 11:32:23 - debug: web-mobile:(onAfterBundleInit) in 14 ms ✓, progress: 4%
2025-8-10 11:32:23 - debug: web-mobile:(onAfterBundleInit) in 14 ms ✓, progress: 13%
2025-8-10 11:32:23 - debug: 查询 Asset Bundle start, progress: 4%
2025-8-10 11:32:23 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-10 11:32:23 - debug: Init bundle root assets start..., progress: 4%
2025-8-10 11:32:23 - debug: Init bundle root assets start..., progress: 13%
2025-8-10 11:32:23 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-10 11:32:23 - debug:   Number of all scenes: 3
2025-8-10 11:32:23 - debug:   Number of all scripts: 28
2025-8-10 11:32:23 - debug:   Number of other assets: 579
2025-8-10 11:32:23 - debug: Init bundle root assets success..., progress: 4%
2025-8-10 11:32:23 - debug: Init bundle root assets success..., progress: 13%
2025-8-10 11:32:23 - debug: reload all scripts.
2025-8-10 11:32:23 - debug: [[Executor]] reload before lock
2025-8-10 11:32:23 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-10 11:32:23 - groupCollapsed: Invalidate all modules
2025-8-10 11:32:23 - debug: Unregister BuiltinPipelineSettings
2025-8-10 11:32:23 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-10 11:32:23 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-10 11:32:23 - debug: Unregister DebugViewRuntimeControl
2025-8-10 11:32:23 - debug: Unregister CameraFollow
2025-8-10 11:32:23 - debug: Unregister SoundManager
2025-8-10 11:32:23 - debug: Unregister AIPlayer
2025-8-10 11:32:23 - debug: Unregister Bullet
2025-8-10 11:32:23 - debug: Unregister player
2025-8-10 11:32:23 - debug: Unregister PlayerManager
2025-8-10 11:32:23 - debug: Unregister SceneTransition
2025-8-10 11:32:23 - debug: Unregister PaintManager
2025-8-10 11:32:23 - debug: Unregister GameOverPanel
2025-8-10 11:32:23 - debug: Unregister GameHUD
2025-8-10 11:32:23 - debug: Unregister GameManager
2025-8-10 11:32:23 - debug: Unregister AIController
2025-8-10 11:32:23 - debug: Unregister CarProperties
2025-8-10 11:32:23 - debug: Unregister CarPropertyDisplay
2025-8-10 11:32:23 - debug: Unregister HealthBarUI
2025-8-10 11:32:23 - debug: Unregister MainMenuController
2025-8-10 11:32:23 - debug: Unregister PaintSpot
2025-8-10 11:32:23 - debug: Unregister PausePanel
2025-8-10 11:32:23 - debug: Unregister PlayerInfoUI
2025-8-10 11:32:23 - debug: Unregister PurchasePanel
2025-8-10 11:32:23 - debug: Unregister SelectManager
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js'
2025-8-10 11:32:23 - debug: Invalidating 'pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js'
2025-8-10 11:32:23 - groupEnd: Invalidate all modules
2025-8-10 11:32:23 - groupCollapsed: Imports all modules
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register CameraFollow
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register SoundManager
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register AIPlayer
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register Bullet
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register player
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register PlayerManager
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register SceneTransition
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register PaintManager
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register GameOverPanel
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register GameHUD
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register GameManager
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register AIController
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register CarProperties
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register HealthBarUI
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register MainMenuController
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register PaintSpot
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register PausePanel
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register PlayerInfoUI
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register PurchasePanel
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Register SelectManager
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js" loaded.
2025-8-10 11:32:23 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-10 11:32:23 - groupEnd: Imports all modules
2025-8-10 11:32:23 - debug: [[Executor]] after unlock
2025-8-10 11:32:23 - debug: Incremental keys: 
2025-8-10 11:32:23 - debug: Init bundle share assets start..., progress: 4%
2025-8-10 11:32:23 - debug: Init bundle share assets start..., progress: 13%
2025-8-10 11:32:23 - debug: Init bundle share assets success..., progress: 4%
2025-8-10 11:32:23 - debug: Init bundle share assets success..., progress: 13%
2025-8-10 11:32:23 - debug: handle json group in bundle internal
2025-8-10 11:32:23 - debug: handle json group in bundle internal success
2025-8-10 11:32:23 - debug: handle json group in bundle resources
2025-8-10 11:32:23 - debug: handle json group in bundle main
2025-8-10 11:32:23 - debug: init image compress task 0 in bundle internal
2025-8-10 11:32:23 - debug: handle json group in bundle main success
2025-8-10 11:32:23 - debug: init image compress task 0 in bundle main
2025-8-10 11:32:23 - debug: handle json group in bundle resources success
2025-8-10 11:32:23 - debug: init image compress task 0 in bundle resources
2025-8-10 11:32:23 - debug: // ---- build task 查询 Asset Bundle ---- (54ms)
2025-8-10 11:32:23 - log: run build task 查询 Asset Bundle success in 54 ms√, progress: 6%
2025-8-10 11:32:23 - debug: [Build Memory track]: 查询 Asset Bundle start:244.89MB, end 246.73MB, increase: 1.84MB
2025-8-10 11:32:23 - debug: 查询 Asset Bundle start, progress: 6%
2025-8-10 11:32:23 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-10 11:32:23 - debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-10 11:32:23 - log: run build task 查询 Asset Bundle success in 9 ms√, progress: 9%
2025-8-10 11:32:23 - debug: [Build Memory track]: 查询 Asset Bundle start:246.75MB, end 246.96MB, increase: 211.59KB
2025-8-10 11:32:23 - debug: 打包脚本 start, progress: 9%
2025-8-10 11:32:23 - debug: // ---- build task 打包脚本 ----
2025-8-10 11:32:23 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:32:23 - log: [build-script]enter sub process 93327, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-10 11:32:24 - debug: excute-script over with build-script 1289ms
2025-8-10 11:32:24 - debug: Generate systemJs..., progress: 9%
2025-8-10 11:32:24 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:32:25 - debug: excute-script over with build-script 853ms
2025-8-10 11:32:25 - debug: 构建项目脚本 start..., progress: 9%
2025-8-10 11:32:25 - debug: Build script in bundle start, progress: 9%
2025-8-10 11:32:25 - debug: Build script in bundle start, progress: 13%
2025-8-10 11:32:25 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:32:26 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts


2025-8-10 11:32:26 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts


2025-8-10 11:32:26 - debug: excute-script over with build-script 1241ms
2025-8-10 11:32:26 - debug: Copy externalScripts success!
2025-8-10 11:32:26 - debug: Build script in bundle success, progress: 9%
2025-8-10 11:32:26 - debug: Build script in bundle success, progress: 13%
2025-8-10 11:32:26 - debug: 构建项目脚本 in (1273 ms) √, progress: 9%
2025-8-10 11:32:26 - debug: 构建引擎脚本 start..., progress: 9%
2025-8-10 11:32:26 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-10 11:32:26 - debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63)}
2025-8-10 11:32:26 - debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"gfx-webgl",
"gfx-webgl2",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="HTML5",
split=false,
nativeCodeBundleMode="both",
targets=undefined,
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat="runtime-resolved",
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false,
"WEBGPU":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-10 11:32:26 - debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "gfx-webgl",
    "gfx-webgl2",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "cocos-js",
  "platform": "HTML5",
  "useCache": true,
  "nativeCodeBundleMode": "both",
  "wasmCompressionMode": false,
  "split": false,
  "assetURLFormat": "runtime-resolved",
  "output": "/Users/<USER>/projects/cocos_project/driftClash/build/web-mobile/cocos-js",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false,
    "WEBGPU": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-10 11:32:26 - debug: 构建引擎脚本 in (27 ms) √, progress: 9%
2025-8-10 11:32:26 - debug: Copy plugin script ..., progress: 9%
2025-8-10 11:32:26 - debug: Generate import-map..., progress: 9%
2025-8-10 11:32:26 - debug: // ---- build task 打包脚本 ---- (3456ms)
2025-8-10 11:32:26 - log: run build task 打包脚本 success in 3 s√, progress: 11%
2025-8-10 11:32:26 - debug: [Build Memory track]: 打包脚本 start:247.07MB, end 246.53MB, increase: -549.49KB
2025-8-10 11:32:26 - debug: Build Assets start, progress: 11%
2025-8-10 11:32:26 - debug: // ---- build task Build Assets ----
2025-8-10 11:32:26 - debug: Build bundles..., progress: 11%
2025-8-10 11:32:26 - debug: Pack Images start, progress: 11%
2025-8-10 11:32:26 - debug: Pack Images start, progress: 13%
2025-8-10 11:32:26 - debug: builder:pack-auto-atlas-image (16ms)
2025-8-10 11:32:26 - debug: Pack Images success, progress: 11%
2025-8-10 11:32:26 - debug: Pack Images success, progress: 13%
2025-8-10 11:32:26 - debug: Compress image start..., progress: 11%
2025-8-10 11:32:26 - debug: Compress image start..., progress: 13%
2025-8-10 11:32:26 - group: Compress image...
2025-8-10 11:32:26 - debug: sort compress task {}
2025-8-10 11:32:26 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-10 11:32:26 - debug: No image need to compress
2025-8-10 11:32:26 - groupEnd: Compress image...
2025-8-10 11:32:26 - debug: Compress image success..., progress: 11%
2025-8-10 11:32:26 - debug: Compress image success..., progress: 13%
2025-8-10 11:32:26 - debug: Output asset in bundles start, progress: 11%
2025-8-10 11:32:26 - debug: Output asset in bundles start, progress: 13%
2025-8-10 11:32:26 - debug: Handle all json groups in bundle internal
2025-8-10 11:32:26 - debug: handle json group
2025-8-10 11:32:26 - debug: Handle all json groups in bundle resources
2025-8-10 11:32:26 - debug: handle json group
2025-8-10 11:32:26 - debug: Handle all json groups in bundle main
2025-8-10 11:32:26 - debug: handle json group
2025-8-10 11:32:26 - debug: Json group(05b737039) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(0d0eedbd0) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(0af50bc43) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(0be0c6543) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(06585a170) compile success，json number: 6
2025-8-10 11:32:26 - debug: handle single json
2025-8-10 11:32:26 - debug: Json group(0fe72bac9) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(0113159d2) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(01344d7e0) compile success，json number: 6
2025-8-10 11:32:26 - debug: handle single json
2025-8-10 11:32:26 - debug: Json group(023d923cb) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(068713cf2) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(01debcabb) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(079c47cc5) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(018dea86c) compile success，json number: 6
2025-8-10 11:32:26 - debug: Json group(0f116e97b) compile success，json number: 6
2025-8-10 11:32:26 - debug: handle single json
2025-8-10 11:32:26 - debug: Output asset in bundles success, progress: 11%
2025-8-10 11:32:26 - debug: Output asset in bundles success, progress: 13%
2025-8-10 11:32:26 - debug: Output asset in bundles start, progress: 11%
2025-8-10 11:32:26 - debug: Output asset in bundles start, progress: 13%
2025-8-10 11:32:26 - debug: compress config of bundle internal...
2025-8-10 11:32:26 - debug: compress config of bundle internal success
2025-8-10 11:32:26 - debug: compress config of bundle resources...
2025-8-10 11:32:26 - debug: compress config of bundle resources success
2025-8-10 11:32:26 - debug: compress config of bundle main...
2025-8-10 11:32:26 - debug: compress config of bundle main success
2025-8-10 11:32:26 - debug: output config of bundle internal
2025-8-10 11:32:26 - debug: output config of bundle internal success
2025-8-10 11:32:26 - debug: output config of bundle resources
2025-8-10 11:32:26 - debug: output config of bundle resources success
2025-8-10 11:32:26 - debug: output config of bundle main
2025-8-10 11:32:26 - debug: output config of bundle main success
2025-8-10 11:32:26 - debug: Output asset in bundles success, progress: 11%
2025-8-10 11:32:26 - debug: Output asset in bundles success, progress: 13%
2025-8-10 11:32:26 - debug: // ---- build task Build Assets ---- (175ms)
2025-8-10 11:32:26 - log: run build task Build Assets success in 175 ms√, progress: 14%
2025-8-10 11:32:26 - debug: [Build Memory track]: Build Assets start:246.55MB, end 250.00MB, increase: 3.45MB
2025-8-10 11:32:26 - debug: 整理部分构建选项内数据到 settings.json start, progress: 14%
2025-8-10 11:32:26 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-10 11:32:27 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-10 11:32:27 - log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 14%
2025-8-10 11:32:27 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:250.07MB, end 250.19MB, increase: 114.12KB
2025-8-10 11:32:27 - debug: 填充脚本数据到 settings.json start, progress: 14%
2025-8-10 11:32:27 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-10 11:32:27 - debug: // ---- build task 填充脚本数据到 settings.json ---- (8ms)
2025-8-10 11:32:27 - log: run build task 填充脚本数据到 settings.json success in 8 ms√, progress: 15%
2025-8-10 11:32:27 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:250.21MB, end 247.27MB, increase: -3016.80KB
2025-8-10 11:32:27 - debug: 整理部分构建选项内数据到 settings.json start, progress: 15%
2025-8-10 11:32:27 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-10 11:32:27 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-8-10 11:32:27 - log: run build task 整理部分构建选项内数据到 settings.json success in 11 ms√, progress: 16%
2025-8-10 11:32:27 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:247.29MB, end 247.50MB, increase: 212.92KB
2025-8-10 11:32:27 - debug: web-mobile:(onBeforeCompressSettings) start..., progress: 16%
2025-8-10 11:32:27 - debug: // ---- build task web-mobile：onBeforeCompressSettings ----
2025-8-10 11:32:27 - debug: // ---- build task web-mobile：onBeforeCompressSettings ---- (8ms)
2025-8-10 11:32:27 - debug: web-mobile:(onBeforeCompressSettings) in 8 ms ✓, progress: 17%
2025-8-10 11:32:27 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 17%
2025-8-10 11:32:27 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-10 11:32:27 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (108ms)
2025-8-10 11:32:27 - debug: cocos-service:(onBeforeCompressSettings) in 108 ms ✓, progress: 18%
2025-8-10 11:32:27 - debug: 整理静态模板文件 start, progress: 18%
2025-8-10 11:32:27 - debug: // ---- build task 整理静态模板文件 ----
2025-8-10 11:32:27 - debug: // ---- build task 整理静态模板文件 ---- (23ms)
2025-8-10 11:32:27 - log: run build task 整理静态模板文件 success in 23 ms√, progress: 20%
2025-8-10 11:32:27 - debug: [Build Memory track]: 整理静态模板文件 start:247.82MB, end 248.11MB, increase: 305.82KB
2025-8-10 11:32:27 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 20%
2025-8-10 11:32:27 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-10 11:32:27 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (197ms)
2025-8-10 11:32:27 - debug: cocos-service:(onAfterCompressSettings) in 197 ms ✓, progress: 21%
2025-8-10 11:32:27 - debug: web-mobile:(onBeforeCopyBuildTemplate) start..., progress: 21%
2025-8-10 11:32:27 - debug: // ---- build task web-mobile：onBeforeCopyBuildTemplate ----
2025-8-10 11:32:27 - debug: // ---- build task web-mobile：onBeforeCopyBuildTemplate ---- (18ms)
2025-8-10 11:32:27 - debug: web-mobile:(onBeforeCopyBuildTemplate) in 18 ms ✓, progress: 22%
2025-8-10 11:32:27 - debug: 给所有的资源加上 MD5 后缀 start, progress: 22%
2025-8-10 11:32:27 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-10 11:32:27 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (8ms)
2025-8-10 11:32:27 - log: run build task 给所有的资源加上 MD5 后缀 success in 8 ms√, progress: 27%
2025-8-10 11:32:27 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:248.74MB, end 248.85MB, increase: 112.87KB
2025-8-10 11:32:27 - debug: cocos-service:(onAfterBuild) start..., progress: 27%
2025-8-10 11:32:27 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-10 11:32:27 - debug: // ---- build task cocos-service：onAfterBuild ---- (164ms)
2025-8-10 11:32:27 - debug: cocos-service:(onAfterBuild) in 164 ms ✓, progress: 28%
2025-8-10 11:32:27 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 28%
2025-8-10 11:32:27 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-10 11:32:27 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (13ms)
2025-8-10 11:32:27 - debug: adsense-h5g-plugin:(onAfterBuild) in 13 ms ✓, progress: 29%
2025-8-10 11:32:27 - log: Asset DB is resume!
2025-8-10 11:32:27 - debug: builder:build-project-total (4688ms)
2025-8-10 11:32:27 - debug: build success in 4688!
2025-8-10 11:32:27 - debug: [Build Memory track]: builder:build-project-total start:244.45MB, end 253.13MB, increase: 8.68MB
2025-8-10 11:32:27 - debug: ================================ build -> run Task (web-mobile) Finished in (4 s)ms ================================
2025-8-10 11:32:50 - debug: =================================== build Task (web-mobile) Start ================================
2025-8-10 11:32:50 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"web-mobile","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"includeModules":{"gfx-webgl2":"on","physics":"inherit-project-setting","physics-2d":"inherit-project-setting"},"macroConfig":{"cleanupImageCache":"inherit-project-setting"}},"nativeCodeBundleMode":"both","polyfills":{"asyncFunctions":true},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"091c5c0e-b72a-4cad-ab68-635bc57ff236","outputName":"web-mobile","taskName":"web-mobile","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"web-mobile":{"useWebGPU":false,"orientation":"landscape","embedWebDebugger":false,"__version__":"1.0.1"},"adsense-h5g-plugin":{"enableAdsense":false,"enableTestAd":false,"__version__":"1.0.1","AFPHostPropertyCode":"ca-host-pub-5396158963872751","AFPHostDomain":"douyougame.com","otherAFPHostPropertyCode":"","otherAFPDomain":""},"cocos-service":{"configID":"e495ea","services":[]}},"__version__":"1.3.9","logDest":"project://temp/builder/log/web-mobile8-10-2025 11-28.log"}
2025-8-10 11:32:50 - debug: Build with Cocos Creator 3.8.6
2025-8-10 11:32:50 - debug: cocos-service:(onBeforeBuild) start..., progress: 0%
2025-8-10 11:32:50 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-10 11:32:51 - debug: // ---- build task cocos-service：onBeforeBuild ---- (190ms)
2025-8-10 11:32:51 - debug: cocos-service:(onBeforeBuild) in 190 ms ✓, progress: 2%
2025-8-10 11:32:51 - debug: scene:(onBeforeBuild) start..., progress: 2%
2025-8-10 11:32:51 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-10 11:32:51 - debug: // ---- build task scene：onBeforeBuild ---- (24ms)
2025-8-10 11:32:51 - debug: scene:(onBeforeBuild) in 24 ms ✓, progress: 4%
2025-8-10 11:32:51 - debug: Start lock asset db..., progress: 4%
2025-8-10 11:32:51 - log: Asset DB is paused with build!
2025-8-10 11:32:51 - debug: Query all assets info in project
2025-8-10 11:32:51 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-10 11:32:51 - debug: web-mobile:(onAfterInit) start..., progress: 4%
2025-8-10 11:32:51 - debug: // ---- build task web-mobile：onAfterInit ----
2025-8-10 11:32:51 - debug: // ---- build task web-mobile：onAfterInit ---- (6ms)
2025-8-10 11:32:51 - debug: web-mobile:(onAfterInit) in 6 ms ✓, progress: 5%
2025-8-10 11:32:51 - debug: cocos-service:(onAfterInit) start..., progress: 5%
2025-8-10 11:32:51 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-10 11:32:51 - debug: // ---- build task cocos-service：onAfterInit ---- (82ms)
2025-8-10 11:32:51 - debug: cocos-service:(onAfterInit) in 82 ms ✓, progress: 7%
2025-8-10 11:32:51 - debug: Skip compress image, progress: 0%
2025-8-10 11:32:51 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 7%
2025-8-10 11:32:51 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-10 11:32:51 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-10 11:32:51 - debug: [adsense-h5g-plugin] remove script success
2025-8-10 11:32:51 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (11ms)
2025-8-10 11:32:51 - debug(2): adsense-h5g-plugin:(onBeforeBundleInit) in 11 ms ✓, progress: 7%
2025-8-10 11:32:51 - debug(2): Init all bundles start..., progress: 7%
2025-8-10 11:32:51 - debug(2): Num of bundles: 3..., progress: 7%
2025-8-10 11:32:51 - debug(2): web-mobile:(onAfterBundleInit) start..., progress: 7%
2025-8-10 11:32:51 - debug: // ---- build task web-mobile：onAfterBundleInit ----
2025-8-10 11:32:51 - debug: // ---- build task web-mobile：onAfterBundleInit ---- (12ms)
2025-8-10 11:32:51 - debug: web-mobile:(onAfterBundleInit) in 12 ms ✓, progress: 7%
2025-8-10 11:32:51 - debug: web-mobile:(onAfterBundleInit) in 12 ms ✓, progress: 13%
2025-8-10 11:32:51 - debug: 查询 Asset Bundle start, progress: 7%
2025-8-10 11:32:51 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-10 11:32:51 - debug: Init bundle root assets start..., progress: 7%
2025-8-10 11:32:51 - debug: Init bundle root assets start..., progress: 13%
2025-8-10 11:32:51 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-10 11:32:51 - debug:   Number of all scenes: 3
2025-8-10 11:32:51 - debug:   Number of all scripts: 28
2025-8-10 11:32:51 - debug:   Number of other assets: 579
2025-8-10 11:32:51 - debug: Init bundle root assets success..., progress: 7%
2025-8-10 11:32:51 - debug: Init bundle root assets success..., progress: 13%
2025-8-10 11:32:51 - debug: reload all scripts.
2025-8-10 11:32:51 - debug: [[Executor]] reload before lock
2025-8-10 11:32:51 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-10 11:32:51 - groupCollapsed: Invalidate all modules
2025-8-10 11:32:51 - debug: Unregister BuiltinPipelineSettings
2025-8-10 11:32:51 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-10 11:32:51 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-10 11:32:51 - debug: Unregister DebugViewRuntimeControl
2025-8-10 11:32:51 - debug: Unregister CameraFollow
2025-8-10 11:32:51 - debug: Unregister SoundManager
2025-8-10 11:32:51 - debug: Unregister AIPlayer
2025-8-10 11:32:51 - debug: Unregister Bullet
2025-8-10 11:32:51 - debug: Unregister player
2025-8-10 11:32:51 - debug: Unregister PlayerManager
2025-8-10 11:32:51 - debug: Unregister SceneTransition
2025-8-10 11:32:51 - debug: Unregister PaintManager
2025-8-10 11:32:51 - debug: Unregister GameOverPanel
2025-8-10 11:32:51 - debug: Unregister GameHUD
2025-8-10 11:32:51 - debug: Unregister GameManager
2025-8-10 11:32:51 - debug: Unregister AIController
2025-8-10 11:32:51 - debug: Unregister CarProperties
2025-8-10 11:32:51 - debug: Unregister CarPropertyDisplay
2025-8-10 11:32:51 - debug: Unregister HealthBarUI
2025-8-10 11:32:51 - debug: Unregister MainMenuController
2025-8-10 11:32:51 - debug: Unregister PaintSpot
2025-8-10 11:32:51 - debug: Unregister PausePanel
2025-8-10 11:32:51 - debug: Unregister PlayerInfoUI
2025-8-10 11:32:51 - debug: Unregister PurchasePanel
2025-8-10 11:32:51 - debug: Unregister SelectManager
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js'
2025-8-10 11:32:51 - debug: Invalidating 'pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js'
2025-8-10 11:32:51 - groupEnd: Invalidate all modules
2025-8-10 11:32:51 - groupCollapsed: Imports all modules
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register CameraFollow
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register SoundManager
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register AIPlayer
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register Bullet
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register player
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register PlayerManager
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register SceneTransition
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register PaintManager
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register GameOverPanel
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register GameHUD
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register GameManager
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register AIController
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register CarProperties
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register HealthBarUI
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register MainMenuController
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register PaintSpot
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register PausePanel
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register PlayerInfoUI
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register PurchasePanel
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Register SelectManager
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js" loaded.
2025-8-10 11:32:51 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-10 11:32:51 - groupEnd: Imports all modules
2025-8-10 11:32:51 - debug: [[Executor]] after unlock
2025-8-10 11:32:51 - debug: Incremental keys: 
2025-8-10 11:32:51 - debug: Init bundle share assets start..., progress: 7%
2025-8-10 11:32:51 - debug: Init bundle share assets start..., progress: 13%
2025-8-10 11:32:51 - debug: Init bundle share assets success..., progress: 7%
2025-8-10 11:32:51 - debug: Init bundle share assets success..., progress: 13%
2025-8-10 11:32:51 - debug: handle json group in bundle internal
2025-8-10 11:32:51 - debug: handle json group in bundle internal success
2025-8-10 11:32:51 - debug: handle json group in bundle resources
2025-8-10 11:32:51 - debug: handle json group in bundle main
2025-8-10 11:32:51 - debug: init image compress task 0 in bundle internal
2025-8-10 11:32:51 - debug: handle json group in bundle main success
2025-8-10 11:32:51 - debug: init image compress task 0 in bundle main
2025-8-10 11:32:51 - debug: handle json group in bundle resources success
2025-8-10 11:32:51 - debug: init image compress task 0 in bundle resources
2025-8-10 11:32:51 - debug: // ---- build task 查询 Asset Bundle ---- (42ms)
2025-8-10 11:32:51 - log: run build task 查询 Asset Bundle success in 42 ms√, progress: 12%
2025-8-10 11:32:51 - debug: [Build Memory track]: 查询 Asset Bundle start:250.09MB, end 250.61MB, increase: 528.38KB
2025-8-10 11:32:51 - debug: 查询 Asset Bundle start, progress: 12%
2025-8-10 11:32:51 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-10 11:32:51 - debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-10 11:32:51 - log: run build task 查询 Asset Bundle success in 9 ms√, progress: 17%
2025-8-10 11:32:51 - debug: [Build Memory track]: 查询 Asset Bundle start:250.63MB, end 250.84MB, increase: 210.20KB
2025-8-10 11:32:51 - debug: 打包脚本 start, progress: 17%
2025-8-10 11:32:51 - debug: // ---- build task 打包脚本 ----
2025-8-10 11:32:51 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:32:51 - log: [build-script]enter sub process 93437, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-10 11:32:52 - debug: excute-script over with build-script 1289ms
2025-8-10 11:32:52 - debug: Generate systemJs..., progress: 17%
2025-8-10 11:32:52 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:32:53 - debug: excute-script over with build-script 781ms
2025-8-10 11:32:53 - debug: 构建项目脚本 start..., progress: 17%
2025-8-10 11:32:53 - debug: Build script in bundle start, progress: 17%
2025-8-10 11:32:53 - debug: Build script in bundle start, progress: 13%
2025-8-10 11:32:53 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-10 11:32:53 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts


2025-8-10 11:32:53 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts


2025-8-10 11:32:54 - debug: excute-script over with build-script 1248ms
2025-8-10 11:32:54 - debug: Copy externalScripts success!
2025-8-10 11:32:54 - debug: Build script in bundle success, progress: 17%
2025-8-10 11:32:54 - debug: Build script in bundle success, progress: 13%
2025-8-10 11:32:54 - debug: 构建项目脚本 in (1279 ms) √, progress: 17%
2025-8-10 11:32:54 - debug: 构建引擎脚本 start..., progress: 17%
2025-8-10 11:32:54 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-10 11:32:54 - debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/1406d433bcc411a2e63a5d269934fd63)}
2025-8-10 11:32:54 - debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"gfx-webgl",
"gfx-webgl2",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="HTML5",
split=false,
nativeCodeBundleMode="both",
targets=undefined,
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat="runtime-resolved",
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false,
"WEBGPU":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-10 11:32:54 - debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "gfx-webgl",
    "gfx-webgl2",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "cocos-js",
  "platform": "HTML5",
  "useCache": true,
  "nativeCodeBundleMode": "both",
  "wasmCompressionMode": false,
  "split": false,
  "assetURLFormat": "runtime-resolved",
  "output": "/Users/<USER>/projects/cocos_project/driftClash/build/web-mobile/cocos-js",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false,
    "WEBGPU": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-10 11:32:54 - debug: 构建引擎脚本 in (26 ms) √, progress: 17%
2025-8-10 11:32:54 - debug: Copy plugin script ..., progress: 17%
2025-8-10 11:32:54 - debug: Generate import-map..., progress: 17%
2025-8-10 11:32:54 - debug: // ---- build task 打包脚本 ---- (3389ms)
2025-8-10 11:32:54 - log: run build task 打包脚本 success in 3 s√, progress: 22%
2025-8-10 11:32:54 - debug: [Build Memory track]: 打包脚本 start:250.95MB, end 251.64MB, increase: 708.32KB
2025-8-10 11:32:54 - debug: Build Assets start, progress: 22%
2025-8-10 11:32:54 - debug: // ---- build task Build Assets ----
2025-8-10 11:32:54 - debug: Build bundles..., progress: 22%
2025-8-10 11:32:54 - debug: Pack Images start, progress: 22%
2025-8-10 11:32:54 - debug: Pack Images start, progress: 13%
2025-8-10 11:32:54 - debug: builder:pack-auto-atlas-image (16ms)
2025-8-10 11:32:54 - debug: Pack Images success, progress: 22%
2025-8-10 11:32:54 - debug: Pack Images success, progress: 13%
2025-8-10 11:32:54 - debug: Compress image start..., progress: 22%
2025-8-10 11:32:54 - debug: Compress image start..., progress: 13%
2025-8-10 11:32:54 - group: Compress image...
2025-8-10 11:32:54 - debug: sort compress task {}
2025-8-10 11:32:54 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-10 11:32:54 - debug: No image need to compress
2025-8-10 11:32:54 - groupEnd: Compress image...
2025-8-10 11:32:54 - debug: Compress image success..., progress: 22%
2025-8-10 11:32:54 - debug: Compress image success..., progress: 13%
2025-8-10 11:32:54 - debug: Output asset in bundles start, progress: 22%
2025-8-10 11:32:54 - debug: Output asset in bundles start, progress: 13%
2025-8-10 11:32:54 - debug: Handle all json groups in bundle internal
2025-8-10 11:32:54 - debug: handle json group
2025-8-10 11:32:54 - debug: Handle all json groups in bundle resources
2025-8-10 11:32:54 - debug: handle json group
2025-8-10 11:32:54 - debug: Handle all json groups in bundle main
2025-8-10 11:32:54 - debug: handle json group
2025-8-10 11:32:54 - debug: Json group(05b737039) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(0d0eedbd0) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(0af50bc43) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(06585a170) compile success，json number: 6
2025-8-10 11:32:54 - debug: handle single json
2025-8-10 11:32:54 - debug: Json group(0be0c6543) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(0fe72bac9) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(0113159d2) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(01344d7e0) compile success，json number: 6
2025-8-10 11:32:54 - debug: handle single json
2025-8-10 11:32:54 - debug: Json group(023d923cb) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(068713cf2) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(01debcabb) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(079c47cc5) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(018dea86c) compile success，json number: 6
2025-8-10 11:32:54 - debug: Json group(0f116e97b) compile success，json number: 6
2025-8-10 11:32:54 - debug: handle single json
2025-8-10 11:32:54 - debug: Output asset in bundles success, progress: 22%
2025-8-10 11:32:54 - debug: Output asset in bundles success, progress: 13%
2025-8-10 11:32:54 - debug: Output asset in bundles start, progress: 22%
2025-8-10 11:32:54 - debug: Output asset in bundles start, progress: 13%
2025-8-10 11:32:54 - debug: compress config of bundle internal...
2025-8-10 11:32:54 - debug: compress config of bundle internal success
2025-8-10 11:32:54 - debug: compress config of bundle resources...
2025-8-10 11:32:54 - debug: compress config of bundle resources success
2025-8-10 11:32:54 - debug: compress config of bundle main...
2025-8-10 11:32:54 - debug: compress config of bundle main success
2025-8-10 11:32:54 - debug: output config of bundle internal
2025-8-10 11:32:54 - debug: output config of bundle internal success
2025-8-10 11:32:54 - debug: output config of bundle resources
2025-8-10 11:32:54 - debug: output config of bundle resources success
2025-8-10 11:32:54 - debug: output config of bundle main
2025-8-10 11:32:54 - debug: output config of bundle main success
2025-8-10 11:32:54 - debug: Output asset in bundles success, progress: 22%
2025-8-10 11:32:54 - debug: Output asset in bundles success, progress: 13%
2025-8-10 11:32:54 - debug: // ---- build task Build Assets ---- (148ms)
2025-8-10 11:32:54 - log: run build task Build Assets success in 148 ms√, progress: 27%
2025-8-10 11:32:54 - debug: [Build Memory track]: Build Assets start:251.66MB, end 253.60MB, increase: 1.93MB
2025-8-10 11:32:54 - debug: 整理部分构建选项内数据到 settings.json start, progress: 27%
2025-8-10 11:32:54 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-10 11:32:54 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (8ms)
2025-8-10 11:32:54 - log: run build task 整理部分构建选项内数据到 settings.json success in 8 ms√, progress: 29%
2025-8-10 11:32:54 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:253.67MB, end 253.79MB, increase: 117.84KB
2025-8-10 11:32:54 - debug: 填充脚本数据到 settings.json start, progress: 29%
2025-8-10 11:32:54 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-10 11:32:54 - debug: // ---- build task 填充脚本数据到 settings.json ---- (8ms)
2025-8-10 11:32:54 - log: run build task 填充脚本数据到 settings.json success in 8 ms√, progress: 31%
2025-8-10 11:32:54 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:253.81MB, end 253.92MB, increase: 112.75KB
2025-8-10 11:32:54 - debug: 整理部分构建选项内数据到 settings.json start, progress: 31%
2025-8-10 11:32:54 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-10 11:32:54 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-8-10 11:32:54 - log: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 32%
2025-8-10 11:32:54 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:253.95MB, end 254.16MB, increase: 212.08KB
2025-8-10 11:32:54 - debug: web-mobile:(onBeforeCompressSettings) start..., progress: 32%
2025-8-10 11:32:54 - debug: // ---- build task web-mobile：onBeforeCompressSettings ----
2025-8-10 11:32:54 - debug: // ---- build task web-mobile：onBeforeCompressSettings ---- (8ms)
2025-8-10 11:32:54 - debug: web-mobile:(onBeforeCompressSettings) in 8 ms ✓, progress: 34%
2025-8-10 11:32:54 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 34%
2025-8-10 11:32:54 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-10 11:32:54 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (84ms)
2025-8-10 11:32:54 - debug: cocos-service:(onBeforeCompressSettings) in 84 ms ✓, progress: 36%
2025-8-10 11:32:54 - debug: 整理静态模板文件 start, progress: 36%
2025-8-10 11:32:54 - debug: // ---- build task 整理静态模板文件 ----
2025-8-10 11:32:54 - debug: // ---- build task 整理静态模板文件 ---- (17ms)
2025-8-10 11:32:54 - log: run build task 整理静态模板文件 success in 17 ms√, progress: 41%
2025-8-10 11:32:54 - debug: [Build Memory track]: 整理静态模板文件 start:254.52MB, end 254.27MB, increase: -249.47KB
2025-8-10 11:32:54 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 41%
2025-8-10 11:32:54 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-10 11:32:55 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (185ms)
2025-8-10 11:32:55 - debug: cocos-service:(onAfterCompressSettings) in 185 ms ✓, progress: 43%
2025-8-10 11:32:55 - debug: web-mobile:(onBeforeCopyBuildTemplate) start..., progress: 43%
2025-8-10 11:32:55 - debug: // ---- build task web-mobile：onBeforeCopyBuildTemplate ----
2025-8-10 11:32:55 - debug: // ---- build task web-mobile：onBeforeCopyBuildTemplate ---- (14ms)
2025-8-10 11:32:55 - debug: web-mobile:(onBeforeCopyBuildTemplate) in 14 ms ✓, progress: 45%
2025-8-10 11:32:55 - debug: 给所有的资源加上 MD5 后缀 start, progress: 45%
2025-8-10 11:32:55 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-10 11:32:55 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (9ms)
2025-8-10 11:32:55 - log: run build task 给所有的资源加上 MD5 后缀 success in 9 ms√, progress: 55%
2025-8-10 11:32:55 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:254.15MB, end 254.26MB, increase: 112.36KB
2025-8-10 11:32:55 - debug: cocos-service:(onAfterBuild) start..., progress: 55%
2025-8-10 11:32:55 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-10 11:32:55 - debug: // ---- build task cocos-service：onAfterBuild ---- (75ms)
2025-8-10 11:32:55 - debug: cocos-service:(onAfterBuild) in 75 ms ✓, progress: 56%
2025-8-10 11:32:55 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 56%
2025-8-10 11:32:55 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-10 11:32:55 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (9ms)
2025-8-10 11:32:55 - debug: adsense-h5g-plugin:(onAfterBuild) in 9 ms ✓, progress: 58%
2025-8-10 11:32:55 - log: Asset DB is resume!
2025-8-10 11:32:55 - debug: builder:build-project-total (4394ms)
2025-8-10 11:32:55 - debug: build success in 4394!
2025-8-10 11:32:55 - debug: [Build Memory track]: builder:build-project-total start:249.64MB, end 254.87MB, increase: 5.23MB
2025-8-10 11:32:55 - debug: ================================ build Task (web-mobile) Finished in (4 s)ms ================================
