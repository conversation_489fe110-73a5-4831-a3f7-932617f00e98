2025-8-20 22:32:01 - debug: =================================== build Task (ios) Start ================================
2025-8-20 22:32:01 - debug: Start build task, options:
{"name":"喷射飞车-SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"/Users/<USER>/projects","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"091c5c0e-b72a-4cad-ab68-635bc57ff236","outputName":"SuperSplash","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[]},"native":{"encrypted":false,"xxteaKey":"7fpcHqYRZFMY1fcX","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-20-2025 22-32.log"}
2025-8-20 22:32:01 - debug: Build with Cocos Creator 3.8.6
2025-8-20 22:32:01 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-20 22:32:01 - debug: // ---- build task native：onBeforeBuild ----
2025-8-20 22:32:01 - debug: // ---- build task native：onBeforeBuild ---- (13ms)
2025-8-20 22:32:01 - debug: native:(onBeforeBuild) in 13 ms ✓, progress: 2%
2025-8-20 22:32:01 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-20 22:32:01 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-20 22:32:02 - debug: // ---- build task cocos-service：onBeforeBuild ---- (273ms)
2025-8-20 22:32:02 - debug: cocos-service:(onBeforeBuild) in 273 ms ✓, progress: 4%
2025-8-20 22:32:02 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-20 22:32:02 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-20 22:32:05 - debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/mainmenu.scene
background: #aaff85; color: #000;
color: #000;
2025-8-20 22:32:05 - debug: // ---- build task scene：onBeforeBuild ---- (3245ms)
2025-8-20 22:32:05 - debug: scene:(onBeforeBuild) in 3245 ms ✓, progress: 5%
2025-8-20 22:32:05 - debug: Start lock asset db..., progress: 5%
2025-8-20 22:32:05 - log: Asset DB is paused with build!
2025-8-20 22:32:05 - debug: Query all assets info in project
2025-8-20 22:32:05 - debug: asset-db:reimport-asset9a4dce19-24b9-4d46-b38f-ef49f2287dcb (39ms)
2025-8-20 22:32:05 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-20 22:32:05 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-20 22:32:05 - debug: // ---- build task native：onAfterInit ----
2025-8-20 22:32:05 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-20 22:32:05 - debug: // ---- build task native：onAfterInit ---- (136ms)
2025-8-20 22:32:05 - debug: native:(onAfterInit) in 136 ms ✓, progress: 7%
2025-8-20 22:32:05 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-20 22:32:05 - debug: // ---- build task ios：onAfterInit ----
2025-8-20 22:32:05 - debug: // ---- build task ios：onAfterInit ---- (18ms)
2025-8-20 22:32:05 - debug: ios:(onAfterInit) in 18 ms ✓, progress: 9%
2025-8-20 22:32:05 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-20 22:32:05 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-20 22:32:05 - debug: // ---- build task cocos-service：onAfterInit ---- (76ms)
2025-8-20 22:32:05 - debug: cocos-service:(onAfterInit) in 76 ms ✓, progress: 11%
2025-8-20 22:32:05 - debug: Skip compress image, progress: 0%
2025-8-20 22:32:05 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-20 22:32:05 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-20 22:32:05 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-20 22:32:05 - debug: [adsense-h5g-plugin] remove script success
2025-8-20 22:32:05 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (18ms)
2025-8-20 22:32:05 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 18 ms ✓, progress: 11%
2025-8-20 22:32:05 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 18 ms ✓, progress: 7%
2025-8-20 22:32:05 - debug: Init all bundles start..., progress: 11%
2025-8-20 22:32:05 - debug: Init all bundles start..., progress: 7%
2025-8-20 22:32:05 - debug: Num of bundles: 3..., progress: 11%
2025-8-20 22:32:05 - debug: Num of bundles: 3..., progress: 7%
2025-8-20 22:32:05 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-20 22:32:05 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-20 22:32:05 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-20 22:32:05 - debug: // ---- build task native：onAfterBundleInit ---- (27ms)
2025-8-20 22:32:05 - debug: native:(onAfterBundleInit) in 27 ms ✓, progress: 11%
2025-8-20 22:32:05 - debug: native:(onAfterBundleInit) in 27 ms ✓, progress: 13%
2025-8-20 22:32:05 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-20 22:32:05 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-20 22:32:05 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-20 22:32:05 - debug: // ---- build task ios：onAfterBundleInit ---- (17ms)
2025-8-20 22:32:05 - debug: ios:(onAfterBundleInit) in 17 ms ✓, progress: 11%
2025-8-20 22:32:05 - debug: ios:(onAfterBundleInit) in 17 ms ✓, progress: 20%
2025-8-20 22:32:05 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-20 22:32:05 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-20 22:32:05 - debug: Init bundle root assets start..., progress: 11%
2025-8-20 22:32:05 - debug: Init bundle root assets start..., progress: 20%
2025-8-20 22:32:05 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-20 22:32:05 - debug:   Number of all scenes: 3
2025-8-20 22:32:05 - debug:   Number of all scripts: 28
2025-8-20 22:32:05 - debug:   Number of other assets: 609
2025-8-20 22:32:05 - debug: Init bundle root assets success..., progress: 11%
2025-8-20 22:32:05 - debug: Init bundle root assets success..., progress: 20%
2025-8-20 22:32:05 - debug: reload all scripts.
2025-8-20 22:32:05 - log: creating executor ...
2025-8-20 22:32:05 - debug: [[Executor]] reload before lock
2025-8-20 22:32:05 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-20 22:32:05 - groupCollapsed: Invalidate all modules
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/base" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl2" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-empty" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgpu" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/3d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/animation" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/skeletal-animation" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/sorting" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/rich-text" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/mask" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/graphics" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui-skew" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/affine-transform" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle-2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-framework" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-cannon" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-physx" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-ammo" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-builtin" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-framework" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-jsb" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-builtin" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-wasm" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/intersection-2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/primitive" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/profiler" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/geometry-renderer" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/audio" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/video" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/xr" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/light-probe" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/terrain" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/webview" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/tween" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/tiled-map" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/vendor-google" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/spine" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/dragon-bones" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline-post-process" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/legacy-pipeline" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/base" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl2" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-empty" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgpu" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/3d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/animation" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/skeletal-animation" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/sorting" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/rich-text" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/mask" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/graphics" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui-skew" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/affine-transform" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle-2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-framework" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-cannon" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-physx" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-ammo" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-builtin" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-framework" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-jsb" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-builtin" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-wasm" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/intersection-2d" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/primitive" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/profiler" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/geometry-renderer" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/audio" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/video" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/xr" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/light-probe" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/terrain" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/webview" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/tween" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/tiled-map" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/vendor-google" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/spine" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/dragon-bones" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline-post-process" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/cc-fu/legacy-pipeline" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-20 22:32:05 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-20 22:32:05 - groupEnd: Invalidate all modules
2025-8-20 22:32:05 - groupCollapsed: Imports all modules
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/engine-export-to-editor/cc/editor/populate-internal-constants" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "cce:/internal/x/engine-export-to-editor/cc/editor/populate-internal-constants" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js is not in module cache!
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/91/91db69cfc7638e790a8b4a74e68aa446e097c982.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register CameraFollow
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/60/60f6ecf5cc17c1da6598d7f9202a78b81dc8873a.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register SoundManager
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/ab/ab66a6622ee4b8eeb035aa78898b50aa594decd6.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register Bullet
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/d9/d90bd5f660cca2468e55c522be6e08ed7db4093d.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register AIPlayer
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/9b/9b583689b76a894a84c2dd298f4c18778bad0e35.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register player
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/43/43ffc2180738b118a8d4d533f2af70ab9b24d0aa.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register PlayerManager
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/0c/0cef8fa3cb540388a42b92cbc6d47180cf46622f.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register SceneTransition
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/74/74948618388cf8fc1977e55f6b358c932c03ec6a.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register PaintManager
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/9c/9c055d4007d002c90b7c1036ef6d1c7ec7d50367.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register GameOverPanel
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/60/60586318bbf26c095fcfd4f6c478aec79fca8ba9.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register GameHUD
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/07/07f6d5c86652bda9da01d2590bb16b9aa9cca3d7.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register GameManager
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/d9/d99ffa78595ef86f45405646d5cc14ad1480dee0.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Register AIController
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/b4/b4ba6f604644fedf452e826105d75ce74a068d1f.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register CarProperties
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/7a/7ace6a5335481e920b9baff76698aea45fdf9be1.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/d1/d1cee1a3354c09e396b1a69dbe10e9cb83fedf20.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register HealthBarUI
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/26/26c36b18bd4a3ff52ca03e2c561f5c664b8a98dd.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register MainMenuController
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/2a/2a304f0f942d7336e9dc095417a34a706c892e8f.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register PaintSpot
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/14/140b5316d14e00f2df5f23208cfd6cf8dd64b85b.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register PausePanel
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/9e/9e5cb90874896d4d482aab7771a29bec1c95f34b.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register PlayerInfoUI
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/57/5747e286ea7bb225640df61cf460ffb63f9b975e.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register PurchasePanel
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/aa/aaafdbc479e118e05b87b66f5a036fc792fd6c50.js" loaded.
2025-8-20 22:32:05 - debug: /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js resolved to /Users/<USER>/projects/cocos_project/driftClash/temp/programming/packer-driver/targets/editor/chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js is not in module cache!
2025-8-20 22:32:05 - debug: [[Executor]] Register SelectManager
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/b9/b9c16df5862bbcca6e642da38ede09779782ac3e.js" loaded.
2025-8-20 22:32:05 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-20 22:32:05 - groupEnd: Imports all modules
2025-8-20 22:32:05 - debug: [[Executor]] after unlock
2025-8-20 22:32:05 - debug: Incremental keys: regeneratorRuntime
2025-8-20 22:32:05 - debug: Init bundle share assets start..., progress: 11%
2025-8-20 22:32:05 - debug: Init bundle share assets start..., progress: 20%
2025-8-20 22:32:05 - warn: The SpriteFrame used by component "cc.Sprite" in prefab "level-1" is missing. Detailed information:
Node path: "level-1/soccor_court"
Asset url: "db://assets/resources/prefab/levels/level-1"
Asset file: "/Users/<USER>/projects/cocos_project/driftClash/assets/img/background/soccor_court.jpeg"
Asset deleted time: "2025/8/18 21:56:49"
Missing uuid: "6f121691-4929-49ed-b949-7e2d17747ab9@f9941"


2025-8-20 22:32:05 - debug: Init bundle share assets success..., progress: 11%
2025-8-20 22:32:05 - debug: Init bundle share assets success..., progress: 20%
2025-8-20 22:32:05 - debug: handle json group in bundle internal
2025-8-20 22:32:05 - debug: handle json group in bundle internal success
2025-8-20 22:32:05 - debug: handle json group in bundle resources
2025-8-20 22:32:05 - debug: handle json group in bundle main
2025-8-20 22:32:05 - debug: init image compress task 0 in bundle internal
2025-8-20 22:32:05 - debug: handle json group in bundle main success
2025-8-20 22:32:05 - debug: init image compress task 0 in bundle main
2025-8-20 22:32:05 - debug: handle json group in bundle resources success
2025-8-20 22:32:05 - debug: init image compress task 0 in bundle resources
2025-8-20 22:32:05 - debug: // ---- build task 查询 Asset Bundle ---- (168ms)
2025-8-20 22:32:05 - log: run build task 查询 Asset Bundle success in 168 ms√, progress: 16%
2025-8-20 22:32:05 - debug: [Build Memory track]: 查询 Asset Bundle start:208.98MB, end 212.56MB, increase: 3.58MB
2025-8-20 22:32:05 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-20 22:32:05 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-20 22:32:05 - debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-8-20 22:32:05 - log: run build task 查询 Asset Bundle success in 23 ms√, progress: 21%
2025-8-20 22:32:05 - debug: [Build Memory track]: 查询 Asset Bundle start:212.59MB, end 212.96MB, increase: 376.59KB
2025-8-20 22:32:05 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-20 22:32:05 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-20 22:32:05 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-20 22:32:05 - debug: // ---- build task native：onAfterBundleDataTask ---- (21ms)
2025-8-20 22:32:05 - debug: native:(onAfterBundleDataTask) in 21 ms ✓, progress: 21%
2025-8-20 22:32:05 - debug: native:(onAfterBundleDataTask) in 21 ms ✓, progress: 27%
2025-8-20 22:32:05 - debug: 打包脚本 start, progress: 21%
2025-8-20 22:32:05 - debug: // ---- build task 打包脚本 ----
2025-8-20 22:32:05 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 22:32:06 - log: [build-script]enter sub process 10573, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-20 22:32:06 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-20 22:32:06 - debug: excute-script over with build-script 903ms
2025-8-20 22:32:06 - debug: Generate systemJs..., progress: 21%
2025-8-20 22:32:06 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 22:32:07 - debug: excute-script over with build-script 1048ms
2025-8-20 22:32:07 - debug: 构建项目脚本 start..., progress: 21%
2025-8-20 22:32:07 - debug: Build script in bundle start, progress: 21%
2025-8-20 22:32:07 - debug: Build script in bundle start, progress: 27%
2025-8-20 22:32:07 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 22:32:08 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts


2025-8-20 22:32:08 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts


2025-8-20 22:32:09 - debug: excute-script over with build-script 1200ms
2025-8-20 22:32:09 - debug: Copy externalScripts success!
2025-8-20 22:32:09 - debug: Build script in bundle success, progress: 21%
2025-8-20 22:32:09 - debug: Build script in bundle success, progress: 27%
2025-8-20 22:32:09 - debug: 构建项目脚本 in (1269 ms) √, progress: 21%
2025-8-20 22:32:09 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-20 22:32:09 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-20 22:32:09 - debug: Engine cache (/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf) does not exist.
2025-8-20 22:32:09 - debug: mangleProperties is disabled, platform: IOS
2025-8-20 22:32:09 - debug: Cache is invalid, start build engine with options: {
  "incremental": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.watch-files.json",
  "engine": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
  "out": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf",
  "moduleFormat": "system",
  "compress": true,
  "nativeCodeBundleMode": "asmjs",
  "sourceMap": false,
  "targets": "chrome 80",
  "loose": true,
  "features": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "platform": "IOS",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "mode": "BUILD",
  "metaFile": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.meta/meta.json",
  "wasmCompressionMode": false,
  "inlineEnum": true,
  "mangleProperties": false,
  "mangleConfigJsonMtime": 0
}

2025-8-20 22:32:09 - debug: md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-20 22:32:09 - log: Run build task(build-engine) in child, see: chrome://inspect/#devices
2025-8-20 22:32:09 - log: [build-engine]enter sub process 10602, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-20 22:32:09 - log: [build-engine]start build engine with options: {"engine":"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine","out":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf","platform":"IOS","moduleFormat":"system","compress":true,"split":false,"nativeCodeBundleMode":"asmjs","assetURLFormat":"runtime-resolved","noDeprecatedFeatures":false,"sourceMap":false,"features":["2d","affine-transform","animation","audio","base","custom-pipeline","dragon-bones","graphics","intersection-2d","mask","particle-2d","physics-2d-box2d","profiler","rich-text","spine-3.8","tiled-map","tween","ui","video","webview","custom-pipeline-builtin-scripts"],"loose":true,"mode":"BUILD","flags":{"DEBUG":false,"LOAD_BULLET_MANUALLY":false,"LOAD_SPINE_MANUALLY":false},"metaFile":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.meta/meta.json","mangleProperties":false,"inlineEnum":true,"incremental":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.watch-files.json","targets":"chrome 80","wasmCompressionMode":false,"mangleConfigJsonMtime":0}


2025-8-20 22:32:10 - log: [build-engine]Module source "internal-constants":
function tryDefineGlobal (name, value) {
    const _global = typeof window === 'undefined' ? global : window;
    if (typeof _global[name] === 'undefined') {
        return (_global[name] = value);
    } else {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return _global[name];
    }
}
export const HTML5 = false;

export const NATIVE = true;

export const ANDROID = false;

export const IOS = true;

export const MAC = false;

export const WINDOWS = false;

export const LINUX = false;

export const OHOS = false;

export const OPEN_HARMONY = false;

export const WECHAT = false;
tryDefineGlobal('CC_WECHAT', false);

export const WECHAT_MINI_PROGRAM = false;

export const XIAOMI = false;
tryDefineGlobal('CC_XIAOMI', false);

export const ALIPAY = false;
tryDefineGlobal('CC_ALIPAY', false);

export const TAOBAO = false;

export const TAOBAO_MINIGAME = false;

export const BYTEDANCE = false;
tryDefineGlobal('CC_BYTEDANCE', false);

export const OPPO = false;
tryDefineGlobal('CC_OPPO', false);

export const VIVO = false;
tryDefineGlobal('CC_VIVO', false);

export const HUAWEI = false;
tryDefineGlobal('CC_HUAWEI', false);

export const MIGU = false;
tryDefineGlobal('CC_MIGU', false);

export const HONOR = false;
tryDefineGlobal('CC_HONOR', false);

export const COCOS_RUNTIME = false;
tryDefineGlobal('CC_COCOS_RUNTIME', false);

export const EDITOR = false;
tryDefineGlobal('CC_EDITOR', false);

export const EDITOR_NOT_IN_PREVIEW = false;

export const PREVIEW = false;
tryDefineGlobal('CC_PREVIEW', false);

export const BUILD = true;
tryDefineGlobal('CC_BUILD', true);

export const TEST = false;
tryDefineGlobal('CC_TEST', false);

export const DEBUG = false;
tryDefineGlobal('CC_DEBUG', false);

export const SERVER_MODE = false;

export const DEV = false;
tryDefineGlobal('CC_DEV', false);

export const MINIGAME = false;
tryDefineGlobal('CC_MINIGAME', false);

export const RUNTIME_BASED = false;
tryDefineGlobal('CC_RUNTIME_BASED', false);

export const SUPPORT_JIT = true;
tryDefineGlobal('CC_SUPPORT_JIT', true);

export const JSB = true;
tryDefineGlobal('CC_JSB', true);

export const NOT_PACK_PHYSX_LIBS = false;

export const NET_MODE = 0;

export const WEBGPU = false;

export const NATIVE_CODE_BUNDLE_MODE = 0;

export const WASM_SUBPACKAGE = false;

export const CULL_MESHOPT = true;

export const LOAD_SPINE_MANUALLY = false;

export const LOAD_BOX2D_MANUALLY = false;

export const LOAD_BULLET_MANUALLY = false;

export const LOAD_PHYSX_MANUALLY = false;

export const USE_3D = false;

export const USE_UI_SKEW = false;

export const USE_XR = false;


Module source "cc":
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/sorting.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/affine-transform.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/animation.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/audio.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/base.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/custom-pipeline.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/dragon-bones.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/graphics.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/intersection-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/mask.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/particle-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-box2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-framework.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/profiler.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/rich-text.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/spine.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tiled-map.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tween.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/ui.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/video.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/webview.ts';


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/custom/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/custom/index.jsb.ts


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/index.jsb.ts


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/root.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/root.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/index.jsb.ts


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/index.jsb.ts


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/dragon-bones/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/dragon-bones/index.jsb.ts


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/index.jsb.ts


2025-8-20 22:32:16 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/pass.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/pass.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/program-lib.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/program-lib.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/material-instance.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/material-instance.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-scene.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-scene.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-window.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-window.jsb.ts


2025-8-20 22:32:17 - log: [build-engine]Redirect module internal:native to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/native-binding/impl.ts


2025-8-20 22:32:17 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/asset-manager/builtin-res-mgr.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/asset-manager/builtin-res-mgr.jsb.ts


2025-8-20 22:32:17 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/material.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/material.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/node.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/node.jsb.ts


2025-8-20 22:32:17 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/runtime-exports.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/index-empty.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/asset.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/camera.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/camera.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/submodel.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/submodel.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/native-pools.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/native-pools.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/render-texture.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/render-texture.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/scene-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/scene-asset.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module pal/system-info to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/system-info/native/system-info.ts
Redirect module pal/env to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/env/native/env.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module pal/pacer to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/pacer/pacer-native.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module pal/input to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/input/native/index.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-2d.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/image-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/image-asset.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module pal/audio to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/audio/native/player.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/misc/create-mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/misc/create-mesh.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/assets/mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/assets/mesh.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/models/morph-model.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/models/morph-model.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module pal/minigame to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/minigame/non-minigame.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-base.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-base.jsb.ts


2025-8-20 22:32:18 - log: [build-engine]Redirect module pal/screen-adapter to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/screen-adapter/native/screen-adapter.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/buffer-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/buffer-asset.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene-globals.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene-globals.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/base/pipeline-state.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/base/pipeline-state.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version-3.8.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/rendering-sub-mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/rendering-sub-mesh.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-cube.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-cube.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/effect-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/effect-asset.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/simple-texture.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/simple-texture.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/model.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/model.jsb.ts


2025-8-20 22:32:19 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/reflection-probe.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/reflection-probe.jsb.ts


2025-8-20 22:32:20 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-20 22:32:20 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-20 22:32:20 - log: [build-engine]==== Performance ====
{"# BUILD":[10288.788958,1082578144,1222732620],"## initialize":[6018.077666,424878068,565536164],"- plugin 6 (node-resolve) - buildStart":[0.03487500000005639,-483424,140178388],"- plugin 8 (commonjs) - buildStart":[0.013166000000182976,3360,140182592],"- plugin 9 (@cocos/typescript) - buildStart":[6017.991625,425351224,565534172],"## generate module graph":[3560.9462919999996,602014352,1167551104],"- plugin 0 (commonjs--resolver) - resolveId":[8.792070000012245,1600456,1166088456],"- plugin 1 (@cocos/ccbuild|external-loader) - resolveId":[3.831890000036765,333332,1165426628],"- plugin 2 (@cocos/ccbuild|module-overrides) - resolveId":[3.61670100003721,114028,1165433544],"- plugin 3 (virtual) - resolveId":[7.7146950000023935,4071524,1165435792],"- plugin 1 (@cocos/ccbuild|external-loader) - load":[2.5703400000129477,-4351444,1166106996],"- plugin 2 (@cocos/ccbuild|module-overrides) - load":[3.979330000011032,1547296,1166108240],"- plugin 3 (virtual) - load":[0.8369120000361363,-417400,1166109164],"- plugin 7 (json) - transform":[1.8280839999742966,705668,1166115064],"- plugin 8 (commonjs) - transform":[68.65736500000276,14501784,1166116796],"- plugin 10 (babel) - transform":[27.06439900001078,10812852,1166118772],"generate ast":[303.70533000000887,171427072,1166187340],"- plugin 4 (@cocos/ccbuild|module-query-plugin) - resolveId":[4.960683000047538,2729956,1165466424],"- plugin 5 (ts-paths) - resolveId":[2.9291570000041247,738228,1165535252],"- plugin 9 (@cocos/typescript) - resolveId":[337.4354000000103,70886136,1165623836],"- plugin 10 (babel) - resolveId":[0.683749999999236,-355540,1165627916],"- plugin 6 (node-resolve) - resolveId":[1.3830909999987853,516492,1011455560],"- plugin 6 (node-resolve) - load":[0.6828019999857133,109756,1166110096],"- plugin 8 (commonjs) - load":[0.9093480000110503,-149344,1166111600],"- plugin 9 (@cocos/typescript) - load":[9.997794999991129,-3286232,1011481092],"- plugin 10 (babel) - load":[69.06958899999881,5484180,1011481752],"## sort and bind modules":[84.85966700000063,8792344,1176343780],"## mark included statements":[621.2657919999983,45702876,1222046948],"treeshaking pass 1":[181.52187500000036,29596692,1206140108],"treeshaking pass 2":[170.38462499999878,5360712,1211501112],"treeshaking pass 3":[54.837665999999444,7896292,1219397696],"treeshaking pass 4":[39.791832999999315,-3976740,1215421248],"treeshaking pass 5":[32.9912089999998,2416220,1217837760],"treeshaking pass 6":[30.29975000000013,-2622544,1215215508],"treeshaking pass 7":[28.579750000000786,2642612,1217858412],"treeshaking pass 8":[27.418582999998762,1599204,1219457908],"treeshaking pass 9":[27.97662499999933,1319256,1220777456],"treeshaking pass 10":[26.97204199999942,1268676,1222046424],"- plugin 8 (commonjs) - buildEnd":[0.0054579999996349216,384,1222050048],"- plugin 9 (@cocos/typescript) - buildEnd":[3.4712500000005093,680960,1222731316]}


2025-8-20 22:32:20 - log: [build-engine]====             ====


2025-8-20 22:32:26 - debug: excute-script over with build-engine 17008ms
2025-8-20 22:32:26 - debug: build engine done: output: /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf
2025-8-20 22:32:26 - debug: 构建引擎脚本 in (17041 ms) √, progress: 21%
2025-8-20 22:32:26 - debug: Copy plugin script ..., progress: 21%
2025-8-20 22:32:26 - debug: Generate import-map..., progress: 21%
2025-8-20 22:32:26 - debug: // ---- build task 打包脚本 ---- (20297ms)
2025-8-20 22:32:26 - log: run build task 打包脚本 success in 20 s√, progress: 26%
2025-8-20 22:32:26 - debug: [Build Memory track]: 打包脚本 start:213.42MB, end 207.62MB, increase: -5939.14KB
2025-8-20 22:32:26 - debug: Build Assets start, progress: 26%
2025-8-20 22:32:26 - debug: // ---- build task Build Assets ----
2025-8-20 22:32:26 - debug: Build bundles..., progress: 26%
2025-8-20 22:32:26 - debug: Pack Images start, progress: 26%
2025-8-20 22:32:26 - debug: Pack Images start, progress: 27%
2025-8-20 22:32:26 - debug: builder:pack-auto-atlas-image (51ms)
2025-8-20 22:32:26 - debug: Pack Images success, progress: 26%
2025-8-20 22:32:26 - debug: Pack Images success, progress: 27%
2025-8-20 22:32:26 - debug: Compress image start..., progress: 26%
2025-8-20 22:32:26 - debug: Compress image start..., progress: 27%
2025-8-20 22:32:26 - group: Compress image...
2025-8-20 22:32:26 - debug: sort compress task {}
2025-8-20 22:32:26 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-20 22:32:26 - debug: No image need to compress
2025-8-20 22:32:26 - groupEnd: Compress image...
2025-8-20 22:32:26 - debug: Compress image success..., progress: 26%
2025-8-20 22:32:26 - debug: Compress image success..., progress: 27%
2025-8-20 22:32:26 - debug: Output asset in bundles start, progress: 26%
2025-8-20 22:32:26 - debug: Output asset in bundles start, progress: 27%
2025-8-20 22:32:26 - debug: Handle all json groups in bundle internal
2025-8-20 22:32:26 - debug: handle json group
2025-8-20 22:32:26 - debug: Handle all json groups in bundle resources
2025-8-20 22:32:26 - debug: handle json group
2025-8-20 22:32:26 - debug: Handle all json groups in bundle main
2025-8-20 22:32:26 - debug: handle json group
2025-8-20 22:32:26 - debug: Json group(05b737039) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(06585a170) compile success，json number: 6
2025-8-20 22:32:26 - debug: handle single json
2025-8-20 22:32:26 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(01959b579) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(08532c3e3) compile success，json number: 6
2025-8-20 22:32:26 - debug: handle single json
2025-8-20 22:32:26 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-20 22:32:26 - warn: The SpriteFrame used by component "cc.Sprite" in prefab "level-1" is missing. Detailed information:
Node path: "level-1/soccor_court"
Asset url: "db://assets/resources/prefab/levels/level-1"
Asset file: "/Users/<USER>/projects/cocos_project/driftClash/assets/img/background/soccor_court.jpeg"
Asset deleted time: "2025/8/18 21:56:49"
Missing uuid: "6f121691-4929-49ed-b949-7e2d17747ab9@f9941"


2025-8-20 22:32:26 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-20 22:32:26 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-20 22:32:26 - debug: handle single json
2025-8-20 22:32:26 - debug: Output asset in bundles success, progress: 26%
2025-8-20 22:32:26 - debug: Output asset in bundles success, progress: 27%
2025-8-20 22:32:26 - debug: Output asset in bundles start, progress: 26%
2025-8-20 22:32:26 - debug: Output asset in bundles start, progress: 27%
2025-8-20 22:32:26 - debug: compress config of bundle internal...
2025-8-20 22:32:26 - debug: compress config of bundle internal success
2025-8-20 22:32:26 - debug: compress config of bundle resources...
2025-8-20 22:32:26 - debug: compress config of bundle resources success
2025-8-20 22:32:26 - debug: compress config of bundle main...
2025-8-20 22:32:26 - debug: compress config of bundle main success
2025-8-20 22:32:26 - debug: output config of bundle internal
2025-8-20 22:32:26 - debug: output config of bundle internal success
2025-8-20 22:32:26 - debug: output config of bundle resources
2025-8-20 22:32:26 - debug: output config of bundle resources success
2025-8-20 22:32:26 - debug: output config of bundle main
2025-8-20 22:32:26 - debug: output config of bundle main success
2025-8-20 22:32:26 - debug: Output asset in bundles success, progress: 26%
2025-8-20 22:32:26 - debug: Output asset in bundles success, progress: 27%
2025-8-20 22:32:26 - debug: // ---- build task Build Assets ---- (480ms)
2025-8-20 22:32:26 - log: run build task Build Assets success in 480 ms√, progress: 31%
2025-8-20 22:32:26 - debug: [Build Memory track]: Build Assets start:207.66MB, end 210.24MB, increase: 2.58MB
2025-8-20 22:32:26 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-20 22:32:26 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-20 22:32:26 - debug: // ---- build task ios：onAfterBuildAssets ---- (25ms)
2025-8-20 22:32:26 - debug: ios:(onAfterBuildAssets) in 25 ms ✓, progress: 33%
2025-8-20 22:32:26 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-20 22:32:26 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-20 22:32:26 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (24ms)
2025-8-20 22:32:26 - log: run build task 整理部分构建选项内数据到 settings.json success in 24 ms√, progress: 34%
2025-8-20 22:32:26 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.05MB, end 209.31MB, increase: 268.67KB
2025-8-20 22:32:26 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-20 22:32:26 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-20 22:32:26 - debug: // ---- build task 填充脚本数据到 settings.json ---- (51ms)
2025-8-20 22:32:26 - log: run build task 填充脚本数据到 settings.json success in 51 ms√, progress: 36%
2025-8-20 22:32:26 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.33MB, end 209.60MB, increase: 269.89KB
2025-8-20 22:32:26 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-20 22:32:26 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-20 22:32:26 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (91ms)
2025-8-20 22:32:26 - log: run build task 整理部分构建选项内数据到 settings.json success in 91 ms√, progress: 38%
2025-8-20 22:32:26 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.62MB, end 210.11MB, increase: 496.76KB
2025-8-20 22:32:26 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-20 22:32:26 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-20 22:32:26 - debug: // ---- build task ios：onBeforeCompressSettings ---- (33ms)
2025-8-20 22:32:26 - debug: ios:(onBeforeCompressSettings) in 33 ms ✓, progress: 40%
2025-8-20 22:32:26 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-20 22:32:26 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-20 22:32:27 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (254ms)
2025-8-20 22:32:27 - debug: cocos-service:(onBeforeCompressSettings) in 254 ms ✓, progress: 41%
2025-8-20 22:32:27 - debug: 整理静态模板文件 start, progress: 41%
2025-8-20 22:32:27 - debug: // ---- build task 整理静态模板文件 ----
2025-8-20 22:32:27 - debug: // ---- build task 整理静态模板文件 ---- (55ms)
2025-8-20 22:32:27 - log: run build task 整理静态模板文件 success in 55 ms√, progress: 46%
2025-8-20 22:32:27 - debug: [Build Memory track]: 整理静态模板文件 start:209.21MB, end 209.71MB, increase: 520.04KB
2025-8-20 22:32:27 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-20 22:32:27 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-20 22:32:27 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"喷射飞车-SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-20 22:32:27 - log: While replace template content, file /Users/<USER>/projects/SuperSplash/proj/main.cpp
2025-8-20 22:32:27 - log: While replace template content, file /Users/<USER>/projects/SuperSplash/proj/main.m
2025-8-20 22:32:27 - log: While replace template content, file /Users/<USER>/projects/cocos_project/driftClash/native/engine/ios/app/build.gradle
2025-8-20 22:32:27 - log: While replace template content, file /Users/<USER>/projects/cocos_project/driftClash/native/engine/ios/instantapp/build.gradle
2025-8-20 22:32:27 - log: While replace template content, file /Users/<USER>/projects/cocos_project/driftClash/native/engine/ios/entry/src/main/config.json
2025-8-20 22:32:27 - debug: // ---- build task native：onAfterCompressSettings ---- (55ms)
2025-8-20 22:32:27 - debug: native:(onAfterCompressSettings) in 55 ms ✓, progress: 48%
2025-8-20 22:32:27 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-20 22:32:27 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-20 22:32:27 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (100ms)
2025-8-20 22:32:27 - debug: cocos-service:(onAfterCompressSettings) in 100 ms ✓, progress: 50%
2025-8-20 22:32:27 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-20 22:32:27 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-20 22:32:27 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (22ms)
2025-8-20 22:32:27 - log: run build task 给所有的资源加上 MD5 后缀 success in 22 ms√, progress: 60%
2025-8-20 22:32:27 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:211.23MB, end 210.11MB, increase: -1140.95KB
2025-8-20 22:32:27 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-20 22:32:27 - debug: // ---- build task native：onAfterBuild ----
2025-8-20 22:32:27 - log: [xcode-select] /Library/Developer/CommandLineTools


2025-8-20 22:32:27 - error: Please check if Xcode is installed.
2025-8-20 22:32:27 - error: Error: Command failed: xcrun xcodebuild -version
xcrun: error: unable to find utility "xcodebuild", not a developer tool or in PATH

    at genericNodeError (node:internal/errors:984:15)
    at wrappedFn (node:internal/errors:538:14)
    at checkExecSyncError (node:child_process:905:11)
    at execSync (node:child_process:977:15)
    at node:electron/js2c/node_init:2:16388
    at Object.getXcodeMajorVerion (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:421:36)
    at IOSPackTool.generate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/platforms/ios.ts:103:32)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at NativePackToolManager.generate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/base/manager.ts:48:9)
    at PackToolHandler.runTask (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/modules/platform-extensions/extensions/native/dist/builder/native-utils/index.ccc:1:2968)
    at BuildTask.onAfterBuild (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/modules/platform-extensions/extensions/native/dist/builder/hooks.ccc:1:5530)
    at BuildTask.handleHook (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/builder/index.ccc:1:13689)
    at BuildTask.runPluginTask (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/builder/manager/task-base.ccc:1:1272)
    at BuildTask.run (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/builder/index.ccc:1:5246)
    at build (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/index.ccc:1:4259)
    at Ipc.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/msg-util.ccc:1:210)
2025-8-20 22:32:27 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/driftClash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/SuperSplash/proj" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/SuperSplash" -DAPP_NAME="喷射飞车-SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-20 22:32:28 - error: [cmake-err] CMake Error at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/iOS-Initialize.cmake:4 (message):
  iphoneos is not an iOS SDK
Call Stack (most recent call first):
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake:21 (include)
  CMakeLists.txt:6 (project)



2025-8-20 22:32:28 - log: [cmake] -- Configuring incomplete, errors occurred!
See also "/Users/<USER>/projects/SuperSplash/proj/CMakeFiles/CMakeOutput.log".


2025-8-20 22:32:28 - error: 构建插件 native 的钩子函数 onAfterBuild 执行失败，请检查插件的代码逻辑~, progress: 62%
2025-8-20 22:32:28 - debug: Error: run cmake failed "cmake -S "/Users/<USER>/projects/cocos_project/driftClash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/SuperSplash/proj" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/SuperSplash" -DAPP_NAME="喷射飞车-SuperSplash" -DLAUNCH_TYPE="Release"", code: 1, signal: null, progress: 64%
2025-8-20 22:32:28 - debug: [Build Memory track]: builder:build-project-total start:203.26MB, end 213.01MB, increase: 9.75MB
2025-8-20 22:32:28 - log: Asset DB is resume!
2025-8-20 22:32:28 - debug: builder:build-project-total (27016ms)
2025-8-20 22:32:28 - debug: build success in 27016!
2025-8-20 22:32:28 - error: Error: run cmake failed "cmake -S "/Users/<USER>/projects/cocos_project/driftClash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/SuperSplash/proj" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/SuperSplash" -DAPP_NAME="喷射飞车-SuperSplash" -DLAUNCH_TYPE="Release"", code: 1, signal: null
    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)
    at ChildProcess.emit (node:events:519:28)
    at maybeClose (node:internal/child_process:1105:16)
    at Socket.<anonymous> (node:internal/child_process:457:11)
    at Socket.emit (node:events:519:28)
    at Pipe.<anonymous> (node:net:338:12)
2025-8-20 22:32:28 - debug: ================================ build Task (ios) Finished in (27 s)ms ================================
