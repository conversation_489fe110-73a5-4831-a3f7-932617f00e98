2025-8-20 23:42:57 - debug: =================================== build Task (ios) Start ================================
2025-8-20 23:42:57 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb","outputName":"ios","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"ceW9Tq/J9DGq1uEO","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-20-2025 23-42.log"}
2025-8-20 23:42:57 - debug: Build with Cocos Creator 3.8.6
2025-8-20 23:42:57 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-20 23:42:57 - debug: // ---- build task native：onBeforeBuild ----
2025-8-20 23:42:57 - debug: // ---- build task native：onBeforeBuild ---- (27ms)
2025-8-20 23:42:57 - debug: native:(onBeforeBuild) in 27 ms ✓, progress: 2%
2025-8-20 23:42:57 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-20 23:42:57 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-20 23:42:58 - debug: // ---- build task cocos-service：onBeforeBuild ---- (615ms)
2025-8-20 23:42:58 - debug: cocos-service:(onBeforeBuild) in 615 ms ✓, progress: 4%
2025-8-20 23:42:58 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-20 23:42:58 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-20 23:42:58 - debug: // ---- build task scene：onBeforeBuild ---- (38ms)
2025-8-20 23:42:58 - debug: scene:(onBeforeBuild) in 38 ms ✓, progress: 5%
2025-8-20 23:42:58 - debug: Start lock asset db..., progress: 5%
2025-8-20 23:42:58 - log: Asset DB is paused with build!
2025-8-20 23:42:58 - debug: Query all assets info in project
2025-8-20 23:42:58 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-20 23:42:58 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-20 23:42:58 - debug: // ---- build task native：onAfterInit ----
2025-8-20 23:42:58 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-20 23:42:58 - debug: // ---- build task native：onAfterInit ---- (22ms)
2025-8-20 23:42:58 - debug: native:(onAfterInit) in 22 ms ✓, progress: 7%
2025-8-20 23:42:58 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-20 23:42:58 - debug: // ---- build task ios：onAfterInit ----
2025-8-20 23:42:58 - debug: // ---- build task ios：onAfterInit ---- (31ms)
2025-8-20 23:42:58 - debug: ios:(onAfterInit) in 31 ms ✓, progress: 9%
2025-8-20 23:42:58 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-20 23:42:58 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-20 23:42:58 - debug: // ---- build task cocos-service：onAfterInit ---- (94ms)
2025-8-20 23:42:58 - debug: cocos-service:(onAfterInit) in 94 ms ✓, progress: 11%
2025-8-20 23:42:58 - debug: Skip compress image, progress: 0%
2025-8-20 23:42:58 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-20 23:42:58 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-20 23:42:58 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-20 23:42:58 - debug: [adsense-h5g-plugin] remove script success
2025-8-20 23:42:58 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (32ms)
2025-8-20 23:42:58 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 32 ms ✓, progress: 11%
2025-8-20 23:42:58 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 32 ms ✓, progress: 7%
2025-8-20 23:42:58 - debug: Init all bundles start..., progress: 11%
2025-8-20 23:42:58 - debug: Init all bundles start..., progress: 7%
2025-8-20 23:42:58 - debug: Num of bundles: 3..., progress: 11%
2025-8-20 23:42:58 - debug: Num of bundles: 3..., progress: 7%
2025-8-20 23:42:58 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-20 23:42:58 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-20 23:42:58 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-20 23:42:58 - debug: // ---- build task native：onAfterBundleInit ---- (49ms)
2025-8-20 23:42:58 - debug: native:(onAfterBundleInit) in 49 ms ✓, progress: 11%
2025-8-20 23:42:58 - debug: native:(onAfterBundleInit) in 49 ms ✓, progress: 13%
2025-8-20 23:42:58 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-20 23:42:58 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-20 23:42:58 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-20 23:42:58 - debug: // ---- build task ios：onAfterBundleInit ---- (33ms)
2025-8-20 23:42:58 - debug: ios:(onAfterBundleInit) in 33 ms ✓, progress: 11%
2025-8-20 23:42:58 - debug: ios:(onAfterBundleInit) in 33 ms ✓, progress: 20%
2025-8-20 23:42:58 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-20 23:42:58 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-20 23:42:58 - debug: Init bundle root assets start..., progress: 11%
2025-8-20 23:42:58 - debug: Init bundle root assets start..., progress: 20%
2025-8-20 23:42:58 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-20 23:42:58 - debug:   Number of all scenes: 3
2025-8-20 23:42:58 - debug:   Number of all scripts: 28
2025-8-20 23:42:58 - debug:   Number of other assets: 609
2025-8-20 23:42:58 - debug: Init bundle root assets success..., progress: 11%
2025-8-20 23:42:58 - debug: Init bundle root assets success..., progress: 20%
2025-8-20 23:42:58 - debug: reload all scripts.
2025-8-20 23:42:58 - debug: [[Executor]] reload before lock
2025-8-20 23:42:58 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-20 23:42:58 - groupCollapsed: Invalidate all modules
2025-8-20 23:42:58 - debug: Unregister BuiltinPipelineSettings
2025-8-20 23:42:58 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-20 23:42:58 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-20 23:42:58 - debug: Unregister DebugViewRuntimeControl
2025-8-20 23:42:58 - debug: Unregister CameraFollow
2025-8-20 23:42:58 - debug: Unregister SoundManager
2025-8-20 23:42:58 - debug: Unregister Bullet
2025-8-20 23:42:58 - debug: Unregister AIPlayer
2025-8-20 23:42:58 - debug: Unregister player
2025-8-20 23:42:58 - debug: Unregister PlayerManager
2025-8-20 23:42:58 - debug: Unregister SceneTransition
2025-8-20 23:42:58 - debug: Unregister PaintManager
2025-8-20 23:42:58 - debug: Unregister GameOverPanel
2025-8-20 23:42:58 - debug: Unregister GameHUD
2025-8-20 23:42:58 - debug: Unregister GameManager
2025-8-20 23:42:58 - debug: Unregister AIController
2025-8-20 23:42:58 - debug: Unregister CarProperties
2025-8-20 23:42:58 - debug: Unregister CarPropertyDisplay
2025-8-20 23:42:58 - debug: Unregister HealthBarUI
2025-8-20 23:42:58 - debug: Unregister MainMenuController
2025-8-20 23:42:58 - debug: Unregister PaintSpot
2025-8-20 23:42:58 - debug: Unregister PausePanel
2025-8-20 23:42:58 - debug: Unregister PlayerInfoUI
2025-8-20 23:42:58 - debug: Unregister PurchasePanel
2025-8-20 23:42:58 - debug: Unregister SelectManager
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js'
2025-8-20 23:42:58 - debug: Invalidating 'pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js'
2025-8-20 23:42:58 - groupEnd: Invalidate all modules
2025-8-20 23:42:58 - groupCollapsed: Imports all modules
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register CameraFollow
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register SoundManager
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register Bullet
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register AIPlayer
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register player
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register PlayerManager
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register SceneTransition
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register PaintManager
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register GameOverPanel
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register GameHUD
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register GameManager
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register AIController
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register CarProperties
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register HealthBarUI
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register MainMenuController
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register PaintSpot
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register PausePanel
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register PlayerInfoUI
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register PurchasePanel
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Register SelectManager
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-20 23:42:58 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-20 23:42:58 - groupEnd: Imports all modules
2025-8-20 23:42:58 - debug: [[Executor]] after unlock
2025-8-20 23:42:58 - debug: Incremental keys: 
2025-8-20 23:42:58 - debug: Init bundle share assets start..., progress: 11%
2025-8-20 23:42:58 - debug: Init bundle share assets start..., progress: 20%
2025-8-20 23:42:58 - debug: Init bundle share assets success..., progress: 11%
2025-8-20 23:42:58 - debug: Init bundle share assets success..., progress: 20%
2025-8-20 23:42:58 - debug: handle json group in bundle internal
2025-8-20 23:42:58 - debug: handle json group in bundle internal success
2025-8-20 23:42:58 - debug: handle json group in bundle resources
2025-8-20 23:42:58 - debug: handle json group in bundle main
2025-8-20 23:42:58 - debug: init image compress task 0 in bundle internal
2025-8-20 23:42:58 - debug: handle json group in bundle main success
2025-8-20 23:42:58 - debug: init image compress task 0 in bundle main
2025-8-20 23:42:58 - debug: handle json group in bundle resources success
2025-8-20 23:42:58 - debug: init image compress task 0 in bundle resources
2025-8-20 23:42:58 - debug: // ---- build task 查询 Asset Bundle ---- (145ms)
2025-8-20 23:42:58 - log: run build task 查询 Asset Bundle success in 145 ms√, progress: 16%
2025-8-20 23:42:58 - debug: [Build Memory track]: 查询 Asset Bundle start:217.32MB, end 218.95MB, increase: 1.63MB
2025-8-20 23:42:58 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-20 23:42:58 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-20 23:42:58 - debug: // ---- build task 查询 Asset Bundle ---- (32ms)
2025-8-20 23:42:58 - log: run build task 查询 Asset Bundle success in 32 ms√, progress: 21%
2025-8-20 23:42:58 - debug: [Build Memory track]: 查询 Asset Bundle start:218.98MB, end 219.58MB, increase: 610.70KB
2025-8-20 23:42:58 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-20 23:42:58 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-20 23:42:58 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-20 23:42:58 - debug: // ---- build task native：onAfterBundleDataTask ---- (42ms)
2025-8-20 23:42:58 - debug: native:(onAfterBundleDataTask) in 42 ms ✓, progress: 21%
2025-8-20 23:42:58 - debug: native:(onAfterBundleDataTask) in 42 ms ✓, progress: 27%
2025-8-20 23:42:58 - debug: 打包脚本 start, progress: 21%
2025-8-20 23:42:58 - debug: // ---- build task 打包脚本 ----
2025-8-20 23:42:58 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 23:42:59 - log: [build-script]enter sub process 11660, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-20 23:42:59 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-20 23:42:59 - debug: excute-script over with build-script 814ms
2025-8-20 23:42:59 - debug: Generate systemJs..., progress: 21%
2025-8-20 23:42:59 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 23:43:00 - debug: excute-script over with build-script 716ms
2025-8-20 23:43:00 - debug: 构建项目脚本 start..., progress: 21%
2025-8-20 23:43:00 - debug: Build script in bundle start, progress: 21%
2025-8-20 23:43:00 - debug: Build script in bundle start, progress: 27%
2025-8-20 23:43:00 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 23:43:01 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts


2025-8-20 23:43:01 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-20 23:43:01 - debug: excute-script over with build-script 1124ms
2025-8-20 23:43:01 - debug: Copy externalScripts success!
2025-8-20 23:43:01 - debug: Build script in bundle success, progress: 21%
2025-8-20 23:43:01 - debug: Build script in bundle success, progress: 27%
2025-8-20 23:43:01 - debug: 构建项目脚本 in (1193 ms) √, progress: 21%
2025-8-20 23:43:01 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-20 23:43:01 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-20 23:43:01 - debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf)}
2025-8-20 23:43:01 - debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-20 23:43:01 - debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "src/cocos-js",
  "platform": "IOS",
  "useCache": true,
  "nativeCodeBundleMode": "asmjs",
  "wasmCompressionMode": false,
  "output": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/cocos-js",
  "targets": "chrome 80",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-20 23:43:01 - debug: 构建引擎脚本 in (50 ms) √, progress: 21%
2025-8-20 23:43:01 - debug: Copy plugin script ..., progress: 21%
2025-8-20 23:43:01 - debug: Generate import-map..., progress: 21%
2025-8-20 23:43:01 - debug: // ---- build task 打包脚本 ---- (2827ms)
2025-8-20 23:43:01 - log: run build task 打包脚本 success in 2 s√, progress: 26%
2025-8-20 23:43:01 - debug: [Build Memory track]: 打包脚本 start:220.24MB, end 220.86MB, increase: 639.37KB
2025-8-20 23:43:01 - debug: Build Assets start, progress: 26%
2025-8-20 23:43:01 - debug: // ---- build task Build Assets ----
2025-8-20 23:43:01 - debug: Build bundles..., progress: 26%
2025-8-20 23:43:01 - debug: Pack Images start, progress: 26%
2025-8-20 23:43:01 - debug: Pack Images start, progress: 27%
2025-8-20 23:43:01 - debug: builder:pack-auto-atlas-image (80ms)
2025-8-20 23:43:01 - debug: Pack Images success, progress: 26%
2025-8-20 23:43:01 - debug: Pack Images success, progress: 27%
2025-8-20 23:43:01 - debug: Compress image start..., progress: 26%
2025-8-20 23:43:01 - debug: Compress image start..., progress: 27%
2025-8-20 23:43:01 - group: Compress image...
2025-8-20 23:43:01 - debug: sort compress task {}
2025-8-20 23:43:01 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-20 23:43:01 - debug: No image need to compress
2025-8-20 23:43:01 - groupEnd: Compress image...
2025-8-20 23:43:01 - debug: Compress image success..., progress: 26%
2025-8-20 23:43:01 - debug: Compress image success..., progress: 27%
2025-8-20 23:43:01 - debug: Output asset in bundles start, progress: 26%
2025-8-20 23:43:01 - debug: Output asset in bundles start, progress: 27%
2025-8-20 23:43:01 - debug: Handle all json groups in bundle internal
2025-8-20 23:43:01 - debug: handle json group
2025-8-20 23:43:01 - debug: Handle all json groups in bundle resources
2025-8-20 23:43:01 - debug: handle json group
2025-8-20 23:43:01 - debug: Handle all json groups in bundle main
2025-8-20 23:43:01 - debug: handle json group
2025-8-20 23:43:01 - debug: Json group(05b737039) compile success，json number: 6
2025-8-20 23:43:01 - debug: Json group(06585a170) compile success，json number: 6
2025-8-20 23:43:01 - debug: handle single json
2025-8-20 23:43:01 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-20 23:43:01 - debug: Json group(01959b579) compile success，json number: 6
2025-8-20 23:43:01 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-20 23:43:01 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-20 23:43:01 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-20 23:43:02 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-20 23:43:02 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-20 23:43:02 - debug: Json group(08532c3e3) compile success，json number: 6
2025-8-20 23:43:02 - debug: handle single json
2025-8-20 23:43:02 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-20 23:43:02 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-20 23:43:02 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-20 23:43:02 - debug: handle single json
2025-8-20 23:43:02 - debug: Output asset in bundles success, progress: 26%
2025-8-20 23:43:02 - debug: Output asset in bundles success, progress: 27%
2025-8-20 23:43:02 - debug: Output asset in bundles start, progress: 26%
2025-8-20 23:43:02 - debug: Output asset in bundles start, progress: 27%
2025-8-20 23:43:02 - debug: compress config of bundle internal...
2025-8-20 23:43:02 - debug: compress config of bundle internal success
2025-8-20 23:43:02 - debug: compress config of bundle resources...
2025-8-20 23:43:02 - debug: compress config of bundle resources success
2025-8-20 23:43:02 - debug: compress config of bundle main...
2025-8-20 23:43:02 - debug: compress config of bundle main success
2025-8-20 23:43:02 - debug: output config of bundle internal
2025-8-20 23:43:02 - debug: output config of bundle internal success
2025-8-20 23:43:02 - debug: output config of bundle resources
2025-8-20 23:43:02 - debug: output config of bundle resources success
2025-8-20 23:43:02 - debug: output config of bundle main
2025-8-20 23:43:02 - debug: output config of bundle main success
2025-8-20 23:43:02 - debug: Output asset in bundles success, progress: 26%
2025-8-20 23:43:02 - debug: Output asset in bundles success, progress: 27%
2025-8-20 23:43:02 - debug: // ---- build task Build Assets ---- (490ms)
2025-8-20 23:43:02 - log: run build task Build Assets success in 490 ms√, progress: 31%
2025-8-20 23:43:02 - debug: [Build Memory track]: Build Assets start:220.89MB, end 223.00MB, increase: 2.12MB
2025-8-20 23:43:02 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-20 23:43:02 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-20 23:43:02 - debug: // ---- build task ios：onAfterBuildAssets ---- (55ms)
2025-8-20 23:43:02 - debug: ios:(onAfterBuildAssets) in 55 ms ✓, progress: 33%
2025-8-20 23:43:02 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-20 23:43:02 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-20 23:43:02 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (35ms)
2025-8-20 23:43:02 - log: run build task 整理部分构建选项内数据到 settings.json success in 35 ms√, progress: 34%
2025-8-20 23:43:02 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:223.84MB, end 224.33MB, increase: 505.63KB
2025-8-20 23:43:02 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-20 23:43:02 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-20 23:43:02 - debug: // ---- build task 填充脚本数据到 settings.json ---- (33ms)
2025-8-20 23:43:02 - log: run build task 填充脚本数据到 settings.json success in 33 ms√, progress: 36%
2025-8-20 23:43:02 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:224.36MB, end 221.76MB, increase: -2658.64KB
2025-8-20 23:43:02 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-20 23:43:02 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-20 23:43:02 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (56ms)
2025-8-20 23:43:02 - log: run build task 整理部分构建选项内数据到 settings.json success in 56 ms√, progress: 38%
2025-8-20 23:43:02 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.79MB, end 222.39MB, increase: 609.02KB
2025-8-20 23:43:02 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-20 23:43:02 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-20 23:43:02 - debug: // ---- build task ios：onBeforeCompressSettings ---- (44ms)
2025-8-20 23:43:02 - debug: ios:(onBeforeCompressSettings) in 44 ms ✓, progress: 40%
2025-8-20 23:43:02 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-20 23:43:02 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-20 23:43:02 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (108ms)
2025-8-20 23:43:02 - debug: cocos-service:(onBeforeCompressSettings) in 108 ms ✓, progress: 41%
2025-8-20 23:43:02 - debug: 整理静态模板文件 start, progress: 41%
2025-8-20 23:43:02 - debug: // ---- build task 整理静态模板文件 ----
2025-8-20 23:43:02 - debug: // ---- build task 整理静态模板文件 ---- (53ms)
2025-8-20 23:43:02 - log: run build task 整理静态模板文件 success in 53 ms√, progress: 46%
2025-8-20 23:43:02 - debug: [Build Memory track]: 整理静态模板文件 start:223.48MB, end 221.87MB, increase: -1644.08KB
2025-8-20 23:43:02 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-20 23:43:02 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-20 23:43:02 - log: Checking template version...
2025-8-20 23:43:02 - log: Validating template consistency...
2025-8-20 23:43:02 - log: Validating platform source code directories...
2025-8-20 23:43:02 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-20 23:43:02 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.cpp
2025-8-20 23:43:02 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.m
2025-8-20 23:43:02 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/settings.gradle
2025-8-20 23:43:02 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-20 23:43:02 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-20 23:43:02 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-20 23:43:02 - debug: // ---- build task native：onAfterCompressSettings ---- (52ms)
2025-8-20 23:43:02 - debug: native:(onAfterCompressSettings) in 52 ms ✓, progress: 48%
2025-8-20 23:43:02 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-20 23:43:02 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-20 23:43:02 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (113ms)
2025-8-20 23:43:02 - debug: cocos-service:(onAfterCompressSettings) in 113 ms ✓, progress: 50%
2025-8-20 23:43:02 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-20 23:43:02 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-20 23:43:02 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (42ms)
2025-8-20 23:43:02 - log: run build task 给所有的资源加上 MD5 后缀 success in 42 ms√, progress: 60%
2025-8-20 23:43:02 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:223.70MB, end 224.19MB, increase: 507.57KB
2025-8-20 23:43:02 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-20 23:43:02 - debug: // ---- build task native：onAfterBuild ----
2025-8-20 23:43:02 - log: [xcode-select] /Applications/Xcode.app/Contents/Developer


2025-8-20 23:43:03 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-20 23:43:04 - log: [cmake] -- The CXX compiler identification is AppleClang 16.0.0.16000026


2025-8-20 23:43:04 - log: [cmake] -- Detecting CXX compiler ABI info


2025-8-20 23:43:05 - log: [cmake] -- Detecting CXX compiler ABI info - done


2025-8-20 23:43:05 - log: [cmake] -- Check for working CXX compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ - skipped


2025-8-20 23:43:05 - log: [cmake] -- Detecting CXX compile features


2025-8-20 23:43:05 - log: [cmake] -- Detecting CXX compile features - done


2025-8-20 23:43:06 - log: [cmake] -- The C compiler identification is AppleClang 16.0.0.16000026


2025-8-20 23:43:06 - log: [cmake] -- The ASM compiler identification is Clang with GNU-like command-line


2025-8-20 23:43:06 - log: [cmake] -- Found assembler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang


2025-8-20 23:43:06 - log: [cmake] -- Detecting C compiler ABI info


2025-8-20 23:43:07 - log: [cmake] -- Detecting C compiler ABI info - done


2025-8-20 23:43:07 - log: [cmake] -- Check for working C compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang - skipped


2025-8-20 23:43:07 - log: [cmake] -- Detecting C compile features


2025-8-20 23:43:07 - log: [cmake] -- Detecting C compile features - done


2025-8-20 23:43:07 - log: [cmake] -- platform: iOS


2025-8-20 23:43:07 - log: [cmake] -- Ignore NO_WERROR


2025-8-20 23:43:07 - log: [cmake] -- OPTION BUILTIN_COCOS_X_PATH:	
-- OPTION USE_BUILTIN_EXTERNAL:	OFF
-- OPTION USE_MODULES:	OFF


2025-8-20 23:43:07 - log: [cmake] -- OPTION CC_USE_METAL:	ON
-- OPTION CC_USE_GLES3:	OFF
-- OPTION CC_USE_GLES2:	OFF
-- OPTION CC_USE_VULKAN:	OFF
-- OPTION CC_DEBUG_FORCE:	OFF
-- OPTION USE_SE_V8:	ON
-- OPTION USE_SE_JSVM:	OFF
-- OPTION USE_V8_DEBUGGER:	ON
-- OPTION USE_V8_DEBUGGER_FORCE:	OFF
-- OPTION USE_SE_SM:	OFF
-- OPTION USE_SOCKET:	OFF
-- OPTION USE_AUDIO:	ON
-- OPTION USE_EDIT_BOX:	ON
-- OPTION USE_VIDEO:	ON
-- OPTION USE_WEBVIEW:	ON
-- OPTION USE_MIDDLEWARE:	ON
-- OPTION USE_DRAGONBONES:	ON
-- OPTION USE_SPINE:	ON
-- OPTION USE_SPINE_3_8:	ON


2025-8-20 23:43:07 - log: [cmake] -- OPTION USE_SPINE_4_2:	OFF
-- OPTION USE_WEBSOCKET_SERVER:	OFF
-- OPTION USE_PHYSICS_PHYSX:	OFF
-- OPTION USE_JOB_SYSTEM_TBB:	OFF
-- OPTION USE_JOB_SYSTEM_TASKFLOW:	OFF
-- OPTION USE_XR:	OFF
-- OPTION USE_SERVER_MODE:	OFF
-- OPTION USE_AR_MODULE:	OFF
-- OPTION USE_AR_AUTO:	OFF
-- OPTION USE_AR_CORE:	OFF
-- OPTION USE_AR_ENGINE:	OFF
-- OPTION USE_CCACHE:	
-- OPTION CCACHE_EXECUTABLE:	CCACHE_EXECUTABLE-NOTFOUND
-- OPTION NODE_EXECUTABLE:	/opt/homebrew/Cellar/node/24.5.0/bin/node
-- OPTION NET_MODE:	0
-- OPTION USE_REMOTE_LOG:	OFF
-- OPTION USE_BOX2D_JSB:	OFF


2025-8-20 23:43:07 - log: [cmake] -- platform path: 


2025-8-20 23:43:07 - log: [cmake] -- Using Xcode 15 or newer, adding extra link flags: -Wl,-ld_classic.


2025-8-20 23:43:07 - log: [cmake] -- Try generating /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Pre-AutoLoadPlulgins.cmake


2025-8-20 23:43:07 - log: [cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js


2025-8-20 23:43:07 - log: [cmake] [searching plugins] no plugins found!


2025-8-20 23:43:07 - log: [cmake] -- Searching hook files Pre*.cmake or *Pre.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-20 23:43:07 - log: [cmake] -- ::Loading Pre-service.cmake


2025-8-20 23:43:07 - log: [cmake] -- No plugins are loaded!


2025-8-20 23:43:07 - log: [cmake] -- Searching hook files Post*.cmake or *Post.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios
-- ::Loading Post-service.cmake


2025-8-20 23:43:07 - log: [cmake] -- Configuring done


2025-8-20 23:43:07 - log: [cmake] -- Generating done


2025-8-20 23:43:07 - log: [cmake-warn] CMake Warning:
  Manually-specified variables were not used by the project:

    LAUNCH_TYPE




2025-8-20 23:43:07 - log: [cmake] -- Build files have been written to: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj


2025-8-20 23:43:07 - debug: // ---- build task native：onAfterBuild ---- (4502ms)
2025-8-20 23:43:07 - debug: native:(onAfterBuild) in 4502 ms ✓, progress: 62%
2025-8-20 23:43:07 - debug: ios:(onAfterBuild) start..., progress: 62%
2025-8-20 23:43:07 - debug: // ---- build task ios：onAfterBuild ----
2025-8-20 23:43:07 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-20 23:43:07 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png
2025-8-20 23:43:07 - debug: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-20 23:43:07 - debug: // ---- build task ios：onAfterBuild ---- (72ms)
2025-8-20 23:43:07 - debug: ios:(onAfterBuild) in 72 ms ✓, progress: 64%
2025-8-20 23:43:07 - debug: cocos-service:(onAfterBuild) start..., progress: 64%
2025-8-20 23:43:07 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-20 23:43:07 - debug: // ---- build task cocos-service：onAfterBuild ---- (134ms)
2025-8-20 23:43:07 - debug: cocos-service:(onAfterBuild) in 134 ms ✓, progress: 65%
2025-8-20 23:43:07 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 65%
2025-8-20 23:43:07 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-20 23:43:07 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (34ms)
2025-8-20 23:43:07 - debug: adsense-h5g-plugin:(onAfterBuild) in 34 ms ✓, progress: 67%
2025-8-20 23:43:07 - log: Asset DB is resume!
2025-8-20 23:43:07 - debug: builder:build-project-total (9901ms)
2025-8-20 23:43:07 - debug: build success in 9901!
2025-8-20 23:43:07 - debug: [Build Memory track]: builder:build-project-total start:217.33MB, end 216.88MB, increase: -456.09KB
2025-8-20 23:43:07 - debug: ================================ build Task (ios) Finished in (9 s)ms ================================
