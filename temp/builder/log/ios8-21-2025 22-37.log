2025-8-21 22:37:14 - debug: =================================== build Task (ios) Start ================================
2025-8-21 22:37:14 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"1563d039-d898-4d8f-9415-9f1da853b8a3","outputName":"ios","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"350F82HeBJpZKzTD","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-21-2025 22-37.log"}
2025-8-21 22:37:14 - debug: Build with Cocos Creator 3.8.6
2025-8-21 22:37:14 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-21 22:37:14 - debug: // ---- build task native：onBeforeBuild ----
2025-8-21 22:37:14 - debug: // ---- build task native：onBeforeBuild ---- (32ms)
2025-8-21 22:37:14 - debug: native:(onBeforeBuild) in 32 ms ✓, progress: 2%
2025-8-21 22:37:14 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-21 22:37:14 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-21 22:37:14 - debug: // ---- build task cocos-service：onBeforeBuild ---- (211ms)
2025-8-21 22:37:14 - debug: cocos-service:(onBeforeBuild) in 211 ms ✓, progress: 4%
2025-8-21 22:37:14 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-21 22:37:14 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-21 22:37:14 - debug: // ---- build task scene：onBeforeBuild ---- (55ms)
2025-8-21 22:37:14 - debug: scene:(onBeforeBuild) in 55 ms ✓, progress: 5%
2025-8-21 22:37:14 - debug: Start lock asset db..., progress: 5%
2025-8-21 22:37:14 - log: Asset DB is paused with build!
2025-8-21 22:37:14 - debug: Query all assets info in project
2025-8-21 22:37:14 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-21 22:37:14 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-21 22:37:14 - debug: // ---- build task native：onAfterInit ----
2025-8-21 22:37:14 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-21 22:37:14 - debug: // ---- build task native：onAfterInit ---- (36ms)
2025-8-21 22:37:14 - debug: native:(onAfterInit) in 36 ms ✓, progress: 7%
2025-8-21 22:37:14 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-21 22:37:14 - debug: // ---- build task ios：onAfterInit ----
2025-8-21 22:37:14 - debug: // ---- build task ios：onAfterInit ---- (60ms)
2025-8-21 22:37:14 - debug: ios:(onAfterInit) in 60 ms ✓, progress: 9%
2025-8-21 22:37:14 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-21 22:37:14 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-21 22:37:14 - debug: // ---- build task cocos-service：onAfterInit ---- (161ms)
2025-8-21 22:37:14 - debug: cocos-service:(onAfterInit) in 161 ms ✓, progress: 11%
2025-8-21 22:37:14 - debug: Skip compress image, progress: 0%
2025-8-21 22:37:14 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-21 22:37:14 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-21 22:37:14 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-21 22:37:14 - debug: [adsense-h5g-plugin] remove script success
2025-8-21 22:37:15 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (58ms)
2025-8-21 22:37:15 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 58 ms ✓, progress: 11%
2025-8-21 22:37:15 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 58 ms ✓, progress: 7%
2025-8-21 22:37:15 - debug: Init all bundles start..., progress: 11%
2025-8-21 22:37:15 - debug: Init all bundles start..., progress: 7%
2025-8-21 22:37:15 - debug: Num of bundles: 3..., progress: 11%
2025-8-21 22:37:15 - debug: Num of bundles: 3..., progress: 7%
2025-8-21 22:37:15 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-21 22:37:15 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-21 22:37:15 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-21 22:37:15 - debug: // ---- build task native：onAfterBundleInit ---- (76ms)
2025-8-21 22:37:15 - debug: native:(onAfterBundleInit) in 76 ms ✓, progress: 11%
2025-8-21 22:37:15 - debug: native:(onAfterBundleInit) in 76 ms ✓, progress: 13%
2025-8-21 22:37:15 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-21 22:37:15 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-21 22:37:15 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-21 22:37:15 - debug: // ---- build task ios：onAfterBundleInit ---- (51ms)
2025-8-21 22:37:15 - debug: ios:(onAfterBundleInit) in 51 ms ✓, progress: 11%
2025-8-21 22:37:15 - debug: ios:(onAfterBundleInit) in 51 ms ✓, progress: 20%
2025-8-21 22:37:15 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-21 22:37:15 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 22:37:15 - debug: Init bundle root assets start..., progress: 11%
2025-8-21 22:37:15 - debug: Init bundle root assets start..., progress: 20%
2025-8-21 22:37:15 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-21 22:37:15 - debug:   Number of all scenes: 3
2025-8-21 22:37:15 - debug:   Number of all scripts: 28
2025-8-21 22:37:15 - debug:   Number of other assets: 609
2025-8-21 22:37:15 - debug: Init bundle root assets success..., progress: 11%
2025-8-21 22:37:15 - debug: Init bundle root assets success..., progress: 20%
2025-8-21 22:37:15 - debug: reload all scripts.
2025-8-21 22:37:15 - debug: [[Executor]] reload before lock
2025-8-21 22:37:15 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-21 22:37:15 - groupCollapsed: Invalidate all modules
2025-8-21 22:37:15 - debug: Unregister BuiltinPipelineSettings
2025-8-21 22:37:15 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-21 22:37:15 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-21 22:37:15 - debug: Unregister DebugViewRuntimeControl
2025-8-21 22:37:15 - debug: Unregister CameraFollow
2025-8-21 22:37:15 - debug: Unregister Bullet
2025-8-21 22:37:15 - debug: Unregister AIPlayer
2025-8-21 22:37:15 - debug: Unregister player
2025-8-21 22:37:15 - debug: Unregister PlayerManager
2025-8-21 22:37:15 - debug: Unregister SceneTransition
2025-8-21 22:37:15 - debug: Unregister SoundManager
2025-8-21 22:37:15 - debug: Unregister PaintManager
2025-8-21 22:37:15 - debug: Unregister GameOverPanel
2025-8-21 22:37:15 - debug: Unregister GameHUD
2025-8-21 22:37:15 - debug: Unregister GameManager
2025-8-21 22:37:15 - debug: Unregister AIController
2025-8-21 22:37:15 - debug: Unregister CarProperties
2025-8-21 22:37:15 - debug: Unregister CarPropertyDisplay
2025-8-21 22:37:15 - debug: Unregister HealthBarUI
2025-8-21 22:37:15 - debug: Unregister MainMenuController
2025-8-21 22:37:15 - debug: Unregister PaintSpot
2025-8-21 22:37:15 - debug: Unregister PausePanel
2025-8-21 22:37:15 - debug: Unregister PlayerInfoUI
2025-8-21 22:37:15 - debug: Unregister PurchasePanel
2025-8-21 22:37:15 - debug: Unregister SelectManager
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js'
2025-8-21 22:37:15 - debug: Invalidating 'pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js'
2025-8-21 22:37:15 - groupEnd: Invalidate all modules
2025-8-21 22:37:15 - groupCollapsed: Imports all modules
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register CameraFollow
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register Bullet
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register AIPlayer
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register player
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register PlayerManager
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register SceneTransition
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register SoundManager
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register PaintManager
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register GameOverPanel
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register GameHUD
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register GameManager
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register AIController
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register CarProperties
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register HealthBarUI
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register MainMenuController
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register PaintSpot
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register PausePanel
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register PlayerInfoUI
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register PurchasePanel
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Register SelectManager
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-21 22:37:15 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 22:37:15 - groupEnd: Imports all modules
2025-8-21 22:37:15 - debug: [[Executor]] after unlock
2025-8-21 22:37:15 - debug: Incremental keys: 
2025-8-21 22:37:15 - debug: Init bundle share assets start..., progress: 11%
2025-8-21 22:37:15 - debug: Init bundle share assets start..., progress: 20%
2025-8-21 22:37:15 - debug: Init bundle share assets success..., progress: 11%
2025-8-21 22:37:15 - debug: Init bundle share assets success..., progress: 20%
2025-8-21 22:37:15 - debug: handle json group in bundle internal
2025-8-21 22:37:15 - debug: handle json group in bundle internal success
2025-8-21 22:37:15 - debug: handle json group in bundle resources
2025-8-21 22:37:15 - debug: handle json group in bundle main
2025-8-21 22:37:15 - debug: init image compress task 0 in bundle internal
2025-8-21 22:37:15 - debug: handle json group in bundle main success
2025-8-21 22:37:15 - debug: init image compress task 0 in bundle main
2025-8-21 22:37:15 - debug: handle json group in bundle resources success
2025-8-21 22:37:15 - debug: init image compress task 0 in bundle resources
2025-8-21 22:37:15 - debug: // ---- build task 查询 Asset Bundle ---- (215ms)
2025-8-21 22:37:15 - log: run build task 查询 Asset Bundle success in 215 ms√, progress: 16%
2025-8-21 22:37:15 - debug: [Build Memory track]: 查询 Asset Bundle start:222.38MB, end 224.03MB, increase: 1.65MB
2025-8-21 22:37:15 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-21 22:37:15 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 22:37:15 - debug: // ---- build task 查询 Asset Bundle ---- (52ms)
2025-8-21 22:37:15 - log: run build task 查询 Asset Bundle success in 52 ms√, progress: 21%
2025-8-21 22:37:15 - debug: [Build Memory track]: 查询 Asset Bundle start:224.06MB, end 224.91MB, increase: 870.82KB
2025-8-21 22:37:15 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-21 22:37:15 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-21 22:37:15 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-21 22:37:15 - debug: // ---- build task native：onAfterBundleDataTask ---- (52ms)
2025-8-21 22:37:15 - debug: native:(onAfterBundleDataTask) in 52 ms ✓, progress: 21%
2025-8-21 22:37:15 - debug: native:(onAfterBundleDataTask) in 52 ms ✓, progress: 27%
2025-8-21 22:37:15 - debug: 打包脚本 start, progress: 21%
2025-8-21 22:37:15 - debug: // ---- build task 打包脚本 ----
2025-8-21 22:37:15 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:37:15 - log: [build-script]enter sub process 71187, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-21 22:37:16 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-21 22:37:16 - debug: excute-script over with build-script 783ms
2025-8-21 22:37:16 - debug: Generate systemJs..., progress: 21%
2025-8-21 22:37:16 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:37:17 - debug: excute-script over with build-script 768ms
2025-8-21 22:37:17 - debug: 构建项目脚本 start..., progress: 21%
2025-8-21 22:37:17 - debug: Build script in bundle start, progress: 21%
2025-8-21 22:37:17 - debug: Build script in bundle start, progress: 27%
2025-8-21 22:37:17 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:37:17 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts


2025-8-21 22:37:17 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-21 22:37:18 - debug: excute-script over with build-script 1169ms
2025-8-21 22:37:18 - debug: Copy externalScripts success!
2025-8-21 22:37:18 - debug: Build script in bundle success, progress: 21%
2025-8-21 22:37:18 - debug: Build script in bundle success, progress: 27%
2025-8-21 22:37:18 - debug: 构建项目脚本 in (1263 ms) √, progress: 21%
2025-8-21 22:37:18 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-21 22:37:18 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-21 22:37:18 - debug: Engine cache (/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf) does not exist.
2025-8-21 22:37:18 - debug: mangleProperties is disabled, platform: IOS
2025-8-21 22:37:18 - debug: Cache is invalid, start build engine with options: {
  "incremental": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.watch-files.json",
  "engine": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
  "out": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf",
  "moduleFormat": "system",
  "compress": true,
  "nativeCodeBundleMode": "asmjs",
  "sourceMap": false,
  "targets": "chrome 80",
  "loose": true,
  "features": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "platform": "IOS",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "mode": "BUILD",
  "metaFile": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.meta/meta.json",
  "wasmCompressionMode": false,
  "inlineEnum": true,
  "mangleProperties": false,
  "mangleConfigJsonMtime": 0
}

2025-8-21 22:37:18 - debug: md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-21 22:37:18 - log: Run build task(build-engine) in child, see: chrome://inspect/#devices
2025-8-21 22:37:18 - log: [build-engine]enter sub process 71208, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-21 22:37:19 - log: [build-engine]start build engine with options: {"engine":"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine","out":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf","platform":"IOS","moduleFormat":"system","compress":true,"split":false,"nativeCodeBundleMode":"asmjs","assetURLFormat":"runtime-resolved","noDeprecatedFeatures":false,"sourceMap":false,"features":["2d","affine-transform","animation","audio","base","custom-pipeline","dragon-bones","graphics","intersection-2d","mask","particle-2d","physics-2d-box2d","profiler","rich-text","spine-3.8","tiled-map","tween","ui","video","webview","custom-pipeline-builtin-scripts"],"loose":true,"mode":"BUILD","flags":{"DEBUG":false,"LOAD_BULLET_MANUALLY":false,"LOAD_SPINE_MANUALLY":false},"metaFile":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.meta/meta.json","mangleProperties":false,"inlineEnum":true,"incremental":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.watch-files.json","targets":"chrome 80","wasmCompressionMode":false,"mangleConfigJsonMtime":0}


2025-8-21 22:37:19 - log: [build-engine]Module source "internal-constants":
function tryDefineGlobal (name, value) {
    const _global = typeof window === 'undefined' ? global : window;
    if (typeof _global[name] === 'undefined') {
        return (_global[name] = value);
    } else {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return _global[name];
    }
}
export const HTML5 = false;

export const NATIVE = true;

export const ANDROID = false;

export const IOS = true;

export const MAC = false;

export const WINDOWS = false;

export const LINUX = false;

export const OHOS = false;

export const OPEN_HARMONY = false;

export const WECHAT = false;
tryDefineGlobal('CC_WECHAT', false);

export const WECHAT_MINI_PROGRAM = false;

export const XIAOMI = false;
tryDefineGlobal('CC_XIAOMI', false);

export const ALIPAY = false;
tryDefineGlobal('CC_ALIPAY', false);

export const TAOBAO = false;

export const TAOBAO_MINIGAME = false;

export const BYTEDANCE = false;
tryDefineGlobal('CC_BYTEDANCE', false);

export const OPPO = false;
tryDefineGlobal('CC_OPPO', false);

export const VIVO = false;
tryDefineGlobal('CC_VIVO', false);

export const HUAWEI = false;
tryDefineGlobal('CC_HUAWEI', false);

export const MIGU = false;
tryDefineGlobal('CC_MIGU', false);

export const HONOR = false;
tryDefineGlobal('CC_HONOR', false);

export const COCOS_RUNTIME = false;
tryDefineGlobal('CC_COCOS_RUNTIME', false);

export const EDITOR = false;
tryDefineGlobal('CC_EDITOR', false);

export const EDITOR_NOT_IN_PREVIEW = false;

export const PREVIEW = false;
tryDefineGlobal('CC_PREVIEW', false);

export const BUILD = true;
tryDefineGlobal('CC_BUILD', true);

export const TEST = false;
tryDefineGlobal('CC_TEST', false);

export const DEBUG = false;
tryDefineGlobal('CC_DEBUG', false);

export const SERVER_MODE = false;

export const DEV = false;
tryDefineGlobal('CC_DEV', false);

export const MINIGAME = false;
tryDefineGlobal('CC_MINIGAME', false);

export const RUNTIME_BASED = false;
tryDefineGlobal('CC_RUNTIME_BASED', false);

export const SUPPORT_JIT = true;
tryDefineGlobal('CC_SUPPORT_JIT', true);

export const JSB = true;
tryDefineGlobal('CC_JSB', true);

export const NOT_PACK_PHYSX_LIBS = false;

export const NET_MODE = 0;

export const WEBGPU = false;

export const NATIVE_CODE_BUNDLE_MODE = 0;

export const WASM_SUBPACKAGE = false;

export const CULL_MESHOPT = true;

export const LOAD_SPINE_MANUALLY = false;

export const LOAD_BOX2D_MANUALLY = false;

export const LOAD_BULLET_MANUALLY = false;

export const LOAD_PHYSX_MANUALLY = false;

export const USE_3D = false;

export const USE_UI_SKEW = false;

export const USE_XR = false;




2025-8-21 22:37:19 - log: [build-engine]Module source "cc":
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/sorting.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/affine-transform.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/animation.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/audio.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/base.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/custom-pipeline.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/dragon-bones.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/graphics.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/intersection-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/mask.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/particle-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-box2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-framework.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/profiler.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/rich-text.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/spine.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tiled-map.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tween.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/ui.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/video.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/webview.ts';


2025-8-21 22:37:25 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/dragon-bones/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/dragon-bones/index.jsb.ts


2025-8-21 22:37:25 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/index.jsb.ts


2025-8-21 22:37:25 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/custom/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/custom/index.jsb.ts


2025-8-21 22:37:25 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/index.jsb.ts


2025-8-21 22:37:25 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/root.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/root.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/index.jsb.ts


2025-8-21 22:37:26 - log: 资源数据库已锁定，资源操作(refresh)将会延迟响应，请稍侯
2025-8-21 22:37:26 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/material.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/material.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/node.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/node.jsb.ts


2025-8-21 22:37:26 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.jsb.ts


2025-8-21 22:37:26 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/index.jsb.ts


2025-8-21 22:37:26 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/pass.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/pass.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/program-lib.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/program-lib.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/material-instance.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/material-instance.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-scene.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-scene.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-window.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-window.jsb.ts


2025-8-21 22:37:26 - log: [build-engine]Redirect module internal:native to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/native-binding/impl.ts


2025-8-21 22:37:26 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/asset-manager/builtin-res-mgr.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/asset-manager/builtin-res-mgr.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/runtime-exports.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/index-empty.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/asset.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module pal/audio to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/audio/native/player.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-base.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-base.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module pal/minigame to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/minigame/non-minigame.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-2d.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/image-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/image-asset.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module pal/screen-adapter to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/screen-adapter/native/screen-adapter.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/misc/create-mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/misc/create-mesh.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/assets/mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/assets/mesh.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/models/morph-model.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/models/morph-model.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/scene-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/scene-asset.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/camera.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/camera.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/submodel.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/submodel.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/native-pools.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/native-pools.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/render-texture.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/render-texture.jsb.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module pal/system-info to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/system-info/native/system-info.ts
Redirect module pal/env to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/env/native/env.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module pal/pacer to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/pacer/pacer-native.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module pal/input to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/input/native/index.ts


2025-8-21 22:37:27 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/buffer-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/buffer-asset.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version-3.8.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/base/pipeline-state.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/base/pipeline-state.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene-globals.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene-globals.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/effect-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/effect-asset.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/rendering-sub-mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/rendering-sub-mesh.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-cube.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-cube.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/simple-texture.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/simple-texture.jsb.ts


2025-8-21 22:37:28 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/model.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/model.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/reflection-probe.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/reflection-probe.jsb.ts


2025-8-21 22:37:29 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-21 22:37:29 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-21 22:37:29 - log: [build-engine]==== Performance ====


2025-8-21 22:37:29 - log: [build-engine]{"# BUILD":[10115.964667,1087998228,1229168572],"## initialize":[5933.600958000001,426263116,567438420],"- plugin 6 (node-resolve) - buildStart":[0.04137500000001637,-180268,141191300],"- plugin 8 (commonjs) - buildStart":[0.01558300000010604,3360,141195504],"- plugin 9 (@cocos/typescript) - buildStart":[5933.507958,426240568,567436428],"## generate module graph":[3400.6559589999997,607324160,1174763168],"- plugin 0 (commonjs--resolver) - resolveId":[6.672199000054206,6734504,1173957536],"- plugin 1 (@cocos/ccbuild|external-loader) - resolveId":[3.4345250000287706,1429544,1173959672],"- plugin 2 (@cocos/ccbuild|module-overrides) - resolveId":[3.2863920000081634,-189516,1173966588],"- plugin 3 (virtual) - resolveId":[7.2924629999997705,4120724,1173968836],"- plugin 1 (@cocos/ccbuild|external-loader) - load":[0.991462999977557,716140,1173315080],"- plugin 2 (@cocos/ccbuild|module-overrides) - load":[3.0117470000277535,1037520,1173316324],"- plugin 3 (virtual) - load":[0.6744460000109029,-191280,1173317248],"- plugin 7 (json) - transform":[2.5247689999769136,558260,1173323148],"- plugin 8 (commonjs) - transform":[64.53945500002283,5440960,1173324880],"- plugin 10 (babel) - transform":[47.20433199999661,4894916,1173326848],"generate ast":[292.44767900000807,148757216,1173948904],"- plugin 4 (@cocos/ccbuild|module-query-plugin) - resolveId":[5.5384640000311265,-1894800,1172664456],"- plugin 5 (ts-paths) - resolveId":[5.242261000007602,-3320272,1172733204],"- plugin 9 (@cocos/typescript) - resolveId":[309.7068160000272,68246708,1172821784],"- plugin 10 (babel) - resolveId":[0.5621279999786566,-809296,1172825896],"- plugin 6 (node-resolve) - resolveId":[1.3727520000002187,504940,1020545980],"- plugin 6 (node-resolve) - load":[0.825489000003472,269056,1173318180],"- plugin 8 (commonjs) - load":[0.8122330000314832,-963096,1173319684],"- plugin 9 (@cocos/typescript) - load":[10.31677499998841,-8639244,1020571512],"- plugin 10 (babel) - load":[64.0762930000019,9987932,1020572172],"## sort and bind modules":[67.08575000000019,7347056,1182110556],"## mark included statements":[710.2367920000015,46372064,1228482912],"treeshaking pass 1":[183.7262499999997,29989640,1212293740],"treeshaking pass 2":[234.9607500000002,13320252,1225614284],"treeshaking pass 3":[64.71929199999977,48088,1225662664],"treeshaking pass 4":[40.97904100000051,4112432,1229775388],"treeshaking pass 5":[35.76050000000032,-5667984,1224107696],"treeshaking pass 6":[28.933375000000524,5639476,1229747464],"treeshaking pass 7":[27.800042000000758,-5591356,1224156400],"treeshaking pass 8":[26.444041000000652,1595380,1225752072],"treeshaking pass 9":[37.47758299999987,1389628,1227141992],"treeshaking pass 10":[28.916457999999693,1340104,1228482388],"- plugin 8 (commonjs) - buildEnd":[0.005333000000973698,384,1228486012],"- plugin 9 (@cocos/typescript) - buildEnd":[4.226749999999811,680948,1229167268]}
====             ====


2025-8-21 22:37:34 - debug: excute-script over with build-engine 16548ms
2025-8-21 22:37:34 - debug: build engine done: output: /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf
2025-8-21 22:37:34 - debug: 构建引擎脚本 in (16628 ms) √, progress: 21%
2025-8-21 22:37:34 - debug: Copy plugin script ..., progress: 21%
2025-8-21 22:37:34 - debug: Generate import-map..., progress: 21%
2025-8-21 22:37:35 - debug: // ---- build task 打包脚本 ---- (19529ms)
2025-8-21 22:37:35 - log: run build task 打包脚本 success in 19 s√, progress: 26%
2025-8-21 22:37:35 - debug: [Build Memory track]: 打包脚本 start:224.36MB, end 225.38MB, increase: 1.02MB
2025-8-21 22:37:35 - debug: Build Assets start, progress: 26%
2025-8-21 22:37:35 - debug: // ---- build task Build Assets ----
2025-8-21 22:37:35 - debug: Build bundles..., progress: 26%
2025-8-21 22:37:35 - debug: Pack Images start, progress: 26%
2025-8-21 22:37:35 - debug: Pack Images start, progress: 27%
2025-8-21 22:37:35 - debug: builder:pack-auto-atlas-image (117ms)
2025-8-21 22:37:35 - debug: Pack Images success, progress: 26%
2025-8-21 22:37:35 - debug: Pack Images success, progress: 27%
2025-8-21 22:37:35 - debug: Compress image start..., progress: 26%
2025-8-21 22:37:35 - debug: Compress image start..., progress: 27%
2025-8-21 22:37:35 - group: Compress image...
2025-8-21 22:37:35 - debug: sort compress task {}
2025-8-21 22:37:35 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-21 22:37:35 - debug: No image need to compress
2025-8-21 22:37:35 - groupEnd: Compress image...
2025-8-21 22:37:35 - debug: Compress image success..., progress: 26%
2025-8-21 22:37:35 - debug: Compress image success..., progress: 27%
2025-8-21 22:37:35 - debug: Output asset in bundles start, progress: 26%
2025-8-21 22:37:35 - debug: Output asset in bundles start, progress: 27%
2025-8-21 22:37:35 - debug: Handle all json groups in bundle internal
2025-8-21 22:37:35 - debug: handle json group
2025-8-21 22:37:35 - debug: Handle all json groups in bundle resources
2025-8-21 22:37:35 - debug: handle json group
2025-8-21 22:37:35 - debug: Handle all json groups in bundle main
2025-8-21 22:37:35 - debug: handle json group
2025-8-21 22:37:35 - debug: Json group(05b737039) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(06585a170) compile success，json number: 6
2025-8-21 22:37:35 - debug: handle single json
2025-8-21 22:37:35 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(01959b579) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(0a498a445) compile success，json number: 6
2025-8-21 22:37:35 - debug: handle single json
2025-8-21 22:37:35 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-21 22:37:35 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-21 22:37:35 - debug: handle single json
2025-8-21 22:37:35 - debug: Output asset in bundles success, progress: 26%
2025-8-21 22:37:35 - debug: Output asset in bundles success, progress: 27%
2025-8-21 22:37:35 - debug: Output asset in bundles start, progress: 26%
2025-8-21 22:37:35 - debug: Output asset in bundles start, progress: 27%
2025-8-21 22:37:35 - debug: compress config of bundle internal...
2025-8-21 22:37:35 - debug: compress config of bundle internal success
2025-8-21 22:37:35 - debug: compress config of bundle resources...
2025-8-21 22:37:35 - debug: compress config of bundle resources success
2025-8-21 22:37:35 - debug: compress config of bundle main...
2025-8-21 22:37:35 - debug: compress config of bundle main success
2025-8-21 22:37:35 - debug: output config of bundle internal
2025-8-21 22:37:35 - debug: output config of bundle internal success
2025-8-21 22:37:35 - debug: output config of bundle resources
2025-8-21 22:37:35 - debug: output config of bundle resources success
2025-8-21 22:37:35 - debug: output config of bundle main
2025-8-21 22:37:35 - debug: output config of bundle main success
2025-8-21 22:37:35 - debug: Output asset in bundles success, progress: 26%
2025-8-21 22:37:35 - debug: Output asset in bundles success, progress: 27%
2025-8-21 22:37:35 - debug: // ---- build task Build Assets ---- (494ms)
2025-8-21 22:37:35 - log: run build task Build Assets success in 494 ms√, progress: 31%
2025-8-21 22:37:35 - debug: [Build Memory track]: Build Assets start:224.45MB, end 209.10MB, increase: -15714.29KB
2025-8-21 22:37:35 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-21 22:37:35 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-21 22:37:35 - debug: // ---- build task ios：onAfterBuildAssets ---- (50ms)
2025-8-21 22:37:35 - debug: ios:(onAfterBuildAssets) in 50 ms ✓, progress: 33%
2025-8-21 22:37:35 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-21 22:37:35 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 22:37:35 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (58ms)
2025-8-21 22:37:35 - log: run build task 整理部分构建选项内数据到 settings.json success in 58 ms√, progress: 34%
2025-8-21 22:37:35 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.96MB, end 210.71MB, increase: 766.32KB
2025-8-21 22:37:35 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-21 22:37:35 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-21 22:37:35 - debug: // ---- build task 填充脚本数据到 settings.json ---- (58ms)
2025-8-21 22:37:35 - log: run build task 填充脚本数据到 settings.json success in 58 ms√, progress: 36%
2025-8-21 22:37:35 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.73MB, end 211.48MB, increase: 770.14KB
2025-8-21 22:37:35 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-21 22:37:35 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 22:37:35 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (64ms)
2025-8-21 22:37:35 - log: run build task 整理部分构建选项内数据到 settings.json success in 64 ms√, progress: 38%
2025-8-21 22:37:35 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.51MB, end 209.24MB, increase: -2325.59KB
2025-8-21 22:37:35 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-21 22:37:35 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-21 22:37:35 - debug: // ---- build task ios：onBeforeCompressSettings ---- (83ms)
2025-8-21 22:37:35 - debug: ios:(onBeforeCompressSettings) in 83 ms ✓, progress: 40%
2025-8-21 22:37:35 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-21 22:37:35 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-21 22:37:36 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (293ms)
2025-8-21 22:37:36 - debug: cocos-service:(onBeforeCompressSettings) in 293 ms ✓, progress: 41%
2025-8-21 22:37:36 - debug: 整理静态模板文件 start, progress: 41%
2025-8-21 22:37:36 - debug: // ---- build task 整理静态模板文件 ----
2025-8-21 22:37:36 - debug: // ---- build task 整理静态模板文件 ---- (82ms)
2025-8-21 22:37:36 - log: run build task 整理静态模板文件 success in 82 ms√, progress: 46%
2025-8-21 22:37:36 - debug: [Build Memory track]: 整理静态模板文件 start:211.23MB, end 209.77MB, increase: -1490.53KB
2025-8-21 22:37:36 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-21 22:37:36 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-21 22:37:36 - log: Checking template version...
2025-8-21 22:37:36 - log: Validating template consistency...
2025-8-21 22:37:36 - log: Validating platform source code directories...
2025-8-21 22:37:36 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-21 22:37:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.cpp
2025-8-21 22:37:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.m
2025-8-21 22:37:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/settings.gradle
2025-8-21 22:37:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-21 22:37:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-21 22:37:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-21 22:37:36 - debug: // ---- build task native：onAfterCompressSettings ---- (68ms)
2025-8-21 22:37:36 - debug: native:(onAfterCompressSettings) in 68 ms ✓, progress: 48%
2025-8-21 22:37:36 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-21 22:37:36 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-21 22:37:36 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (123ms)
2025-8-21 22:37:36 - debug: cocos-service:(onAfterCompressSettings) in 123 ms ✓, progress: 50%
2025-8-21 22:37:36 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-21 22:37:36 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-21 22:37:36 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (57ms)
2025-8-21 22:37:36 - log: run build task 给所有的资源加上 MD5 后缀 success in 57 ms√, progress: 60%
2025-8-21 22:37:36 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:212.23MB, end 210.03MB, increase: -2244.06KB
2025-8-21 22:37:36 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-21 22:37:36 - debug: // ---- build task native：onAfterBuild ----
2025-8-21 22:37:36 - log: [xcode-select] /Applications/Xcode.app/Contents/Developer


2025-8-21 22:37:36 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-21 22:37:38 - log: [cmake] -- The CXX compiler identification is AppleClang 16.0.0.16000026


2025-8-21 22:37:38 - log: [cmake] -- Detecting CXX compiler ABI info


2025-8-21 22:37:39 - log: [cmake] -- Detecting CXX compiler ABI info - done


2025-8-21 22:37:39 - log: [cmake] -- Check for working CXX compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ - skipped


2025-8-21 22:37:39 - log: [cmake] -- Detecting CXX compile features


2025-8-21 22:37:39 - log: [cmake] -- Detecting CXX compile features - done


2025-8-21 22:37:40 - log: [cmake] -- The C compiler identification is AppleClang 16.0.0.16000026


2025-8-21 22:37:40 - log: [cmake] -- The ASM compiler identification is Clang with GNU-like command-line


2025-8-21 22:37:40 - log: [cmake] -- Found assembler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang


2025-8-21 22:37:40 - log: [cmake] -- Detecting C compiler ABI info


2025-8-21 22:37:41 - log: [cmake] -- Detecting C compiler ABI info - done


2025-8-21 22:37:41 - log: [cmake] -- Check for working C compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang - skipped


2025-8-21 22:37:41 - log: [cmake] -- Detecting C compile features


2025-8-21 22:37:41 - log: [cmake] -- Detecting C compile features - done


2025-8-21 22:37:41 - log: [cmake] -- platform: iOS


2025-8-21 22:37:41 - log: [cmake] -- Ignore NO_WERROR


2025-8-21 22:37:41 - log: [cmake] -- OPTION BUILTIN_COCOS_X_PATH:	
-- OPTION USE_BUILTIN_EXTERNAL:	OFF
-- OPTION USE_MODULES:	OFF
-- OPTION CC_USE_METAL:	ON


2025-8-21 22:37:41 - log: [cmake] -- OPTION CC_USE_GLES3:	OFF
-- OPTION CC_USE_GLES2:	OFF
-- OPTION CC_USE_VULKAN:	OFF
-- OPTION CC_DEBUG_FORCE:	OFF
-- OPTION USE_SE_V8:	ON
-- OPTION USE_SE_JSVM:	OFF
-- OPTION USE_V8_DEBUGGER:	ON
-- OPTION USE_V8_DEBUGGER_FORCE:	OFF
-- OPTION USE_SE_SM:	OFF
-- OPTION USE_SOCKET:	OFF
-- OPTION USE_AUDIO:	ON
-- OPTION USE_EDIT_BOX:	ON
-- OPTION USE_VIDEO:	ON
-- OPTION USE_WEBVIEW:	ON
-- OPTION USE_MIDDLEWARE:	ON
-- OPTION USE_DRAGONBONES:	ON
-- OPTION USE_SPINE:	ON
-- OPTION USE_SPINE_3_8:	ON
-- OPTION USE_SPINE_4_2:	OFF
-- OPTION USE_WEBSOCKET_SERVER:	OFF
-- OPTION USE_PHYSICS_PHYSX:	OFF
-- OPTION USE_JOB_SYSTEM_TBB:	OFF
-- OPTION USE_JOB_SYSTEM_TASKFLOW:	OFF
-- OPTION USE_XR:	OFF
-- OPTION USE_SERVER_MODE:	OFF
-- OPTION USE_AR_MODULE:	OFF


2025-8-21 22:37:41 - log: [cmake] -- OPTION USE_AR_AUTO:	OFF
-- OPTION USE_AR_CORE:	OFF
-- OPTION USE_AR_ENGINE:	OFF
-- OPTION USE_CCACHE:	
-- OPTION CCACHE_EXECUTABLE:	CCACHE_EXECUTABLE-NOTFOUND
-- OPTION NODE_EXECUTABLE:	/opt/homebrew/Cellar/node/24.5.0/bin/node
-- OPTION NET_MODE:	0
-- OPTION USE_REMOTE_LOG:	OFF
-- OPTION USE_BOX2D_JSB:	OFF


2025-8-21 22:37:41 - log: [cmake] -- platform path: 


2025-8-21 22:37:41 - log: [cmake] -- Using Xcode 15 or newer, adding extra link flags: -Wl,-ld_classic.


2025-8-21 22:37:41 - log: [cmake] -- Try generating /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Pre-AutoLoadPlulgins.cmake


2025-8-21 22:37:41 - log: [cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js


2025-8-21 22:37:41 - log: [cmake] [searching plugins] no plugins found!


2025-8-21 22:37:41 - log: [cmake] -- Searching hook files Pre*.cmake or *Pre.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-21 22:37:41 - log: [cmake] -- ::Loading Pre-service.cmake


2025-8-21 22:37:41 - log: [cmake] -- No plugins are loaded!


2025-8-21 22:37:41 - log: [cmake] -- Searching hook files Post*.cmake or *Post.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios
-- ::Loading Post-service.cmake


2025-8-21 22:37:41 - log: [cmake] -- Configuring done


2025-8-21 22:37:41 - log: [cmake] -- Generating done


2025-8-21 22:37:41 - log: [cmake-warn] CMake Warning:
  Manually-specified variables were not used by the project:

    LAUNCH_TYPE




2025-8-21 22:37:41 - log: [cmake] -- Build files have been written to: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj


2025-8-21 22:37:41 - debug: // ---- build task native：onAfterBuild ---- (5173ms)
2025-8-21 22:37:41 - debug: native:(onAfterBuild) in 5173 ms ✓, progress: 62%
2025-8-21 22:37:41 - debug: ios:(onAfterBuild) start..., progress: 62%
2025-8-21 22:37:41 - debug: // ---- build task ios：onAfterBuild ----
2025-8-21 22:37:41 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 22:37:41 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png
2025-8-21 22:37:41 - debug: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 22:37:41 - debug: // ---- build task ios：onAfterBuild ---- (66ms)
2025-8-21 22:37:41 - debug: ios:(onAfterBuild) in 66 ms ✓, progress: 64%
2025-8-21 22:37:41 - debug: cocos-service:(onAfterBuild) start..., progress: 64%
2025-8-21 22:37:41 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-21 22:37:41 - debug: // ---- build task cocos-service：onAfterBuild ---- (225ms)
2025-8-21 22:37:41 - debug: cocos-service:(onAfterBuild) in 225 ms ✓, progress: 65%
2025-8-21 22:37:41 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 65%
2025-8-21 22:37:41 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-21 22:37:42 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (61ms)
2025-8-21 22:37:42 - debug: adsense-h5g-plugin:(onAfterBuild) in 61 ms ✓, progress: 67%
2025-8-21 22:37:42 - log: Asset DB is resume!
2025-8-21 22:37:42 - debug: refresh db internal success
2025-8-21 22:37:42 - debug: refresh db assets success
2025-8-21 22:37:42 - debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-21 22:37:42 - debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-21 22:37:42 - debug: builder:build-project-total (27682ms)
2025-8-21 22:37:42 - debug: asset-db:refresh-all-database (44ms)
2025-8-21 22:37:42 - debug: asset-db:worker-effect-data-processing (22ms)
2025-8-21 22:37:42 - debug: asset-db-hook-engine-extends-afterRefresh (22ms)
2025-8-21 22:37:42 - debug: build success in 27682!
2025-8-21 22:37:42 - debug: [Build Memory track]: builder:build-project-total start:222.18MB, end 210.57MB, increase: -11881.90KB
2025-8-21 22:37:42 - debug: ================================ build Task (ios) Finished in (27 s)ms ================================
2025-8-21 22:41:59 - debug: =================================== build Task (ios) Start ================================
2025-8-21 22:41:59 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"1563d039-d898-4d8f-9415-9f1da853b8a3","outputName":"ios","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"350F82HeBJpZKzTD","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-21-2025 22-37.log"}
2025-8-21 22:41:59 - debug: Build with Cocos Creator 3.8.6
2025-8-21 22:41:59 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-21 22:41:59 - debug: // ---- build task native：onBeforeBuild ----
2025-8-21 22:41:59 - debug: // ---- build task native：onBeforeBuild ---- (44ms)
2025-8-21 22:41:59 - debug: native:(onBeforeBuild) in 44 ms ✓, progress: 2%
2025-8-21 22:41:59 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-21 22:41:59 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-21 22:41:59 - debug: // ---- build task cocos-service：onBeforeBuild ---- (248ms)
2025-8-21 22:41:59 - debug: cocos-service:(onBeforeBuild) in 248 ms ✓, progress: 4%
2025-8-21 22:41:59 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-21 22:41:59 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-21 22:41:59 - debug: // ---- build task scene：onBeforeBuild ---- (53ms)
2025-8-21 22:41:59 - debug: scene:(onBeforeBuild) in 53 ms ✓, progress: 5%
2025-8-21 22:41:59 - debug: Start lock asset db..., progress: 5%
2025-8-21 22:41:59 - log: Asset DB is paused with build!
2025-8-21 22:41:59 - debug: Query all assets info in project
2025-8-21 22:41:59 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-21 22:41:59 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-21 22:41:59 - debug: // ---- build task native：onAfterInit ----
2025-8-21 22:41:59 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-21 22:41:59 - debug: // ---- build task native：onAfterInit ---- (34ms)
2025-8-21 22:41:59 - debug: native:(onAfterInit) in 34 ms ✓, progress: 7%
2025-8-21 22:41:59 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-21 22:41:59 - debug: // ---- build task ios：onAfterInit ----
2025-8-21 22:41:59 - debug: // ---- build task ios：onAfterInit ---- (52ms)
2025-8-21 22:41:59 - debug: ios:(onAfterInit) in 52 ms ✓, progress: 9%
2025-8-21 22:41:59 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-21 22:41:59 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-21 22:41:59 - debug: // ---- build task cocos-service：onAfterInit ---- (143ms)
2025-8-21 22:41:59 - debug: cocos-service:(onAfterInit) in 143 ms ✓, progress: 11%
2025-8-21 22:41:59 - debug: Skip compress image, progress: 0%
2025-8-21 22:41:59 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-21 22:41:59 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-21 22:41:59 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-21 22:41:59 - debug: [adsense-h5g-plugin] remove script success
2025-8-21 22:41:59 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (52ms)
2025-8-21 22:41:59 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 52 ms ✓, progress: 11%
2025-8-21 22:41:59 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 52 ms ✓, progress: 7%
2025-8-21 22:41:59 - debug: Init all bundles start..., progress: 11%
2025-8-21 22:41:59 - debug: Init all bundles start..., progress: 7%
2025-8-21 22:41:59 - debug: Num of bundles: 3..., progress: 11%
2025-8-21 22:41:59 - debug: Num of bundles: 3..., progress: 7%
2025-8-21 22:41:59 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-21 22:41:59 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-21 22:41:59 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-21 22:41:59 - debug: // ---- build task native：onAfterBundleInit ---- (78ms)
2025-8-21 22:41:59 - debug: native:(onAfterBundleInit) in 78 ms ✓, progress: 11%
2025-8-21 22:41:59 - debug: native:(onAfterBundleInit) in 78 ms ✓, progress: 13%
2025-8-21 22:41:59 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-21 22:41:59 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-21 22:41:59 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-21 22:41:59 - debug: // ---- build task ios：onAfterBundleInit ---- (58ms)
2025-8-21 22:41:59 - debug: ios:(onAfterBundleInit) in 58 ms ✓, progress: 11%
2025-8-21 22:41:59 - debug: ios:(onAfterBundleInit) in 58 ms ✓, progress: 20%
2025-8-21 22:41:59 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-21 22:41:59 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 22:41:59 - debug: Init bundle root assets start..., progress: 11%
2025-8-21 22:41:59 - debug: Init bundle root assets start..., progress: 20%
2025-8-21 22:42:00 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-21 22:42:00 - debug:   Number of all scenes: 3
2025-8-21 22:42:00 - debug:   Number of all scripts: 28
2025-8-21 22:42:00 - debug:   Number of other assets: 609
2025-8-21 22:42:00 - debug: Init bundle root assets success..., progress: 11%
2025-8-21 22:42:00 - debug: Init bundle root assets success..., progress: 20%
2025-8-21 22:42:00 - debug: reload all scripts.
2025-8-21 22:42:00 - debug: [[Executor]] reload before lock
2025-8-21 22:42:00 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-21 22:42:00 - groupCollapsed: Invalidate all modules
2025-8-21 22:42:00 - debug: Unregister BuiltinPipelineSettings
2025-8-21 22:42:00 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-21 22:42:00 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-21 22:42:00 - debug: Unregister DebugViewRuntimeControl
2025-8-21 22:42:00 - debug: Unregister CameraFollow
2025-8-21 22:42:00 - debug: Unregister Bullet
2025-8-21 22:42:00 - debug: Unregister AIPlayer
2025-8-21 22:42:00 - debug: Unregister player
2025-8-21 22:42:00 - debug: Unregister PlayerManager
2025-8-21 22:42:00 - debug: Unregister SceneTransition
2025-8-21 22:42:00 - debug: Unregister SoundManager
2025-8-21 22:42:00 - debug: Unregister PaintManager
2025-8-21 22:42:00 - debug: Unregister GameOverPanel
2025-8-21 22:42:00 - debug: Unregister GameHUD
2025-8-21 22:42:00 - debug: Unregister GameManager
2025-8-21 22:42:00 - debug: Unregister AIController
2025-8-21 22:42:00 - debug: Unregister CarProperties
2025-8-21 22:42:00 - debug: Unregister CarPropertyDisplay
2025-8-21 22:42:00 - debug: Unregister HealthBarUI
2025-8-21 22:42:00 - debug: Unregister MainMenuController
2025-8-21 22:42:00 - debug: Unregister PaintSpot
2025-8-21 22:42:00 - debug: Unregister PausePanel
2025-8-21 22:42:00 - debug: Unregister PlayerInfoUI
2025-8-21 22:42:00 - debug: Unregister PurchasePanel
2025-8-21 22:42:00 - debug: Unregister SelectManager
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js'
2025-8-21 22:42:00 - debug: Invalidating 'pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js'
2025-8-21 22:42:00 - groupEnd: Invalidate all modules
2025-8-21 22:42:00 - groupCollapsed: Imports all modules
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register CameraFollow
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register Bullet
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register AIPlayer
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register player
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register PlayerManager
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register SceneTransition
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register SoundManager
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register PaintManager
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register GameOverPanel
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register GameHUD
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register GameManager
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register AIController
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register CarProperties
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register HealthBarUI
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register MainMenuController
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register PaintSpot
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register PausePanel
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register PlayerInfoUI
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register PurchasePanel
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Register SelectManager
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-21 22:42:00 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 22:42:00 - groupEnd: Imports all modules
2025-8-21 22:42:00 - debug: [[Executor]] after unlock
2025-8-21 22:42:00 - debug: Incremental keys: 
2025-8-21 22:42:00 - debug: Init bundle share assets start..., progress: 11%
2025-8-21 22:42:00 - debug: Init bundle share assets start..., progress: 20%
2025-8-21 22:42:00 - debug: Init bundle share assets success..., progress: 11%
2025-8-21 22:42:00 - debug: Init bundle share assets success..., progress: 20%
2025-8-21 22:42:00 - debug: handle json group in bundle internal
2025-8-21 22:42:00 - debug: handle json group in bundle internal success
2025-8-21 22:42:00 - debug: handle json group in bundle resources
2025-8-21 22:42:00 - debug: handle json group in bundle main
2025-8-21 22:42:00 - debug: init image compress task 0 in bundle internal
2025-8-21 22:42:00 - debug: handle json group in bundle main success
2025-8-21 22:42:00 - debug: init image compress task 0 in bundle main
2025-8-21 22:42:00 - debug: handle json group in bundle resources success
2025-8-21 22:42:00 - debug: init image compress task 0 in bundle resources
2025-8-21 22:42:00 - debug: // ---- build task 查询 Asset Bundle ---- (225ms)
2025-8-21 22:42:00 - log: run build task 查询 Asset Bundle success in 225 ms√, progress: 16%
2025-8-21 22:42:00 - debug: [Build Memory track]: 查询 Asset Bundle start:219.82MB, end 221.55MB, increase: 1.72MB
2025-8-21 22:42:00 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-21 22:42:00 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 22:42:00 - debug: // ---- build task 查询 Asset Bundle ---- (58ms)
2025-8-21 22:42:00 - log: run build task 查询 Asset Bundle success in 58 ms√, progress: 21%
2025-8-21 22:42:00 - debug: [Build Memory track]: 查询 Asset Bundle start:221.57MB, end 222.48MB, increase: 925.86KB
2025-8-21 22:42:00 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-21 22:42:00 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-21 22:42:00 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-21 22:42:00 - debug: // ---- build task native：onAfterBundleDataTask ---- (61ms)
2025-8-21 22:42:00 - debug: native:(onAfterBundleDataTask) in 61 ms ✓, progress: 21%
2025-8-21 22:42:00 - debug: native:(onAfterBundleDataTask) in 61 ms ✓, progress: 27%
2025-8-21 22:42:00 - debug: 打包脚本 start, progress: 21%
2025-8-21 22:42:00 - debug: // ---- build task 打包脚本 ----
2025-8-21 22:42:00 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:42:00 - log: [build-script]enter sub process 73421, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-21 22:42:01 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-21 22:42:01 - debug: excute-script over with build-script 955ms
2025-8-21 22:42:01 - debug: Generate systemJs..., progress: 21%
2025-8-21 22:42:01 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:42:02 - debug: excute-script over with build-script 856ms
2025-8-21 22:42:02 - debug: 构建项目脚本 start..., progress: 21%
2025-8-21 22:42:02 - debug: Build script in bundle start, progress: 21%
2025-8-21 22:42:02 - debug: Build script in bundle start, progress: 27%
2025-8-21 22:42:02 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:42:02 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts


2025-8-21 22:42:02 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-21 22:42:03 - debug: excute-script over with build-script 1240ms
2025-8-21 22:42:03 - debug: Copy externalScripts success!
2025-8-21 22:42:03 - debug: Build script in bundle success, progress: 21%
2025-8-21 22:42:03 - debug: Build script in bundle success, progress: 27%
2025-8-21 22:42:03 - debug: 构建项目脚本 in (1374 ms) √, progress: 21%
2025-8-21 22:42:03 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-21 22:42:03 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-21 22:42:03 - debug: Engine cache (/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf) does not exist.
2025-8-21 22:42:03 - debug: mangleProperties is disabled, platform: IOS
2025-8-21 22:42:03 - debug: Cache is invalid, start build engine with options: {
  "incremental": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.watch-files.json",
  "engine": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
  "out": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf",
  "moduleFormat": "system",
  "compress": true,
  "nativeCodeBundleMode": "asmjs",
  "sourceMap": false,
  "targets": "chrome 80",
  "loose": true,
  "features": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "platform": "IOS",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "mode": "BUILD",
  "metaFile": "/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.meta/meta.json",
  "wasmCompressionMode": false,
  "inlineEnum": true,
  "mangleProperties": false,
  "mangleConfigJsonMtime": 0
}

2025-8-21 22:42:03 - debug: md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-21 22:42:03 - log: Run build task(build-engine) in child, see: chrome://inspect/#devices
2025-8-21 22:42:03 - log: [build-engine]enter sub process 73449, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-21 22:42:04 - log: [build-engine]start build engine with options: {"engine":"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine","out":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf","platform":"IOS","moduleFormat":"system","compress":true,"split":false,"nativeCodeBundleMode":"asmjs","assetURLFormat":"runtime-resolved","noDeprecatedFeatures":false,"sourceMap":false,"features":["2d","affine-transform","animation","audio","base","custom-pipeline","dragon-bones","graphics","intersection-2d","mask","particle-2d","physics-2d-box2d","profiler","rich-text","spine-3.8","tiled-map","tween","ui","video","webview","custom-pipeline-builtin-scripts"],"loose":true,"mode":"BUILD","flags":{"DEBUG":false,"LOAD_BULLET_MANUALLY":false,"LOAD_SPINE_MANUALLY":false},"metaFile":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.meta/meta.json","mangleProperties":false,"inlineEnum":true,"incremental":"/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf.watch-files.json","targets":"chrome 80","wasmCompressionMode":false,"mangleConfigJsonMtime":0}


2025-8-21 22:42:04 - log: [build-engine]Module source "internal-constants":
function tryDefineGlobal (name, value) {
    const _global = typeof window === 'undefined' ? global : window;
    if (typeof _global[name] === 'undefined') {
        return (_global[name] = value);
    } else {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return _global[name];
    }
}
export const HTML5 = false;

export const NATIVE = true;

export const ANDROID = false;

export const IOS = true;

export const MAC = false;

export const WINDOWS = false;

export const LINUX = false;

export const OHOS = false;

export const OPEN_HARMONY = false;

export const WECHAT = false;
tryDefineGlobal('CC_WECHAT', false);

export const WECHAT_MINI_PROGRAM = false;

export const XIAOMI = false;
tryDefineGlobal('CC_XIAOMI', false);

export const ALIPAY = false;
tryDefineGlobal('CC_ALIPAY', false);

export const TAOBAO = false;

export const TAOBAO_MINIGAME = false;

export const BYTEDANCE = false;
tryDefineGlobal('CC_BYTEDANCE', false);

export const OPPO = false;
tryDefineGlobal('CC_OPPO', false);

export const VIVO = false;
tryDefineGlobal('CC_VIVO', false);

export const HUAWEI = false;
tryDefineGlobal('CC_HUAWEI', false);

export const MIGU = false;
tryDefineGlobal('CC_MIGU', false);

export const HONOR = false;
tryDefineGlobal('CC_HONOR', false);

export const COCOS_RUNTIME = false;
tryDefineGlobal('CC_COCOS_RUNTIME', false);

export const EDITOR = false;
tryDefineGlobal('CC_EDITOR', false);

export const EDITOR_NOT_IN_PREVIEW = false;

export const PREVIEW = false;
tryDefineGlobal('CC_PREVIEW', false);

export const BUILD = true;
tryDefineGlobal('CC_BUILD', true);

export const TEST = false;
tryDefineGlobal('CC_TEST', false);

export const DEBUG = false;
tryDefineGlobal('CC_DEBUG', false);

export const SERVER_MODE = false;

export const DEV = false;
tryDefineGlobal('CC_DEV', false);

export const MINIGAME = false;
tryDefineGlobal('CC_MINIGAME', false);

export const RUNTIME_BASED = false;
tryDefineGlobal('CC_RUNTIME_BASED', false);

export const SUPPORT_JIT = true;
tryDefineGlobal('CC_SUPPORT_JIT', true);

export const JSB = true;
tryDefineGlobal('CC_JSB', true);

export const NOT_PACK_PHYSX_LIBS = false;

export const NET_MODE = 0;

export const WEBGPU = false;

export const NATIVE_CODE_BUNDLE_MODE = 0;

export const WASM_SUBPACKAGE = false;

export const CULL_MESHOPT = true;

export const LOAD_SPINE_MANUALLY = false;

export const LOAD_BOX2D_MANUALLY = false;

export const LOAD_BULLET_MANUALLY = false;

export const LOAD_PHYSX_MANUALLY = false;

export const USE_3D = false;

export const USE_UI_SKEW = false;

export const USE_XR = false;




2025-8-21 22:42:04 - log: [build-engine]Module source "cc":
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/sorting.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/affine-transform.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/animation.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/audio.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/base.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/custom-pipeline.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/dragon-bones.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/graphics.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/intersection-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/mask.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/particle-2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-box2d.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/physics-2d-framework.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/profiler.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/rich-text.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/spine.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tiled-map.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/tween.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/ui.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/video.ts';
export * from '/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/exports/webview.ts';


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/index.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/dragon-bones/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/dragon-bones/index.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/custom/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/custom/index.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/index.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/rendering/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/root.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/root.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/index.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/material.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/material.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/node.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/node.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/2d/renderer/native-2d.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/index.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/index.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/pass.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/pass.jsb.ts


2025-8-21 22:42:11 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/program-lib.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/program-lib.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/material-instance.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/material-instance.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-scene.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-scene.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-window.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/render-window.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module internal:native to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/native-binding/impl.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/asset-manager/builtin-res-mgr.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/asset-manager/builtin-res-mgr.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module pal/audio to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/audio/native/player.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/asset.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-base.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-base.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-2d.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-2d.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/image-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/image-asset.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/runtime-exports.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/animation/marionette/index-empty.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module pal/minigame to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/minigame/non-minigame.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/misc/create-mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/misc/create-mesh.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/assets/mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/assets/mesh.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/models/morph-model.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/3d/models/morph-model.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module pal/screen-adapter to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/screen-adapter/native/screen-adapter.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/scene-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/scene-asset.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/camera.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/camera.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/submodel.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/submodel.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/native-pools.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/core/native-pools.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/render-texture.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/render-texture.jsb.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module pal/system-info to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/system-info/native/system-info.ts
Redirect module pal/env to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/env/native/env.ts


2025-8-21 22:42:12 - log: [build-engine]Redirect module pal/pacer to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/pacer/pacer-native.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module pal/input to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/pal/input/native/index.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/buffer-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/buffer-asset.jsb.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/spine/lib/spine-version-3.8.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/base/pipeline-state.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/gfx/base/pipeline-state.jsb.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene.jsb.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene-globals.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/scene-graph/scene-globals.jsb.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/rendering-sub-mesh.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/rendering-sub-mesh.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-cube.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/texture-cube.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/effect-asset.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/effect-asset.jsb.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/simple-texture.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/asset/assets/simple-texture.jsb.ts


2025-8-21 22:42:13 - log: [build-engine]Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/model.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/model.jsb.ts
Redirect module /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/reflection-probe.ts to /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/cocos/render-scene/scene/reflection-probe.jsb.ts


2025-8-21 22:42:14 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-21 22:42:14 - log: [build-engine]Rollup warning 'THIS_IS_UNDEFINED' is omitted for /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/node_modules/@cocos/dragonbones-js/out/dragonBones.js


2025-8-21 22:42:15 - log: [build-engine]==== Performance ====
{"# BUILD":[10146.755625,1088289988,1229063440],"## initialize":[5976.445959000001,426987428,567142576],"- plugin 6 (node-resolve) - buildStart":[0.0451660000001084,-220736,140171144],"- plugin 8 (commonjs) - buildStart":[0.014208000000053289,3360,140175348],"- plugin 9 (@cocos/typescript) - buildStart":[5976.322082999999,426964880,567140584],"## generate module graph":[3457.1065,607317660,1174460824],"- plugin 0 (commonjs--resolver) - resolveId":[8.392448999998123,2286688,1173640772],"- plugin 1 (@cocos/ccbuild|external-loader) - resolveId":[3.486036000051172,714204,1173642908],"- plugin 2 (@cocos/ccbuild|module-overrides) - resolveId":[3.3832289999709246,563260,1173655308],"- plugin 3 (virtual) - resolveId":[9.979042000012669,-4901028,1173837676],"- plugin 1 (@cocos/ccbuild|external-loader) - load":[1.0483409999915239,393228,1173018328],"- plugin 2 (@cocos/ccbuild|module-overrides) - load":[2.879496000009567,2082532,1173019572],"- plugin 3 (virtual) - load":[0.748603999977604,468856,1173020496],"- plugin 7 (json) - transform":[1.7092130000019097,400760,1173026396],"- plugin 8 (commonjs) - transform":[62.895924999984345,6942892,1173028128],"- plugin 10 (babel) - transform":[39.1180320000276,-113292676,1173681076],"generate ast":[300.55628600000637,154477412,1173632140],"- plugin 4 (@cocos/ccbuild|module-query-plugin) - resolveId":[4.9984319999812215,2416668,1173688188],"- plugin 5 (ts-paths) - resolveId":[4.621522000023106,-3215508,1173756928],"- plugin 9 (@cocos/typescript) - resolveId":[310.098246000005,55967080,1172535644],"- plugin 10 (babel) - resolveId":[0.6297369999729199,426464,1172539724],"- plugin 6 (node-resolve) - resolveId":[1.3095079999948211,-517596,1019638016],"- plugin 6 (node-resolve) - load":[0.7653689999624476,16396,1173021428],"- plugin 8 (commonjs) - load":[0.9082060000191632,332840,1173022932],"- plugin 9 (@cocos/typescript) - load":[7.517723000008118,4319060,1019663548],"- plugin 10 (babel) - load":[61.83832900000198,5601408,1019664208],"## sort and bind modules":[76.85033300000032,7409896,1181871052],"## mark included statements":[633.0173749999994,46506424,1228377768],"treeshaking pass 1":[214.21433400000024,29986620,1212051224],"treeshaking pass 2":[168.0565000000006,13582084,1225633600],"treeshaking pass 3":[56.057458000001134,888,1225634780],"treeshaking pass 4":[39.377416999999696,-3983508,1221651564],"treeshaking pass 5":[31.51850000000013,2478748,1224130604],"treeshaking pass 6":[26.73320799999965,5662160,1229793056],"treeshaking pass 7":[24.948624999999083,-5635084,1224158264],"treeshaking pass 8":[24.01341599999978,1626600,1225785156],"treeshaking pass 9":[24.3174999999992,1353316,1227138764],"treeshaking pass 10":[23.24791600000026,1238188,1228377244],"- plugin 8 (commonjs) - buildEnd":[0.005124999999679858,384,1228380868],"- plugin 9 (@cocos/typescript) - buildEnd":[3.193375000000742,680960,1229062136]}


2025-8-21 22:42:15 - log: [build-engine]====             ====


2025-8-21 22:42:20 - debug: excute-script over with build-engine 16564ms
2025-8-21 22:42:20 - debug: build engine done: output: /var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf
2025-8-21 22:42:20 - debug: 构建引擎脚本 in (16650 ms) √, progress: 21%
2025-8-21 22:42:20 - debug: Copy plugin script ..., progress: 21%
2025-8-21 22:42:20 - debug: Generate import-map..., progress: 21%
2025-8-21 22:42:20 - debug: // ---- build task 打包脚本 ---- (19926ms)
2025-8-21 22:42:20 - log: run build task 打包脚本 success in 19 s√, progress: 26%
2025-8-21 22:42:20 - debug: [Build Memory track]: 打包脚本 start:221.90MB, end 221.89MB, increase: -2.13KB
2025-8-21 22:42:20 - debug: Build Assets start, progress: 26%
2025-8-21 22:42:20 - debug: // ---- build task Build Assets ----
2025-8-21 22:42:20 - debug: Build bundles..., progress: 26%
2025-8-21 22:42:20 - debug: Pack Images start, progress: 26%
2025-8-21 22:42:20 - debug: Pack Images start, progress: 27%
2025-8-21 22:42:20 - debug: builder:pack-auto-atlas-image (149ms)
2025-8-21 22:42:20 - debug: Pack Images success, progress: 26%
2025-8-21 22:42:20 - debug: Pack Images success, progress: 27%
2025-8-21 22:42:20 - debug: Compress image start..., progress: 26%
2025-8-21 22:42:20 - debug: Compress image start..., progress: 27%
2025-8-21 22:42:20 - group: Compress image...
2025-8-21 22:42:20 - debug: sort compress task {}
2025-8-21 22:42:20 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-21 22:42:20 - debug: No image need to compress
2025-8-21 22:42:20 - groupEnd: Compress image...
2025-8-21 22:42:20 - debug: Compress image success..., progress: 26%
2025-8-21 22:42:20 - debug: Compress image success..., progress: 27%
2025-8-21 22:42:20 - debug: Output asset in bundles start, progress: 26%
2025-8-21 22:42:20 - debug: Output asset in bundles start, progress: 27%
2025-8-21 22:42:20 - debug: Handle all json groups in bundle internal
2025-8-21 22:42:20 - debug: handle json group
2025-8-21 22:42:20 - debug: Handle all json groups in bundle resources
2025-8-21 22:42:20 - debug: handle json group
2025-8-21 22:42:20 - debug: Handle all json groups in bundle main
2025-8-21 22:42:20 - debug: handle json group
2025-8-21 22:42:20 - debug: Json group(05b737039) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(06585a170) compile success，json number: 6
2025-8-21 22:42:20 - debug: handle single json
2025-8-21 22:42:20 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(01959b579) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(0a498a445) compile success，json number: 6
2025-8-21 22:42:20 - debug: handle single json
2025-8-21 22:42:20 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-21 22:42:20 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-21 22:42:20 - debug: handle single json
2025-8-21 22:42:20 - debug: Output asset in bundles success, progress: 26%
2025-8-21 22:42:20 - debug: Output asset in bundles success, progress: 27%
2025-8-21 22:42:20 - debug: Output asset in bundles start, progress: 26%
2025-8-21 22:42:20 - debug: Output asset in bundles start, progress: 27%
2025-8-21 22:42:20 - debug: compress config of bundle internal...
2025-8-21 22:42:20 - debug: compress config of bundle internal success
2025-8-21 22:42:20 - debug: compress config of bundle resources...
2025-8-21 22:42:20 - debug: compress config of bundle resources success
2025-8-21 22:42:20 - debug: compress config of bundle main...
2025-8-21 22:42:20 - debug: compress config of bundle main success
2025-8-21 22:42:20 - debug: output config of bundle internal
2025-8-21 22:42:20 - debug: output config of bundle internal success
2025-8-21 22:42:20 - debug: output config of bundle resources
2025-8-21 22:42:20 - debug: output config of bundle resources success
2025-8-21 22:42:20 - debug: output config of bundle main
2025-8-21 22:42:20 - debug: output config of bundle main success
2025-8-21 22:42:20 - debug: Output asset in bundles success, progress: 26%
2025-8-21 22:42:20 - debug: Output asset in bundles success, progress: 27%
2025-8-21 22:42:20 - debug: // ---- build task Build Assets ---- (525ms)
2025-8-21 22:42:20 - log: run build task Build Assets success in 525 ms√, progress: 31%
2025-8-21 22:42:20 - debug: [Build Memory track]: Build Assets start:221.92MB, end 208.51MB, increase: -13733.08KB
2025-8-21 22:42:20 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-21 22:42:20 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-21 22:42:20 - debug: // ---- build task ios：onAfterBuildAssets ---- (59ms)
2025-8-21 22:42:20 - debug: ios:(onAfterBuildAssets) in 59 ms ✓, progress: 33%
2025-8-21 22:42:20 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-21 22:42:20 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 22:42:20 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (62ms)
2025-8-21 22:42:20 - log: run build task 整理部分构建选项内数据到 settings.json success in 62 ms√, progress: 34%
2025-8-21 22:42:20 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.36MB, end 210.11MB, increase: 765.99KB
2025-8-21 22:42:20 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-21 22:42:20 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-21 22:42:21 - debug: // ---- build task 填充脚本数据到 settings.json ---- (96ms)
2025-8-21 22:42:21 - log: run build task 填充脚本数据到 settings.json success in 96 ms√, progress: 36%
2025-8-21 22:42:21 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.14MB, end 210.89MB, increase: 767.65KB
2025-8-21 22:42:21 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-21 22:42:21 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 22:42:21 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (90ms)
2025-8-21 22:42:21 - log: run build task 整理部分构建选项内数据到 settings.json success in 90 ms√, progress: 38%
2025-8-21 22:42:21 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.92MB, end 208.72MB, increase: -2244.64KB
2025-8-21 22:42:21 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-21 22:42:21 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-21 22:42:21 - debug: // ---- build task ios：onBeforeCompressSettings ---- (89ms)
2025-8-21 22:42:21 - debug: ios:(onBeforeCompressSettings) in 89 ms ✓, progress: 40%
2025-8-21 22:42:21 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-21 22:42:21 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-21 22:42:21 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (263ms)
2025-8-21 22:42:21 - debug: cocos-service:(onBeforeCompressSettings) in 263 ms ✓, progress: 41%
2025-8-21 22:42:21 - debug: 整理静态模板文件 start, progress: 41%
2025-8-21 22:42:21 - debug: // ---- build task 整理静态模板文件 ----
2025-8-21 22:42:21 - debug: // ---- build task 整理静态模板文件 ---- (72ms)
2025-8-21 22:42:21 - log: run build task 整理静态模板文件 success in 72 ms√, progress: 46%
2025-8-21 22:42:21 - debug: [Build Memory track]: 整理静态模板文件 start:210.71MB, end 209.66MB, increase: -1073.51KB
2025-8-21 22:42:21 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-21 22:42:21 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-21 22:42:21 - log: Checking template version...
2025-8-21 22:42:21 - log: Validating template consistency...
2025-8-21 22:42:21 - log: Validating platform source code directories...
2025-8-21 22:42:21 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-21 22:42:21 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.cpp
2025-8-21 22:42:21 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.m
2025-8-21 22:42:21 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/settings.gradle
2025-8-21 22:42:21 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-21 22:42:21 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-21 22:42:21 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-21 22:42:21 - debug: // ---- build task native：onAfterCompressSettings ---- (61ms)
2025-8-21 22:42:21 - debug: native:(onAfterCompressSettings) in 61 ms ✓, progress: 48%
2025-8-21 22:42:21 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-21 22:42:21 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-21 22:42:21 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (154ms)
2025-8-21 22:42:21 - debug: cocos-service:(onAfterCompressSettings) in 154 ms ✓, progress: 50%
2025-8-21 22:42:21 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-21 22:42:21 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-21 22:42:21 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (55ms)
2025-8-21 22:42:21 - log: run build task 给所有的资源加上 MD5 后缀 success in 55 ms√, progress: 60%
2025-8-21 22:42:21 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:209.19MB, end 209.94MB, increase: 767.91KB
2025-8-21 22:42:21 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-21 22:42:21 - debug: // ---- build task native：onAfterBuild ----
2025-8-21 22:42:21 - log: [xcode-select] /Applications/Xcode.app/Contents/Developer


2025-8-21 22:42:21 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-21 22:42:24 - log: [cmake] -- The CXX compiler identification is AppleClang 16.0.0.16000026


2025-8-21 22:42:24 - log: [cmake] -- Detecting CXX compiler ABI info


2025-8-21 22:42:25 - log: [cmake] -- Detecting CXX compiler ABI info - done


2025-8-21 22:42:25 - log: [cmake] -- Check for working CXX compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ - skipped


2025-8-21 22:42:25 - log: [cmake] -- Detecting CXX compile features


2025-8-21 22:42:25 - log: [cmake] -- Detecting CXX compile features - done


2025-8-21 22:42:25 - log: [cmake] -- The C compiler identification is AppleClang 16.0.0.16000026


2025-8-21 22:42:25 - log: [cmake] -- The ASM compiler identification is Clang with GNU-like command-line


2025-8-21 22:42:25 - log: [cmake] -- Found assembler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang


2025-8-21 22:42:25 - log: [cmake] -- Detecting C compiler ABI info


2025-8-21 22:42:26 - log: [cmake] -- Detecting C compiler ABI info - done


2025-8-21 22:42:26 - log: [cmake] -- Check for working C compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang - skipped


2025-8-21 22:42:26 - log: [cmake] -- Detecting C compile features


2025-8-21 22:42:26 - log: [cmake] -- Detecting C compile features - done


2025-8-21 22:42:26 - log: [cmake] -- platform: iOS


2025-8-21 22:42:26 - log: [cmake] -- Ignore NO_WERROR


2025-8-21 22:42:26 - log: [cmake] -- OPTION BUILTIN_COCOS_X_PATH:	
-- OPTION USE_BUILTIN_EXTERNAL:	OFF
-- OPTION USE_MODULES:	OFF


2025-8-21 22:42:26 - log: [cmake] -- OPTION CC_USE_METAL:	ON
-- OPTION CC_USE_GLES3:	OFF
-- OPTION CC_USE_GLES2:	OFF
-- OPTION CC_USE_VULKAN:	OFF
-- OPTION CC_DEBUG_FORCE:	OFF
-- OPTION USE_SE_V8:	ON
-- OPTION USE_SE_JSVM:	OFF
-- OPTION USE_V8_DEBUGGER:	ON
-- OPTION USE_V8_DEBUGGER_FORCE:	OFF
-- OPTION USE_SE_SM:	OFF
-- OPTION USE_SOCKET:	OFF
-- OPTION USE_AUDIO:	ON
-- OPTION USE_EDIT_BOX:	ON
-- OPTION USE_VIDEO:	ON
-- OPTION USE_WEBVIEW:	ON
-- OPTION USE_MIDDLEWARE:	ON
-- OPTION USE_DRAGONBONES:	ON
-- OPTION USE_SPINE:	ON
-- OPTION USE_SPINE_3_8:	ON
-- OPTION USE_SPINE_4_2:	OFF
-- OPTION USE_WEBSOCKET_SERVER:	OFF
-- OPTION USE_PHYSICS_PHYSX:	OFF
-- OPTION USE_JOB_SYSTEM_TBB:	OFF


2025-8-21 22:42:26 - log: [cmake] -- OPTION USE_JOB_SYSTEM_TASKFLOW:	OFF
-- OPTION USE_XR:	OFF
-- OPTION USE_SERVER_MODE:	OFF
-- OPTION USE_AR_MODULE:	OFF
-- OPTION USE_AR_AUTO:	OFF
-- OPTION USE_AR_CORE:	OFF
-- OPTION USE_AR_ENGINE:	OFF
-- OPTION USE_CCACHE:	
-- OPTION CCACHE_EXECUTABLE:	CCACHE_EXECUTABLE-NOTFOUND
-- OPTION NODE_EXECUTABLE:	/opt/homebrew/Cellar/node/24.5.0/bin/node
-- OPTION NET_MODE:	0
-- OPTION USE_REMOTE_LOG:	OFF
-- OPTION USE_BOX2D_JSB:	OFF


2025-8-21 22:42:26 - log: [cmake] -- platform path: 


2025-8-21 22:42:26 - log: [cmake] -- Using Xcode 15 or newer, adding extra link flags: -Wl,-ld_classic.


2025-8-21 22:42:26 - log: [cmake] -- Try generating /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Pre-AutoLoadPlulgins.cmake


2025-8-21 22:42:26 - log: [cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js


2025-8-21 22:42:26 - log: [cmake] [searching plugins] no plugins found!


2025-8-21 22:42:26 - log: [cmake] -- Searching hook files Pre*.cmake or *Pre.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-21 22:42:26 - log: [cmake] -- ::Loading Pre-service.cmake


2025-8-21 22:42:26 - log: [cmake] -- No plugins are loaded!


2025-8-21 22:42:26 - log: [cmake] -- Searching hook files Post*.cmake or *Post.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-21 22:42:26 - log: [cmake] -- ::Loading Post-service.cmake


2025-8-21 22:42:26 - log: [cmake] -- Configuring done


2025-8-21 22:42:27 - log: [cmake] -- Generating done


2025-8-21 22:42:27 - log: [cmake-warn] CMake Warning:
  Manually-specified variables were not used by the project:

    LAUNCH_TYPE




2025-8-21 22:42:27 - log: [cmake] -- Build files have been written to: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj


2025-8-21 22:42:27 - debug: // ---- build task native：onAfterBuild ---- (5303ms)
2025-8-21 22:42:27 - debug: native:(onAfterBuild) in 5303 ms ✓, progress: 62%
2025-8-21 22:42:27 - debug: ios:(onAfterBuild) start..., progress: 62%
2025-8-21 22:42:27 - debug: // ---- build task ios：onAfterBuild ----
2025-8-21 22:42:27 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 22:42:27 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png
2025-8-21 22:42:27 - debug: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 22:42:27 - debug: // ---- build task ios：onAfterBuild ---- (61ms)
2025-8-21 22:42:27 - debug: ios:(onAfterBuild) in 61 ms ✓, progress: 64%
2025-8-21 22:42:27 - debug: cocos-service:(onAfterBuild) start..., progress: 64%
2025-8-21 22:42:27 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-21 22:42:27 - debug: // ---- build task cocos-service：onAfterBuild ---- (231ms)
2025-8-21 22:42:27 - debug: cocos-service:(onAfterBuild) in 231 ms ✓, progress: 65%
2025-8-21 22:42:27 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 65%
2025-8-21 22:42:27 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-21 22:42:27 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (71ms)
2025-8-21 22:42:27 - debug: adsense-h5g-plugin:(onAfterBuild) in 71 ms ✓, progress: 67%
2025-8-21 22:42:27 - log: Asset DB is resume!
2025-8-21 22:42:27 - debug: builder:build-project-total (28361ms)
2025-8-21 22:42:27 - debug: build success in 28361!
2025-8-21 22:42:27 - debug: [Build Memory track]: builder:build-project-total start:224.50MB, end 213.92MB, increase: -10834.65KB
2025-8-21 22:42:27 - debug: ================================ build Task (ios) Finished in (28 s)ms ================================
2025-8-21 22:52:31 - debug: =================================== build Task (ios) Start ================================
2025-8-21 22:52:31 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"1563d039-d898-4d8f-9415-9f1da853b8a3","outputName":"ios","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"350F82HeBJpZKzTD","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-21-2025 22-37.log"}
2025-8-21 22:52:31 - debug: Build with Cocos Creator 3.8.6
2025-8-21 22:52:31 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-21 22:52:31 - debug: // ---- build task native：onBeforeBuild ----
2025-8-21 22:52:31 - debug: // ---- build task native：onBeforeBuild ---- (42ms)
2025-8-21 22:52:31 - debug: native:(onBeforeBuild) in 42 ms ✓, progress: 2%
2025-8-21 22:52:31 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-21 22:52:31 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-21 22:52:31 - debug: // ---- build task cocos-service：onBeforeBuild ---- (211ms)
2025-8-21 22:52:31 - debug: cocos-service:(onBeforeBuild) in 211 ms ✓, progress: 4%
2025-8-21 22:52:31 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-21 22:52:31 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-21 22:52:31 - debug: // ---- build task scene：onBeforeBuild ---- (55ms)
2025-8-21 22:52:31 - debug: scene:(onBeforeBuild) in 55 ms ✓, progress: 5%
2025-8-21 22:52:31 - debug: Start lock asset db..., progress: 5%
2025-8-21 22:52:31 - log: Asset DB is paused with build!
2025-8-21 22:52:31 - debug: Query all assets info in project
2025-8-21 22:52:31 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-21 22:52:31 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-21 22:52:31 - debug: // ---- build task native：onAfterInit ----
2025-8-21 22:52:31 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-21 22:52:31 - debug: // ---- build task native：onAfterInit ---- (31ms)
2025-8-21 22:52:31 - debug: native:(onAfterInit) in 31 ms ✓, progress: 7%
2025-8-21 22:52:31 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-21 22:52:31 - debug: // ---- build task ios：onAfterInit ----
2025-8-21 22:52:32 - debug: // ---- build task ios：onAfterInit ---- (57ms)
2025-8-21 22:52:32 - debug: ios:(onAfterInit) in 57 ms ✓, progress: 9%
2025-8-21 22:52:32 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-21 22:52:32 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-21 22:52:32 - debug: // ---- build task cocos-service：onAfterInit ---- (154ms)
2025-8-21 22:52:32 - debug: cocos-service:(onAfterInit) in 154 ms ✓, progress: 11%
2025-8-21 22:52:32 - debug: Skip compress image, progress: 0%
2025-8-21 22:52:32 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-21 22:52:32 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-21 22:52:32 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-21 22:52:32 - debug: [adsense-h5g-plugin] remove script success
2025-8-21 22:52:32 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (53ms)
2025-8-21 22:52:32 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 53 ms ✓, progress: 11%
2025-8-21 22:52:32 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 53 ms ✓, progress: 7%
2025-8-21 22:52:32 - debug: Init all bundles start..., progress: 11%
2025-8-21 22:52:32 - debug: Init all bundles start..., progress: 7%
2025-8-21 22:52:32 - debug: Num of bundles: 3..., progress: 11%
2025-8-21 22:52:32 - debug: Num of bundles: 3..., progress: 7%
2025-8-21 22:52:32 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-21 22:52:32 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-21 22:52:32 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-21 22:52:32 - debug: // ---- build task native：onAfterBundleInit ---- (84ms)
2025-8-21 22:52:32 - debug: native:(onAfterBundleInit) in 84 ms ✓, progress: 11%
2025-8-21 22:52:32 - debug: native:(onAfterBundleInit) in 84 ms ✓, progress: 13%
2025-8-21 22:52:32 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-21 22:52:32 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-21 22:52:32 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-21 22:52:32 - debug: // ---- build task ios：onAfterBundleInit ---- (53ms)
2025-8-21 22:52:32 - debug: ios:(onAfterBundleInit) in 53 ms ✓, progress: 11%
2025-8-21 22:52:32 - debug: ios:(onAfterBundleInit) in 53 ms ✓, progress: 20%
2025-8-21 22:52:32 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-21 22:52:32 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 22:52:32 - debug: Init bundle root assets start..., progress: 11%
2025-8-21 22:52:32 - debug: Init bundle root assets start..., progress: 20%
2025-8-21 22:52:32 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-21 22:52:32 - debug:   Number of all scenes: 3
2025-8-21 22:52:32 - debug:   Number of all scripts: 28
2025-8-21 22:52:32 - debug:   Number of other assets: 609
2025-8-21 22:52:32 - debug: Init bundle root assets success..., progress: 11%
2025-8-21 22:52:32 - debug: Init bundle root assets success..., progress: 20%
2025-8-21 22:52:32 - debug: reload all scripts.
2025-8-21 22:52:32 - debug: [[Executor]] reload before lock
2025-8-21 22:52:32 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-21 22:52:32 - groupCollapsed: Invalidate all modules
2025-8-21 22:52:32 - debug: Unregister BuiltinPipelineSettings
2025-8-21 22:52:32 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-21 22:52:32 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-21 22:52:32 - debug: Unregister DebugViewRuntimeControl
2025-8-21 22:52:32 - debug: Unregister CameraFollow
2025-8-21 22:52:32 - debug: Unregister Bullet
2025-8-21 22:52:32 - debug: Unregister AIPlayer
2025-8-21 22:52:32 - debug: Unregister player
2025-8-21 22:52:32 - debug: Unregister PlayerManager
2025-8-21 22:52:32 - debug: Unregister SceneTransition
2025-8-21 22:52:32 - debug: Unregister SoundManager
2025-8-21 22:52:32 - debug: Unregister PaintManager
2025-8-21 22:52:32 - debug: Unregister GameOverPanel
2025-8-21 22:52:32 - debug: Unregister GameHUD
2025-8-21 22:52:32 - debug: Unregister GameManager
2025-8-21 22:52:32 - debug: Unregister AIController
2025-8-21 22:52:32 - debug: Unregister CarProperties
2025-8-21 22:52:32 - debug: Unregister CarPropertyDisplay
2025-8-21 22:52:32 - debug: Unregister HealthBarUI
2025-8-21 22:52:32 - debug: Unregister MainMenuController
2025-8-21 22:52:32 - debug: Unregister PaintSpot
2025-8-21 22:52:32 - debug: Unregister PausePanel
2025-8-21 22:52:32 - debug: Unregister PlayerInfoUI
2025-8-21 22:52:32 - debug: Unregister PurchasePanel
2025-8-21 22:52:32 - debug: Unregister SelectManager
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js'
2025-8-21 22:52:32 - debug: Invalidating 'pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js'
2025-8-21 22:52:32 - groupEnd: Invalidate all modules
2025-8-21 22:52:32 - groupCollapsed: Imports all modules
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register CameraFollow
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register Bullet
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register AIPlayer
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register player
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register PlayerManager
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register SceneTransition
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register SoundManager
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register PaintManager
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register GameOverPanel
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register GameHUD
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register GameManager
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register AIController
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register CarProperties
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register HealthBarUI
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register MainMenuController
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register PaintSpot
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register PausePanel
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register PlayerInfoUI
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register PurchasePanel
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Register SelectManager
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-21 22:52:32 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 22:52:32 - groupEnd: Imports all modules
2025-8-21 22:52:32 - debug: [[Executor]] after unlock
2025-8-21 22:52:32 - debug: Incremental keys: 
2025-8-21 22:52:32 - debug: Init bundle share assets start..., progress: 11%
2025-8-21 22:52:32 - debug: Init bundle share assets start..., progress: 20%
2025-8-21 22:52:32 - debug: Init bundle share assets success..., progress: 11%
2025-8-21 22:52:32 - debug: Init bundle share assets success..., progress: 20%
2025-8-21 22:52:32 - debug: handle json group in bundle internal
2025-8-21 22:52:32 - debug: handle json group in bundle internal success
2025-8-21 22:52:32 - debug: handle json group in bundle resources
2025-8-21 22:52:32 - debug: handle json group in bundle main
2025-8-21 22:52:32 - debug: init image compress task 0 in bundle internal
2025-8-21 22:52:32 - debug: handle json group in bundle main success
2025-8-21 22:52:32 - debug: init image compress task 0 in bundle main
2025-8-21 22:52:32 - debug: handle json group in bundle resources success
2025-8-21 22:52:32 - debug: init image compress task 0 in bundle resources
2025-8-21 22:52:32 - debug: // ---- build task 查询 Asset Bundle ---- (214ms)
2025-8-21 22:52:32 - log: run build task 查询 Asset Bundle success in 214 ms√, progress: 16%
2025-8-21 22:52:32 - debug: [Build Memory track]: 查询 Asset Bundle start:224.84MB, end 226.94MB, increase: 2.09MB
2025-8-21 22:52:32 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-21 22:52:32 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 22:52:32 - debug: // ---- build task 查询 Asset Bundle ---- (52ms)
2025-8-21 22:52:32 - log: run build task 查询 Asset Bundle success in 52 ms√, progress: 21%
2025-8-21 22:52:32 - debug: [Build Memory track]: 查询 Asset Bundle start:226.96MB, end 227.82MB, increase: 874.97KB
2025-8-21 22:52:32 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-21 22:52:32 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-21 22:52:32 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-21 22:52:32 - debug: // ---- build task native：onAfterBundleDataTask ---- (53ms)
2025-8-21 22:52:32 - debug: native:(onAfterBundleDataTask) in 53 ms ✓, progress: 21%
2025-8-21 22:52:32 - debug: native:(onAfterBundleDataTask) in 53 ms ✓, progress: 27%
2025-8-21 22:52:32 - debug: 打包脚本 start, progress: 21%
2025-8-21 22:52:32 - debug: // ---- build task 打包脚本 ----
2025-8-21 22:52:32 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:52:32 - log: [build-script]enter sub process 76290, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-21 22:52:33 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-21 22:52:33 - debug: excute-script over with build-script 836ms
2025-8-21 22:52:33 - debug: Generate systemJs..., progress: 21%
2025-8-21 22:52:33 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:52:34 - debug: excute-script over with build-script 825ms
2025-8-21 22:52:34 - debug: 构建项目脚本 start..., progress: 21%
2025-8-21 22:52:34 - debug: Build script in bundle start, progress: 21%
2025-8-21 22:52:34 - debug: Build script in bundle start, progress: 27%
2025-8-21 22:52:34 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 22:52:35 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts


2025-8-21 22:52:35 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-21 22:52:35 - debug: excute-script over with build-script 1193ms
2025-8-21 22:52:35 - debug: Copy externalScripts success!
2025-8-21 22:52:35 - debug: Build script in bundle success, progress: 21%
2025-8-21 22:52:35 - debug: Build script in bundle success, progress: 27%
2025-8-21 22:52:35 - debug: 构建项目脚本 in (1316 ms) √, progress: 21%
2025-8-21 22:52:35 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-21 22:52:35 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-21 22:52:35 - debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf)}
2025-8-21 22:52:35 - debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-21 22:52:35 - debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "src/cocos-js",
  "platform": "IOS",
  "useCache": true,
  "nativeCodeBundleMode": "asmjs",
  "wasmCompressionMode": false,
  "output": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/cocos-js",
  "targets": "chrome 80",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-21 22:52:35 - debug: 构建引擎脚本 in (70 ms) √, progress: 21%
2025-8-21 22:52:35 - debug: Copy plugin script ..., progress: 21%
2025-8-21 22:52:35 - debug: Generate import-map..., progress: 21%
2025-8-21 22:52:35 - debug: // ---- build task 打包脚本 ---- (3132ms)
2025-8-21 22:52:35 - log: run build task 打包脚本 success in 3 s√, progress: 26%
2025-8-21 22:52:35 - debug: [Build Memory track]: 打包脚本 start:227.24MB, end 210.04MB, increase: -17617.16KB
2025-8-21 22:52:35 - debug: Build Assets start, progress: 26%
2025-8-21 22:52:35 - debug: // ---- build task Build Assets ----
2025-8-21 22:52:35 - debug: Build bundles..., progress: 26%
2025-8-21 22:52:35 - debug: Pack Images start, progress: 26%
2025-8-21 22:52:35 - debug: Pack Images start, progress: 27%
2025-8-21 22:52:35 - debug: builder:pack-auto-atlas-image (111ms)
2025-8-21 22:52:35 - debug: Pack Images success, progress: 26%
2025-8-21 22:52:35 - debug: Pack Images success, progress: 27%
2025-8-21 22:52:35 - debug: Compress image start..., progress: 26%
2025-8-21 22:52:35 - debug: Compress image start..., progress: 27%
2025-8-21 22:52:35 - group: Compress image...
2025-8-21 22:52:35 - debug: sort compress task {}
2025-8-21 22:52:35 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-21 22:52:35 - debug: No image need to compress
2025-8-21 22:52:35 - groupEnd: Compress image...
2025-8-21 22:52:35 - debug: Compress image success..., progress: 26%
2025-8-21 22:52:35 - debug: Compress image success..., progress: 27%
2025-8-21 22:52:35 - debug: Output asset in bundles start, progress: 26%
2025-8-21 22:52:35 - debug: Output asset in bundles start, progress: 27%
2025-8-21 22:52:35 - debug: Handle all json groups in bundle internal
2025-8-21 22:52:35 - debug: handle json group
2025-8-21 22:52:35 - debug: Handle all json groups in bundle resources
2025-8-21 22:52:35 - debug: handle json group
2025-8-21 22:52:35 - debug: Handle all json groups in bundle main
2025-8-21 22:52:35 - debug: handle json group
2025-8-21 22:52:36 - debug: Json group(05b737039) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(06585a170) compile success，json number: 6
2025-8-21 22:52:36 - debug: handle single json
2025-8-21 22:52:36 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(01959b579) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0a498a445) compile success，json number: 6
2025-8-21 22:52:36 - debug: handle single json
2025-8-21 22:52:36 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-21 22:52:36 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-21 22:52:36 - debug: handle single json
2025-8-21 22:52:36 - debug: Output asset in bundles success, progress: 26%
2025-8-21 22:52:36 - debug: Output asset in bundles success, progress: 27%
2025-8-21 22:52:36 - debug: Output asset in bundles start, progress: 26%
2025-8-21 22:52:36 - debug: Output asset in bundles start, progress: 27%
2025-8-21 22:52:36 - debug: compress config of bundle internal...
2025-8-21 22:52:36 - debug: compress config of bundle internal success
2025-8-21 22:52:36 - debug: compress config of bundle resources...
2025-8-21 22:52:36 - debug: compress config of bundle resources success
2025-8-21 22:52:36 - debug: compress config of bundle main...
2025-8-21 22:52:36 - debug: compress config of bundle main success
2025-8-21 22:52:36 - debug: output config of bundle internal
2025-8-21 22:52:36 - debug: output config of bundle internal success
2025-8-21 22:52:36 - debug: output config of bundle resources
2025-8-21 22:52:36 - debug: output config of bundle resources success
2025-8-21 22:52:36 - debug: output config of bundle main
2025-8-21 22:52:36 - debug: output config of bundle main success
2025-8-21 22:52:36 - debug: Output asset in bundles success, progress: 26%
2025-8-21 22:52:36 - debug: Output asset in bundles success, progress: 27%
2025-8-21 22:52:36 - debug: // ---- build task Build Assets ---- (443ms)
2025-8-21 22:52:36 - log: run build task Build Assets success in 443 ms√, progress: 31%
2025-8-21 22:52:36 - debug: [Build Memory track]: Build Assets start:210.06MB, end 213.26MB, increase: 3.20MB
2025-8-21 22:52:36 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-21 22:52:36 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-21 22:52:36 - debug: // ---- build task ios：onAfterBuildAssets ---- (72ms)
2025-8-21 22:52:36 - debug: ios:(onAfterBuildAssets) in 72 ms ✓, progress: 33%
2025-8-21 22:52:36 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-21 22:52:36 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 22:52:36 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (59ms)
2025-8-21 22:52:36 - log: run build task 整理部分构建选项内数据到 settings.json success in 59 ms√, progress: 34%
2025-8-21 22:52:36 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.99MB, end 211.74MB, increase: 766.22KB
2025-8-21 22:52:36 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-21 22:52:36 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-21 22:52:36 - debug: // ---- build task 填充脚本数据到 settings.json ---- (59ms)
2025-8-21 22:52:36 - log: run build task 填充脚本数据到 settings.json success in 59 ms√, progress: 36%
2025-8-21 22:52:36 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.77MB, end 212.52MB, increase: 768.00KB
2025-8-21 22:52:36 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-21 22:52:36 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 22:52:36 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (61ms)
2025-8-21 22:52:36 - log: run build task 整理部分构建选项内数据到 settings.json success in 61 ms√, progress: 38%
2025-8-21 22:52:36 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.55MB, end 213.42MB, increase: 896.30KB
2025-8-21 22:52:36 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-21 22:52:36 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-21 22:52:36 - debug: // ---- build task ios：onBeforeCompressSettings ---- (64ms)
2025-8-21 22:52:36 - debug: ios:(onBeforeCompressSettings) in 64 ms ✓, progress: 40%
2025-8-21 22:52:36 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-21 22:52:36 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-21 22:52:36 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (155ms)
2025-8-21 22:52:36 - debug: cocos-service:(onBeforeCompressSettings) in 155 ms ✓, progress: 41%
2025-8-21 22:52:36 - debug: 整理静态模板文件 start, progress: 41%
2025-8-21 22:52:36 - debug: // ---- build task 整理静态模板文件 ----
2025-8-21 22:52:36 - debug: // ---- build task 整理静态模板文件 ---- (61ms)
2025-8-21 22:52:36 - log: run build task 整理静态模板文件 success in 61 ms√, progress: 46%
2025-8-21 22:52:36 - debug: [Build Memory track]: 整理静态模板文件 start:212.29MB, end 210.72MB, increase: -1612.82KB
2025-8-21 22:52:36 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-21 22:52:36 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-21 22:52:36 - log: Checking template version...
2025-8-21 22:52:36 - log: Validating template consistency...
2025-8-21 22:52:36 - log: Validating platform source code directories...
2025-8-21 22:52:36 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-21 22:52:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.cpp
2025-8-21 22:52:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.m
2025-8-21 22:52:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/settings.gradle
2025-8-21 22:52:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-21 22:52:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-21 22:52:36 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-21 22:52:36 - debug: // ---- build task native：onAfterCompressSettings ---- (57ms)
2025-8-21 22:52:36 - debug: native:(onAfterCompressSettings) in 57 ms ✓, progress: 48%
2025-8-21 22:52:36 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-21 22:52:36 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-21 22:52:37 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (112ms)
2025-8-21 22:52:37 - debug: cocos-service:(onAfterCompressSettings) in 112 ms ✓, progress: 50%
2025-8-21 22:52:37 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-21 22:52:37 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-21 22:52:37 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (57ms)
2025-8-21 22:52:37 - log: run build task 给所有的资源加上 MD5 后缀 success in 57 ms√, progress: 60%
2025-8-21 22:52:37 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:212.79MB, end 213.54MB, increase: 767.98KB
2025-8-21 22:52:37 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-21 22:52:37 - debug: // ---- build task native：onAfterBuild ----
2025-8-21 22:52:37 - log: [xcode-select] /Applications/Xcode.app/Contents/Developer


2025-8-21 22:52:37 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-21 22:52:39 - log: [cmake] -- The CXX compiler identification is AppleClang 16.0.0.16000026


2025-8-21 22:52:39 - log: [cmake] -- Detecting CXX compiler ABI info


2025-8-21 22:52:40 - log: [cmake] -- Detecting CXX compiler ABI info - done


2025-8-21 22:52:40 - log: [cmake] -- Check for working CXX compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ - skipped


2025-8-21 22:52:40 - log: [cmake] -- Detecting CXX compile features


2025-8-21 22:52:40 - log: [cmake] -- Detecting CXX compile features - done


2025-8-21 22:52:41 - log: [cmake] -- The C compiler identification is AppleClang 16.0.0.16000026


2025-8-21 22:52:41 - log: [cmake] -- The ASM compiler identification is Clang with GNU-like command-line


2025-8-21 22:52:41 - log: [cmake] -- Found assembler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang


2025-8-21 22:52:41 - log: [cmake] -- Detecting C compiler ABI info


2025-8-21 22:52:41 - log: [cmake] -- Detecting C compiler ABI info - done


2025-8-21 22:52:41 - log: [cmake] -- Check for working C compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang - skipped


2025-8-21 22:52:41 - log: [cmake] -- Detecting C compile features


2025-8-21 22:52:41 - log: [cmake] -- Detecting C compile features - done


2025-8-21 22:52:41 - log: [cmake] -- platform: iOS


2025-8-21 22:52:41 - log: [cmake] -- Ignore NO_WERROR


2025-8-21 22:52:41 - log: [cmake] -- OPTION BUILTIN_COCOS_X_PATH:	
-- OPTION USE_BUILTIN_EXTERNAL:	OFF
-- OPTION USE_MODULES:	OFF
-- OPTION CC_USE_METAL:	ON


2025-8-21 22:52:41 - log: [cmake] -- OPTION CC_USE_GLES3:	OFF
-- OPTION CC_USE_GLES2:	OFF
-- OPTION CC_USE_VULKAN:	OFF
-- OPTION CC_DEBUG_FORCE:	OFF
-- OPTION USE_SE_V8:	ON
-- OPTION USE_SE_JSVM:	OFF
-- OPTION USE_V8_DEBUGGER:	ON
-- OPTION USE_V8_DEBUGGER_FORCE:	OFF
-- OPTION USE_SE_SM:	OFF
-- OPTION USE_SOCKET:	OFF
-- OPTION USE_AUDIO:	ON
-- OPTION USE_EDIT_BOX:	ON
-- OPTION USE_VIDEO:	ON
-- OPTION USE_WEBVIEW:	ON
-- OPTION USE_MIDDLEWARE:	ON
-- OPTION USE_DRAGONBONES:	ON
-- OPTION USE_SPINE:	ON
-- OPTION USE_SPINE_3_8:	ON
-- OPTION USE_SPINE_4_2:	OFF


2025-8-21 22:52:41 - log: [cmake] -- OPTION USE_WEBSOCKET_SERVER:	OFF
-- OPTION USE_PHYSICS_PHYSX:	OFF
-- OPTION USE_JOB_SYSTEM_TBB:	OFF
-- OPTION USE_JOB_SYSTEM_TASKFLOW:	OFF
-- OPTION USE_XR:	OFF
-- OPTION USE_SERVER_MODE:	OFF
-- OPTION USE_AR_MODULE:	OFF


2025-8-21 22:52:41 - log: [cmake] -- OPTION USE_AR_AUTO:	OFF
-- OPTION USE_AR_CORE:	OFF
-- OPTION USE_AR_ENGINE:	OFF
-- OPTION USE_CCACHE:	
-- OPTION CCACHE_EXECUTABLE:	CCACHE_EXECUTABLE-NOTFOUND
-- OPTION NODE_EXECUTABLE:	/opt/homebrew/Cellar/node/24.5.0/bin/node
-- OPTION NET_MODE:	0
-- OPTION USE_REMOTE_LOG:	OFF
-- OPTION USE_BOX2D_JSB:	OFF


2025-8-21 22:52:41 - log: [cmake] -- platform path: 


2025-8-21 22:52:42 - log: [cmake] -- Using Xcode 15 or newer, adding extra link flags: -Wl,-ld_classic.


2025-8-21 22:52:42 - log: [cmake] -- Try generating /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Pre-AutoLoadPlulgins.cmake


2025-8-21 22:52:42 - log: [cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js


2025-8-21 22:52:42 - log: [cmake] [searching plugins] no plugins found!


2025-8-21 22:52:42 - log: [cmake] -- Searching hook files Pre*.cmake or *Pre.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-21 22:52:42 - log: [cmake] -- ::Loading Pre-service.cmake


2025-8-21 22:52:42 - log: [cmake] -- No plugins are loaded!


2025-8-21 22:52:42 - log: [cmake] -- Searching hook files Post*.cmake or *Post.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios
-- ::Loading Post-service.cmake


2025-8-21 22:52:42 - log: [cmake] -- Configuring done


2025-8-21 22:52:42 - log: [cmake] -- Generating done


2025-8-21 22:52:42 - log: [cmake-warn] CMake Warning:
  Manually-specified variables were not used by the project:

    LAUNCH_TYPE




2025-8-21 22:52:42 - log: [cmake] -- Build files have been written to: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj


2025-8-21 22:52:42 - debug: // ---- build task native：onAfterBuild ---- (5123ms)
2025-8-21 22:52:42 - debug: native:(onAfterBuild) in 5123 ms ✓, progress: 62%
2025-8-21 22:52:42 - debug: ios:(onAfterBuild) start..., progress: 62%
2025-8-21 22:52:42 - debug: // ---- build task ios：onAfterBuild ----
2025-8-21 22:52:42 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 22:52:42 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png
2025-8-21 22:52:42 - debug: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 22:52:42 - debug: // ---- build task ios：onAfterBuild ---- (62ms)
2025-8-21 22:52:42 - debug: ios:(onAfterBuild) in 62 ms ✓, progress: 64%
2025-8-21 22:52:42 - debug: cocos-service:(onAfterBuild) start..., progress: 64%
2025-8-21 22:52:42 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-21 22:52:42 - debug: // ---- build task cocos-service：onAfterBuild ---- (222ms)
2025-8-21 22:52:42 - debug: cocos-service:(onAfterBuild) in 222 ms ✓, progress: 65%
2025-8-21 22:52:42 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 65%
2025-8-21 22:52:42 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-21 22:52:42 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (79ms)
2025-8-21 22:52:42 - debug: adsense-h5g-plugin:(onAfterBuild) in 79 ms ✓, progress: 67%
2025-8-21 22:52:42 - log: Asset DB is resume!
2025-8-21 22:52:42 - debug: builder:build-project-total (11005ms)
2025-8-21 22:52:42 - debug: build success in 11005!
2025-8-21 22:52:42 - debug: [Build Memory track]: builder:build-project-total start:225.24MB, end 210.60MB, increase: -15000.29KB
2025-8-21 22:52:42 - debug: ================================ build Task (ios) Finished in (11 s)ms ================================
2025-8-21 23:07:07 - debug: =================================== build Task (ios) Start ================================
2025-8-21 23:07:07 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"1563d039-d898-4d8f-9415-9f1da853b8a3","outputName":"ios","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"350F82HeBJpZKzTD","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-21-2025 22-37.log"}
2025-8-21 23:07:07 - debug: Build with Cocos Creator 3.8.6
2025-8-21 23:07:07 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-21 23:07:07 - debug: // ---- build task native：onBeforeBuild ----
2025-8-21 23:07:08 - debug: // ---- build task native：onBeforeBuild ---- (46ms)
2025-8-21 23:07:08 - debug: native:(onBeforeBuild) in 46 ms ✓, progress: 2%
2025-8-21 23:07:08 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-21 23:07:08 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-21 23:07:08 - debug: // ---- build task cocos-service：onBeforeBuild ---- (282ms)
2025-8-21 23:07:08 - debug: cocos-service:(onBeforeBuild) in 282 ms ✓, progress: 4%
2025-8-21 23:07:08 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-21 23:07:08 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-21 23:07:08 - debug: // ---- build task scene：onBeforeBuild ---- (59ms)
2025-8-21 23:07:08 - debug: scene:(onBeforeBuild) in 59 ms ✓, progress: 5%
2025-8-21 23:07:08 - debug: Start lock asset db..., progress: 5%
2025-8-21 23:07:08 - log: Asset DB is paused with build!
2025-8-21 23:07:08 - debug: Query all assets info in project
2025-8-21 23:07:08 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-21 23:07:08 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-21 23:07:08 - debug: // ---- build task native：onAfterInit ----
2025-8-21 23:07:08 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-21 23:07:08 - debug: // ---- build task native：onAfterInit ---- (31ms)
2025-8-21 23:07:08 - debug: native:(onAfterInit) in 31 ms ✓, progress: 7%
2025-8-21 23:07:08 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-21 23:07:08 - debug: // ---- build task ios：onAfterInit ----
2025-8-21 23:07:08 - debug: // ---- build task ios：onAfterInit ---- (49ms)
2025-8-21 23:07:08 - debug: ios:(onAfterInit) in 49 ms ✓, progress: 9%
2025-8-21 23:07:08 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-21 23:07:08 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-21 23:07:08 - debug: // ---- build task cocos-service：onAfterInit ---- (168ms)
2025-8-21 23:07:08 - debug: cocos-service:(onAfterInit) in 168 ms ✓, progress: 11%
2025-8-21 23:07:08 - debug: Skip compress image, progress: 0%
2025-8-21 23:07:08 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-21 23:07:08 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-21 23:07:08 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-21 23:07:08 - debug: [adsense-h5g-plugin] remove script success
2025-8-21 23:07:08 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (59ms)
2025-8-21 23:07:08 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 59 ms ✓, progress: 11%
2025-8-21 23:07:08 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 59 ms ✓, progress: 7%
2025-8-21 23:07:08 - debug: Init all bundles start..., progress: 11%
2025-8-21 23:07:08 - debug: Init all bundles start..., progress: 7%
2025-8-21 23:07:08 - debug: Num of bundles: 3..., progress: 11%
2025-8-21 23:07:08 - debug: Num of bundles: 3..., progress: 7%
2025-8-21 23:07:08 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-21 23:07:08 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-21 23:07:08 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-21 23:07:08 - debug: // ---- build task native：onAfterBundleInit ---- (113ms)
2025-8-21 23:07:08 - debug: native:(onAfterBundleInit) in 113 ms ✓, progress: 11%
2025-8-21 23:07:08 - debug: native:(onAfterBundleInit) in 113 ms ✓, progress: 13%
2025-8-21 23:07:08 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-21 23:07:08 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-21 23:07:08 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-21 23:07:08 - debug: // ---- build task ios：onAfterBundleInit ---- (64ms)
2025-8-21 23:07:08 - debug: ios:(onAfterBundleInit) in 64 ms ✓, progress: 11%
2025-8-21 23:07:08 - debug: ios:(onAfterBundleInit) in 64 ms ✓, progress: 20%
2025-8-21 23:07:08 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-21 23:07:08 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 23:07:08 - debug: Init bundle root assets start..., progress: 11%
2025-8-21 23:07:08 - debug: Init bundle root assets start..., progress: 20%
2025-8-21 23:07:09 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-21 23:07:09 - debug:   Number of all scenes: 3
2025-8-21 23:07:09 - debug:   Number of all scripts: 28
2025-8-21 23:07:09 - debug:   Number of other assets: 609
2025-8-21 23:07:09 - debug: Init bundle root assets success..., progress: 11%
2025-8-21 23:07:09 - debug: Init bundle root assets success..., progress: 20%
2025-8-21 23:07:09 - debug: reload all scripts.
2025-8-21 23:07:09 - debug: [[Executor]] reload before lock
2025-8-21 23:07:09 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-21 23:07:09 - groupCollapsed: Invalidate all modules
2025-8-21 23:07:09 - debug: Unregister BuiltinPipelineSettings
2025-8-21 23:07:09 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-21 23:07:09 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-21 23:07:09 - debug: Unregister DebugViewRuntimeControl
2025-8-21 23:07:09 - debug: Unregister CameraFollow
2025-8-21 23:07:09 - debug: Unregister Bullet
2025-8-21 23:07:09 - debug: Unregister AIPlayer
2025-8-21 23:07:09 - debug: Unregister player
2025-8-21 23:07:09 - debug: Unregister PlayerManager
2025-8-21 23:07:09 - debug: Unregister SceneTransition
2025-8-21 23:07:09 - debug: Unregister SoundManager
2025-8-21 23:07:09 - debug: Unregister PaintManager
2025-8-21 23:07:09 - debug: Unregister GameOverPanel
2025-8-21 23:07:09 - debug: Unregister GameHUD
2025-8-21 23:07:09 - debug: Unregister GameManager
2025-8-21 23:07:09 - debug: Unregister AIController
2025-8-21 23:07:09 - debug: Unregister CarProperties
2025-8-21 23:07:09 - debug: Unregister CarPropertyDisplay
2025-8-21 23:07:09 - debug: Unregister HealthBarUI
2025-8-21 23:07:09 - debug: Unregister MainMenuController
2025-8-21 23:07:09 - debug: Unregister PaintSpot
2025-8-21 23:07:09 - debug: Unregister PausePanel
2025-8-21 23:07:09 - debug: Unregister PlayerInfoUI
2025-8-21 23:07:09 - debug: Unregister PurchasePanel
2025-8-21 23:07:09 - debug: Unregister SelectManager
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js'
2025-8-21 23:07:09 - debug: Invalidating 'pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js'
2025-8-21 23:07:09 - groupEnd: Invalidate all modules
2025-8-21 23:07:09 - groupCollapsed: Imports all modules
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register CameraFollow
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register Bullet
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register AIPlayer
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register player
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register PlayerManager
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register SceneTransition
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register SoundManager
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register PaintManager
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register GameOverPanel
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register GameHUD
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register GameManager
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register AIController
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register CarProperties
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register HealthBarUI
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register MainMenuController
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register PaintSpot
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register PausePanel
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register PlayerInfoUI
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register PurchasePanel
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Register SelectManager
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-21 23:07:09 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-21 23:07:09 - groupEnd: Imports all modules
2025-8-21 23:07:09 - debug: [[Executor]] after unlock
2025-8-21 23:07:09 - debug: Incremental keys: 
2025-8-21 23:07:09 - debug: Init bundle share assets start..., progress: 11%
2025-8-21 23:07:09 - debug: Init bundle share assets start..., progress: 20%
2025-8-21 23:07:09 - debug: Init bundle share assets success..., progress: 11%
2025-8-21 23:07:09 - debug: Init bundle share assets success..., progress: 20%
2025-8-21 23:07:09 - debug: handle json group in bundle internal
2025-8-21 23:07:09 - debug: handle json group in bundle internal success
2025-8-21 23:07:09 - debug: handle json group in bundle resources
2025-8-21 23:07:09 - debug: handle json group in bundle main
2025-8-21 23:07:09 - debug: init image compress task 0 in bundle internal
2025-8-21 23:07:09 - debug: handle json group in bundle main success
2025-8-21 23:07:09 - debug: init image compress task 0 in bundle main
2025-8-21 23:07:09 - debug: handle json group in bundle resources success
2025-8-21 23:07:09 - debug: init image compress task 0 in bundle resources
2025-8-21 23:07:09 - debug: // ---- build task 查询 Asset Bundle ---- (242ms)
2025-8-21 23:07:09 - log: run build task 查询 Asset Bundle success in 242 ms√, progress: 16%
2025-8-21 23:07:09 - debug: [Build Memory track]: 查询 Asset Bundle start:222.62MB, end 223.78MB, increase: 1.15MB
2025-8-21 23:07:09 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-21 23:07:09 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-21 23:07:09 - debug: // ---- build task 查询 Asset Bundle ---- (61ms)
2025-8-21 23:07:09 - log: run build task 查询 Asset Bundle success in 61 ms√, progress: 21%
2025-8-21 23:07:09 - debug: [Build Memory track]: 查询 Asset Bundle start:223.80MB, end 224.66MB, increase: 872.58KB
2025-8-21 23:07:09 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-21 23:07:09 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-21 23:07:09 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-21 23:07:09 - debug: // ---- build task native：onAfterBundleDataTask ---- (60ms)
2025-8-21 23:07:09 - debug: native:(onAfterBundleDataTask) in 60 ms ✓, progress: 21%
2025-8-21 23:07:09 - debug: native:(onAfterBundleDataTask) in 60 ms ✓, progress: 27%
2025-8-21 23:07:09 - debug: 打包脚本 start, progress: 21%
2025-8-21 23:07:09 - debug: // ---- build task 打包脚本 ----
2025-8-21 23:07:09 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 23:07:09 - log: [build-script]enter sub process 79248, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-21 23:07:10 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-21 23:07:10 - debug: excute-script over with build-script 862ms
2025-8-21 23:07:10 - debug: Generate systemJs..., progress: 21%
2025-8-21 23:07:10 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 23:07:10 - debug: excute-script over with build-script 811ms
2025-8-21 23:07:10 - debug: 构建项目脚本 start..., progress: 21%
2025-8-21 23:07:10 - debug: Build script in bundle start, progress: 21%
2025-8-21 23:07:10 - debug: Build script in bundle start, progress: 27%
2025-8-21 23:07:11 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-21 23:07:11 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts


2025-8-21 23:07:11 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-21 23:07:12 - debug: excute-script over with build-script 1081ms
2025-8-21 23:07:12 - debug: Copy externalScripts success!
2025-8-21 23:07:12 - debug: Build script in bundle success, progress: 21%
2025-8-21 23:07:12 - debug: Build script in bundle success, progress: 27%
2025-8-21 23:07:12 - debug: 构建项目脚本 in (1204 ms) √, progress: 21%
2025-8-21 23:07:12 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-21 23:07:12 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-21 23:07:12 - debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf)}
2025-8-21 23:07:12 - debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-21 23:07:12 - debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "src/cocos-js",
  "platform": "IOS",
  "useCache": true,
  "nativeCodeBundleMode": "asmjs",
  "wasmCompressionMode": false,
  "output": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/cocos-js",
  "targets": "chrome 80",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-21 23:07:12 - debug: 构建引擎脚本 in (71 ms) √, progress: 21%
2025-8-21 23:07:12 - debug: Copy plugin script ..., progress: 21%
2025-8-21 23:07:12 - debug: Generate import-map..., progress: 21%
2025-8-21 23:07:12 - debug: // ---- build task 打包脚本 ---- (3032ms)
2025-8-21 23:07:12 - log: run build task 打包脚本 success in 3 s√, progress: 26%
2025-8-21 23:07:12 - debug: [Build Memory track]: 打包脚本 start:224.03MB, end 224.23MB, increase: 204.27KB
2025-8-21 23:07:12 - debug: Build Assets start, progress: 26%
2025-8-21 23:07:12 - debug: // ---- build task Build Assets ----
2025-8-21 23:07:12 - debug: Build bundles..., progress: 26%
2025-8-21 23:07:12 - debug: Pack Images start, progress: 26%
2025-8-21 23:07:12 - debug: Pack Images start, progress: 27%
2025-8-21 23:07:12 - debug: builder:pack-auto-atlas-image (115ms)
2025-8-21 23:07:12 - debug: Pack Images success, progress: 26%
2025-8-21 23:07:12 - debug: Pack Images success, progress: 27%
2025-8-21 23:07:12 - debug: Compress image start..., progress: 26%
2025-8-21 23:07:12 - debug: Compress image start..., progress: 27%
2025-8-21 23:07:12 - group: Compress image...
2025-8-21 23:07:12 - debug: sort compress task {}
2025-8-21 23:07:12 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-21 23:07:12 - debug: No image need to compress
2025-8-21 23:07:12 - groupEnd: Compress image...
2025-8-21 23:07:12 - debug: Compress image success..., progress: 26%
2025-8-21 23:07:12 - debug: Compress image success..., progress: 27%
2025-8-21 23:07:12 - debug: Output asset in bundles start, progress: 26%
2025-8-21 23:07:12 - debug: Output asset in bundles start, progress: 27%
2025-8-21 23:07:12 - debug: Handle all json groups in bundle internal
2025-8-21 23:07:12 - debug: handle json group
2025-8-21 23:07:12 - debug: Handle all json groups in bundle resources
2025-8-21 23:07:12 - debug: handle json group
2025-8-21 23:07:12 - debug: Handle all json groups in bundle main
2025-8-21 23:07:12 - debug: handle json group
2025-8-21 23:07:12 - debug: Json group(05b737039) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(06585a170) compile success，json number: 6
2025-8-21 23:07:12 - debug: handle single json
2025-8-21 23:07:12 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(01959b579) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0a498a445) compile success，json number: 6
2025-8-21 23:07:12 - debug: handle single json
2025-8-21 23:07:12 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-21 23:07:12 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-21 23:07:12 - debug: handle single json
2025-8-21 23:07:12 - debug: Output asset in bundles success, progress: 26%
2025-8-21 23:07:12 - debug: Output asset in bundles success, progress: 27%
2025-8-21 23:07:12 - debug: Output asset in bundles start, progress: 26%
2025-8-21 23:07:12 - debug: Output asset in bundles start, progress: 27%
2025-8-21 23:07:12 - debug: compress config of bundle internal...
2025-8-21 23:07:12 - debug: compress config of bundle internal success
2025-8-21 23:07:12 - debug: compress config of bundle resources...
2025-8-21 23:07:12 - debug: compress config of bundle resources success
2025-8-21 23:07:12 - debug: compress config of bundle main...
2025-8-21 23:07:12 - debug: compress config of bundle main success
2025-8-21 23:07:12 - debug: output config of bundle internal
2025-8-21 23:07:12 - debug: output config of bundle internal success
2025-8-21 23:07:12 - debug: output config of bundle resources
2025-8-21 23:07:12 - debug: output config of bundle resources success
2025-8-21 23:07:12 - debug: output config of bundle main
2025-8-21 23:07:12 - debug: output config of bundle main success
2025-8-21 23:07:12 - debug: Output asset in bundles success, progress: 26%
2025-8-21 23:07:12 - debug: Output asset in bundles success, progress: 27%
2025-8-21 23:07:12 - debug: // ---- build task Build Assets ---- (497ms)
2025-8-21 23:07:12 - log: run build task Build Assets success in 497 ms√, progress: 31%
2025-8-21 23:07:12 - debug: [Build Memory track]: Build Assets start:224.25MB, end 226.56MB, increase: 2.31MB
2025-8-21 23:07:12 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-21 23:07:12 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-21 23:07:12 - debug: // ---- build task ios：onAfterBuildAssets ---- (56ms)
2025-8-21 23:07:12 - debug: ios:(onAfterBuildAssets) in 56 ms ✓, progress: 33%
2025-8-21 23:07:12 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-21 23:07:12 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 23:07:12 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (63ms)
2025-8-21 23:07:12 - log: run build task 整理部分构建选项内数据到 settings.json success in 63 ms√, progress: 34%
2025-8-21 23:07:12 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:227.41MB, end 228.16MB, increase: 766.29KB
2025-8-21 23:07:12 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-21 23:07:12 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-21 23:07:13 - debug: // ---- build task 填充脚本数据到 settings.json ---- (59ms)
2025-8-21 23:07:13 - log: run build task 填充脚本数据到 settings.json success in 59 ms√, progress: 36%
2025-8-21 23:07:13 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:228.19MB, end 225.81MB, increase: -2435.71KB
2025-8-21 23:07:13 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-21 23:07:13 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-21 23:07:13 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (93ms)
2025-8-21 23:07:13 - log: run build task 整理部分构建选项内数据到 settings.json success in 93 ms√, progress: 38%
2025-8-21 23:07:13 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:225.84MB, end 227.05MB, increase: 1.22MB
2025-8-21 23:07:13 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-21 23:07:13 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-21 23:07:13 - debug: // ---- build task ios：onBeforeCompressSettings ---- (61ms)
2025-8-21 23:07:13 - debug: ios:(onBeforeCompressSettings) in 61 ms ✓, progress: 40%
2025-8-21 23:07:13 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-21 23:07:13 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-21 23:07:13 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (119ms)
2025-8-21 23:07:13 - debug: cocos-service:(onBeforeCompressSettings) in 119 ms ✓, progress: 41%
2025-8-21 23:07:13 - debug: 整理静态模板文件 start, progress: 41%
2025-8-21 23:07:13 - debug: // ---- build task 整理静态模板文件 ----
2025-8-21 23:07:13 - debug: // ---- build task 整理静态模板文件 ---- (64ms)
2025-8-21 23:07:13 - log: run build task 整理静态模板文件 success in 64 ms√, progress: 46%
2025-8-21 23:07:13 - debug: [Build Memory track]: 整理静态模板文件 start:225.54MB, end 227.83MB, increase: 2.29MB
2025-8-21 23:07:13 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-21 23:07:13 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-21 23:07:13 - log: Checking template version...
2025-8-21 23:07:13 - log: Validating template consistency...
2025-8-21 23:07:13 - log: Validating platform source code directories...
2025-8-21 23:07:13 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-21 23:07:13 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.cpp
2025-8-21 23:07:13 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.m
2025-8-21 23:07:13 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/settings.gradle
2025-8-21 23:07:13 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-21 23:07:13 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-21 23:07:13 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-21 23:07:13 - debug: // ---- build task native：onAfterCompressSettings ---- (73ms)
2025-8-21 23:07:13 - debug: native:(onAfterCompressSettings) in 73 ms ✓, progress: 48%
2025-8-21 23:07:13 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-21 23:07:13 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-21 23:07:13 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (146ms)
2025-8-21 23:07:13 - debug: cocos-service:(onAfterCompressSettings) in 146 ms ✓, progress: 50%
2025-8-21 23:07:13 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-21 23:07:13 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-21 23:07:13 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (56ms)
2025-8-21 23:07:13 - log: run build task 给所有的资源加上 MD5 后缀 success in 56 ms√, progress: 60%
2025-8-21 23:07:13 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:210.86MB, end 211.61MB, increase: 767.88KB
2025-8-21 23:07:13 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-21 23:07:13 - debug: // ---- build task native：onAfterBuild ----
2025-8-21 23:07:13 - log: [xcode-select] /Applications/Xcode.app/Contents/Developer


2025-8-21 23:07:13 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-21 23:07:16 - log: [cmake] -- The CXX compiler identification is AppleClang 16.0.0.16000026


2025-8-21 23:07:16 - log: [cmake] -- Detecting CXX compiler ABI info


2025-8-21 23:07:16 - log: [cmake] -- Detecting CXX compiler ABI info - done


2025-8-21 23:07:16 - log: [cmake] -- Check for working CXX compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ - skipped


2025-8-21 23:07:16 - log: [cmake] -- Detecting CXX compile features


2025-8-21 23:07:16 - log: [cmake] -- Detecting CXX compile features - done


2025-8-21 23:07:17 - log: [cmake] -- The C compiler identification is AppleClang 16.0.0.16000026


2025-8-21 23:07:17 - log: [cmake] -- The ASM compiler identification is Clang with GNU-like command-line


2025-8-21 23:07:17 - log: [cmake] -- Found assembler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang


2025-8-21 23:07:17 - log: [cmake] -- Detecting C compiler ABI info


2025-8-21 23:07:18 - log: [cmake] -- Detecting C compiler ABI info - done


2025-8-21 23:07:18 - log: [cmake] -- Check for working C compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang - skipped


2025-8-21 23:07:18 - log: [cmake] -- Detecting C compile features


2025-8-21 23:07:18 - log: [cmake] -- Detecting C compile features - done


2025-8-21 23:07:18 - log: [cmake] -- platform: iOS


2025-8-21 23:07:18 - log: [cmake] -- Ignore NO_WERROR


2025-8-21 23:07:18 - log: [cmake] -- OPTION BUILTIN_COCOS_X_PATH:	
-- OPTION USE_BUILTIN_EXTERNAL:	OFF
-- OPTION USE_MODULES:	OFF


2025-8-21 23:07:18 - log: [cmake] -- OPTION CC_USE_METAL:	ON
-- OPTION CC_USE_GLES3:	OFF
-- OPTION CC_USE_GLES2:	OFF
-- OPTION CC_USE_VULKAN:	OFF
-- OPTION CC_DEBUG_FORCE:	OFF
-- OPTION USE_SE_V8:	ON
-- OPTION USE_SE_JSVM:	OFF
-- OPTION USE_V8_DEBUGGER:	ON
-- OPTION USE_V8_DEBUGGER_FORCE:	OFF
-- OPTION USE_SE_SM:	OFF
-- OPTION USE_SOCKET:	OFF
-- OPTION USE_AUDIO:	ON
-- OPTION USE_EDIT_BOX:	ON
-- OPTION USE_VIDEO:	ON
-- OPTION USE_WEBVIEW:	ON
-- OPTION USE_MIDDLEWARE:	ON
-- OPTION USE_DRAGONBONES:	ON
-- OPTION USE_SPINE:	ON
-- OPTION USE_SPINE_3_8:	ON
-- OPTION USE_SPINE_4_2:	OFF
-- OPTION USE_WEBSOCKET_SERVER:	OFF
-- OPTION USE_PHYSICS_PHYSX:	OFF
-- OPTION USE_JOB_SYSTEM_TBB:	OFF
-- OPTION USE_JOB_SYSTEM_TASKFLOW:	OFF
-- OPTION USE_XR:	OFF
-- OPTION USE_SERVER_MODE:	OFF
-- OPTION USE_AR_MODULE:	OFF


2025-8-21 23:07:18 - log: [cmake] -- OPTION USE_AR_AUTO:	OFF
-- OPTION USE_AR_CORE:	OFF
-- OPTION USE_AR_ENGINE:	OFF
-- OPTION USE_CCACHE:	
-- OPTION CCACHE_EXECUTABLE:	CCACHE_EXECUTABLE-NOTFOUND
-- OPTION NODE_EXECUTABLE:	/opt/homebrew/Cellar/node/24.5.0/bin/node
-- OPTION NET_MODE:	0
-- OPTION USE_REMOTE_LOG:	OFF
-- OPTION USE_BOX2D_JSB:	OFF


2025-8-21 23:07:18 - log: [cmake] -- platform path: 


2025-8-21 23:07:18 - log: [cmake] -- Using Xcode 15 or newer, adding extra link flags: -Wl,-ld_classic.


2025-8-21 23:07:18 - log: [cmake] -- Try generating /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Pre-AutoLoadPlulgins.cmake


2025-8-21 23:07:18 - log: [cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js


2025-8-21 23:07:18 - log: [cmake] [searching plugins] no plugins found!


2025-8-21 23:07:18 - log: [cmake] -- Searching hook files Pre*.cmake or *Pre.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-21 23:07:18 - log: [cmake] -- ::Loading Pre-service.cmake


2025-8-21 23:07:18 - log: [cmake] -- No plugins are loaded!


2025-8-21 23:07:18 - log: [cmake] -- Searching hook files Post*.cmake or *Post.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios
-- ::Loading Post-service.cmake


2025-8-21 23:07:18 - log: [cmake] -- Configuring done


2025-8-21 23:07:18 - log: [cmake] -- Generating done


2025-8-21 23:07:18 - log: [cmake-warn] CMake Warning:
  Manually-specified variables were not used by the project:

    LAUNCH_TYPE




2025-8-21 23:07:18 - log: [cmake] -- Build files have been written to: /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj


2025-8-21 23:07:18 - debug: // ---- build task native：onAfterBuild ---- (5337ms)
2025-8-21 23:07:18 - debug: native:(onAfterBuild) in 5337 ms ✓, progress: 62%
2025-8-21 23:07:18 - debug: ios:(onAfterBuild) start..., progress: 62%
2025-8-21 23:07:18 - debug: // ---- build task ios：onAfterBuild ----
2025-8-21 23:07:19 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 23:07:19 - log: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png
2025-8-21 23:07:19 - debug: Generate splash to:
/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-21 23:07:19 - debug: // ---- build task ios：onAfterBuild ---- (71ms)
2025-8-21 23:07:19 - debug: ios:(onAfterBuild) in 71 ms ✓, progress: 64%
2025-8-21 23:07:19 - debug: cocos-service:(onAfterBuild) start..., progress: 64%
2025-8-21 23:07:19 - debug: // ---- build task cocos-service：onAfterBuild ----
2025-8-21 23:07:19 - debug: // ---- build task cocos-service：onAfterBuild ---- (256ms)
2025-8-21 23:07:19 - debug: cocos-service:(onAfterBuild) in 256 ms ✓, progress: 65%
2025-8-21 23:07:19 - debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 65%
2025-8-21 23:07:19 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-8-21 23:07:19 - debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (65ms)
2025-8-21 23:07:19 - debug: adsense-h5g-plugin:(onAfterBuild) in 65 ms ✓, progress: 67%
2025-8-21 23:07:19 - log: Asset DB is resume!
2025-8-21 23:07:19 - debug: builder:build-project-total (11416ms)
2025-8-21 23:07:19 - debug: build success in 11416!
2025-8-21 23:07:19 - debug: [Build Memory track]: builder:build-project-total start:222.21MB, end 212.75MB, increase: -9684.18KB
2025-8-21 23:07:19 - debug: ================================ build Task (ios) Finished in (11 s)ms ================================
