2025-8-20 22:38:26 - debug: =================================== build Task (ios-001) Start ================================
2025-8-20 22:38:26 - debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"/Users/<USER>/projects","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"091c5c0e-b72a-4cad-ab68-635bc57ff236","outputName":"SuperSplash","taskName":"ios-001","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"e60SAlm2BAoJwuJd","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-20-2025 22-38.log"}
2025-8-20 22:38:26 - debug: Build with Cocos Creator 3.8.6
2025-8-20 22:38:26 - debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-20 22:38:26 - debug: // ---- build task native：onBeforeBuild ----
2025-8-20 22:38:26 - debug: // ---- build task native：onBeforeBuild ---- (19ms)
2025-8-20 22:38:26 - debug: native:(onBeforeBuild) in 19 ms ✓, progress: 2%
2025-8-20 22:38:26 - debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-20 22:38:26 - debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-20 22:38:26 - debug: // ---- build task cocos-service：onBeforeBuild ---- (195ms)
2025-8-20 22:38:26 - debug: cocos-service:(onBeforeBuild) in 195 ms ✓, progress: 4%
2025-8-20 22:38:26 - debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-20 22:38:26 - debug: // ---- build task scene：onBeforeBuild ----
2025-8-20 22:38:26 - debug: // ---- build task scene：onBeforeBuild ---- (35ms)
2025-8-20 22:38:26 - debug: scene:(onBeforeBuild) in 35 ms ✓, progress: 5%
2025-8-20 22:38:26 - debug: Start lock asset db..., progress: 5%
2025-8-20 22:38:26 - log: Asset DB is paused with build!
2025-8-20 22:38:26 - debug: Query all assets info in project
2025-8-20 22:38:26 - debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-20 22:38:26 - debug: native:(onAfterInit) start..., progress: 5%
2025-8-20 22:38:26 - debug: // ---- build task native：onAfterInit ----
2025-8-20 22:38:26 - debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-20 22:38:26 - debug: // ---- build task native：onAfterInit ---- (18ms)
2025-8-20 22:38:26 - debug: native:(onAfterInit) in 18 ms ✓, progress: 7%
2025-8-20 22:38:26 - debug: ios:(onAfterInit) start..., progress: 7%
2025-8-20 22:38:26 - debug: // ---- build task ios：onAfterInit ----
2025-8-20 22:38:26 - debug: // ---- build task ios：onAfterInit ---- (21ms)
2025-8-20 22:38:26 - debug: ios:(onAfterInit) in 21 ms ✓, progress: 9%
2025-8-20 22:38:26 - debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-20 22:38:26 - debug: // ---- build task cocos-service：onAfterInit ----
2025-8-20 22:38:26 - debug: // ---- build task cocos-service：onAfterInit ---- (87ms)
2025-8-20 22:38:26 - debug: cocos-service:(onAfterInit) in 87 ms ✓, progress: 11%
2025-8-20 22:38:26 - debug: Skip compress image, progress: 0%
2025-8-20 22:38:26 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-20 22:38:26 - debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-20 22:38:26 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-20 22:38:26 - debug: [adsense-h5g-plugin] remove script success
2025-8-20 22:38:26 - debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (26ms)
2025-8-20 22:38:26 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 26 ms ✓, progress: 11%
2025-8-20 22:38:26 - debug: adsense-h5g-plugin:(onBeforeBundleInit) in 26 ms ✓, progress: 7%
2025-8-20 22:38:26 - debug: Init all bundles start..., progress: 11%
2025-8-20 22:38:26 - debug: Init all bundles start..., progress: 7%
2025-8-20 22:38:26 - debug: Num of bundles: 3..., progress: 11%
2025-8-20 22:38:26 - debug: Num of bundles: 3..., progress: 7%
2025-8-20 22:38:26 - debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-20 22:38:26 - debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-20 22:38:26 - debug: // ---- build task native：onAfterBundleInit ----
2025-8-20 22:38:26 - debug: // ---- build task native：onAfterBundleInit ---- (43ms)
2025-8-20 22:38:26 - debug: native:(onAfterBundleInit) in 43 ms ✓, progress: 11%
2025-8-20 22:38:26 - debug: native:(onAfterBundleInit) in 43 ms ✓, progress: 13%
2025-8-20 22:38:26 - debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-20 22:38:26 - debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-20 22:38:26 - debug: // ---- build task ios：onAfterBundleInit ----
2025-8-20 22:38:26 - debug: // ---- build task ios：onAfterBundleInit ---- (22ms)
2025-8-20 22:38:26 - debug: ios:(onAfterBundleInit) in 22 ms ✓, progress: 11%
2025-8-20 22:38:26 - debug: ios:(onAfterBundleInit) in 22 ms ✓, progress: 20%
2025-8-20 22:38:26 - debug: 查询 Asset Bundle start, progress: 11%
2025-8-20 22:38:26 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-20 22:38:26 - debug: Init bundle root assets start..., progress: 11%
2025-8-20 22:38:26 - debug: Init bundle root assets start..., progress: 20%
2025-8-20 22:38:26 - debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-20 22:38:26 - debug:   Number of all scenes: 3
2025-8-20 22:38:26 - debug:   Number of all scripts: 28
2025-8-20 22:38:26 - debug:   Number of other assets: 609
2025-8-20 22:38:26 - debug: Init bundle root assets success..., progress: 11%
2025-8-20 22:38:26 - debug: Init bundle root assets success..., progress: 20%
2025-8-20 22:38:26 - debug: reload all scripts.
2025-8-20 22:38:26 - debug: [[Executor]] reload before lock
2025-8-20 22:38:26 - debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-20 22:38:26 - groupCollapsed: Invalidate all modules
2025-8-20 22:38:26 - debug: Unregister BuiltinPipelineSettings
2025-8-20 22:38:26 - debug: Unregister BuiltinPipelinePassBuilder
2025-8-20 22:38:26 - debug: Unregister BuiltinDepthOfFieldPass
2025-8-20 22:38:26 - debug: Unregister DebugViewRuntimeControl
2025-8-20 22:38:26 - debug: Unregister CameraFollow
2025-8-20 22:38:26 - debug: Unregister SoundManager
2025-8-20 22:38:26 - debug: Unregister Bullet
2025-8-20 22:38:26 - debug: Unregister AIPlayer
2025-8-20 22:38:26 - debug: Unregister player
2025-8-20 22:38:26 - debug: Unregister PlayerManager
2025-8-20 22:38:26 - debug: Unregister SceneTransition
2025-8-20 22:38:26 - debug: Unregister PaintManager
2025-8-20 22:38:26 - debug: Unregister GameOverPanel
2025-8-20 22:38:26 - debug: Unregister GameHUD
2025-8-20 22:38:26 - debug: Unregister GameManager
2025-8-20 22:38:26 - debug: Unregister AIController
2025-8-20 22:38:26 - debug: Unregister CarProperties
2025-8-20 22:38:26 - debug: Unregister CarPropertyDisplay
2025-8-20 22:38:26 - debug: Unregister HealthBarUI
2025-8-20 22:38:26 - debug: Unregister MainMenuController
2025-8-20 22:38:26 - debug: Unregister PaintSpot
2025-8-20 22:38:26 - debug: Unregister PausePanel
2025-8-20 22:38:26 - debug: Unregister PlayerInfoUI
2025-8-20 22:38:26 - debug: Unregister PurchasePanel
2025-8-20 22:38:26 - debug: Unregister SelectManager
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js'
2025-8-20 22:38:26 - debug: Invalidating 'pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js'
2025-8-20 22:38:26 - groupEnd: Invalidate all modules
2025-8-20 22:38:26 - groupCollapsed: Imports all modules
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register CameraFollow
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register SoundManager
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register Bullet
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register AIPlayer
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register player
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register PlayerManager
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register SceneTransition
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register PaintManager
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register GameOverPanel
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register GameHUD
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register GameManager
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register AIController
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register CarProperties
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register CarPropertyDisplay
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register HealthBarUI
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register MainMenuController
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register PaintSpot
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register PausePanel
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register PlayerInfoUI
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register PurchasePanel
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Register SelectManager
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-20 22:38:26 - debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-20 22:38:26 - groupEnd: Imports all modules
2025-8-20 22:38:26 - debug: [[Executor]] after unlock
2025-8-20 22:38:26 - debug: Incremental keys: 
2025-8-20 22:38:26 - debug: Init bundle share assets start..., progress: 11%
2025-8-20 22:38:26 - debug: Init bundle share assets start..., progress: 20%
2025-8-20 22:38:26 - warn: The SpriteFrame used by component "cc.Sprite" in prefab "level-1" is missing. Detailed information:
Node path: "level-1/soccor_court"
Asset url: "db://assets/resources/prefab/levels/level-1"
Asset file: "/Users/<USER>/projects/cocos_project/driftClash/assets/img/background/soccor_court.jpeg"
Asset deleted time: "2025/8/18 21:56:49"
Missing uuid: "6f121691-4929-49ed-b949-7e2d17747ab9@f9941"


2025-8-20 22:38:26 - debug: Init bundle share assets success..., progress: 11%
2025-8-20 22:38:26 - debug: Init bundle share assets success..., progress: 20%
2025-8-20 22:38:27 - debug: handle json group in bundle internal
2025-8-20 22:38:27 - debug: handle json group in bundle internal success
2025-8-20 22:38:27 - debug: handle json group in bundle resources
2025-8-20 22:38:27 - debug: handle json group in bundle main
2025-8-20 22:38:27 - debug: init image compress task 0 in bundle internal
2025-8-20 22:38:27 - debug: handle json group in bundle main success
2025-8-20 22:38:27 - debug: init image compress task 0 in bundle main
2025-8-20 22:38:27 - debug: handle json group in bundle resources success
2025-8-20 22:38:27 - debug: init image compress task 0 in bundle resources
2025-8-20 22:38:27 - debug: // ---- build task 查询 Asset Bundle ---- (101ms)
2025-8-20 22:38:27 - log: run build task 查询 Asset Bundle success in 101 ms√, progress: 16%
2025-8-20 22:38:27 - debug: [Build Memory track]: 查询 Asset Bundle start:210.80MB, end 212.47MB, increase: 1.66MB
2025-8-20 22:38:27 - debug: 查询 Asset Bundle start, progress: 16%
2025-8-20 22:38:27 - debug: // ---- build task 查询 Asset Bundle ----
2025-8-20 22:38:27 - debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-8-20 22:38:27 - log: run build task 查询 Asset Bundle success in 23 ms√, progress: 21%
2025-8-20 22:38:27 - debug: [Build Memory track]: 查询 Asset Bundle start:212.49MB, end 212.93MB, increase: 443.82KB
2025-8-20 22:38:27 - debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-20 22:38:27 - debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-20 22:38:27 - debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-20 22:38:27 - debug: // ---- build task native：onAfterBundleDataTask ---- (21ms)
2025-8-20 22:38:27 - debug: native:(onAfterBundleDataTask) in 21 ms ✓, progress: 21%
2025-8-20 22:38:27 - debug: native:(onAfterBundleDataTask) in 21 ms ✓, progress: 27%
2025-8-20 22:38:27 - debug: 打包脚本 start, progress: 21%
2025-8-20 22:38:27 - debug: // ---- build task 打包脚本 ----
2025-8-20 22:38:27 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 22:38:27 - log: [build-script]enter sub process 12306, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-20 22:38:27 - log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-20 22:38:27 - debug: excute-script over with build-script 755ms
2025-8-20 22:38:27 - debug: Generate systemJs..., progress: 21%
2025-8-20 22:38:27 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 22:38:28 - debug: excute-script over with build-script 842ms
2025-8-20 22:38:28 - debug: 构建项目脚本 start..., progress: 21%
2025-8-20 22:38:28 - debug: Build script in bundle start, progress: 21%
2025-8-20 22:38:28 - debug: Build script in bundle start, progress: 27%
2025-8-20 22:38:28 - log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-20 22:38:29 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts


2025-8-20 22:38:29 - warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-20 22:38:29 - debug: excute-script over with build-script 1073ms
2025-8-20 22:38:29 - debug: Copy externalScripts success!
2025-8-20 22:38:29 - debug: Build script in bundle success, progress: 21%
2025-8-20 22:38:29 - debug: Build script in bundle success, progress: 27%
2025-8-20 22:38:29 - debug: 构建项目脚本 in (1125 ms) √, progress: 21%
2025-8-20 22:38:29 - debug: 构建引擎脚本 start..., progress: 21%
2025-8-20 22:38:29 - debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-20 22:38:29 - debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf)}
2025-8-20 22:38:29 - debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-20 22:38:29 - debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "src/cocos-js",
  "platform": "IOS",
  "useCache": true,
  "nativeCodeBundleMode": "asmjs",
  "wasmCompressionMode": false,
  "output": "/Users/<USER>/projects/SuperSplash/data/src/cocos-js",
  "targets": "chrome 80",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-20 22:38:29 - debug: 构建引擎脚本 in (41 ms) √, progress: 21%
2025-8-20 22:38:29 - debug: Copy plugin script ..., progress: 21%
2025-8-20 22:38:29 - debug: Generate import-map..., progress: 21%
2025-8-20 22:38:29 - debug: // ---- build task 打包脚本 ---- (2797ms)
2025-8-20 22:38:29 - log: run build task 打包脚本 success in 2 s√, progress: 26%
2025-8-20 22:38:29 - debug: [Build Memory track]: 打包脚本 start:213.45MB, end 212.84MB, increase: -624.31KB
2025-8-20 22:38:29 - debug: Build Assets start, progress: 26%
2025-8-20 22:38:29 - debug: // ---- build task Build Assets ----
2025-8-20 22:38:29 - debug: Build bundles..., progress: 26%
2025-8-20 22:38:29 - debug: Pack Images start, progress: 26%
2025-8-20 22:38:29 - debug: Pack Images start, progress: 27%
2025-8-20 22:38:29 - debug: builder:pack-auto-atlas-image (48ms)
2025-8-20 22:38:29 - debug: Pack Images success, progress: 26%
2025-8-20 22:38:29 - debug: Pack Images success, progress: 27%
2025-8-20 22:38:29 - debug: Compress image start..., progress: 26%
2025-8-20 22:38:29 - debug: Compress image start..., progress: 27%
2025-8-20 22:38:29 - group: Compress image...
2025-8-20 22:38:29 - debug: sort compress task {}
2025-8-20 22:38:29 - debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-20 22:38:29 - debug: No image need to compress
2025-8-20 22:38:29 - groupEnd: Compress image...
2025-8-20 22:38:29 - debug: Compress image success..., progress: 26%
2025-8-20 22:38:29 - debug: Compress image success..., progress: 27%
2025-8-20 22:38:29 - debug: Output asset in bundles start, progress: 26%
2025-8-20 22:38:29 - debug: Output asset in bundles start, progress: 27%
2025-8-20 22:38:29 - debug: Handle all json groups in bundle internal
2025-8-20 22:38:29 - debug: handle json group
2025-8-20 22:38:29 - debug: Handle all json groups in bundle resources
2025-8-20 22:38:29 - debug: handle json group
2025-8-20 22:38:29 - debug: Handle all json groups in bundle main
2025-8-20 22:38:29 - debug: handle json group
2025-8-20 22:38:29 - debug: Json group(05b737039) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(0b9729f75) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(06585a170) compile success，json number: 6
2025-8-20 22:38:29 - debug: handle single json
2025-8-20 22:38:29 - debug: Json group(047956f3a) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(08532c3e3) compile success，json number: 6
2025-8-20 22:38:29 - debug: handle single json
2025-8-20 22:38:29 - debug: Json group(01959b579) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(09bd04adc) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-20 22:38:29 - warn: The SpriteFrame used by component "cc.Sprite" in prefab "level-1" is missing. Detailed information:
Node path: "level-1/soccor_court"
Asset url: "db://assets/resources/prefab/levels/level-1"
Asset file: "/Users/<USER>/projects/cocos_project/driftClash/assets/img/background/soccor_court.jpeg"
Asset deleted time: "2025/8/18 21:56:49"
Missing uuid: "6f121691-4929-49ed-b949-7e2d17747ab9@f9941"


2025-8-20 22:38:29 - debug: Json group(0d882e0be) compile success，json number: 6
2025-8-20 22:38:29 - debug: Json group(0c2a51634) compile success，json number: 6
2025-8-20 22:38:30 - debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-20 22:38:30 - debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-20 22:38:30 - debug: handle single json
2025-8-20 22:38:30 - debug: Output asset in bundles success, progress: 26%
2025-8-20 22:38:30 - debug: Output asset in bundles success, progress: 27%
2025-8-20 22:38:30 - debug: Output asset in bundles start, progress: 26%
2025-8-20 22:38:30 - debug: Output asset in bundles start, progress: 27%
2025-8-20 22:38:30 - debug: compress config of bundle internal...
2025-8-20 22:38:30 - debug: compress config of bundle internal success
2025-8-20 22:38:30 - debug: compress config of bundle resources...
2025-8-20 22:38:30 - debug: compress config of bundle resources success
2025-8-20 22:38:30 - debug: compress config of bundle main...
2025-8-20 22:38:30 - debug: compress config of bundle main success
2025-8-20 22:38:30 - debug: output config of bundle internal
2025-8-20 22:38:30 - debug: output config of bundle internal success
2025-8-20 22:38:30 - debug: output config of bundle resources
2025-8-20 22:38:30 - debug: output config of bundle resources success
2025-8-20 22:38:30 - debug: output config of bundle main
2025-8-20 22:38:30 - debug: output config of bundle main success
2025-8-20 22:38:30 - debug: Output asset in bundles success, progress: 26%
2025-8-20 22:38:30 - debug: Output asset in bundles success, progress: 27%
2025-8-20 22:38:30 - debug: // ---- build task Build Assets ---- (252ms)
2025-8-20 22:38:30 - log: run build task Build Assets success in 252 ms√, progress: 31%
2025-8-20 22:38:30 - debug: [Build Memory track]: Build Assets start:212.86MB, end 216.79MB, increase: 3.93MB
2025-8-20 22:38:30 - debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-20 22:38:30 - debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-20 22:38:30 - debug: // ---- build task ios：onAfterBuildAssets ---- (30ms)
2025-8-20 22:38:30 - debug: ios:(onAfterBuildAssets) in 30 ms ✓, progress: 33%
2025-8-20 22:38:30 - debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-20 22:38:30 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-20 22:38:30 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (53ms)
2025-8-20 22:38:30 - log: run build task 整理部分构建选项内数据到 settings.json success in 53 ms√, progress: 34%
2025-8-20 22:38:30 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.24MB, end 217.58MB, increase: 339.82KB
2025-8-20 22:38:30 - debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-20 22:38:30 - debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-20 22:38:30 - debug: // ---- build task 填充脚本数据到 settings.json ---- (36ms)
2025-8-20 22:38:30 - log: run build task 填充脚本数据到 settings.json success in 36 ms√, progress: 36%
2025-8-20 22:38:30 - debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.60MB, end 217.94MB, increase: 340.59KB
2025-8-20 22:38:30 - debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-20 22:38:30 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-20 22:38:30 - debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (23ms)
2025-8-20 22:38:30 - log: run build task 整理部分构建选项内数据到 settings.json success in 23 ms√, progress: 38%
2025-8-20 22:38:30 - debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.96MB, end 215.35MB, increase: -2678.19KB
2025-8-20 22:38:30 - debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-20 22:38:30 - debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-20 22:38:30 - debug: // ---- build task ios：onBeforeCompressSettings ---- (22ms)
2025-8-20 22:38:30 - debug: ios:(onBeforeCompressSettings) in 22 ms ✓, progress: 40%
2025-8-20 22:38:30 - debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-20 22:38:30 - debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-20 22:38:30 - debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (81ms)
2025-8-20 22:38:30 - debug: cocos-service:(onBeforeCompressSettings) in 81 ms ✓, progress: 41%
2025-8-20 22:38:30 - debug: 整理静态模板文件 start, progress: 41%
2025-8-20 22:38:30 - debug: // ---- build task 整理静态模板文件 ----
2025-8-20 22:38:30 - debug: // ---- build task 整理静态模板文件 ---- (29ms)
2025-8-20 22:38:30 - log: run build task 整理静态模板文件 success in 29 ms√, progress: 46%
2025-8-20 22:38:30 - debug: [Build Memory track]: 整理静态模板文件 start:216.16MB, end 218.20MB, increase: 2.03MB
2025-8-20 22:38:30 - debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-20 22:38:30 - debug: // ---- build task native：onAfterCompressSettings ----
2025-8-20 22:38:30 - log: Checking template version...
2025-8-20 22:38:30 - log: Validating template consistency...
2025-8-20 22:38:30 - log: Validating platform source code directories...
2025-8-20 22:38:30 - debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-20 22:38:30 - log: While replace template content, file /Users/<USER>/projects/SuperSplash/proj/main.cpp
2025-8-20 22:38:30 - log: While replace template content, file /Users/<USER>/projects/SuperSplash/proj/main.m
2025-8-20 22:38:30 - log: While replace template content, file /Users/<USER>/projects/SuperSplash/proj/settings.gradle
2025-8-20 22:38:30 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-20 22:38:30 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-20 22:38:30 - log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-20 22:38:30 - debug: // ---- build task native：onAfterCompressSettings ---- (23ms)
2025-8-20 22:38:30 - debug: native:(onAfterCompressSettings) in 23 ms ✓, progress: 48%
2025-8-20 22:38:30 - debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-20 22:38:30 - debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-20 22:38:30 - debug: // ---- build task cocos-service：onAfterCompressSettings ---- (378ms)
2025-8-20 22:38:30 - debug: cocos-service:(onAfterCompressSettings) in 378 ms ✓, progress: 50%
2025-8-20 22:38:30 - debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-20 22:38:30 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-20 22:38:30 - debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (40ms)
2025-8-20 22:38:30 - log: run build task 给所有的资源加上 MD5 后缀 success in 40 ms√, progress: 60%
2025-8-20 22:38:30 - debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:216.71MB, end 217.04MB, increase: 342.46KB
2025-8-20 22:38:30 - debug: native:(onAfterBuild) start..., progress: 60%
2025-8-20 22:38:30 - debug: // ---- build task native：onAfterBuild ----
2025-8-20 22:38:30 - log: [xcode-select] /Library/Developer/CommandLineTools


2025-8-20 22:38:30 - error: Please check if Xcode is installed.
2025-8-20 22:38:30 - error: Error: Command failed: xcrun xcodebuild -version
xcrun: error: unable to find utility "xcodebuild", not a developer tool or in PATH

    at genericNodeError (node:internal/errors:984:15)
    at wrappedFn (node:internal/errors:538:14)
    at checkExecSyncError (node:child_process:905:11)
    at execSync (node:child_process:977:15)
    at node:electron/js2c/node_init:2:16388
    at Object.getXcodeMajorVerion (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:421:36)
    at IOSPackTool.generate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/platforms/ios.ts:103:32)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at NativePackToolManager.generate (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/base/manager.ts:48:9)
    at PackToolHandler.runTask (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/modules/platform-extensions/extensions/native/dist/builder/native-utils/index.ccc:1:2968)
    at BuildTask.onAfterBuild (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/modules/platform-extensions/extensions/native/dist/builder/hooks.ccc:1:5530)
    at BuildTask.handleHook (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/builder/index.ccc:1:13689)
    at BuildTask.runPluginTask (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/builder/manager/task-base.ccc:1:1272)
    at BuildTask.run (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/builder/index.ccc:1:5246)
    at build (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/index.ccc:1:4259)
    at Ipc.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/msg-util.ccc:1:210)
2025-8-20 22:38:30 - log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/SuperSplash/proj" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/SuperSplash" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-20 22:38:30 - error: [cmake-err] CMake Error at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/iOS-Initialize.cmake:4 (message):
  iphoneos is not an iOS SDK
Call Stack (most recent call first):
  /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake:21 (include)
  CMakeLists.txt:6 (project)



2025-8-20 22:38:30 - log: [cmake] -- Configuring incomplete, errors occurred!
See also "/Users/<USER>/projects/SuperSplash/proj/CMakeFiles/CMakeOutput.log".


2025-8-20 22:38:30 - error: 构建插件 native 的钩子函数 onAfterBuild 执行失败，请检查插件的代码逻辑~, progress: 62%
2025-8-20 22:38:30 - debug: Error: run cmake failed "cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/SuperSplash/proj" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/SuperSplash" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"", code: 1, signal: null, progress: 64%
2025-8-20 22:38:30 - debug: [Build Memory track]: builder:build-project-total start:210.16MB, end 218.02MB, increase: 7.86MB
2025-8-20 22:38:30 - log: Asset DB is resume!
2025-8-20 22:38:30 - debug: builder:build-project-total (4556ms)
2025-8-20 22:38:30 - warn: The SpriteFrame used by component "cc.Sprite" in prefab "level-1" is missing. Detailed information:
Node path: "level-1/soccor_court"
Asset url: "db://assets/resources/prefab/levels/level-1"
Asset file: "/Users/<USER>/projects/cocos_project/driftClash/assets/img/background/soccor_court.jpeg"
Asset deleted time: "2025/8/18 21:56:49"
Missing uuid: "6f121691-4929-49ed-b949-7e2d17747ab9@f9941"


2025-8-20 22:38:30 - debug: build success in 4556!
2025-8-20 22:38:30 - error: Error: run cmake failed "cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/SuperSplash/proj" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/SuperSplash" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"", code: 1, signal: null
    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)
    at ChildProcess.emit (node:events:519:28)
    at maybeClose (node:internal/child_process:1105:16)
    at Process.ChildProcess._handle.onexit (node:internal/child_process:305:5)
2025-8-20 22:38:30 - debug: ================================ build Task (ios-001) Finished in (4 s)ms ================================
