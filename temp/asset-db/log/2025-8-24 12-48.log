2025-8-24 12:48:45-debug: start **** info
2025-8-24 12:48:45-log: Cannot access game frame or container.
2025-8-24 12:48:45-debug: asset-db:require-engine-code (359ms)
2025-8-24 12:48:45-log: [box2d]:box2d wasm lib loaded.
2025-8-24 12:48:45-log: meshopt wasm decoder initialized
2025-8-24 12:48:45-log: [bullet]:bullet wasm lib loaded.
2025-8-24 12:48:45-log: Cocos Creator v3.8.6
2025-8-24 12:48:45-log: Forward render pipeline initialized.
2025-8-24 12:48:45-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.31MB, end 118.34MB, increase: 89.03MB
2025-8-24 12:48:45-debug: [Assets Memory track]: asset-db-plugin-register: programming start:119.18MB, end 126.84MB, increase: 7.66MB
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db-plugin-register: builder start:126.87MB, end 223.98MB, increase: 97.11MB
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.19MB, end 227.07MB, increase: 2.88MB
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db-plugin-register: project start:119.06MB, end 227.23MB, increase: 108.17MB
2025-8-24 12:48:45-log: Using legacy pipeline
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:118.36MB, end 227.78MB, increase: 109.41MB
2025-8-24 12:48:46-debug: run package(channel-upload-tools) handler(enable) start
2025-8-24 12:48:46-debug: run package(google-play) handler(enable) start
2025-8-24 12:48:46-debug: run package(google-play) handler(enable) success!
2025-8-24 12:48:46-debug: run package(harmonyos-next) handler(enable) start
2025-8-24 12:48:46-debug: run package(harmonyos-next) handler(enable) success!
2025-8-24 12:48:46-debug: run package(honor-mini-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(honor-mini-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(huawei-agc) handler(enable) start
2025-8-24 12:48:46-debug: run package(huawei-agc) handler(enable) success!
2025-8-24 12:48:46-debug: run package(huawei-quick-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(huawei-quick-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(ios) handler(enable) success!
2025-8-24 12:48:46-debug: run package(ios) handler(enable) start
2025-8-24 12:48:46-debug: run package(linux) handler(enable) start
2025-8-24 12:48:46-debug: run package(linux) handler(enable) success!
2025-8-24 12:48:46-debug: run package(mac) handler(enable) start
2025-8-24 12:48:46-debug: run package(mac) handler(enable) success!
2025-8-24 12:48:46-debug: run package(migu-mini-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(migu-mini-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(native) handler(enable) start
2025-8-24 12:48:46-debug: run package(native) handler(enable) success!
2025-8-24 12:48:46-debug: run package(ohos) handler(enable) start
2025-8-24 12:48:46-debug: run package(ohos) handler(enable) success!
2025-8-24 12:48:46-debug: run package(oppo-mini-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(channel-upload-tools) handler(enable) success!
2025-8-24 12:48:46-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-24 12:48:46-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-24 12:48:46-debug: run package(taobao-mini-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(fb-instant-games) handler(enable) success!
2025-8-24 12:48:46-debug: run package(vivo-mini-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(web-desktop) handler(enable) start
2025-8-24 12:48:46-debug: run package(fb-instant-games) handler(enable) start
2025-8-24 12:48:46-debug: run package(web-mobile) handler(enable) start
2025-8-24 12:48:46-debug: run package(web-mobile) handler(enable) success!
2025-8-24 12:48:46-debug: run package(wechatgame) handler(enable) start
2025-8-24 12:48:46-debug: run package(wechatgame) handler(enable) success!
2025-8-24 12:48:46-debug: run package(wechatprogram) handler(enable) start
2025-8-24 12:48:46-debug: run package(wechatprogram) handler(enable) success!
2025-8-24 12:48:46-debug: run package(windows) handler(enable) start
2025-8-24 12:48:46-debug: run package(windows) handler(enable) success!
2025-8-24 12:48:46-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-24 12:48:46-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(web-desktop) handler(enable) success!
2025-8-24 12:48:46-debug: run package(cocos-service) handler(enable) success!
2025-8-24 12:48:46-debug: run package(im-plugin) handler(enable) start
2025-8-24 12:48:46-debug: run package(im-plugin) handler(enable) success!
2025-8-24 12:48:46-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-24 12:48:46-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-24 12:48:46-debug: run package(placeholder) handler(enable) start
2025-8-24 12:48:46-debug: run package(cocos-service) handler(enable) start
2025-8-24 12:48:46-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-24 12:48:46-debug: run package(placeholder) handler(enable) success!
2025-8-24 12:48:46-debug: asset-db:worker-init: initPlugin (844ms)
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db:worker-init start:29.30MB, end 229.05MB, increase: 199.75MB
2025-8-24 12:48:46-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-24 12:48:46-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-24 12:48:46-debug: Run asset db hook programming:beforePreStart ...
2025-8-24 12:48:46-debug: Run asset db hook programming:beforePreStart success!
2025-8-24 12:48:46-debug: asset-db:worker-init (1279ms)
2025-8-24 12:48:46-debug: asset-db-hook-programming-beforePreStart (35ms)
2025-8-24 12:48:46-debug: asset-db-hook-engine-extends-beforePreStart (35ms)
2025-8-24 12:48:46-debug: Preimport db internal success
2025-8-24 12:48:46-debug: Run asset db hook programming:afterPreStart ...
2025-8-24 12:48:46-debug: starting packer-driver...
2025-8-24 12:48:46-debug: Preimport db assets success
2025-8-24 12:48:46-debug: initialize scripting environment...
2025-8-24 12:48:46-debug: [[Executor]] prepare before lock
2025-8-24 12:48:46-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-24 12:48:46-debug: [[Executor]] prepare after unlock
2025-8-24 12:48:46-debug: Run asset db hook programming:afterPreStart success!
2025-8-24 12:48:46-debug: asset-db-hook-programming-afterPreStart (246ms)
2025-8-24 12:48:46-debug: asset-db:worker-effect-data-processing (138ms)
2025-8-24 12:48:46-debug: asset-db-hook-engine-extends-afterPreStart (138ms)
2025-8-24 12:48:46-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-24 12:48:46-debug: Start up the 'internal' database...
2025-8-24 12:48:46-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:229.14MB, end 242.72MB, increase: 13.58MB
2025-8-24 12:48:46-debug: Start up the 'assets' database...
2025-8-24 12:48:46-debug: asset-db:worker-startup-database[internal] (371ms)
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:226.86MB, end 241.40MB, increase: 14.53MB
2025-8-24 12:48:46-debug: lazy register asset handler directory
2025-8-24 12:48:46-debug: lazy register asset handler json
2025-8-24 12:48:46-debug: lazy register asset handler spine-data
2025-8-24 12:48:46-debug: lazy register asset handler dragonbones
2025-8-24 12:48:46-debug: lazy register asset handler dragonbones-atlas
2025-8-24 12:48:46-debug: lazy register asset handler *
2025-8-24 12:48:46-debug: lazy register asset handler text
2025-8-24 12:48:46-debug: lazy register asset handler typescript
2025-8-24 12:48:46-debug: lazy register asset handler scene
2025-8-24 12:48:46-debug: lazy register asset handler prefab
2025-8-24 12:48:46-debug: lazy register asset handler sprite-frame
2025-8-24 12:48:46-debug: lazy register asset handler buffer
2025-8-24 12:48:46-debug: lazy register asset handler tiled-map
2025-8-24 12:48:46-debug: lazy register asset handler image
2025-8-24 12:48:46-debug: lazy register asset handler sign-image
2025-8-24 12:48:46-debug: lazy register asset handler alpha-image
2025-8-24 12:48:46-debug: lazy register asset handler texture
2025-8-24 12:48:46-debug: lazy register asset handler texture-cube
2025-8-24 12:48:46-debug: lazy register asset handler erp-texture-cube
2025-8-24 12:48:46-debug: lazy register asset handler javascript
2025-8-24 12:48:46-debug: lazy register asset handler texture-cube-face
2025-8-24 12:48:46-debug: lazy register asset handler rt-sprite-frame
2025-8-24 12:48:46-debug: lazy register asset handler terrain
2025-8-24 12:48:46-debug: lazy register asset handler gltf
2025-8-24 12:48:46-debug: lazy register asset handler render-texture
2025-8-24 12:48:46-debug: lazy register asset handler gltf-animation
2025-8-24 12:48:46-debug: lazy register asset handler gltf-mesh
2025-8-24 12:48:46-debug: lazy register asset handler gltf-material
2025-8-24 12:48:46-debug: lazy register asset handler gltf-scene
2025-8-24 12:48:46-debug: lazy register asset handler fbx
2025-8-24 12:48:46-debug: lazy register asset handler material
2025-8-24 12:48:46-debug: lazy register asset handler physics-material
2025-8-24 12:48:46-debug: lazy register asset handler effect
2025-8-24 12:48:46-debug: lazy register asset handler effect-header
2025-8-24 12:48:46-debug: lazy register asset handler audio-clip
2025-8-24 12:48:46-debug: lazy register asset handler animation-clip
2025-8-24 12:48:46-debug: lazy register asset handler animation-graph
2025-8-24 12:48:46-debug: lazy register asset handler animation-graph-variant
2025-8-24 12:48:46-debug: lazy register asset handler gltf-embeded-image
2025-8-24 12:48:46-debug: lazy register asset handler ttf-font
2025-8-24 12:48:46-debug: lazy register asset handler bitmap-font
2025-8-24 12:48:46-debug: lazy register asset handler sprite-atlas
2025-8-24 12:48:46-debug: lazy register asset handler auto-atlas
2025-8-24 12:48:46-debug: lazy register asset handler gltf-skeleton
2025-8-24 12:48:46-debug: lazy register asset handler render-pipeline
2025-8-24 12:48:46-debug: lazy register asset handler render-stage
2025-8-24 12:48:46-debug: [Assets Memory track]: asset-db:worker-init: startup start:229.21MB, end 241.41MB, increase: 12.20MB
2025-8-24 12:48:46-debug: lazy register asset handler label-atlas
2025-8-24 12:48:46-debug: lazy register asset handler instantiation-mesh
2025-8-24 12:48:46-debug: lazy register asset handler instantiation-material
2025-8-24 12:48:46-debug: lazy register asset handler instantiation-skeleton
2025-8-24 12:48:46-debug: lazy register asset handler instantiation-animation
2025-8-24 12:48:46-debug: lazy register asset handler video-clip
2025-8-24 12:48:46-debug: lazy register asset handler animation-mask
2025-8-24 12:48:46-debug: lazy register asset handler render-flow
2025-8-24 12:48:46-debug: lazy register asset handler particle
2025-8-24 12:48:46-debug: asset-db:worker-startup-database[assets] (341ms)
2025-8-24 12:48:46-debug: asset-db:ready (2624ms)
2025-8-24 12:48:46-debug: fix the bug of updateDefaultUserData
2025-8-24 12:48:46-debug: asset-db:start-database (395ms)
2025-8-24 12:48:46-debug: init worker message success
2025-8-24 12:48:46-debug: [Build Memory track]: builder:worker-init start:244.45MB, end 256.23MB, increase: 11.78MB
2025-8-24 12:48:46-debug: programming:execute-script (2ms)
2025-8-24 12:48:46-debug: builder:worker-init (188ms)
2025-8-24 12:48:48-debug: refresh db internal success
2025-8-24 12:48:48-debug: refresh db assets success
2025-8-24 12:48:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 12:48:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 12:48:48-debug: asset-db:refresh-all-database (17ms)
2025-8-24 12:49:08-debug: Start record console... {file(/Users/<USER>/projects/cocos_project/SuperSplash/temp/builder/log/ios8-24-2025 12-49.log)}
2025-8-24 12:49:08-debug: =================================== build Task (ios) Start ================================
2025-8-24 12:49:08-debug: Start build task, options:
{"name":"SuperSplash","server":"","engineModulesConfigKey":"defaultConfig","platform":"ios","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":false,"md5Cache":false,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"inherit-project-setting"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"asmjs","polyfills":{"asyncFunctions":false},"experimentalEraseModules":false,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb","outputName":"ios","taskName":"ios","scenes":[{"url":"db://assets/Scenes/LevelSelect.scene","uuid":"091c5c0e-b72a-4cad-ab68-635bc57ff236"},{"url":"db://assets/Scenes/gamescene.scene","uuid":"1563d039-d898-4d8f-9415-9f1da853b8a3"},{"url":"db://assets/Scenes/mainmenu.scene","uuid":"9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}],"wasmCompressionMode":false,"packages":{"ios":{"executableName":"SuperSplash","packageName":"com.rio.supersplsh","renderBackEnd":{"metal":true},"skipUpdateXcodeProject":false,"orientation":{"portrait":false,"upsideDown":false,"landscapeRight":true,"landscapeLeft":false},"osTarget":{"iphoneos":false,"simulator":true},"targetVersion":"15.0","__version__":"1.0.1","developerTeam":"UWR5Y8Y7U8_0D198E51CA352285F98A27F6273A0515BD826D7C"},"cocos-service":{"configID":"e495ea","services":[],"__version__":"3.0.9"},"native":{"encrypted":false,"xxteaKey":"99ZvA0bkJB9olDaw","compressZip":false,"JobSystem":"none","__version__":"1.0.2"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/ios8-24-2025 12-49.log"}
2025-8-24 12:49:08-debug: Build with Cocos Creator 3.8.6
2025-8-24 12:49:08-debug: native:(onBeforeBuild) start..., progress: 0%
2025-8-24 12:49:08-debug: // ---- build task native：onBeforeBuild ----
2025-8-24 12:49:08-debug: // ---- build task native：onBeforeBuild ---- (47ms)
2025-8-24 12:49:08-debug: native:(onBeforeBuild) in 47 ms ✓, progress: 2%
2025-8-24 12:49:08-debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-8-24 12:49:08-debug: // ---- build task cocos-service：onBeforeBuild ----
2025-8-24 12:49:09-debug: // ---- build task cocos-service：onBeforeBuild ---- (251ms)
2025-8-24 12:49:09-debug: cocos-service:(onBeforeBuild) in 251 ms ✓, progress: 4%
2025-8-24 12:49:09-debug: scene:(onBeforeBuild) start..., progress: 4%
2025-8-24 12:49:09-debug: // ---- build task scene：onBeforeBuild ----
2025-8-24 12:49:09-debug: // ---- build task scene：onBeforeBuild ---- (87ms)
2025-8-24 12:49:09-debug: scene:(onBeforeBuild) in 87 ms ✓, progress: 5%
2025-8-24 12:49:09-debug: Start lock asset db..., progress: 5%
2025-8-24 12:49:09-log: Asset DB is paused with build!
2025-8-24 12:49:09-debug: Query all assets info in project
2025-8-24 12:49:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-24 12:49:09-debug: native:(onAfterInit) start..., progress: 5%
2025-8-24 12:49:09-debug: // ---- build task native：onAfterInit ----
2025-8-24 12:49:09-debug: Native engine root:/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
2025-8-24 12:49:09-debug: // ---- build task native：onAfterInit ---- (200ms)
2025-8-24 12:49:09-debug: native:(onAfterInit) in 200 ms ✓, progress: 7%
2025-8-24 12:49:09-debug: ios:(onAfterInit) start..., progress: 7%
2025-8-24 12:49:09-debug: // ---- build task ios：onAfterInit ----
2025-8-24 12:49:09-debug: // ---- build task ios：onAfterInit ---- (76ms)
2025-8-24 12:49:09-debug: // ---- build task cocos-service：onAfterInit ----
2025-8-24 12:49:09-debug: cocos-service:(onAfterInit) start..., progress: 9%
2025-8-24 12:49:09-debug: ios:(onAfterInit) in 76 ms ✓, progress: 9%
2025-8-24 12:49:09-debug: // ---- build task cocos-service：onAfterInit ---- (127ms)
2025-8-24 12:49:09-debug: Skip compress image, progress: 0%
2025-8-24 12:49:09-debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 0%
2025-8-24 12:49:09-debug: [adsense-h5g-plugin] remove script success
2025-8-24 12:49:09-debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 11%
2025-8-24 12:49:09-debug: cocos-service:(onAfterInit) in 127 ms ✓, progress: 11%
2025-8-24 12:49:09-debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-8-24 12:49:09-debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (73ms)
2025-8-24 12:49:09-debug: adsense-h5g-plugin:(onBeforeBundleInit) in 73 ms ✓, progress: 7%
2025-8-24 12:49:09-debug: adsense-h5g-plugin:(onBeforeBundleInit) in 73 ms ✓, progress: 11%
2025-8-24 12:49:09-debug: Init all bundles start..., progress: 11%
2025-8-24 12:49:09-debug: native:(onAfterBundleInit) start..., progress: 11%
2025-8-24 12:49:09-debug: native:(onAfterBundleInit) start..., progress: 7%
2025-8-24 12:49:09-debug: // ---- build task native：onAfterBundleInit ----
2025-8-24 12:49:09-debug: Init all bundles start..., progress: 7%
2025-8-24 12:49:09-debug: Num of bundles: 3..., progress: 11%
2025-8-24 12:49:09-debug: Num of bundles: 3..., progress: 7%
2025-8-24 12:49:09-debug: // ---- build task native：onAfterBundleInit ---- (148ms)
2025-8-24 12:49:09-debug: native:(onAfterBundleInit) in 148 ms ✓, progress: 11%
2025-8-24 12:49:09-debug: native:(onAfterBundleInit) in 148 ms ✓, progress: 13%
2025-8-24 12:49:09-debug: ios:(onAfterBundleInit) start..., progress: 11%
2025-8-24 12:49:09-debug: ios:(onAfterBundleInit) start..., progress: 13%
2025-8-24 12:49:09-debug: // ---- build task ios：onAfterBundleInit ----
2025-8-24 12:49:10-debug: // ---- build task ios：onAfterBundleInit ---- (73ms)
2025-8-24 12:49:10-debug: ios:(onAfterBundleInit) in 73 ms ✓, progress: 11%
2025-8-24 12:49:10-debug: ios:(onAfterBundleInit) in 73 ms ✓, progress: 20%
2025-8-24 12:49:10-debug: 查询 Asset Bundle start, progress: 11%
2025-8-24 12:49:10-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 12:49:10-debug: Init bundle root assets start..., progress: 11%
2025-8-24 12:49:10-debug: Init bundle root assets start..., progress: 20%
2025-8-24 12:49:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-24 12:49:10-debug:   Number of other assets: 610
2025-8-24 12:49:10-debug: Init bundle root assets success..., progress: 20%
2025-8-24 12:49:10-debug: reload all scripts.
2025-8-24 12:49:10-log: creating executor ...
2025-8-24 12:49:10-debug:   Number of all scenes: 3
2025-8-24 12:49:10-debug:   Number of all scripts: 28
2025-8-24 12:49:10-debug: Init bundle root assets success..., progress: 11%
2025-8-24 12:49:10-debug: [[Executor]] reload before lock
2025-8-24 12:49:10-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-empty" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgpu" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/3d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/animation" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/skeletal-animation" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/sorting" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/rich-text" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/mask" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui-skew" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/graphics" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/affine-transform" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle-2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-framework" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-cannon" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-physx" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-ammo" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-builtin" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-framework" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-jsb" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-builtin" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-wasm" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/intersection-2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/primitive" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/profiler" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/geometry-renderer" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/audio" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/video" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/base" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/xr" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/terrain" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/webview" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tween" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tiled-map" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/vendor-google" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/spine" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/dragon-bones" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline-post-process" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/legacy-pipeline" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/base" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl2" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/animation" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl2" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgpu" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-empty" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/skeletal-animation" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/sorting" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/rich-text" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/mask" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui-skew" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/light-probe" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/affine-transform" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle-2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/graphics" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-cannon" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-physx" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-ammo" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/3d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-framework" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-jsb" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-builtin" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-builtin" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-wasm" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/primitive" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/profiler" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/geometry-renderer" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/audio" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/video" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/xr" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/intersection-2d" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/terrain" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/webview" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tween" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tiled-map" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/light-probe" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/dragon-bones" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline-post-process" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/legacy-pipeline" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-24 12:49:10-debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-framework" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/spine" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/cc-fu/vendor-google" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/engine-export-to-editor/cc/editor/populate-internal-constants" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/f8/f84f3b58ec61e2a9c03c5f86bacdfd0d6a2a3647.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "cce:/internal/x/engine-export-to-editor/cc/editor/populate-internal-constants" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/60/60dfea53e1c17502cf4f0661946a964ad3ff0296.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/93/936a458df8c410426dcbc8860ee5a32e64530057.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/10/10f25daca5a1665545ebc4efbd45bd86fed6143e.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/de/de51ae29bdc33d4721612db5b322a18ddcf7dd5c.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register BuiltinPipelineSettings
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register DebugViewRuntimeControl
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/c1/c107986e77a987fe49f2e4b1c0dcc489194dbd3f.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js is not in module cache!
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/50/501ac7410841a88e6540edf8ac9ef6b58f44e840.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register Bullet
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register SoundManager
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/da/da56edff780e583578bcbe6fd7546a92ebf27b1e.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register AIPlayer
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/bf/bffa694b6a42ea502801eb3e252af84ebdf912e3.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register player
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/fa/fa9335eab9a9f1afdfe6746522dff0a52db7ffcf.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register PlayerManager
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/60/6031a6df382439e751708d8e1ff225136ce4b996.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register SceneTransition
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/7b/7b4b511aca644805dad617e0005683d5fa26b665.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/96/9627b0d9d58257818228bafb8d66e482f9701299.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/d0/d00576e9277e6b88b985f2cfb67d72a5b2888d19.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register GameOverPanel
2025-8-24 12:49:10-debug: [[Executor]] Register PaintManager
2025-8-24 12:49:10-debug: [[Executor]] Register GameHUD
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/f5/f5d0d9de8e98f9ba667b19cb4e3228cbc440561f.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register GameManager
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/52/5226b2c7ce6b19f4f28975e17d285b490ae9ad7c.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register AIController
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/2b/2bf461b36224b78bdc82ef905b4c406c53e98aa0.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register CameraFollow
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/6e/6e3db3d463ace5cac70cc212be5e384ebcc64b5f.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/75/754c8adada1366d19c52f9e4051a45ea2591d86e.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register CarProperties
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/3a/3a063ae6ce429763dd1200933f2c0af587c74ad8.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register CarPropertyDisplay
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/90/90df3cbac4939c24c528c1e36504a3db0c6ac7df.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register HealthBarUI
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/8d/8d571f891c0e57353a549f6c7f2dfb0cd3e6d844.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register MainMenuController
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/05/054d9434f81bde272393a693c0acd2cc7f26001f.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/03/0389729ada1c174ba688491f37fd362a910a7fc7.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Register PaintSpot
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register PausePanel
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/7d/7db7c970c834bcbcb366e98d02ffbb216840294d.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register PlayerInfoUI
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/1f/1f9fb870b3a7c6e0f41857918e51ef5c063f0494.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register PurchasePanel
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/09/097e43139f5ae17c456828e701fc64d3b48ad47c.js" loaded.
2025-8-24 12:49:10-debug: /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js resolved to /Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js is not in module cache!
2025-8-24 12:49:10-debug: [[Executor]] Register SelectManager
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/8c/8c188c8a6d0fb2c10532d0c76cb976b42adb07da.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-8-24 12:49:10-debug: [[Executor]] after unlock
2025-8-24 12:49:10-debug: Init bundle share assets start..., progress: 20%
2025-8-24 12:49:10-debug: Init bundle share assets start..., progress: 11%
2025-8-24 12:49:10-debug: Incremental keys: regeneratorRuntime
2025-8-24 12:49:10-debug: Init bundle share assets success..., progress: 11%
2025-8-24 12:49:10-debug: Init bundle share assets success..., progress: 20%
2025-8-24 12:49:10-debug: handle json group in bundle internal
2025-8-24 12:49:10-debug: init image compress task 0 in bundle internal
2025-8-24 12:49:10-debug: handle json group in bundle main success
2025-8-24 12:49:10-debug: handle json group in bundle resources
2025-8-24 12:49:10-debug: handle json group in bundle resources success
2025-8-24 12:49:10-debug: init image compress task 0 in bundle resources
2025-8-24 12:49:10-debug: init image compress task 0 in bundle main
2025-8-24 12:49:10-debug: handle json group in bundle internal success
2025-8-24 12:49:10-debug: handle json group in bundle main
2025-8-24 12:49:10-debug: // ---- build task 查询 Asset Bundle ---- (277ms)
2025-8-24 12:49:10-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 12:49:10-debug: [Build Memory track]: 查询 Asset Bundle start:260.90MB, end 206.38MB, increase: -55824.64KB
2025-8-24 12:49:10-debug: 查询 Asset Bundle start, progress: 16%
2025-8-24 12:49:10-log: run build task 查询 Asset Bundle success in 277 ms√, progress: 16%
2025-8-24 12:49:10-debug: // ---- build task 查询 Asset Bundle ---- (113ms)
2025-8-24 12:49:10-debug: [Build Memory track]: 查询 Asset Bundle start:206.41MB, end 205.49MB, increase: -943.02KB
2025-8-24 12:49:10-log: run build task 查询 Asset Bundle success in 113 ms√, progress: 21%
2025-8-24 12:49:10-debug: // ---- build task native：onAfterBundleDataTask ----
2025-8-24 12:49:10-debug: native:(onAfterBundleDataTask) start..., progress: 21%
2025-8-24 12:49:10-debug: native:(onAfterBundleDataTask) start..., progress: 20%
2025-8-24 12:49:10-debug: // ---- build task native：onAfterBundleDataTask ---- (74ms)
2025-8-24 12:49:10-debug: 打包脚本 start, progress: 21%
2025-8-24 12:49:10-debug: native:(onAfterBundleDataTask) in 74 ms ✓, progress: 21%
2025-8-24 12:49:10-debug: // ---- build task 打包脚本 ----
2025-8-24 12:49:10-debug: native:(onAfterBundleDataTask) in 74 ms ✓, progress: 27%
2025-8-24 12:49:10-log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-24 12:49:10-log: [build-script]enter sub process 94409, /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Frameworks/CocosCreator Helper (Renderer).app/Contents/MacOS/CocosCreator Helper (Renderer),/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/static/sub-process-index


2025-8-24 12:49:11-log: [build-script]Caught exception during build core-js: WebpackOptionsValidationError: Invalid configuration object. Webpack has been initialised using a configuration object that does not match the API schema.
 - configuration.entry should be an non-empty array.
   -> A non-empty array of non-empty strings
This may indicates the core-js polyfill is not necessary. See https://github.com/zloirock/core-js/issues/822


2025-8-24 12:49:11-debug: excute-script over with build-script 751ms
2025-8-24 12:49:11-debug: Generate systemJs..., progress: 21%
2025-8-24 12:49:11-log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-24 12:49:12-debug: excute-script over with build-script 836ms
2025-8-24 12:49:12-debug: 构建项目脚本 start..., progress: 21%
2025-8-24 12:49:12-debug: Build script in bundle start, progress: 27%
2025-8-24 12:49:12-debug: Build script in bundle start, progress: 21%
2025-8-24 12:49:12-log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-8-24 12:49:12-warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-24 12:49:12-warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts


2025-8-24 12:49:13-debug: excute-script over with build-script 1094ms
2025-8-24 12:49:13-debug: Copy externalScripts success!
2025-8-24 12:49:13-debug: Build script in bundle success, progress: 21%
2025-8-24 12:49:13-debug: Build script in bundle success, progress: 27%
2025-8-24 12:49:13-debug: 构建项目脚本 in (1251 ms) √, progress: 21%
2025-8-24 12:49:13-debug: 构建引擎脚本 start..., progress: 21%
2025-8-24 12:49:13-debug: mangleProperties is enabled, found engine-mangle-config.json, use it
2025-8-24 12:49:13-debug: Use cache engine: {link(/var/folders/2x/6k_688657tzc4t9l49jfmwdc0000gn/T/CocosCreator/3.8.6/builder/engine/861d47c70b6ce433c44778583bde2ccf)}
2025-8-24 12:49:13-debug: Use cache, md5String: debug=false,
sourceMaps=false,
includeModules=["2d",
"affine-transform",
"animation",
"audio",
"base",
"custom-pipeline",
"dragon-bones",
"graphics",
"intersection-2d",
"mask",
"particle-2d",
"physics-2d-box2d",
"profiler",
"rich-text",
"spine-3.8",
"tiled-map",
"tween",
"ui",
"video",
"webview",
"custom-pipeline-builtin-scripts"],
engineVersion="3.8.6",
platform="IOS",
split=undefined,
nativeCodeBundleMode="asmjs",
targets="chrome 80",
entry="/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine",
noDeprecatedFeatures=undefined,
loose=true,
assetURLFormat=undefined,
flags={"DEBUG":false,
"LOAD_BULLET_MANUALLY":false,
"LOAD_SPINE_MANUALLY":false},
preserveType=undefined,
wasmCompressionMode=false,
enableNamedRegisterForSystemJSModuleFormat=undefined,
mangleProperties=false,
inlineEnum=true,


2025-8-24 12:49:13-debug: Use cache, options: {
  "debug": false,
  "mangleProperties": false,
  "inlineEnum": true,
  "sourceMaps": false,
  "includeModules": [
    "2d",
    "affine-transform",
    "animation",
    "audio",
    "base",
    "custom-pipeline",
    "dragon-bones",
    "graphics",
    "intersection-2d",
    "mask",
    "particle-2d",
    "physics-2d-box2d",
    "profiler",
    "rich-text",
    "spine-3.8",
    "tiled-map",
    "tween",
    "ui",
    "video",
    "webview",
    "custom-pipeline-builtin-scripts"
  ],
  "engineVersion": "3.8.6",
  "md5Map": [],
  "engineName": "src/cocos-js",
  "platform": "IOS",
  "useCache": true,
  "nativeCodeBundleMode": "asmjs",
  "wasmCompressionMode": false,
  "output": "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/data/src/cocos-js",
  "targets": "chrome 80",
  "flags": {
    "DEBUG": false,
    "LOAD_BULLET_MANUALLY": false,
    "LOAD_SPINE_MANUALLY": false
  },
  "entry": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine"
}

2025-8-24 12:49:13-debug: Copy plugin script ..., progress: 21%
2025-8-24 12:49:13-debug: 构建引擎脚本 in (138 ms) √, progress: 21%
2025-8-24 12:49:13-debug: Generate import-map..., progress: 21%
2025-8-24 12:49:13-debug: // ---- build task 打包脚本 ---- (3087ms)
2025-8-24 12:49:13-log: run build task 打包脚本 success in 3 s√, progress: 26%
2025-8-24 12:49:13-debug: [Build Memory track]: 打包脚本 start:206.68MB, end 211.41MB, increase: 4.73MB
2025-8-24 12:49:13-debug: Build Assets start, progress: 26%
2025-8-24 12:49:13-debug: // ---- build task Build Assets ----
2025-8-24 12:49:13-debug: Build bundles..., progress: 26%
2025-8-24 12:49:13-debug: Pack Images start, progress: 26%
2025-8-24 12:49:13-debug: Pack Images start, progress: 27%
2025-8-24 12:49:13-debug: builder:pack-auto-atlas-image (144ms)
2025-8-24 12:49:13-debug: Compress image start..., progress: 27%
2025-8-24 12:49:13-debug: sort compress task {}
2025-8-24 12:49:13-debug: Pack Images success, progress: 26%
2025-8-24 12:49:13-debug: No image need to compress
2025-8-24 12:49:13-debug: Compress image success..., progress: 26%
2025-8-24 12:49:13-debug: Compress image success..., progress: 27%
2025-8-24 12:49:13-debug: Output asset in bundles start, progress: 26%
2025-8-24 12:49:13-debug: Output asset in bundles start, progress: 27%
2025-8-24 12:49:13-debug: Handle all json groups in bundle internal
2025-8-24 12:49:13-debug: handle json group
2025-8-24 12:49:13-debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-8-24 12:49:13-debug: Handle all json groups in bundle resources
2025-8-24 12:49:13-debug: Pack Images success, progress: 27%
2025-8-24 12:49:13-debug: Handle all json groups in bundle main
2025-8-24 12:49:13-debug: Compress image start..., progress: 26%
2025-8-24 12:49:13-debug: handle json group
2025-8-24 12:49:13-debug: handle json group
2025-8-24 12:49:13-debug: Json group(05b737039) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(06585a170) compile success，json number: 6
2025-8-24 12:49:13-debug: handle single json
2025-8-24 12:49:13-debug: Json group(0eafb1115) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(0ca5151ba) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(0badfa8c2) compile success，json number: 6
2025-8-24 12:49:13-debug: handle single json
2025-8-24 12:49:13-debug: Json group(01959b579) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(09bd04adc) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(09b90c6a5) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(0bc3ecf13) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(0d882e0be) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(0c2a51634) compile success，json number: 6
2025-8-24 12:49:13-debug: Json group(0ea25dec6) compile success，json number: 6
2025-8-24 12:49:13-debug: handle single json
2025-8-24 12:49:13-debug: Json group(0e09c4e9e) compile success，json number: 6
2025-8-24 12:49:13-debug: Output asset in bundles success, progress: 26%
2025-8-24 12:49:13-debug: Output asset in bundles success, progress: 27%
2025-8-24 12:49:13-debug: Output asset in bundles start, progress: 27%
2025-8-24 12:49:13-debug: Output asset in bundles start, progress: 26%
2025-8-24 12:49:13-debug: compress config of bundle main...
2025-8-24 12:49:13-debug: output config of bundle internal
2025-8-24 12:49:13-debug: compress config of bundle main success
2025-8-24 12:49:13-debug: output config of bundle internal success
2025-8-24 12:49:13-debug: output config of bundle resources success
2025-8-24 12:49:13-debug: compress config of bundle internal success
2025-8-24 12:49:13-debug: output config of bundle main
2025-8-24 12:49:13-debug: output config of bundle main success
2025-8-24 12:49:13-debug: Output asset in bundles success, progress: 26%
2025-8-24 12:49:13-debug: Output asset in bundles success, progress: 27%
2025-8-24 12:49:13-debug: compress config of bundle resources success
2025-8-24 12:49:13-debug: compress config of bundle resources...
2025-8-24 12:49:13-debug: compress config of bundle internal...
2025-8-24 12:49:13-debug: output config of bundle resources
2025-8-24 12:49:14-debug: // ---- build task Build Assets ---- (500ms)
2025-8-24 12:49:14-log: run build task Build Assets success in 500 ms√, progress: 31%
2025-8-24 12:49:14-debug: ios:(onAfterBuildAssets) start..., progress: 31%
2025-8-24 12:49:14-debug: // ---- build task ios：onAfterBuildAssets ----
2025-8-24 12:49:14-debug: [Build Memory track]: Build Assets start:211.44MB, end 210.88MB, increase: -576.93KB
2025-8-24 12:49:14-debug: // ---- build task ios：onAfterBuildAssets ---- (74ms)
2025-8-24 12:49:14-debug: ios:(onAfterBuildAssets) in 74 ms ✓, progress: 33%
2025-8-24 12:49:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 33%
2025-8-24 12:49:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 12:49:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (74ms)
2025-8-24 12:49:14-log: run build task 整理部分构建选项内数据到 settings.json success in 74 ms√, progress: 34%
2025-8-24 12:49:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.00MB, end 212.99MB, increase: 1019.66KB
2025-8-24 12:49:14-debug: 填充脚本数据到 settings.json start, progress: 34%
2025-8-24 12:49:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-24 12:49:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (75ms)
2025-8-24 12:49:14-log: run build task 填充脚本数据到 settings.json success in 75 ms√, progress: 36%
2025-8-24 12:49:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.03MB, end 214.04MB, increase: 1.01MB
2025-8-24 12:49:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 36%
2025-8-24 12:49:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 12:49:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (404ms)
2025-8-24 12:49:14-debug: // ---- build task ios：onBeforeCompressSettings ----
2025-8-24 12:49:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.07MB, end 209.07MB, increase: -5123.27KB
2025-8-24 12:49:14-debug: ios:(onBeforeCompressSettings) start..., progress: 38%
2025-8-24 12:49:14-log: run build task 整理部分构建选项内数据到 settings.json success in 404 ms√, progress: 38%
2025-8-24 12:49:14-debug: // ---- build task ios：onBeforeCompressSettings ---- (82ms)
2025-8-24 12:49:14-debug: ios:(onBeforeCompressSettings) in 82 ms ✓, progress: 40%
2025-8-24 12:49:14-debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-8-24 12:49:14-debug: cocos-service:(onBeforeCompressSettings) start..., progress: 40%
2025-8-24 12:49:15-debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (276ms)
2025-8-24 12:49:15-debug: cocos-service:(onBeforeCompressSettings) in 276 ms ✓, progress: 41%
2025-8-24 12:49:15-debug: 整理静态模板文件 start, progress: 41%
2025-8-24 12:49:15-debug: // ---- build task 整理静态模板文件 ----
2025-8-24 12:49:15-debug: // ---- build task 整理静态模板文件 ---- (120ms)
2025-8-24 12:49:15-debug: [Build Memory track]: 整理静态模板文件 start:211.69MB, end 209.11MB, increase: -2640.57KB
2025-8-24 12:49:15-debug: // ---- build task native：onAfterCompressSettings ----
2025-8-24 12:49:15-debug: native:(onAfterCompressSettings) start..., progress: 46%
2025-8-24 12:49:15-log: run build task 整理静态模板文件 success in 120 ms√, progress: 46%
2025-8-24 12:49:15-log: Checking template version...
2025-8-24 12:49:15-log: Validating template consistency...
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/120.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/29.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/114.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/1024.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/40.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/57.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/58.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/60.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/87.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/80.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png
2025-8-24 12:49:15-log: To avoid this warning, set field 'skipCheck' in cocos-version.json to true.
2025-8-24 12:49:15-log: Consider fix the problem or remove the directory
2025-8-24 12:49:15-log: Failed to validate "native" directory
2025-8-24 12:49:15-log: Validating platform source code directories...
2025-8-24 12:49:15-log: Following files are missing
2025-8-24 12:49:15-debug: generateCMakeConfig, {"CC_USE_GLES3":"set(CC_USE_GLES3 OFF)","CC_USE_GLES2":"set(CC_USE_GLES2 OFF)","USE_SERVER_MODE":"set(USE_SERVER_MODE OFF)","NET_MODE":"set(NET_MODE 0)","XXTEAKEY":"","CC_ENABLE_SWAPPY":"set(CC_ENABLE_SWAPPY OFF)","APP_NAME":"set(APP_NAME \"SuperSplash\")","COCOS_X_PATH":"set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")","USE_JOB_SYSTEM_TASKFLOW":"set(USE_JOB_SYSTEM_TASKFLOW OFF)","USE_JOB_SYSTEM_TBB":"set(USE_JOB_SYSTEM_TBB OFF)","ENABLE_FLOAT_OUTPUT":"set(ENABLE_FLOAT_OUTPUT OFF)","USE_PHYSICS_PHYSX":"set(USE_PHYSICS_PHYSX OFF)","USE_BOX2D_JSB":"set(USE_BOX2D_JSB OFF)","USE_OCCLUSION_QUERY":"set(USE_OCCLUSION_QUERY OFF)","USE_GEOMETRY_RENDERER":"set(USE_GEOMETRY_RENDERER OFF)","USE_DEBUG_RENDERER":"set(USE_DEBUG_RENDERER OFF)","USE_AUDIO":"set(USE_AUDIO ON)","USE_VIDEO":"set(USE_VIDEO ON)","USE_WEBVIEW":"set(USE_WEBVIEW ON)","USE_SOCKET":"set(USE_SOCKET OFF)","USE_WEBSOCKET_SERVER":"set(USE_WEBSOCKET_SERVER OFF)","USE_VENDOR":"set(USE_VENDOR OFF)","USE_SPINE_3_8":"set(USE_SPINE_3_8 ON)","USE_SPINE_4_2":"set(USE_SPINE_4_2 OFF)","USE_DRAGONBONES":"set(USE_DRAGONBONES ON)","CC_USE_METAL":"set(CC_USE_METAL ON)","MACOSX_BUNDLE_GUI_IDENTIFIER":"set(MACOSX_BUNDLE_GUI_IDENTIFIER com.rio.supersplsh)","DEVELOPMENT_TEAM":"set(DEVELOPMENT_TEAM UWR5Y8Y7U8)","TARGET_IOS_VERSION":"set(TARGET_IOS_VERSION 15.0)","USE_PORTRAIT":"set(USE_PORTRAIT OFF)","CUSTOM_COPY_RESOURCE_HOOK":"set(CUSTOM_COPY_RESOURCE_HOOK OFF)","CC_EXECUTABLE_NAME":"set(CC_EXECUTABLE_NAME \"SuperSplash\")"}
2025-8-24 12:49:15-log:   /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Images.xcassets/AppIcon.appiconset/180.png
2025-8-24 12:49:15-log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.cpp
2025-8-24 12:49:15-log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/main.m
2025-8-24 12:49:15-log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/settings.gradle
2025-8-24 12:49:15-log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/app/build.gradle
2025-8-24 12:49:15-log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/instantapp/build.gradle
2025-8-24 12:49:15-log: While replace template content, file /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/entry/src/main/config.json
2025-8-24 12:49:15-debug: // ---- build task native：onAfterCompressSettings ---- (98ms)
2025-8-24 12:49:15-debug: native:(onAfterCompressSettings) in 98 ms ✓, progress: 48%
2025-8-24 12:49:15-debug: cocos-service:(onAfterCompressSettings) start..., progress: 48%
2025-8-24 12:49:15-debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-8-24 12:49:15-debug: // ---- build task cocos-service：onAfterCompressSettings ---- (215ms)
2025-8-24 12:49:15-debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-8-24 12:49:15-debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-8-24 12:49:15-debug: cocos-service:(onAfterCompressSettings) in 215 ms ✓, progress: 50%
2025-8-24 12:49:15-debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (77ms)
2025-8-24 12:49:15-log: run build task 给所有的资源加上 MD5 后缀 success in 77 ms√, progress: 60%
2025-8-24 12:49:15-debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:212.57MB, end 213.60MB, increase: 1.03MB
2025-8-24 12:49:15-debug: native:(onAfterBuild) start..., progress: 60%
2025-8-24 12:49:15-debug: // ---- build task native：onAfterBuild ----
2025-8-24 12:49:15-log: [xcode-select] /Applications/Xcode.app/Contents/Developer


2025-8-24 12:49:15-log: run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"
2025-8-24 12:49:18-log: [cmake] -- The CXX compiler identification is AppleClang 16.0.0.16000026


2025-8-24 12:49:18-log: [cmake] -- Detecting CXX compiler ABI info


2025-8-24 12:49:19-log: [cmake] -- Detecting CXX compiler ABI info - done


2025-8-24 12:49:19-log: [cmake] -- Check for working CXX compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ - skipped


2025-8-24 12:49:19-log: [cmake] -- Detecting CXX compile features


2025-8-24 12:49:19-log: [cmake] -- Detecting CXX compile features - done


2025-8-24 12:49:20-log: [cmake] -- The C compiler identification is AppleClang 16.0.0.16000026


2025-8-24 12:49:20-log: [cmake] -- The ASM compiler identification is Clang with GNU-like command-line


2025-8-24 12:49:20-log: [cmake] -- Found assembler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang


2025-8-24 12:49:20-log: [cmake] -- Detecting C compiler ABI info


2025-8-24 12:49:21-log: [cmake] -- Detecting C compiler ABI info - done


2025-8-24 12:49:21-log: [cmake] -- Check for working C compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang - skipped


2025-8-24 12:49:21-log: [cmake] -- Detecting C compile features


2025-8-24 12:49:21-log: [cmake] -- Detecting C compile features - done


2025-8-24 12:49:21-log: [cmake] -- platform: iOS


2025-8-24 12:49:21-log: [cmake] -- Ignore NO_WERROR


2025-8-24 12:49:21-log: [cmake] -- OPTION BUILTIN_COCOS_X_PATH:	
-- OPTION USE_BUILTIN_EXTERNAL:	OFF
-- OPTION USE_MODULES:	OFF


2025-8-24 12:49:21-log: [cmake] -- OPTION CC_USE_METAL:	ON
-- OPTION CC_USE_GLES3:	OFF
-- OPTION CC_USE_GLES2:	OFF
-- OPTION CC_USE_VULKAN:	OFF
-- OPTION CC_DEBUG_FORCE:	OFF
-- OPTION USE_SE_V8:	ON
-- OPTION USE_SE_JSVM:	OFF
-- OPTION USE_V8_DEBUGGER:	ON
-- OPTION USE_V8_DEBUGGER_FORCE:	OFF
-- OPTION USE_SE_SM:	OFF
-- OPTION USE_SOCKET:	OFF
-- OPTION USE_AUDIO:	ON
-- OPTION USE_EDIT_BOX:	ON
-- OPTION USE_VIDEO:	ON
-- OPTION USE_WEBVIEW:	ON
-- OPTION USE_MIDDLEWARE:	ON
-- OPTION USE_DRAGONBONES:	ON
-- OPTION USE_SPINE:	ON
-- OPTION USE_SPINE_3_8:	ON
-- OPTION USE_SPINE_4_2:	OFF
-- OPTION USE_WEBSOCKET_SERVER:	OFF
-- OPTION USE_PHYSICS_PHYSX:	OFF
-- OPTION USE_JOB_SYSTEM_TBB:	OFF
-- OPTION USE_JOB_SYSTEM_TASKFLOW:	OFF
-- OPTION USE_XR:	OFF


2025-8-24 12:49:21-log: [cmake] -- OPTION USE_SERVER_MODE:	OFF
-- OPTION USE_AR_MODULE:	OFF
-- OPTION USE_AR_AUTO:	OFF
-- OPTION USE_AR_CORE:	OFF
-- OPTION USE_AR_ENGINE:	OFF
-- OPTION USE_CCACHE:	
-- OPTION CCACHE_EXECUTABLE:	CCACHE_EXECUTABLE-NOTFOUND
-- OPTION NODE_EXECUTABLE:	/opt/homebrew/Cellar/node/24.5.0/bin/node
-- OPTION NET_MODE:	0
-- OPTION USE_REMOTE_LOG:	OFF
-- OPTION USE_BOX2D_JSB:	OFF


2025-8-24 12:49:21-log: [cmake] -- platform path: 


2025-8-24 12:49:21-log: [cmake] -- Using Xcode 15 or newer, adding extra link flags: -Wl,-ld_classic.


2025-8-24 12:49:21-error: [cmake-err] CMake Error: File /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundLandscape.png does not exist.
CMake Error at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/templates/cmake/apple.cmake:47 (configure_file):
  configure_file Problem configuring file
Call Stack (most recent call first):
  CMakeLists.txt:18 (cc_ios_before_target)



2025-8-24 12:49:21-log: [cmake] -- Try generating /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/Pre-AutoLoadPlulgins.cmake


2025-8-24 12:49:21-log: [cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js


2025-8-24 12:49:21-log: [cmake] [searching plugins] no plugins found!


2025-8-24 12:49:21-log: [cmake] -- Searching hook files Pre*.cmake or *Pre.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios


2025-8-24 12:49:21-log: [cmake] -- ::Loading Pre-service.cmake


2025-8-24 12:49:21-log: [cmake] -- No plugins are loaded!


2025-8-24 12:49:21-log: [cmake] -- Searching hook files Post*.cmake or *Post.cmake in /Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios
-- ::Loading Post-service.cmake


2025-8-24 12:49:21-log: [cmake] -- Configuring incomplete, errors occurred!
See also "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeOutput.log".
See also "/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj/CMakeFiles/CMakeError.log".


2025-8-24 12:49:21-error: 构建插件 native 的钩子函数 onAfterBuild 执行失败，请检查插件的代码逻辑~, progress: 62%
2025-8-24 12:49:21-debug: Error: run cmake failed "cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"", code: 1, signal: null, progress: 64%
2025-8-24 12:49:21-debug: [Build Memory track]: builder:build-project-total start:256.16MB, end 213.14MB, increase: -44053.52KB
2025-8-24 12:49:21-log: Asset DB is resume!
2025-8-24 12:49:21-debug: builder:build-project-total (13125ms)
2025-8-24 12:49:21-debug: build success in 13125!
2025-8-24 12:49:21-debug: Stop record console. {file(/Users/<USER>/projects/cocos_project/SuperSplash/temp/builder/log/ios8-24-2025 12-49.log)}
2025-8-24 12:49:21-debug: ================================ build Task (ios) Finished in (13 s)ms ================================
2025-8-24 12:49:21-error: Error: run cmake failed "cmake -S "/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios" -GXcode -B"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR="/Users/<USER>/projects/cocos_project/SuperSplash/build/ios" -DAPP_NAME="SuperSplash" -DLAUNCH_TYPE="Release"", code: 1, signal: null
    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)
    at ChildProcess.emit (node:events:519:28)
    at maybeClose (node:internal/child_process:1105:16)
    at Process.ChildProcess._handle.onexit (node:internal/child_process:305:5)
2025-8-24 12:53:32-debug: refresh db internal success
2025-8-24 12:53:32-debug: refresh db assets success
2025-8-24 12:53:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 12:53:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 12:53:32-debug: asset-db:refresh-all-database (48ms)
2025-8-24 12:54:09-debug: refresh db internal success
2025-8-24 12:54:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 12:54:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 12:54:09-debug: refresh db assets success
2025-8-24 12:54:09-debug: asset-db:refresh-all-database (28ms)
2025-8-24 12:54:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-24 12:57:03-debug: refresh db internal success
2025-8-24 12:57:03-debug: refresh db assets success
2025-8-24 12:57:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 12:57:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 12:57:03-debug: asset-db:refresh-all-database (38ms)
2025-8-24 12:57:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-24 12:57:19-debug: refresh db internal success
2025-8-24 12:57:19-debug: refresh db assets success
2025-8-24 12:57:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 12:57:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 12:57:19-debug: asset-db:refresh-all-database (30ms)
2025-8-24 12:57:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-24 12:57:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-24 13:03:01-debug: refresh db internal success
2025-8-24 13:03:01-debug: refresh db assets success
2025-8-24 13:03:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 13:03:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 13:03:01-debug: asset-db:refresh-all-database (35ms)
2025-8-24 13:03:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-24 13:03:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
