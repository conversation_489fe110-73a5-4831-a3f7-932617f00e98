2025-8-24 01:00:41-log: Cannot access game frame or container.
2025-8-24 01:00:41-debug: asset-db:require-engine-code (369ms)
2025-8-24 01:00:41-log: [box2d]:box2d wasm lib loaded.
2025-8-24 01:00:41-log: meshopt wasm decoder initialized
2025-8-24 01:00:41-log: [bullet]:bullet wasm lib loaded.
2025-8-24 01:00:41-log: Cocos Creator v3.8.6
2025-8-24 01:00:41-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.88MB, end 88.54MB, increase: 7.66MB
2025-8-24 01:00:42-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.57MB, end 224.14MB, increase: 135.57MB
2025-8-24 01:00:41-log: Forward render pipeline initialized.
2025-8-24 01:00:42-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.35MB, end 227.19MB, increase: 2.85MB
2025-8-24 01:00:42-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.75MB, end 227.36MB, increase: 146.61MB
2025-8-24 01:00:42-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.20MB, end 227.69MB, increase: 147.50MB
2025-8-24 01:00:41-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.20MB, end 80.04MB, increase: 50.84MB
2025-8-24 01:00:41-log: Using legacy pipeline
2025-8-24 01:00:42-debug: run package(channel-upload-tools) handler(enable) start
2025-8-24 01:00:42-debug: run package(google-play) handler(enable) start
2025-8-24 01:00:42-debug: run package(google-play) handler(enable) success!
2025-8-24 01:00:42-debug: run package(harmonyos-next) handler(enable) start
2025-8-24 01:00:42-debug: run package(harmonyos-next) handler(enable) success!
2025-8-24 01:00:42-debug: run package(fb-instant-games) handler(enable) start
2025-8-24 01:00:42-debug: run package(fb-instant-games) handler(enable) success!
2025-8-24 01:00:42-debug: run package(huawei-agc) handler(enable) start
2025-8-24 01:00:42-debug: run package(huawei-agc) handler(enable) success!
2025-8-24 01:00:42-debug: run package(huawei-quick-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(honor-mini-game) handler(enable) success!
2025-8-24 01:00:42-debug: run package(ios) handler(enable) start
2025-8-24 01:00:42-debug: run package(ios) handler(enable) success!
2025-8-24 01:00:42-debug: run package(linux) handler(enable) start
2025-8-24 01:00:42-debug: run package(linux) handler(enable) success!
2025-8-24 01:00:42-debug: run package(mac) handler(enable) start
2025-8-24 01:00:42-debug: run package(mac) handler(enable) success!
2025-8-24 01:00:42-debug: run package(honor-mini-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(channel-upload-tools) handler(enable) success!
2025-8-24 01:00:42-debug: run package(huawei-quick-game) handler(enable) success!
2025-8-24 01:00:42-debug: run package(migu-mini-game) handler(enable) success!
2025-8-24 01:00:42-debug: run package(migu-mini-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(ohos) handler(enable) success!
2025-8-24 01:00:42-debug: run package(oppo-mini-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-24 01:00:42-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-24 01:00:42-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-24 01:00:42-debug: run package(taobao-mini-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-24 01:00:42-debug: run package(vivo-mini-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-24 01:00:42-debug: run package(web-desktop) handler(enable) start
2025-8-24 01:00:42-debug: run package(web-desktop) handler(enable) success!
2025-8-24 01:00:42-debug: run package(web-mobile) handler(enable) start
2025-8-24 01:00:42-debug: run package(web-mobile) handler(enable) success!
2025-8-24 01:00:42-debug: run package(wechatgame) handler(enable) start
2025-8-24 01:00:42-debug: run package(wechatgame) handler(enable) success!
2025-8-24 01:00:42-debug: run package(wechatprogram) handler(enable) start
2025-8-24 01:00:42-debug: run package(wechatprogram) handler(enable) success!
2025-8-24 01:00:42-debug: run package(windows) handler(enable) start
2025-8-24 01:00:42-debug: run package(windows) handler(enable) success!
2025-8-24 01:00:42-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-24 01:00:42-debug: run package(ohos) handler(enable) start
2025-8-24 01:00:42-debug: run package(cocos-service) handler(enable) start
2025-8-24 01:00:42-debug: run package(cocos-service) handler(enable) success!
2025-8-24 01:00:42-debug: run package(im-plugin) handler(enable) start
2025-8-24 01:00:42-debug: run package(im-plugin) handler(enable) success!
2025-8-24 01:00:42-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-24 01:00:42-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-24 01:00:42-debug: run package(placeholder) handler(enable) start
2025-8-24 01:00:42-debug: run package(placeholder) handler(enable) success!
2025-8-24 01:00:42-debug: asset-db:worker-init: initPlugin (1017ms)
2025-8-24 01:00:42-debug: run package(native) handler(enable) start
2025-8-24 01:00:42-debug: run package(native) handler(enable) success!
2025-8-24 01:00:42-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-24 01:00:42-debug: [Assets Memory track]: asset-db:worker-init start:29.19MB, end 225.18MB, increase: 195.98MB
2025-8-24 01:00:42-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-24 01:00:42-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-24 01:00:42-debug: Run asset db hook programming:beforePreStart success!
2025-8-24 01:00:42-debug: Run asset db hook programming:beforePreStart ...
2025-8-24 01:00:42-debug: asset-db:worker-init (1471ms)
2025-8-24 01:00:42-debug: asset-db-hook-programming-beforePreStart (36ms)
2025-8-24 01:00:42-debug: asset-db-hook-engine-extends-beforePreStart (36ms)
2025-8-24 01:00:42-debug: Preimport db internal success
2025-8-24 01:00:42-debug: Preimport db assets success
2025-8-24 01:00:42-debug: Run asset db hook programming:afterPreStart ...
2025-8-24 01:00:42-debug: starting packer-driver...
2025-8-24 01:00:42-debug: initialize scripting environment...
2025-8-24 01:00:42-debug: [[Executor]] prepare before lock
2025-8-24 01:00:42-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-24 01:00:42-debug: [[Executor]] prepare after unlock
2025-8-24 01:00:43-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.19MB, end 230.66MB, increase: 5.47MB
2025-8-24 01:00:43-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-24 01:00:42-debug: Run asset db hook programming:afterPreStart success!
2025-8-24 01:00:43-debug: Start up the 'internal' database...
2025-8-24 01:00:43-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-24 01:00:43-debug: asset-db-hook-programming-afterPreStart (272ms)
2025-8-24 01:00:43-debug: asset-db:worker-effect-data-processing (147ms)
2025-8-24 01:00:43-debug: asset-db-hook-engine-extends-afterPreStart (148ms)
2025-8-24 01:00:43-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:225.27MB, end 242.90MB, increase: 17.63MB
2025-8-24 01:00:43-debug: Start up the 'assets' database...
2025-8-24 01:00:43-debug: asset-db:worker-startup-database[internal] (385ms)
2025-8-24 01:00:43-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:227.61MB, end 241.59MB, increase: 13.98MB
2025-8-24 01:00:43-debug: lazy register asset handler json
2025-8-24 01:00:43-debug: lazy register asset handler text
2025-8-24 01:00:43-debug: lazy register asset handler spine-data
2025-8-24 01:00:43-debug: lazy register asset handler dragonbones
2025-8-24 01:00:43-debug: lazy register asset handler dragonbones-atlas
2025-8-24 01:00:43-debug: lazy register asset handler terrain
2025-8-24 01:00:43-debug: lazy register asset handler javascript
2025-8-24 01:00:43-debug: lazy register asset handler typescript
2025-8-24 01:00:43-debug: lazy register asset handler scene
2025-8-24 01:00:43-debug: lazy register asset handler prefab
2025-8-24 01:00:43-debug: lazy register asset handler sprite-frame
2025-8-24 01:00:43-debug: lazy register asset handler tiled-map
2025-8-24 01:00:43-debug: lazy register asset handler buffer
2025-8-24 01:00:43-debug: lazy register asset handler image
2025-8-24 01:00:43-debug: lazy register asset handler sign-image
2025-8-24 01:00:43-debug: lazy register asset handler alpha-image
2025-8-24 01:00:43-debug: lazy register asset handler directory
2025-8-24 01:00:43-debug: lazy register asset handler texture-cube
2025-8-24 01:00:43-debug: lazy register asset handler *
2025-8-24 01:00:43-debug: lazy register asset handler render-texture
2025-8-24 01:00:43-debug: lazy register asset handler texture-cube-face
2025-8-24 01:00:43-debug: lazy register asset handler rt-sprite-frame
2025-8-24 01:00:43-debug: lazy register asset handler gltf
2025-8-24 01:00:43-debug: lazy register asset handler gltf-mesh
2025-8-24 01:00:43-debug: lazy register asset handler gltf-animation
2025-8-24 01:00:43-debug: lazy register asset handler gltf-skeleton
2025-8-24 01:00:43-debug: lazy register asset handler gltf-material
2025-8-24 01:00:43-debug: lazy register asset handler gltf-scene
2025-8-24 01:00:43-debug: lazy register asset handler gltf-embeded-image
2025-8-24 01:00:43-debug: lazy register asset handler fbx
2025-8-24 01:00:43-debug: lazy register asset handler material
2025-8-24 01:00:43-debug: lazy register asset handler physics-material
2025-8-24 01:00:43-debug: lazy register asset handler texture
2025-8-24 01:00:43-debug: lazy register asset handler effect-header
2025-8-24 01:00:43-debug: lazy register asset handler audio-clip
2025-8-24 01:00:43-debug: lazy register asset handler animation-clip
2025-8-24 01:00:43-debug: lazy register asset handler animation-graph
2025-8-24 01:00:43-debug: lazy register asset handler effect
2025-8-24 01:00:43-debug: lazy register asset handler ttf-font
2025-8-24 01:00:43-debug: lazy register asset handler animation-graph-variant
2025-8-24 01:00:43-debug: lazy register asset handler animation-mask
2025-8-24 01:00:43-debug: lazy register asset handler particle
2025-8-24 01:00:43-debug: lazy register asset handler sprite-atlas
2025-8-24 01:00:43-debug: lazy register asset handler auto-atlas
2025-8-24 01:00:43-debug: lazy register asset handler bitmap-font
2025-8-24 01:00:43-debug: [Assets Memory track]: asset-db:worker-init: startup start:230.61MB, end 241.60MB, increase: 10.99MB
2025-8-24 01:00:43-debug: lazy register asset handler label-atlas
2025-8-24 01:00:43-debug: lazy register asset handler render-flow
2025-8-24 01:00:43-debug: lazy register asset handler instantiation-material
2025-8-24 01:00:43-debug: lazy register asset handler erp-texture-cube
2025-8-24 01:00:43-debug: lazy register asset handler render-pipeline
2025-8-24 01:00:43-debug: lazy register asset handler instantiation-animation
2025-8-24 01:00:43-debug: lazy register asset handler video-clip
2025-8-24 01:00:43-debug: lazy register asset handler instantiation-skeleton
2025-8-24 01:00:43-debug: lazy register asset handler render-stage
2025-8-24 01:00:43-debug: lazy register asset handler instantiation-mesh
2025-8-24 01:00:43-debug: asset-db:worker-startup-database[assets] (356ms)
2025-8-24 01:00:43-debug: asset-db:start-database (410ms)
2025-8-24 01:00:43-debug: asset-db:ready (2954ms)
2025-8-24 01:00:43-debug: fix the bug of updateDefaultUserData
2025-8-24 01:00:43-debug: init worker message success
2025-8-24 01:00:43-debug: programming:execute-script (3ms)
2025-8-24 01:00:43-debug: [Build Memory track]: builder:worker-init start:244.69MB, end 256.32MB, increase: 11.63MB
2025-8-24 01:00:43-debug: builder:worker-init (193ms)
2025-8-24 01:00:45-debug: refresh db internal success
2025-8-24 01:00:45-debug: refresh db assets success
2025-8-24 01:00:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-24 01:00:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-24 01:00:45-debug: asset-db:refresh-all-database (35ms)
2025-8-24 01:00:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-24 01:00:47-debug: Query all assets info in project
2025-8-24 01:00:47-debug: Skip compress image, progress: 0%
2025-8-24 01:00:47-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-24 01:00:47-debug: Init all bundles start..., progress: 0%
2025-8-24 01:00:47-debug: 查询 Asset Bundle start, progress: 0%
2025-8-24 01:00:47-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 01:00:47-debug: Num of bundles: 3..., progress: 0%
2025-8-24 01:00:47-debug: Init bundle root assets start..., progress: 0%
2025-8-24 01:00:47-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-24 01:00:47-debug:   Number of all scenes: 3
2025-8-24 01:00:47-debug: Init bundle root assets success..., progress: 0%
2025-8-24 01:00:47-debug:   Number of all scripts: 28
2025-8-24 01:00:47-debug:   Number of other assets: 610
2025-8-24 01:00:47-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-24 01:00:47-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 01:00:47-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-8-24 01:00:47-debug: [Build Memory track]: 查询 Asset Bundle start:256.80MB, end 257.84MB, increase: 1.04MB
2025-8-24 01:00:47-debug: 查询 Asset Bundle start, progress: 5%
2025-8-24 01:00:47-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-24 01:00:47-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-8-24 01:00:47-debug: [Build Memory track]: 查询 Asset Bundle start:257.88MB, end 258.02MB, increase: 144.48KB
2025-8-24 01:00:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-24 01:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 01:00:47-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-24 01:00:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:258.05MB, end 258.07MB, increase: 19.38KB
2025-8-24 01:00:47-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-24 01:00:47-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-24 01:00:47-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-24 01:00:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-24 01:00:47-debug: [Build Memory track]: 填充脚本数据到 settings.json start:258.11MB, end 258.13MB, increase: 18.88KB
2025-8-24 01:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 01:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-24 01:00:47-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-8-24 01:00:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:258.16MB, end 258.38MB, increase: 222.31KB
2025-8-24 01:00:49-debug: Query all assets info in project
2025-8-24 01:00:49-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-24 01:00:49-debug: Skip compress image, progress: 0%
2025-8-24 01:00:49-debug: Init all bundles start..., progress: 0%
2025-8-24 01:00:49-debug: Num of bundles: 3..., progress: 0%
2025-8-24 01:00:49-debug: Init bundle root assets start..., progress: 0%
2025-8-24 01:00:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 01:00:49-debug: 查询 Asset Bundle start, progress: 0%
2025-8-24 01:00:49-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-24 01:00:49-debug: Init bundle root assets success..., progress: 0%
2025-8-24 01:00:49-debug:   Number of all scripts: 28
2025-8-24 01:00:49-debug:   Number of other assets: 610
2025-8-24 01:00:49-debug:   Number of all scenes: 3
2025-8-24 01:00:49-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-24 01:00:49-debug: [Build Memory track]: 查询 Asset Bundle start:259.49MB, end 254.35MB, increase: -5256.77KB
2025-8-24 01:00:49-debug: 查询 Asset Bundle start, progress: 5%
2025-8-24 01:00:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 01:00:49-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 5%
2025-8-24 01:00:49-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-8-24 01:00:49-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-24 01:00:49-debug: [Build Memory track]: 查询 Asset Bundle start:254.39MB, end 254.52MB, increase: 139.06KB
2025-8-24 01:00:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-24 01:00:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 01:00:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-24 01:00:49-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-24 01:00:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:254.78MB, end 254.60MB, increase: -181.11KB
2025-8-24 01:00:49-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-24 01:00:49-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-24 01:00:49-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-24 01:00:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-24 01:00:49-debug: [Build Memory track]: 填充脚本数据到 settings.json start:254.63MB, end 254.87MB, increase: 242.38KB
2025-8-24 01:00:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 01:00:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-24 01:00:49-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-8-24 01:00:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:254.69MB, end 254.90MB, increase: 220.00KB
2025-8-24 01:00:50-debug: Query all assets info in project
2025-8-24 01:00:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-24 01:00:50-debug: Skip compress image, progress: 0%
2025-8-24 01:00:50-debug: Init all bundles start..., progress: 0%
2025-8-24 01:00:50-debug: 查询 Asset Bundle start, progress: 0%
2025-8-24 01:00:50-debug: Num of bundles: 3..., progress: 0%
2025-8-24 01:00:50-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 01:00:50-debug: Init bundle root assets start..., progress: 0%
2025-8-24 01:00:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-24 01:00:50-debug: Init bundle root assets success..., progress: 0%
2025-8-24 01:00:50-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-24 01:00:50-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 5%
2025-8-24 01:00:50-debug: [Build Memory track]: 查询 Asset Bundle start:256.32MB, end 257.19MB, increase: 888.45KB
2025-8-24 01:00:50-debug: 查询 Asset Bundle start, progress: 5%
2025-8-24 01:00:50-debug: // ---- build task 查询 Asset Bundle ----
2025-8-24 01:00:50-debug:   Number of all scenes: 3
2025-8-24 01:00:50-debug:   Number of all scripts: 28
2025-8-24 01:00:50-debug:   Number of other assets: 610
2025-8-24 01:00:50-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-24 01:00:50-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-8-24 01:00:50-debug: [Build Memory track]: 查询 Asset Bundle start:257.03MB, end 257.17MB, increase: 142.16KB
2025-8-24 01:00:50-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-24 01:00:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 01:00:50-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-24 01:00:50-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:257.20MB, end 257.22MB, increase: 18.81KB
2025-8-24 01:00:50-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-24 01:00:50-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-24 01:00:50-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-24 01:00:50-debug: [Build Memory track]: 填充脚本数据到 settings.json start:257.25MB, end 257.27MB, increase: 18.00KB
2025-8-24 01:00:50-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-24 01:00:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-24 01:00:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-24 01:00:50-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-8-24 01:00:50-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:257.30MB, end 257.52MB, increase: 218.67KB
