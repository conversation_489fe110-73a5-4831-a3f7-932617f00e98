[1, ["dec5GXjXRCFb/ngYogPNxm", "7bzlqIK8NFS5ZUlrYuHKKf@f9941", "0egy8lrORML7so3k+5KNar"], ["node", "_spriteFrame", "explosionPrefab", "_defaultClip", "root", "data"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_components", "_prefab"], 1, 9, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["91ffczWtpJKh6L+Ua3qo7Ip", ["speed", "damage", "bulletType", "lifeTime", "explosionRadius", "node", "__prefab", "explosionPrefab"], -2, 1, 4, 6], ["cc.RigidBody2D", ["enabledContactListener", "bullet", "node", "__prefab"], 1, 1, 4], ["cc.BoxCollider2D", ["_sensor", "node", "__prefab", "_offset", "_size"], 2, 1, 4, 5, 5], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 3, 3], [2, 0, 1, 2, 1], [4, 0, 1, 2, 3, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 6], [6, 0, 1, 2, 3, 3], [7, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 3, 4, 2], [9, 0, 1, 2, 3, 4, 5, 5]], [[1, "dart"], [2, "dart", 4, [[3, -2, [0, "9cbtBk4jRNX6ed0WVOqeL1"], [5, 50, 50]], [4, 0, -3, [0, "a3mwrWJdRLLq6lcvJ4muo0"], 0], [5, 20, 4, 1, 2, 0, -4, [0, "dd+CZkI+xB9pPwMl6qkkM+"], 1], [6, true, true, -5, [0, "1aa1pr+ylBTY7CvLmOqrpP"]], [7, true, -6, [0, "e3oeF8241IFZ22eomt2a41"], [0, 0.2, 0.1], [5, 50.2, 49.9]], [8, true, -7, [0, "45zG9bAVtMsIZH2/CAz2j2"], [2], 3]], [9, "81/HPjmJ1Mz7rPzVFz0YOJ", null, null, null, -1, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 7], [0, 0, 0, 0], [1, 2, -1, 3], [1, 2, 0, 0]]