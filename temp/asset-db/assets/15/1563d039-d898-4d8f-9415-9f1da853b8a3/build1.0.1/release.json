[1, ["dcta33lHROwrrB/vxfhE6n@f9941", "c7CfSpAIpItbqKNqaD7scb@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "ffSxFV24tM2pvAbnQ+VxC8@f9941", "b5EDA9xoxOG4BzahbrTfn4@f9941", "374ExlSsRBuIFpomb0Dp+o@f9941", "8eL0KXkaZHzIzXQ4qF541b@f9941", "9f2QDdIhtPiY8s+6NCQ8g1@f9941", "24pwTaKGdEbY0aXpIMdeCd@f9941", "35fLH7RCdGd66+qjz5+d9V@f9941", "0adPQ1OoNEsa80qLFuph7J@f9941", "b7MFJ8MjNBwqr3fNq1j5dJ@f9941", "acTGImtcpAqo5l2VOrk8a0@f9941", "57zIv2tepBbJICQmVTdBpN@f9941"], ["node", "_spriteFrame", "_parent", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "_cameraComponent", "LevelSelectButton", "restartButton", "ai4RatioLabel", "ai3RatioLabel", "ai2RatioLabel", "ai1RatioLabel", "star3Sprite", "star2Sprite", "star1Sprite", "gameTimeLabel", "<PERSON><PERSON><PERSON><PERSON>", "performance<PERSON>abel", "titleLabel", "gameHUD", "mainMenuButton", "resumeButton", "pauseButton", "enemyCountLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scene", "_barSprite"], [["cc.Node", ["_name", "_layer", "_id", "_active", "_components", "_parent", "_children", "_lpos", "_lrot", "_euler"], -1, 9, 1, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame", "_color"], 1, 1, 6, 5], ["cc.Node", ["_name", "_layer", "_id", "_lpos", "_components", "_parent", "_lscale", "_children", "_lrot", "_euler"], 0, 5, 12, 1, 5, 9, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_enableOutline", "_enableShadow", "_isBold", "_fontSize", "node", "_color"], -3, 1, 5], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_right", "_left", "node"], -2, 1], ["cc.Node", ["_name", "_layer", "_children", "_components", "_lpos", "_parent"], 1, 2, 12, 5, 1], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_far", "_visibility", "_fov", "_fovAxis", "_priority", "_clearFlags", "node", "_color"], -6, 1, 5], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "_barSprite"], 1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "_target", "_normalColor"], 2, 1, 1, 5], ["cc.SceneAsset", ["_name"], 2], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["daf21OCa1hMm660rOlgyMop", ["node"], 3, 1], ["cc.<PERSON>", ["_alignCanvasWithScreen", "node", "_cameraComponent"], 2, 1, 1], ["b67f4UjjapGSoVG2Jvvuyl3", ["node", "playGround", "canvas", "spawnPoint", "camera", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enemyCountLabel", "pauseButton", "pausePanel", "gameOverPanel", "resumeButton", "mainMenuButton", "gameHUD"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingLeft", "_paddingRight", "_spacingX", "node"], -2, 1], ["85096jcoYBCMKDGuUlvynIB", ["node", "titleLabel", "performance<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "gameTimeLabel", "star1Sprite", "star2Sprite", "star3Sprite", "player<PERSON>ati<PERSON><PERSON><PERSON><PERSON>", "ai1RatioLabel", "ai2RatioLabel", "ai3RatioLabel", "ai4RatioLabel", "restartButton", "LevelSelectButton"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["8d994pxBTRN65Jo2JjZ4MAH", ["node", "countdown<PERSON><PERSON>l", "ai1RatioLabel", "ai2RatioLabel", "ai3RatioLabel", "ai4RatioLabel", "shootButton", "ammoLabel", "reloadProgressBar", "upButton", "downButton", "leftButton", "rightB<PERSON>on"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["f2409Jfl1NKgaxzoqghBXWi", ["node"], 3, 1], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3]], [[2, 0, 1, 5, 4, 3, 3], [6, 0, 1, 1], [6, 0, 1, 2, 1], [1, 1, 0, 2, 3, 3], [9, 0, 1, 3, 2, 2], [3, 0, 1, 4, 2, 3, 6, 7, 6], [0, 0, 5, 7, 8, 9, 2], [6, 0, 1], [0, 0, 1, 5, 6, 4, 7, 3], [3, 0, 1, 5, 2, 3, 6, 7, 6], [1, 0, 2, 3, 2], [2, 0, 1, 5, 4, 3, 8, 6, 9, 3], [1, 0, 2, 2], [0, 0, 1, 2, 6, 4, 7, 4], [0, 0, 2, 5, 4, 3], [0, 0, 1, 5, 4, 7, 3], [5, 0, 1, 5, 2, 3, 4, 3], [14, 0, 1, 2, 2], [1, 1, 0, 2, 4, 3], [3, 0, 1, 4, 2, 6, 7, 5], [3, 0, 1, 5, 4, 2, 3, 6, 7, 7], [10, 0, 2], [11, 0, 1, 2, 3, 2], [0, 0, 1, 5, 4, 3], [0, 0, 3, 1, 5, 6, 4, 4], [0, 0, 3, 1, 6, 4, 4], [0, 0, 1, 5, 6, 4, 3], [2, 0, 2, 7, 3, 3], [2, 0, 1, 5, 4, 3, 6, 3], [2, 0, 1, 4, 3, 3], [5, 0, 1, 2, 3, 4, 3], [5, 0, 1, 2, 3, 3], [12, 0, 1, 2, 3, 2], [7, 0, 5, 6, 1, 2, 3, 4, 9, 10, 8], [7, 0, 7, 1, 2, 3, 8, 4, 9, 10, 8], [13, 0, 1], [4, 0, 1, 2, 5, 4], [4, 0, 3, 1, 5, 4], [4, 0, 5, 2], [4, 0, 4, 1, 5, 4], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [8, 0, 1, 2, 3], [8, 0, 1, 2, 3, 3], [1, 0, 2, 4, 3, 2], [1, 1, 0, 2, 4, 3, 3], [9, 0, 1, 2, 2], [16, 0, 1, 2, 3, 4, 5, 6], [3, 0, 1, 2, 3, 6, 7, 5], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [18, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [19, 0, 1], [20, 0, 1, 2, 3, 4, 5, 7], [21, 0, 1, 2, 3, 4, 5, 6, 7, 1], [22, 0, 1, 1], [23, 0, 1, 1], [24, 1], [25, 1], [26, 1], [27, 0, 2], [28, 1], [29, 1]], [[21, "gamescene"], [25, "gameOverPanel", false, 524288, [-18, -19, -20, -21, -22, -23, -24, -25], [[1, -1, [5, 697.2560000000001, 602.98]], [10, 0, -2, 8], [48, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]]], [31, "GameHUD", 524288, [-28, -29, -30, -31, -32, -33, -34], [[[1, -26, [5, 1280, 720]], -27], 4, 1]], [13, "UICanvas", 524288, "d175nThgdJ/In481e1Y5Bq", [-39, -40, 2, -41, 1], [[1, -35, [5, 1280, 720]], [17, false, -37, -36], [38, 45, -38]], [1, 640, 360, 1000]], [27, "spwanpoints", "87GGE01slMnLBedPHLZh6l", [[6, "point1", -42, [1, 1250, 1053.802, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [6, "point2", -43, [1, 1250, 1412.514, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [6, "point3", -44, [1, 1250, 686.162, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [6, "point4", -45, [1, 2200, 691.991, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [6, "point5", -46, [1, 2200, 1053.803, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [6, "point6", -47, [1, 2200, 1416.186, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]]], [1, -1042.636, -690.422, 0]], [13, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-52, -53], [[1, -48, [5, 1280, 720]], [17, false, -50, -49], [36, 45, 5.684341886080802e-14, 5.684341886080802e-14, -51]], [1, 640, 360.00000000000006, 0]], [8, "<PERSON><PERSON><PERSON>", 524288, 1, [-55, -56, -57, -58, -59], [[7, -54]], [1, -87.418, 12.883, 0]], [26, "buttons", 524288, 2, [-61, -62, -63, -64, -65], [[7, -60]]], [22, "gamescene", [5, 4, -66, -67, 3], [51, null, null, "1563d039-d898-4d8f-9415-9f1da853b8a3", null, null, null], [52, [53, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [54, [4, 4283190348], [0, 512, 512]], [55], [56], [57], [58, false], [59], [60]]], [30, "PlayerHp", 524288, [-72], [[[1, -68, [5, 300, 15]], [3, 1, 0, -69, 15], -70, [39, 9, 4.9325000000000045, -53.615, -71]], 4, 4, 1, 4], [1, 122.367, 70.312, 0]], [0, "pauseButton", 524288, 3, [[[1, -73, [5, 60, 60]], [3, 1, 0, -74, 0], -75, [37, 33, 26.44899999999995, 11.946999999999974, -76]], 4, 4, 1, 4], [1, 583.551, 318.053, 0]], [24, "PausePanel", false, 524288, 3, [-79, -80], [[1, -77, [5, 736.704, 519.579]], [10, 0, -78, 4]]], [16, "reviewLable", 524288, 1, [-83, -84, -85], [[[2, -81, [5, 164, 54.4], [0, 0, 0.5]], -82], 4, 1], [1, -248.751, 153.187, 0]], [8, "<PERSON><PERSON><PERSON>", 524288, 2, [-87, -88, -89, -90], [[2, -86, [5, 100, 49.981], [0, 0, 0.5]]], [1, -608.344, 211.042, 0]], [8, "Layout", 524288, 11, [-93, -94], [[1, -91, [5, 290, 100]], [46, 1, 1, 20, 20, 20, -92]], [1, 0, -40.852, 0]], [0, "resume", 524288, 14, [[[1, -95, [5, 120, 120]], [3, 1, 0, -96, 2], -97], 4, 4, 1], [1, -65, -13.417, 0]], [0, "backtomenu", 524288, 14, [[[1, -98, [5, 110, 110]], [3, 1, 0, -99, 3], -100], 4, 4, 1], [1, 70, -13.417, 0]], [0, "backtomenu", 524288, 1, [[[7, -101], [3, 1, 0, -102, 6], -103], 4, 4, 1], [1, 247.778, -211.065, 0]], [0, "restart", 524288, 1, [[[1, -104, [5, 70, 70]], [3, 1, 0, -105, 7], -106], 4, 4, 1], [1, 139.084, -212.43, 0]], [0, "shoot", 524288, 7, [[[1, -107, [5, 120, 110]], [10, 0, -108, 9], -109], 4, 4, 1], [1, -444.039, -61.346, 0]], [28, "up", 524288, 7, [[[7, -110], [3, 1, 0, -111, 10], -112], 4, 4, 1], [1, 445, -141.3, 0], [1, 1.2, 1.2, 1]], [11, "down", 524288, 7, [[[7, -113], [3, 1, 0, -114, 11], -115], 4, 4, 1], [1, 445, -277.97, 0], [3, 0, 0, 1, 6.123233995736766e-17], [1, 1.2, 1.2, 1], [1, 180, 180, 7.016709298534876e-15]], [11, "left", 524288, 7, [[[1, -116, [5, 120, 120]], [3, 1, 0, -117, 12], -118], 4, 4, 1], [1, -534.14, -207.422, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, 90]], [11, "right", 524288, 7, [[[1, -119, [5, 120, 120]], [3, 1, 0, -120, 13], -121], 4, 4, 1], [1, -347.03, -207.422, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [16, "reload", 524288, 2, [-125], [[[1, -122, [5, 60.42099999999999, 5.605]], [44, 1, 0, -123, [4, 4289583323], 14], -124], 4, 4, 1], [1, -448.353, -5.115, 0]], [0, "Camera", 33554432, 5, [[-126, [35, -127]], 1, 4], [1, 0, 0, 956.097]], [8, "playerinfos", 524288, 2, [9, -129], [[1, -128, [5, 65.131, 48.394]]], [1, -580, 253.588, 0]], [23, "PlayGround", 33554432, 5, [[1, -130, [5, 1, 1]]]], [15, "pause", 524288, 11, [[1, -131, [5, 262.051, 124.21035172363621]], [10, 0, -132, 1]], [1, 0, 117.101, 0]], [15, "mask", 524288, 1, [[1, -133, [5, 652.0539999999999, 488.35599999999994]], [43, 0, -134, [4, 1555543993], 5]], [1, 2.273999999999997, -32.86250000000003, 0]], [0, "GameOverLable", 524288, 1, [[[1, -135, [5, 164, 54.4]], -136], 4, 1], [1, -0.866, 256.191, 0]], [0, "TimeLable", 524288, 1, [[[2, -137, [5, 84, 54.4], [0, 0, 0.5]], -138], 4, 1], [1, -246.196, -116.715, 0]], [0, "star1", 524288, 12, [[[1, -139, [5, 60, 60]], -140], 4, 1], [1, 372.454, -1.203, 0]], [0, "star2", 524288, 12, [[[1, -141, [5, 60, 60]], -142], 4, 1], [1, 413.89, -1.203, 0]], [0, "star3", 524288, 12, [[[1, -143, [5, 60, 60]], -144], 4, 1], [1, 457.51, -1.203, 0]], [0, "player", 524288, 6, [[[2, -145, [5, 215.23046875, 54.4], [0, 0, 0.5]], -146], 4, 1], [1, -158.526, 57.495, 0]], [0, "AI-1", 524288, 6, [[[2, -147, [5, 159.625, 54.4], [0, 0, 0.5]], -148], 4, 1], [1, -158.495, 0.92, 0]], [0, "AI-2", 524288, 6, [[[2, -149, [5, 159.625, 54.4], [0, 0, 0.5]], -150], 4, 1], [1, 122.996, 1.886, 0]], [0, "AI-3", 524288, 6, [[[2, -151, [5, 159.625, 54.4], [0, 0, 0.5]], -152], 4, 1], [1, -163.2, -59.264, 0]], [0, "AI-4", 524288, 6, [[[2, -153, [5, 159.625, 54.4], [0, 0, 0.5]], -154], 4, 1], [1, 121.1, -54.495, 0]], [0, "rewardLable", 524288, 1, [[[2, -155, [5, 164, 54.4], [0, 0, 0.5]], -156], 4, 1], [1, 35.027, -116.404, 0]], [0, "countdown", 524288, 2, [[[1, -157, [5, 70.73828125, 54.4]], -158], 4, 1], [1, 6.219, 318.008, 0]], [0, "opponentCount", 524288, 2, [[[2, -159, [5, 170.6845703125, 54.4], [0, 0, 0.5]], -160], 4, 1], [1, -610.706, 287.038, 0]], [0, "ai-1", 524288, 13, [[[2, -161, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -162], 4, 1], [1, 0, -15.057, 0]], [0, "ai-2", 524288, 13, [[[2, -163, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -164], 4, 1], [1, 0, -49.108, 0]], [0, "ai-3", 524288, 13, [[[2, -165, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -166], 4, 1], [1, 0, -83.952, 0]], [0, "ai-4", 524288, 13, [[[2, -167, [5, 54.0244140625, 54.4], [0, 0, 0.5]], -168], 4, 1], [1, 0, -113.178, 0]], [0, "ammo", 524288, 2, [[[1, -169, [5, 62.90625, 54.4]], -170], 4, 1], [1, -447.105, 14.594, 0]], [0, "Bar", 524288, 24, [[[2, -171, [5, 60.421, 5.122], [0, 0, 0.5]], -172], 4, 1], [1, -29.613999999999997, -0.15900000000000003, 0]], [29, "<PERSON><PERSON><PERSON>", 524288, [[[2, -173, [5, 154.0439453125, 54.4], [0, 0, 0.5]], -174], 4, 1], [1, -1.516, -9.959, 0]], [8, "paint", 524288, 26, [49], [[7, -175]], [1, -28.799, 0, 0]], [0, "Bar", 524288, 9, [[[2, -176, [5, 300, 15], [0, 0, 0.5]], -177], 4, 1], [1, -150, 0, 0]], [33, 0, 25.363636363636363, 1, 360, 0, 2000, 1108344836, 25, [4, 16777215]], [14, "GameManager", "dcbqoNkjtBg5W8+uTDZHUD", 8, [[40, -184, 27, 5, 4, 25, -183, -182, -181, 11, 1, -180, -179, -178]]], [41, 300, 1, 9], [32, "Camera", 3, [-185], [1, 0, 0, 1000]], [34, 0, 1, 360, 0, 2000, 6, 524288, 55, [4, 4278190080]], [4, 3, 10, [4, 4292269782], 10], [4, 3, 15, [4, 4292269782], 15], [4, 3, 16, [4, 4292269782], 16], [19, "游戏结束", 40, true, true, 30, [4, 4292607817]], [19, "时间", 40, true, true, 31, [4, 4292607817]], [12, 0, 32], [12, 0, 33], [12, 0, 34], [5, "表现评价", 40, true, true, true, 12, [4, 4290080757]], [5, "player:20%", 40, true, true, true, 35, [4, 4286019573]], [5, "AI1:20%", 40, true, true, true, 36, [4, 4281786343]], [5, "AI2:20%", 40, true, true, true, 37, [4, 4286572139]], [5, "AI3:20%", 40, true, true, true, 38, [4, 4292194730]], [5, "AI4:20%", 40, true, true, true, 39, [4, 4290816725]], [47, "获得奖励", 40, true, true, 40, [4, 4292398852]], [4, 3, 17, [4, 4292269782], 17], [4, 3, 18, [4, 4292269782], 18], [5, "120", 40, true, true, true, 41, [4, 4278255380]], [20, "剩余对手：4", 30, 30, true, true, true, 42, [4, 4284045791]], [9, "ai-1", 30, 30, true, true, 43, [4, 4278224383]], [9, "ai-2", 30, 30, true, true, 44, [4, 4278226685]], [9, "ai-3", 30, 30, true, true, 45, [4, 4278221567]], [9, "ai-4", 30, 30, true, true, 46, [4, 4281174768]], [45, 3, 19, 19], [4, 3, 20, [4, 4292269782], 20], [4, 3, 21, [4, 4292269782], 21], [4, 3, 22, [4, 4292269782], 22], [4, 3, 23, [4, 4292269782], 23], [20, "ammo", 20, 20, true, true, true, 47, [4, 4285393139]], [18, 1, 0, 48, [4, 4278253035]], [42, 60.421, 1, 24, 86], [49, 2, 74, 76, 77, 78, 79, 80, 85, 87, 81, 82, 83, 84], [9, "玩家：10%", 30, 30, true, true, 49, [4, 4284177395]], [18, 1, 0, 51, [4, 4278255462]], [14, "AIController", "9d9+jzMZ1Fa5Gun1m2L8KH", 8, [[50, -186]]]], 0, [0, 0, 1, 0, 0, 1, 0, 9, 72, 0, 10, 73, 0, 11, 70, 0, 12, 69, 0, 13, 68, 0, 14, 67, 0, 7, 66, 0, 15, 64, 0, 16, 63, 0, 17, 62, 0, 18, 61, 0, 19, 71, 0, 20, 65, 0, 21, 60, 0, 0, 1, 0, -1, 29, 0, -2, 30, 0, -3, 31, 0, -4, 12, 0, -5, 6, 0, -6, 40, 0, -7, 17, 0, -8, 18, 0, 0, 2, 0, -2, 88, 0, -1, 41, 0, -2, 26, 0, -3, 42, 0, -4, 13, 0, -5, 7, 0, -6, 47, 0, -7, 24, 0, 0, 3, 0, 8, 56, 0, 0, 3, 0, 0, 3, 0, -1, 55, 0, -2, 10, 0, -4, 11, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 2, 4, 0, 0, 5, 0, 8, 52, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, -2, 27, 0, 0, 6, 0, -1, 35, 0, -2, 36, 0, -3, 37, 0, -4, 38, 0, -5, 39, 0, 0, 7, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -5, 23, 0, -3, 53, 0, -4, 91, 0, 0, 9, 0, 0, 9, 0, -3, 54, 0, 0, 9, 0, -1, 51, 0, 0, 10, 0, 0, 10, 0, -3, 57, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 28, 0, -2, 14, 0, 0, 12, 0, -2, 65, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, 0, 13, 0, -1, 43, 0, -2, 44, 0, -3, 45, 0, -4, 46, 0, 0, 14, 0, 0, 14, 0, -1, 15, 0, -2, 16, 0, 0, 15, 0, 0, 15, 0, -3, 58, 0, 0, 16, 0, 0, 16, 0, -3, 59, 0, 0, 17, 0, 0, 17, 0, -3, 72, 0, 0, 18, 0, 0, 18, 0, -3, 73, 0, 0, 19, 0, 0, 19, 0, -3, 80, 0, 0, 20, 0, 0, 20, 0, -3, 81, 0, 0, 21, 0, 0, 21, 0, -3, 82, 0, 0, 22, 0, 0, 22, 0, -3, 83, 0, 0, 23, 0, 0, 23, 0, -3, 84, 0, 0, 24, 0, 0, 24, 0, -3, 87, 0, -1, 48, 0, -1, 52, 0, 0, 25, 0, 0, 26, 0, -2, 50, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, -2, 60, 0, 0, 31, 0, -2, 61, 0, 0, 32, 0, -2, 62, 0, 0, 33, 0, -2, 63, 0, 0, 34, 0, -2, 64, 0, 0, 35, 0, -2, 66, 0, 0, 36, 0, -2, 67, 0, 0, 37, 0, -2, 68, 0, 0, 38, 0, -2, 69, 0, 0, 39, 0, -2, 70, 0, 0, 40, 0, -2, 71, 0, 0, 41, 0, -2, 74, 0, 0, 42, 0, -2, 75, 0, 0, 43, 0, -2, 76, 0, 0, 44, 0, -2, 77, 0, 0, 45, 0, -2, 78, 0, 0, 46, 0, -2, 79, 0, 0, 47, 0, -2, 85, 0, 0, 48, 0, -2, 86, 0, 0, 49, 0, -2, 89, 0, 0, 50, 0, 0, 51, 0, -2, 90, 0, 22, 88, 0, 23, 59, 0, 24, 58, 0, 25, 57, 0, 26, 75, 0, 27, 54, 0, 0, 53, 0, -1, 56, 0, 0, 91, 0, 28, 8, 1, 2, 3, 2, 2, 3, 3, 2, 8, 4, 2, 8, 5, 2, 8, 9, 2, 26, 49, 2, 50, 54, 29, 90, 88, 7, 89, 186], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 57, 57, 57, 57, 58, 58, 58, 58, 59, 59, 59, 59, 62, 63, 64, 72, 72, 72, 72, 73, 86, 90], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 5, 6, 3, 4, 5, 6, 3, 4, 5, 6, 1, 1, 1, 3, 4, 5, 6, 3, 1, 1], [6, 11, 7, 0, 12, 13, 0, 8, 14, 15, 1, 1, 1, 1, 9, 9, 6, 2, 3, 4, 7, 2, 3, 4, 0, 2, 3, 4, 5, 5, 5, 0, 2, 3, 4, 8, 10, 10]]