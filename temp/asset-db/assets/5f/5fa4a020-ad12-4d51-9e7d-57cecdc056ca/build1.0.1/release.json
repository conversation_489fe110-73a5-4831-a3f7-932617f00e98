[1, ["9f2QDdIhtPiY8s+6NCQ8g1@f9941", "24pwTaKGdEbY0aXpIMdeCd@f9941"], ["node", "_spriteFrame", "root", "_barSprite", "data"], [["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_color", "_spriteFrame"], 1, 1, 4, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_lscale"], 1, 2, 9, 4, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "__prefab", "_barSprite"], 1, 1, 4, 1], ["79d6fgSlVRCYoTh/XQ+YRv5", ["node", "__prefab"], 3, 1, 4]], [[5, 0, 2], [6, 0, 1, 2, 3, 4, 5, 5], [2, 0, 2], [3, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 2, 3, 1], [0, 0, 1, 2, 1], [1, 0, 1, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 5, 3], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 1]], [[2, "healthBar"], [3, "healthBar", 33554432, [-7], [[6, -2, [0, "b9VSg4lIBD2674EWNr0cu8"], [5, 300, 15]], [8, 1, 0, -3, [0, "cdITcchadObYwtHhZvTDcL"], [4, **********], 0], [9, 300, 0.5, -5, [0, "e9cQsECkREsqrYHJQVVWOE"], -4], [10, -6, [0, "88S4hp27BPFpL6l7nafPlR"]]], [1, "40tQNkNPtP4aURzO7VMMIR", null, null, null, -1, 0], [1, 0.2, 0.3, 1]], [4, "Bar", 33554432, 1, [[[5, -8, [0, "27NHqliW9Fb4bDUyccWJj+"], [5, 150, 15], [0, 0, 0.5]], -9], 4, 1], [1, "5cYVKvVKJIe6PUw2kXTDSC", null, null, null, 1, 0], [1, -150, 0, 0]], [7, 1, 0, 2, [0, "e4T3FcLEZKR5ceOsPb+eF1"], [4, **********]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 3, 3, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -2, 3, 0, 4, 1, 9], [0, 3], [1, 1], [0, 1]]