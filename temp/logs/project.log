8-21-2025 00:26:06 - log: Load engine in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine
8-21-2025 00:26:06 - log: Register native engine in /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/native
8-21-2025 00:26:07 - log: Request namespace: device-list
8-21-2025 00:26:10 - info: [PreviewInEditor] 预览环境初始化完毕
8-21-2025 00:26:10 - log: [Scene] meshopt wasm decoder initialized
8-21-2025 00:26:10 - log: [Scene] [box2d]:box2d wasm lib loaded.
8-21-2025 00:26:10 - log: [Scene] [bullet]:bullet wasm lib loaded.
8-21-2025 00:26:10 - log: [Scene] [PHYSICS]: using builtin.
8-21-2025 00:26:10 - log: [Scene] Cocos Creator v3.8.6
8-21-2025 00:26:10 - log: [Scene] Using custom pipeline: Builtin
8-21-2025 00:26:10 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to box2d.
8-21-2025 00:27:49 - info: [Window] Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
8-21-2025 00:27:49 - info: [Window] You are running Vue in development mode.
Make sure to turn on production mode when deploying for production.
See more tips at https://vuejs.org/guide/deployment.html
8-21-2025 00:27:57 - info: [Window] Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
8-21-2025 00:27:57 - info: [Window] You are running Vue in development mode.
Make sure to turn on production mode when deploying for production.
See more tips at https://vuejs.org/guide/deployment.html
8-21-2025 00:28:30 - warn: [Assets] [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
Warn: [Assets] [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts

    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at NewConsole.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/asset-db/dist/worker/console.ccc:1:1510)
    at NewConsole.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/console.ccc:1:2337)
    at Socket.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/worker-pools/sub-process-manager.ccc:1:3399)
    at Socket.emit (node:events:519:28)
    at addChunk (node:internal/streams/readable:559:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
    at Socket.Readable.push (node:internal/streams/readable:390:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
8-21-2025 00:28:30 - warn: [Assets] [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
Warn: [Assets] [build-script][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIPlayer.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/Bullet.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/player.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/AIController.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameOverPanel.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameHUD.ts -> file:///Users/<USER>/projects/cocos_project/SuperSplash/assets/scripts/GameManager.ts

    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at NewConsole.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/asset-db/dist/worker/console.ccc:1:1510)
    at NewConsole.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/console.ccc:1:2337)
    at Socket.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/builder/dist/worker/worker-pools/sub-process-manager.ccc:1:3399)
    at Socket.emit (node:events:519:28)
    at addChunk (node:internal/streams/readable:559:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
    at Socket.Readable.push (node:internal/streams/readable:390:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
8-21-2025 00:35:48 - info: [Window] Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
8-21-2025 00:35:48 - info: [Window] You are running Vue in development mode.
Make sure to turn on production mode when deploying for production.
See more tips at https://vuejs.org/guide/deployment.html
8-21-2025 00:36:18 - log: Use preview template {link(/Users/<USER>/projects/cocos_project/SuperSplash/preview-template/index.ejs)}
8-21-2025 07:41:00 - info: [Window] Download the Vue Devtools extension for a better development experience:
https://github.com/vuejs/vue-devtools
8-21-2025 07:41:00 - info: [Window] You are running Vue in development mode.
Make sure to turn on production mode when deploying for production.
See more tips at https://vuejs.org/guide/deployment.html
8-21-2025 07:41:25 - warn: [Scene] 你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。Error: [Scene] 你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at logger (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:755:13)
    at ExecutorSystem.resolve [as _detailResolve] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:772:17)
    at ExecutorSystem._resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:137:14)
    at SystemJS.resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:36:23)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:355:37
    at Array.map (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:353:41
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)
8-21-2025 07:41:25 - error: [Scene] {hidden(::SceneExecutorImportExceptionHandler::)} Error: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
    at rejector (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:759:15)
    at ExecutorSystem.resolve [as _detailResolve] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:777:13)
    at ExecutorSystem._resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:137:14)
    at SystemJS.resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:36:23)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:355:37
    at Array.map (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:353:41
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)Error: [Scene] {hidden(::SceneExecutorImportExceptionHandler::)} Error: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
    at SystemJS.resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:36:23)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:355:37
    at Array.map (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:353:41
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)
    at Logger._logHandler (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/startup/log.ccc:1:487)
    at Logger.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@base/electron-logger/lib/renderer.ccc:1:458)
    at console.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@base/electron-logger/lib/renderer.ccc:1:1414)
    at console.error (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at ScriptManager._handleImportException (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:5408)
    at Executor.importExceptionHandler [as _importExceptionHandler] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:3604)
    at Executor._onModuleLoaded (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:401:22)
    at SystemJS.onload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:99:18)
    at triggerOnload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:270:10)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:431:7
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)
8-21-2025 07:41:26 - warn: [Scene] 你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。Error: [Scene] 你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。
    at console.warn (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at logger (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:755:13)
    at ExecutorSystem.resolve [as _detailResolve] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:772:17)
    at ExecutorSystem._resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:137:14)
    at SystemJS.resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:36:23)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:355:37
    at Array.map (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:353:41
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)
8-21-2025 07:41:26 - error: [Scene] {hidden(::SceneExecutorImportExceptionHandler::)} Error: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
    at rejector (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:759:15)
    at ExecutorSystem.resolve [as _detailResolve] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:777:13)
    at ExecutorSystem._resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:137:14)
    at SystemJS.resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:36:23)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:355:37
    at Array.map (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:353:41
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)Error: [Scene] {hidden(::SceneExecutorImportExceptionHandler::)} Error: Error: 以 file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar.unpacked/modules/platform-extensions/extensions/adsense-h5g-plugin/static/adsense-h5g-api/h5_games_ads.ts 为起点找不到模块 "./ad_event"
    at SystemJS.resolve (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/editor-systemjs/index.ts:36:23)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:355:37
    at Array.map (<anonymous>)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:353:41
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)
    at Logger._logHandler (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/startup/log.ccc:1:487)
    at Logger.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@base/electron-logger/lib/renderer.ccc:1:458)
    at console.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@base/electron-logger/lib/renderer.ccc:1:1414)
    at console.error (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@sentry/src/instrument/console.ts:40:20)
    at ScriptManager._handleImportException (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:5408)
    at Executor.importExceptionHandler [as _importExceptionHandler] (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:3604)
    at Executor._onModuleLoaded (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:401:22)
    at SystemJS.onload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:99:18)
    at triggerOnload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:270:10)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/static/executor/systemjs-bridge/out/index.js:431:7
    at file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:8:13
    at Object.execute (file:///Users/<USER>/projects/cocos_project/SuperSplash/temp/programming/packer-driver/targets/editor/chunks/6d/cce:/internal/x/prerequisite-imports:4:1)
    at Executor._importPrerequisiteModules (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:358:13)
    at Executor.reload (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/node_modules/@editor/lib-programming/src/executor/index.ts:235:13)
    at /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1787
    at GlobalEnv.processQueue (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:2184)
    at GlobalEnv.record (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/app.asar/builtin/scene/dist/script/3d/manager/scripts.ccc:1:1955)
