import { _decorator, Component, director } from 'cc';
const { ccclass } = _decorator;

/**
 * 启动控制器
 * 用于处理游戏启动时的初始化逻辑
 * 避免在主菜单场景中直接初始化复杂的单例组件
 */
@ccclass('StartupController')
export class StartupController extends Component {
    
    start() {
        console.log('StartupController: Game starting...');
        
        // 延迟加载主菜单，确保所有系统准备就绪
        this.scheduleOnce(() => {
            this.loadMainMenu();
        }, 0.5);
    }

    private loadMainMenu() {
        try {
            console.log('StartupController: Loading main menu...');
            director.loadScene('mainmenu');
        } catch (error) {
            console.error('StartupController: Error loading main menu:', error);
            // 如果主菜单加载失败，直接加载关卡选择
            director.loadScene('LevelSelect');
        }
    }
}
