{"__version__": "1.3.9", "log": {"level": 1}, "common": {"polyfills": {"asyncFunctions": true}, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "name": "SuperSplash", "buildPath": "/Users/<USER>/Desktop", "outputName": "web-desktop", "mainBundleCompressionType": "merge_dep", "platform": "web-desktop", "md5Cache": false, "experimentalEraseModules": false}, "BuildTaskManager": {"taskMap": {"1754796521852": {"type": "build", "id": "1754796521852", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 11:32:55 build success in 4 s!", "detailMessage": "// ---- build task cocos-service：onBeforeCompressSettings ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "web-mobile", "taskName": "web-mobile", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-mobile8-10-2025 11-28.log"}, "time": "8-10-2025 11:32:50", "dirty": false}, "1754796652802": {"type": "build", "id": "1754796652802", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 11:30:57 build success in 4 s!", "detailMessage": "// ---- build task web-desktop：onBeforeCopyBuildTemplate ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-desktop", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-desktop", "taskName": "web-desktop", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-desktop8-10-2025 11-30.log"}, "time": "8-10-2025 11:30:52", "dirty": false}, "1754796712646": {"type": "build", "id": "1754796712646", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 11:31:57 build success in 4 s!", "detailMessage": "Json group(0113159d2) compile success，json number: 6\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-desktop", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-desktop-001", "taskName": "web-desktop-001", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-desktop8-10-2025 11-31.log"}, "time": "8-10-2025 11:31:52", "dirty": false}, "1754796833428": {"type": "build", "id": "1754796833428", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 11:33:58 build success in 5 s!", "detailMessage": "Asset DB is resume!\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-mobile", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-mobile", "taskName": "web-mobile", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-mobile8-10-2025 11-33.log"}, "time": "8-10-2025 11:33:53", "dirty": false}, "1754797764344": {"type": "build", "id": "1754797764344", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 11:49:29 build success in 4 s!", "detailMessage": "// ---- build task cocos-service：onBeforeCompressSettings ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-mobile", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-mobile", "taskName": "web-mobile-001", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-mobile8-10-2025 11-49.log"}, "time": "8-10-2025 11:49:24", "dirty": false}, "1754797776377": {"type": "build", "id": "1754797776377", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 11:49:40 build success in 4 s!", "detailMessage": "// ---- build task cocos-service：onAfterBuild ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-desktop", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-desktop", "taskName": "web-desktop", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-desktop8-10-2025 11-49.log"}, "time": "8-10-2025 11:49:36", "dirty": false}, "1754798775129": {"type": "build", "id": "1754798775129", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 12:06:20 build success in 4 s!", "detailMessage": "// ---- build task cocos-service：onBeforeCompressSettings ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-desktop", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-desktop", "taskName": "web-desktop", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-desktop8-10-2025 12-06.log"}, "time": "8-10-2025 12:06:15", "dirty": false}, "1754798781613": {"type": "build", "id": "1754798781613", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 12:06:26 build success in 4 s!", "detailMessage": "Asset DB is resume!\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-mobile", "buildPath": "/Users/<USER>/Desktop", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "web-mobile", "taskName": "web-mobile", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-mobile8-10-2025 12-06.log"}, "time": "8-10-2025 12:06:21", "dirty": false}, "1754805683813": {"type": "build", "id": "1754805683813", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-10 14:01:29 build success in 5 s!", "detailMessage": "// ---- build task cocos-service：onAfterCompressSettings ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "SuperSplash-mobile", "taskName": "web-mobile", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/web-mobile8-10-2025 14-01.log"}, "time": "8-10-2025 14:01:23", "dirty": false}, "1755700321733": {"type": "build", "id": "1755700321733", "progress": 1, "state": "failure", "stage": "build", "message": "2025-8-20 22:32:28 build task failed! Error: run cmake failed \"cmake -S \"/Users/<USER>/projects/cocos_project/driftClash/native/engine/ios\" -GXcode -B\"/Users/<USER>/projects/SuperSplash/proj\" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR=\"/Users/<USER>/projects/SuperSplash\" -DAPP_NAME=\"喷射飞车-SuperSplash\" -DLAUNCH_TYPE=\"Release\"\", code: 1, signal: null\n    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:519:28)\n    at <PERSON><PERSON>.<anonymous> (node:net:338:12)", "detailMessage": "Asset DB is resume!\r", "options": {"name": "喷射飞车-SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "/Users/<USER>/projects", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "SuperSplash", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-32.log"}, "time": "8-20-2025 22:32:01", "dirty": false}, "1755700586493": {"type": "build", "id": "1755700586493", "progress": 1, "state": "failure", "stage": "build", "message": "2025-8-20 22:36:31 build task failed! Error: run cmake failed \"cmake -S \"/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios\" -GXcode -B\"/Users/<USER>/projects/ios/proj\" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR=\"/Users/<USER>/projects/ios\" -DAPP_NAME=\"喷射飞车-SuperSplash\" -DLAUNCH_TYPE=\"Release\"\", code: 1, signal: null\n    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:519:28)\n    at <PERSON>pe.<anonymous> (node:net:338:12)", "detailMessage": "// ---- build task 给所有的资源加上 MD5 后缀 ----\r", "options": {"name": "喷射飞车-SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "/Users/<USER>/projects", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-36.log"}, "time": "8-20-2025 22:36:26", "dirty": false}, "1755700706383": {"type": "build", "id": "1755700706383", "progress": 1, "state": "failure", "stage": "build", "message": "2025-8-20 22:38:30 build task failed! Error: run cmake failed \"cmake -S \"/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios\" -GXcode -B\"/Users/<USER>/projects/SuperSplash/proj\" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR=\"/Users/<USER>/projects/SuperSplash\" -DAPP_NAME=\"SuperSplash\" -DLAUNCH_TYPE=\"Release\"\", code: 1, signal: null\n    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:305:5)", "detailMessage": "run /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -S \"/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios\" -GXcode -B\"/Users/<USER>/proje...", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "/Users/<USER>/projects", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "SuperSplash", "taskName": "ios-001", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-38.log"}, "time": "8-20-2025 22:38:26", "dirty": false}, "1755700914487": {"type": "build", "id": "1755700914487", "progress": 1, "state": "failure", "stage": "build", "message": "2025-8-20 22:44:46 build task failed! Error: run cmake failed \"cmake -S \"/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios\" -GXcode -B\"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj\" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR=\"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios\" -DAPP_NAME=\"SuperSplash\" -DLAUNCH_TYPE=\"Release\"\", code: 1, signal: null\n    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:519:28)\n    at Pipe.<anonymous> (node:net:338:12)", "detailMessage": "// ---- build task cocos-service：onAfterCompressSettings ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-41.log"}, "time": "8-20-2025 22:44:42", "dirty": false}, "1755701125065": {"type": "build", "id": "1755701125065", "progress": 1, "state": "failure", "stage": "build", "message": "2025-8-20 22:47:22 build task failed! Error: run cmake failed \"cmake -S \"/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios\" -GXcode -B\"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj\" -T buildsystem=1 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR=\"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios\" -DAPP_NAME=\"SuperSplash\" -DLAUNCH_TYPE=\"Release\"\", code: 1, signal: null\n    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:305:5)", "detailMessage": "// ---- build task ios：onBeforeCompressSettings ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "ios", "taskName": "ios-001", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-45.log"}, "time": "8-20-2025 22:47:17", "dirty": false}, "1755701446834": {"type": "build", "id": "1755701446834", "progress": 1, "state": "failure", "stage": "build", "message": "2025-8-20 22:53:44 build task failed! Error: run cmake failed \"cmake -S \"/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios\" -GXcode -B\"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios/proj\" -T buildsystem=12 -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_C_COMPILER=clang -DRES_DIR=\"/Users/<USER>/projects/cocos_project/SuperSplash/build/ios\" -DAPP_NAME=\"SuperSplash\" -DLAUNCH_TYPE=\"Release\"\", code: 1, signal: null\n    at ChildProcess.<anonymous> (/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/scripts/native-pack-tool/source/utils.ts:484:28)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:305:5)", "detailMessage": "// ---- build task 整理静态模板文件 ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-50.log"}, "time": "8-20-2025 22:53:39", "dirty": false}, "1755701708190": {"type": "build", "id": "1755701708190", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-20 22:55:35 build success in 27 s!", "detailMessage": "// ---- build task cocos-service：onAfterBuild ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "ios", "taskName": "ios-001", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 22-55.log"}, "time": "8-20-2025 22:55:08", "dirty": false}, "1755702795848": {"type": "build", "id": "1755702795848", "progress": 1, "state": "failure", "stage": "run", "message": "2025-8-20 23:13:48 Run build stage[run] in package /Users/<USER>/projects/cocos_project/SuperSplash/build/ios failed!", "detailMessage": "TypeError: Cannot read properties of undefined (reading 'match'), progress: 37%\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 23-13.log"}, "time": "8-20-2025 23:13:15", "dirty": false}, "1755703242417": {"type": "build", "id": "1755703242417", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-20 23:21:09 build success in 27 s!", "detailMessage": "Generate splash to:\r/Users/<USER>/projects/cocos_project/SuperSplash/native/engine/ios/LaunchScreenBackgroundPortrait.png\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 23-20.log"}, "time": "8-20-2025 23:20:42", "dirty": false}, "1755704577751": {"type": "build", "id": "1755704577751", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-20 23:43:07 build success in 9 s!", "detailMessage": "[cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js\r\r\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 23-42.log"}, "time": "8-20-2025 23:42:57", "dirty": false}, "1755705592929": {"type": "build", "id": "1755705592929", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-21 00:00:19 build success in 26 s!", "detailMessage": "// ---- build task cocos-service：onAfterBuild ----\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-20-2025 23-59.log"}, "time": "8-20-2025 23:59:52", "dirty": false}, "1755707307524": {"type": "build", "id": "1755707307524", "progress": 1, "state": "success", "stage": "build", "message": "2025-8-21 00:28:53 build success in 25 s!", "detailMessage": "[cmake] --  execute /opt/homebrew/Cellar/node/24.5.0/bin/node plugin_parser.js\r\r\r", "options": {"name": "SuperSplash", "server": "", "engineModulesConfigKey": "defaultConfig", "platform": "ios", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "091c5c0e-b72a-4cad-ab68-635bc57ff236", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/Scenes/LevelSelect.scene", "uuid": "091c5c0e-b72a-4cad-ab68-635bc57ff236"}, {"url": "db://assets/Scenes/gamescene.scene", "uuid": "1563d039-d898-4d8f-9415-9f1da853b8a3"}, {"url": "db://assets/Scenes/mainmenu.scene", "uuid": "9a4dce19-24b9-4d46-b38f-ef49f2287dcb"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios8-21-2025 00-28.log"}, "time": "8-21-2025 00:28:27", "dirty": false}}}}